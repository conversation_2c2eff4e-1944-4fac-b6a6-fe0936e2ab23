{"passed": 8, "failed": 0, "tests": [{"name": "Production Backend Health", "status": "PASSED", "duration": 709}, {"name": "Production Frontend Health", "status": "PASSED", "duration": 859}, {"name": "User Authentication (All Roles)", "status": "PASSED", "duration": 2491}, {"name": "Products API", "status": "PASSED", "duration": 373}, {"name": "Order Validation", "status": "PASSED", "duration": 170}, {"name": "Order Creation", "status": "PASSED", "duration": 1488}, {"name": "Chef Order Management", "status": "PASSED", "duration": 1282}, {"name": "Manager Access", "status": "PASSED", "duration": 916}], "timing": {"Production Backend Health": 709, "Production Frontend Health": 859, "User Authentication (All Roles)": 2491, "Products API": 373, "Order Validation": 170, "Order Creation": 1488, "Chef Order Management": 1282, "Manager Access": 916}}