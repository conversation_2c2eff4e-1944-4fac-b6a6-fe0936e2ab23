import React from 'react';
import type { ChefNotification } from '../../models/ChefModels';

interface NotificationsViewProps {
  notifications: ChefNotification[];
  onMarkAsRead: (notificationId: string) => void;
  onMarkAllAsRead: () => void;
  onDeleteNotification: (notificationId: string) => void;
  loading: boolean;
}

export const NotificationsView: React.FC<NotificationsViewProps> = ({
  notifications,
  onMarkAsRead,
  onMarkAllAsRead,
  onDeleteNotification,
  loading
}) => {
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'new_order': return '🆕';
      case 'order_updated': return '📝';
      case 'priority_order': return '🚨';
      case 'general': return '📢';
      default: return '📱';
    }
  };

  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'new_order': return '#4caf50';
      case 'order_updated': return '#2196f3';
      case 'priority_order': return '#f44336';
      case 'general': return '#ff9800';
      default: return '#9e9e9e';
    }
  };

  const formatTime = (date: string) => {
    const now = new Date();
    const notificationDate = new Date(date);
    const diffInMinutes = Math.floor((now.getTime() - notificationDate.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) {
      return 'الآن';
    } else if (diffInMinutes < 60) {
      return `منذ ${diffInMinutes} دقيقة`;
    } else if (diffInMinutes < 1440) {
      const hours = Math.floor(diffInMinutes / 60);
      return `منذ ${hours} ساعة`;
    } else {
      const days = Math.floor(diffInMinutes / 1440);
      return `منذ ${days} يوم`;
    }
  };

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>جاري تحميل الإشعارات...</p>
      </div>
    );
  }

  return (
    <div className="notifications-view">
      <div className="notifications-header">
        <h2>الإشعارات</h2>
        {notifications.filter(n => !n.isRead).length > 0 && (
          <button
            className="btn btn-secondary"
            onClick={onMarkAllAsRead}
          >
            تحديد الكل كمقروء
          </button>
        )}
      </div>

      <div className="notifications-list">
        {notifications.length === 0 ? (
          <div className="empty-state">
            <div className="empty-icon">🔔</div>
            <h3>لا توجد إشعارات</h3>
            <p>ستظهر الإشعارات الجديدة هنا</p>
          </div>
        ) : (
          notifications.map((notification) => (
            <div
              key={notification._id}
              className={`notification-card ${!notification.isRead ? 'unread' : ''}`}
            >
              <div className="notification-content">
                <div className="notification-icon">
                  <span
                    style={{ 
                      backgroundColor: getNotificationColor(notification.type),
                      color: 'white',
                      borderRadius: '50%',
                      width: '30px',
                      height: '30px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: '14px'
                    }}
                  >
                    {getNotificationIcon(notification.type)}
                  </span>
                </div>
                
                <div className="notification-body">
                  <h4>{notification.title}</h4>
                  <p>{notification.message}</p>
                  <div className="notification-meta">
                    <span className="notification-time">
                      {formatTime(notification.createdAt)}
                    </span>
                    {notification.orderId && (
                      <span className="notification-order">
                        طلب #{notification.orderId}
                      </span>
                    )}
                  </div>
                </div>
              </div>

              <div className="notification-actions">
                {!notification.isRead && (
                  <button
                    className="btn btn-sm btn-outline"
                    onClick={() => onMarkAsRead(notification._id)}
                    title="تحديد كمقروء"
                  >
                    ✓
                  </button>
                )}
                <button
                  className="btn btn-sm btn-outline btn-danger"
                  onClick={() => onDeleteNotification(notification._id)}
                  title="حذف الإشعار"
                >
                  🗑️
                </button>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};
