// اختبار شامل للوحة النادل مع البيانات الحقيقية في الإنتاج
import fetch from 'node-fetch';

const PRODUCTION_API = 'https://deshacoffee-production.up.railway.app';

class WaiterDashboardTester {
    constructor() {
        this.results = {
            totalTests: 0,
            passedTests: 0,
            failedTests: 0,
            issues: []
        };
    }

    async test(description, testFn) {
        this.results.totalTests++;
        console.log(`🧪 ${description}...`);
        
        try {
            await testFn();
            this.results.passedTests++;
            console.log(`✅ ${description} - نجح`);
        } catch (error) {
            this.results.failedTests++;
            this.results.issues.push({
                test: description,
                error: error.message
            });
            console.log(`❌ ${description} - فشل: ${error.message}`);
        }
    }

    async testTableAccounts() {
        await this.test('جلب حسابات الطاولات', async () => {
            const response = await fetch(`${PRODUCTION_API}/api/table-accounts`);
            if (!response.ok) throw new Error(`HTTP ${response.status}`);
            
            const data = await response.json();
            const accounts = Array.isArray(data) ? data : (data.data || []);
            
            console.log(`   📊 تم جلب ${accounts.length} حساب طاولة`);
            
            if (accounts.length > 0) {
                // اختبار بنية البيانات
                const firstAccount = accounts[0];
                if (!firstAccount.tableNumber) throw new Error('رقم الطاولة مفقود');
                if (!Array.isArray(firstAccount.orders)) throw new Error('قائمة الطلبات غير صحيحة');
                
                console.log(`   ✓ بنية البيانات صحيحة`);
                
                // اختبار حساب المبالغ
                let totalCalculated = 0;
                let totalOrders = 0;
                
                accounts.forEach(account => {
                    const accountTotal = account.totalAmount && account.totalAmount > 0
                        ? account.totalAmount
                        : (account.orders?.reduce((sum, order) => {
                            const orderPrice = order.totalPrice || 0;
                            return sum + orderPrice;
                        }, 0) || 0);
                    
                    totalCalculated += accountTotal;
                    totalOrders += account.orders?.length || 0;
                });
                
                console.log(`   💰 إجمالي المبيعات المحسوب: ${totalCalculated.toFixed(2)} ج.م`);
                console.log(`   📋 إجمالي الطلبات: ${totalOrders}`);
            }
        });
    }

    async testOrderNumbers() {
        await this.test('فحص أرقام الطلبات', async () => {
            const response = await fetch(`${PRODUCTION_API}/api/table-accounts`);
            const data = await response.json();
            const accounts = Array.isArray(data) ? data : (data.data || []);
            
            let ordersWithoutNumbers = 0;
            let totalOrdersChecked = 0;
            
            accounts.forEach(account => {
                if (account.orders) {
                    account.orders.forEach(order => {
                        totalOrdersChecked++;
                        if (!order.orderNumber && !order._id) {
                            ordersWithoutNumbers++;
                        }
                    });
                }
            });
            
            console.log(`   📊 تم فحص ${totalOrdersChecked} طلب`);
            console.log(`   ⚠️  طلبات بدون أرقام: ${ordersWithoutNumbers}`);
            
            if (ordersWithoutNumbers > 0) {
                console.log(`   💡 سيتم عرضها كـ "غير محدد" في الواجهة`);
            }
        });
    }

    async testOrdersAPI() {
        await this.test('واجهة برمجة تطبيقات الطلبات', async () => {
            const response = await fetch(`${PRODUCTION_API}/api/orders`);
            if (!response.ok) throw new Error(`HTTP ${response.status}`);
            
            const orders = await response.json();
            console.log(`   📦 تم جلب ${orders.length} طلب من API المباشر`);
            
            if (orders.length > 0) {
                const recentOrder = orders[0];
                console.log(`   🔍 آخر طلب:`);
                console.log(`      - ID: ${recentOrder._id || 'غير محدد'}`);
                console.log(`      - رقم الطلب: ${recentOrder.orderNumber || 'غير محدد'}`);
                console.log(`      - المبلغ: ${recentOrder.totalPrice || 0} ج.م`);
                console.log(`      - الحالة: ${recentOrder.status || 'غير محدد'}`);
            }
        });
    }

    async testCalculationAccuracy() {
        await this.test('دقة الحسابات', async () => {
            const response = await fetch(`${PRODUCTION_API}/api/table-accounts`);
            const data = await response.json();
            const accounts = Array.isArray(data) ? data : (data.data || []);
            
            let calculationErrors = 0;
            
            accounts.forEach((account, index) => {
                if (account.orders && account.orders.length > 0) {
                    const calculatedTotal = account.orders.reduce((sum, order) => {
                        return sum + (order.totalPrice || 0);
                    }, 0);
                    
                    const declaredTotal = account.totalAmount || 0;
                    
                    if (declaredTotal > 0 && Math.abs(calculatedTotal - declaredTotal) > 0.01) {
                        calculationErrors++;
                        console.log(`   ⚠️  طاولة ${account.tableNumber}: مخالفة في الحساب`);
                        console.log(`      المحسوب: ${calculatedTotal.toFixed(2)} ج.م`);
                        console.log(`      المعلن: ${declaredTotal.toFixed(2)} ج.م`);
                    }
                }
            });
            
            if (calculationErrors === 0) {
                console.log(`   ✓ جميع الحسابات دقيقة`);
            } else {
                console.log(`   ⚠️  تم العثور على ${calculationErrors} خطأ في الحساب`);
            }
        });
    }

    async runAllTests() {
        console.log('🚀 بدء الاختبار الشامل للوحة النادل - البيانات الحقيقية');
        console.log('🌐 الخادم: Railway Production');
        console.log('🗄️  قاعدة البيانات: MongoDB Atlas');
        console.log('=' .repeat(60));
        
        await this.testTableAccounts();
        await this.testOrderNumbers();
        await this.testOrdersAPI();
        await this.testCalculationAccuracy();
        
        console.log('=' .repeat(60));
        console.log('📊 ملخص نتائج الاختبار:');
        console.log(`   إجمالي الاختبارات: ${this.results.totalTests}`);
        console.log(`   نجح: ${this.results.passedTests} ✅`);
        console.log(`   فشل: ${this.results.failedTests} ❌`);
        
        if (this.results.failedTests > 0) {
            console.log('\n🔍 تفاصيل المشاكل:');
            this.results.issues.forEach((issue, index) => {
                console.log(`   ${index + 1}. ${issue.test}: ${issue.error}`);
            });
        }
        
        const successRate = (this.results.passedTests / this.results.totalTests * 100).toFixed(1);
        console.log(`\n🎯 معدل النجاح: ${successRate}%`);
        
        if (this.results.failedTests === 0) {
            console.log('\n🎉 جميع الاختبارات نجحت! لوحة النادل تعمل بشكل مثالي.');
        } else {
            console.log('\n⚠️  يوجد مشاكل تحتاج إلى مراجعة.');
        }
    }
}

// تشغيل الاختبار
const tester = new WaiterDashboardTester();
tester.runAllTests().catch(error => {
    console.error('❌ خطأ عام في الاختبار:', error.message);
    process.exit(1);
});
