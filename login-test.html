<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تسجيل الدخول - مقهى ديشا</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            text-align: right;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 500px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        button {
            background-color: #8B4513;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
        button:hover {
            background-color: #6B3410;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .user-preset {
            background-color: #e7f3ff;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            cursor: pointer;
            border: 1px solid #b3d9ff;
        }
        .user-preset:hover {
            background-color: #d4edfc;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 اختبار تسجيل الدخول - نظام إدارة مقهى ديشا</h1>
        
        <div class="form-group">
            <h3>👥 المستخدمين المتاحين (اضغط لملء البيانات):</h3>
            <div class="user-preset" onclick="fillUser('Beso', 'MOHAMEDmostafa123', 'مدير')">
                🏆 <strong>Beso</strong> - مدير عام / MOHAMEDmostafa123
            </div>
            <div class="user-preset" onclick="fillUser('azz', '253040', 'نادل')">
                👔 <strong>azz</strong> - نادل / 253040
            </div>
            <div class="user-preset" onclick="fillUser('Bosy', '253040', 'نادل')">
                👔 <strong>Bosy</strong> - نادل / 253040
            </div>
            <div class="user-preset" onclick="fillUser('khaled', '253040', 'طباخ')">
                👨‍🍳 <strong>khaled</strong> - طباخ / 253040
            </div>
        </div>

        <form id="loginForm">
            <div class="form-group">
                <label for="username">اسم المستخدم:</label>
                <input type="text" id="username" name="username" required>
            </div>
            
            <div class="form-group">
                <label for="password">كلمة المرور:</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <div class="form-group">
                <label for="apiUrl">رابط API:</label>
                <select id="apiUrl">
                    <option value="https://deshacoffee-production.up.railway.app">Production (Railway)</option>
                    <option value="http://localhost:4003">Local Development</option>
                </select>
            </div>
            
            <button type="submit">تسجيل الدخول</button>
        </form>
        
        <div id="result"></div>
    </div>

    <script>
        function fillUser(username, password, role) {
            document.getElementById('username').value = username;
            document.getElementById('password').value = password;
        }

        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();
            const apiUrl = document.getElementById('apiUrl').value;
            const resultDiv = document.getElementById('result');
            
            if (!username || !password) {
                resultDiv.innerHTML = '<div class="error">الرجاء إدخال اسم المستخدم وكلمة المرور</div>';
                return;
            }
            
            resultDiv.innerHTML = '<div>جاري تسجيل الدخول...</div>';
            
            try {
                const response = await fetch(`${apiUrl}/api/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultDiv.innerHTML = `<div class="success">
✅ تسجيل الدخول نجح!

📊 بيانات المستخدم:
اسم المستخدم: ${data.user.username}
الاسم: ${data.user.name}
الدور: ${data.user.role}
البريد الإلكتروني: ${data.user.email}
الحالة: ${data.user.status}

🔑 Token متوفر: ${data.token ? 'نعم' : 'لا'}

🌐 API المستخدم: ${apiUrl}
                    </div>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">
❌ فشل تسجيل الدخول!

الخطأ: ${data.message || data.error || 'خطأ غير معروف'}
كود HTTP: ${response.status}

🌐 API المستخدم: ${apiUrl}
                    </div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">
❌ خطأ في الشبكة!

تفاصيل الخطأ: ${error.message}

🌐 API المستخدم: ${apiUrl}

💡 تأكد من:
- تشغيل الخادم
- صحة رابط API
- الاتصال بالإنترنت
                </div>`;
            }
        });
    </script>
</body>
</html>
