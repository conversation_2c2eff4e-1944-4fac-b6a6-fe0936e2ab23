import { useEffect, useState } from 'react';
import { checkServerHealth } from '../utils/api';
import './ConnectionStatus.css';

interface ConnectionState {
  isConnected: boolean;
  serverStatus: string;
  databaseStatus: string;
  lastChecked: Date | null;
  error: string | null;
}

const ConnectionStatus = () => {
  const [state, setState] = useState<ConnectionState>({
    isConnected: false,
    serverStatus: 'جاري الفحص...',
    databaseStatus: 'جاري الفحص...',
    lastChecked: null,
    error: null
  });
  const [isChecking, setIsChecking] = useState(true);
  const [retryCount, setRetryCount] = useState(0);
  const checkConnection = async () => {
    try {
      const response = await checkServerHealth();

      // تحقق من صحة الاستجابة
      const isServerConnected = response.success && response.data?.status === 'OK';
      const isDatabaseConnected = response.success && response.data?.database?.connected === true;

      setState({
        isConnected: isServerConnected,
        serverStatus: isServerConnected ? 'متصل' : 'غير متصل',
        databaseStatus: isDatabaseConnected ? 'متصل' : 'غير متصل',
        lastChecked: new Date(),
        error: null
      });
      setIsChecking(false);
      setRetryCount(0);
    } catch (error: any) {
      console.error('Connection check failed:', error);
      setState(prev => ({
        ...prev,
        isConnected: false,
        serverStatus: 'غير متصل',
        databaseStatus: 'غير متصل',
        error: error.message || 'فشل في الاتصال بالخادم'
      }));
      setIsChecking(false);
      if (retryCount < 3) {
        setTimeout(() => {
          setIsChecking(true);
          setRetryCount(prev => prev + 1);
        }, 5000);
      }
    }
  };

  useEffect(() => {
    if (isChecking) {
      checkConnection();
    }
  }, [isChecking, retryCount]);
  // إخفاء الشاشة إذا كان الاتصال متصل
  if (state.isConnected) {
    return null;
  }

  return (
    <div className={`connection-status-bar error`}
         style={{position:'fixed',top:10,right:10,zIndex:1000,minWidth:220,maxWidth:320,boxShadow:'0 2px 8px #0001',borderRadius:8,background:'#fff',padding:'1.2rem',direction:'rtl',textAlign:'right'}}>
      <div className="connection-status-header" style={{display:'flex',alignItems:'center',gap:8}}>
        <span className="status-indicator error"
              style={{display:'inline-block',width:12,height:12,borderRadius:'50%',background:'#f44336',marginLeft:8}}></span>
        <strong style={{fontSize:16}}>
          غير متصل بالخادم
        </strong>
      </div>
      <div className="connection-details" style={{fontSize:14,marginTop:8}}>
        <span>الخادم: {state.serverStatus}</span><br/>
        <span>قاعدة البيانات: {state.databaseStatus}</span>
        {state.error && <div className="error-message" style={{color:'#f44336',marginTop:4}}>{state.error}</div>}
      </div>
      <div className="connection-actions" style={{marginTop:8}}>
        <button onClick={() => setIsChecking(true)} disabled={isChecking} style={{padding:'4px 16px',borderRadius:4,border:'none',background:'#f44336',color:'#fff',cursor:'pointer'}}>
          {isChecking ? 'جاري إعادة المحاولة...' : 'إعادة المحاولة'}
        </button>
        <small style={{display:'block',marginTop:4,color:'#888'}}>
          {state.lastChecked
            ? `آخر فحص: ${state.lastChecked.toLocaleTimeString()}`
            : 'لم يتم الفحص بعد'}
        </small>
      </div>
    </div>
  );
};

export default ConnectionStatus;