import mongoose from 'mongoose';
import bcrypt from 'bcrypt';
import dotenv from 'dotenv';

// تحميل متغيرات البيئة
dotenv.config();

// إعدادات الاتصال بقاعدة البيانات
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb+srv://besomustafa:<EMAIL>/deshacoffee?retryWrites=true&w=majority&appName=MyCoffeChop';

// تعريف نموذج المستخدم
const userSchema = new mongoose.Schema({
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  password: {
    type: String,
    required: true
  },
  role: {
    type: String,
    required: true,
    enum: ['manager', 'waiter', 'chef']
  },
  active: {
    type: Boolean,
    default: true
  },
  name: {
    type: String,
    default: ''
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

const User = mongoose.model('User', userSchema);

// قائمة المستخدمين المراد إضافتهم
const usersToAdd = [
  {
    username: 'Beso',
    password: 'MOHAMEDmostafa123',
    role: 'manager', // مدير
    name: 'بيسو'
  },
  {
    username: 'azza',
    password: '253040',
    role: 'waiter', // نادل
    name: 'عزة'
  },
  {
    username: 'Bosy',
    password: '253040',
    role: 'waiter', // نادل
    name: 'بوسي'
  },
  {
    username: 'khaled',
    password: '253040',
    role: 'chef', // طباخ
    name: 'خالد'
  }
];

// دالة تشفير كلمة المرور
async function hashPassword(password) {
  const saltRounds = 12;
  return await bcrypt.hash(password, saltRounds);
}

// دالة إضافة المستخدمين
async function addUsers() {
  let connection;
  
  try {
    console.log('🔌 جاري الاتصال بقاعدة البيانات MongoDB...');
    
    // الاتصال بقاعدة البيانات
    connection = await mongoose.connect(MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');
    console.log(`📊 قاعدة البيانات: ${connection.connection.name}`);
    console.log('');

    // التحقق من وجود المجموعة (collection)
    const collections = await mongoose.connection.db.listCollections().toArray();
    const userCollectionExists = collections.some(col => col.name === 'users');
    
    if (!userCollectionExists) {
      console.log('📝 مجموعة المستخدمين غير موجودة، سيتم إنشاؤها تلقائياً');
    }

    console.log('👥 جاري إضافة المستخدمين...');
    console.log('');

    // إضافة كل مستخدم
    for (const userData of usersToAdd) {
      try {
        // التحقق من وجود المستخدم
        const existingUser = await User.findOne({ username: userData.username });
        
        // تشفير كلمة المرور
        console.log(`🔐 جاري تشفير كلمة المرور للمستخدم: ${userData.username}`);
        const hashedPassword = await hashPassword(userData.password);
        
        if (existingUser) {
          // تحديث المستخدم الموجود
          console.log(`🔄 المستخدم ${userData.username} موجود بالفعل، جاري تحديث بياناته...`);
          
          existingUser.password = hashedPassword;
          existingUser.role = userData.role;
          existingUser.active = true;
          existingUser.name = userData.name;
          
          await existingUser.save();
          console.log(`✅ تم تحديث المستخدم: ${userData.username} (${userData.role})`);
        } else {
          // إنشاء مستخدم جديد
          console.log(`➕ جاري إنشاء مستخدم جديد: ${userData.username}`);
          
          const newUser = new User({
            username: userData.username,
            password: hashedPassword,
            role: userData.role,
            active: true,
            name: userData.name
          });
          
          await newUser.save();
          console.log(`✅ تم إضافة المستخدم الجديد: ${userData.username} (${userData.role})`);
        }
        
        console.log(`   📧 اسم المستخدم: ${userData.username}`);
        console.log(`   🔑 كلمة المرور الأصلية: ${userData.password}`);
        console.log(`   👤 الدور: ${userData.role}`);
        console.log(`   📝 الاسم: ${userData.name}`);
        console.log('');
        
      } catch (userError) {
        console.error(`❌ خطأ في إضافة المستخدم ${userData.username}:`, userError.message);
      }
    }

    // التحقق من النتائج النهائية
    const totalUsers = await User.countDocuments();
    console.log('📊 إحصائيات نهائية:');
    console.log(`   📈 إجمالي المستخدمين في قاعدة البيانات: ${totalUsers}`);
    
    // عرض جميع المستخدمين
    const allUsers = await User.find({}, 'username role active name').lean();
    console.log('');
    console.log('👥 قائمة المستخدمين الحالية:');
    allUsers.forEach((user, index) => {
      const roleArabic = user.role === 'manager' ? 'مدير' : user.role === 'waiter' ? 'نادل' : 'طباخ';
      console.log(`   ${index + 1}. ${user.username} - ${roleArabic} - ${user.active ? 'نشط' : 'غير نشط'} - ${user.name || 'بدون اسم'}`);
    });

    console.log('');
    console.log('🎉 تمت العملية بنجاح! يمكنك الآن تسجيل الدخول باستخدام البيانات التالية:');
    console.log('');
    
    usersToAdd.forEach((user, index) => {
      const roleArabic = user.role === 'manager' ? 'مدير' : user.role === 'waiter' ? 'نادل' : 'طباخ';
      console.log(`${index + 1}. اسم المستخدم: ${user.username}`);
      console.log(`   كلمة المرور: ${user.password}`);
      console.log(`   الدور: ${roleArabic}`);
      console.log('');
    });

  } catch (error) {
    console.error('');
    console.error('❌ حدث خطأ أثناء إضافة المستخدمين:');
    console.error('   النوع:', error.name);
    console.error('   الرسالة:', error.message);
    
    if (error.name === 'MongoNetworkError') {
      console.error('');
      console.error('🔗 مشكلة في الاتصال بقاعدة البيانات. تأكد من:');
      console.error('   1. صحة رابط MONGODB_URI');
      console.error('   2. أن عنوان IP الخاص بك مضاف إلى MongoDB Atlas');
      console.error('   3. أن بيانات الاعتماد صحيحة');
    }
    
  } finally {
    try {
      if (connection) {
        await mongoose.disconnect();
        console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
      }
    } catch (disconnectError) {
      console.error('❌ خطأ في قطع الاتصال:', disconnectError.message);
    }
  }
}

// تشغيل الدالة
console.log('🚀 بدء عملية إضافة المستخدمين إلى قاعدة البيانات...');
console.log('⏰ الوقت:', new Date().toLocaleString('ar-EG'));
console.log('');

addUsers()
  .then(() => {
    console.log('✅ انتهت العملية');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ فشلت العملية:', error);
    process.exit(1);
  });