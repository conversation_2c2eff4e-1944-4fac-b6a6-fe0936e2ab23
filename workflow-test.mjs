// Automated Workflow Test Script
// This script tests the complete workflow of the coffee shop management system

const API_BASE = 'http://localhost:4003';

class WorkflowTester {
  constructor() {
    this.testResults = {
      passed: 0,
      failed: 0,
      tests: []
    };
  }

  async runTest(testName, testFunction) {
    console.log(`🧪 Running test: ${testName}`);
    try {
      await testFunction();
      this.testResults.passed++;
      this.testResults.tests.push({ name: testName, status: 'PASSED' });
      console.log(`✅ ${testName} - PASSED`);
    } catch (error) {
      this.testResults.failed++;
      this.testResults.tests.push({ 
        name: testName, 
        status: 'FAILED', 
        error: error.message 
      });
      console.error(`❌ ${testName} - FAILED: ${error.message}`);
    }
  }

  async authenticatedRequest(endpoint, options = {}) {
    // For testing purposes, we'll use a mock token
    // In real scenario, you'd get this from login
    const token = 'mock_test_token';
    
    const response = await fetch(`${API_BASE}${endpoint}`, {
      ...options,
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
        ...options.headers
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${await response.text()}`);
    }

    return response.json();
  }
  // Test 1: Check if servers are running
  async testServerStatus() {
    const response = await fetch(`${API_BASE}/health`);
    if (!response.ok) {
      throw new Error('Backend server not responding');
    }
    const data = await response.json();
    if (data.status !== 'OK') {
      throw new Error('Backend health check failed');
    }
    console.log(`🟢 Backend server is healthy (${data.version})`);
    console.log(`🗄️ Database: ${data.database.message}`);
  }

  // Test 2: Test order creation without table number (should fail)
  async testOrderWithoutTableNumber() {
    try {
      const orderData = {
        waiterName: 'test_waiter',
        items: [{
          product: '507f1f77bcf86cd799439011',
          name: 'قهوة عربية',
          quantity: 1,
          price: 15
        }],
        totalPrice: 15,
        customerName: 'أحمد محمد',
        status: 'pending'
        // No tableNumber - this should fail
      };

      await this.authenticatedRequest('/api/orders', {
        method: 'POST',
        body: JSON.stringify(orderData)
      });

      throw new Error('Order creation should have failed without table number');
    } catch (error) {
      if (error.message.includes('رقم الطاولة مطلوب') || 
          error.message.includes('table number') ||
          error.message.includes('HTTP 400')) {
        // This is expected - the test should pass
        return;
      }
      throw error;
    }
  }

  // Test 3: Test valid order creation
  async testValidOrderCreation() {
    const orderData = {
      waiterName: 'test_waiter',
      items: [{
        product: '507f1f77bcf86cd799439011',
        name: 'قهوة عربية',
        quantity: 1,
        price: 15,
        notes: 'بدون سكر'
      }],
      totalPrice: 15,
      tableNumber: '5',
      customerName: 'أحمد محمد',
      status: 'pending'
    };

    const result = await this.authenticatedRequest('/api/orders', {
      method: 'POST',
      body: JSON.stringify(orderData)
    });

    if (!result.success || !result.data) {
      throw new Error('Order creation failed');
    }

    // Store order ID for later tests
    this.testOrderId = result.data._id;
    console.log(`📋 Created test order: ${this.testOrderId}`);
  }

  // Test 4: Test table conflict prevention
  async testTableConflictPrevention() {
    // Try to create another order with the same table number but different waiter
    const conflictOrderData = {
      waiterName: 'different_waiter',
      items: [{
        product: '507f1f77bcf86cd799439011',
        name: 'شاي أحمر',
        quantity: 1,
        price: 10
      }],
      totalPrice: 10,
      tableNumber: '5', // Same table as previous test
      customerName: 'محمد علي',
      status: 'pending'
    };

    try {
      await this.authenticatedRequest('/api/orders', {
        method: 'POST',
        body: JSON.stringify(conflictOrderData)
      });
      
      throw new Error('Order creation should have failed due to table conflict');
    } catch (error) {
      if (error.message.includes('مستخدمة حاليًا') || 
          error.message.includes('HTTP 409') ||
          error.message.includes('conflict')) {
        // This is expected - the test should pass
        return;
      }
      throw error;
    }
  }

  // Test 5: Test order status updates
  async testOrderStatusUpdate() {
    if (!this.testOrderId) {
      throw new Error('No test order available for status update');
    }

    // Update order status to preparing
    const updateResult = await this.authenticatedRequest(`/api/orders/${this.testOrderId}`, {
      method: 'PUT',
      body: JSON.stringify({ status: 'preparing' })
    });

    if (!updateResult.success) {
      throw new Error('Order status update failed');
    }

    // Verify the status was updated
    const orderResult = await this.authenticatedRequest(`/api/orders/${this.testOrderId}`);
    
    if (orderResult.data.status !== 'preparing') {
      throw new Error('Order status was not updated correctly');
    }
  }

  // Test 6: Test fetching orders
  async testFetchOrders() {
    const orders = await this.authenticatedRequest('/api/orders');
    
    if (!Array.isArray(orders)) {
      throw new Error('Orders endpoint should return an array');
    }

    // Check if our test order is in the list
    if (this.testOrderId) {
      const testOrder = orders.find(order => order._id === this.testOrderId);
      if (!testOrder) {
        throw new Error('Test order not found in orders list');
      }
    }
  }

  // Test 7: Test table accounts
  async testTableAccounts() {
    const tableAccounts = await this.authenticatedRequest('/api/table-accounts');
    
    if (!Array.isArray(tableAccounts)) {
      throw new Error('Table accounts endpoint should return an array');
    }
  }

  // Run all tests
  async runAllTests() {
    console.log('🚀 Starting Workflow Tests...\n');
    
    await this.runTest('Server Status Check', () => this.testServerStatus());
    await this.runTest('Order Without Table Number (Should Fail)', () => this.testOrderWithoutTableNumber());
    await this.runTest('Valid Order Creation', () => this.testValidOrderCreation());
    await this.runTest('Table Conflict Prevention', () => this.testTableConflictPrevention());
    await this.runTest('Order Status Update', () => this.testOrderStatusUpdate());
    await this.runTest('Fetch Orders', () => this.testFetchOrders());
    await this.runTest('Table Accounts', () => this.testTableAccounts());

    // Print results
    console.log('\n📊 Test Results:');
    console.log('================');
    console.log(`✅ Passed: ${this.testResults.passed}`);
    console.log(`❌ Failed: ${this.testResults.failed}`);
    console.log(`📋 Total: ${this.testResults.tests.length}`);
    
    if (this.testResults.failed > 0) {
      console.log('\n❌ Failed Tests:');
      this.testResults.tests
        .filter(test => test.status === 'FAILED')
        .forEach(test => {
          console.log(`  - ${test.name}: ${test.error}`);
        });
    }

    const successRate = (this.testResults.passed / this.testResults.tests.length) * 100;
    console.log(`\n🎯 Success Rate: ${successRate.toFixed(1)}%`);

    if (successRate === 100) {
      console.log('\n🏆 ALL TESTS PASSED! ✅');
      console.log('🎉 Workflow is working correctly!');
    } else {
      console.log('\n⚠️  Some tests failed. Please check the system.');
    }

    return this.testResults;
  }
}

// Run tests immediately
const tester = new WorkflowTester();
tester.runAllTests().catch(console.error);

export default WorkflowTester;
