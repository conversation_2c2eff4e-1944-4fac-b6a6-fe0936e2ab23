// Service Worker للـ PWA
const CACHE_NAME = 'coffee-shop-v1';
const urlsToCache = [
  '/',
  '/coffee-logo.svg',
  '/coffee-cup.svg',
  '/manifest.json'
];

// تثبيت Service Worker
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('فتح الكاش');
        return cache.addAll(urlsToCache);
      })
  );
});

// جلب الملفات من الكاش
self.addEventListener('fetch', (event) => {
  const url = event.request.url;

  // تجاهل جميع ملفات JavaScript/CSS المُولدة من Vite
  if (url.includes('/assets/') ||
      url.includes('/@vite/') ||
      url.includes('/@react-refresh') ||
      url.includes('/src/') ||
      url.includes('.js') ||
      url.includes('.css') ||
      url.includes('.tsx') ||
      url.includes('.ts') ||
      url.includes('index-') ||
      url.includes('.map')) {
    // السماح للطلب بالمرور مباشرة بدون تدخل
    return;
  }

  // فقط cache الملفات الثابتة الأساسية
  if (url.includes('.svg') || url.includes('.png') || url.includes('.jpg') || url.includes('manifest.json')) {
    event.respondWith(
      caches.match(event.request)
        .then((response) => {
          return response || fetch(event.request);
        })
        .catch(() => fetch(event.request))
    );
  }
});

// تحديث Service Worker
self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            console.log('حذف الكاش القديم:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
});
