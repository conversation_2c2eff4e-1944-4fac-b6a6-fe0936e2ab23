@echo off
echo ========================================
echo       اختبار نظام إشعارات مقهى ديشا
echo ========================================
echo.

echo [1] تشغيل الخادم الخلفي (Backend)
start "Backend Server" cmd /k "cd /d C:\Users\<USER>\OneDrive\Desktop\PRINT\Coffee\Coffee\backend && npm start"

echo.
echo انتظار 5 ثوانٍ لبدء الخادم...
timeout /t 5 /nobreak > nul

echo.
echo [2] تشغيل واجهة المستخدم (Frontend)
start "Frontend Dev Server" cmd /k "cd /d C:\Users\<USER>\OneDrive\Desktop\PRINT\Coffee\Coffee && npm start"

echo.
echo [3] فتح المتصفح للاختبار
timeout /t 3 /nobreak > nul
start http://localhost:3000

echo.
echo ========================================
echo            تعليمات الاختبار
echo ========================================
echo.
echo 1. افتح نافذتين في المتصفح:
echo    - النافذة الأولى: ChefDashboard
echo    - النافذة الثانية: WaiterDashboard
echo.
echo 2. قم بإنشاء طلب جديد من WaiterDashboard
echo.
echo 3. تحقق من ظهور الإشعار في ChefDashboard
echo.
echo 4. قم بتحديث حالة الطلب من ChefDashboard
echo.
echo 5. تحقق من ظهور التحديث في WaiterDashboard
echo.
echo للاختبار المتقدم:
echo - شغل: node socket-test-client.js
echo.
echo ========================================
echo اضغط أي مفتاح لإغلاق هذه النافذة...
pause > nul
