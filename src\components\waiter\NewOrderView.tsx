// مكون الطلب الجديد - لوحة النادل
import React, { useState, useEffect } from 'react';
import type { CartItem, WaiterTableAccount, NewOrderData } from '../../models/WaiterModels';
import { WaiterDataService } from '../../services/WaiterDataService';
import { useToast } from '../../hooks/useToast';
import { getOrderFinalPrice } from '../../utils/orderHelpers';

interface NewOrderViewProps {
  cart: CartItem[];
  onUpdateCartQuantity: (productId: string, quantity: number) => void;
  onRemoveFromCart: (productId: string) => void;
  onClearCart: () => void;
  onSubmitOrder: (orderData: NewOrderData) => Promise<boolean>;
  selectedTable: WaiterTableAccount | null;
  onTableSelect: (table: WaiterTableAccount | null) => void;
}

export const NewOrderView: React.FC<NewOrderViewProps> = ({
  cart,
  onUpdateCartQuantity,
  onRemoveFromCart,
  onClearCart,
  onSubmitOrder,
  selectedTable,
  onTableSelect
}) => {
  const [tables, setTables] = useState<WaiterTableAccount[]>([]);
  const [customerName, setCustomerName] = useState('');
  const [orderNotes, setOrderNotes] = useState('');
  const [itemNotes, setItemNotes] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const { showSuccess, showError } = useToast();

  // جلب الطاولات المتاحة
  useEffect(() => {
    const fetchTables = async () => {
      try {
        setLoading(true);
        const waiterService = WaiterDataService.getInstance();
        const tablesData = await waiterService.getMyTables();
        setTables(tablesData);
      } catch (error) {
        console.error('❌ خطأ في جلب الطاولات:', error);
        showError('فشل في جلب الطاولات');
      } finally {
        setLoading(false);
      }
    };

    fetchTables();
  }, [showError]);

  // حساب المجموع
  const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const { finalPrice, appliedDiscount } = getOrderFinalPrice({
    totalPrice: subtotal,
    discountPercent: selectedTable?.discountPercent || 0
  });

  // تحديث الكمية
  const handleQuantityChange = (productId: string, quantity: number) => {
    if (quantity <= 0) {
      onRemoveFromCart(productId);
    } else {
      onUpdateCartQuantity(productId, quantity);
    }
  };

  // تحديث ملاحظات العنصر
  const handleItemNoteChange = (productId: string, note: string) => {
    setItemNotes(prev => ({
      ...prev,
      [productId]: note
    }));
  };

  // إرسال الطلب
  const handleSubmitOrder = async () => {
    if (cart.length === 0) {
      showError('السلة فارغة');
      return;
    }

    if (!selectedTable) {
      showError('يرجى اختيار طاولة');
      return;
    }

    try {
      setSubmitting(true);
        const orderData: NewOrderData = {
        tableId: selectedTable._id,
        tableNumber: selectedTable.tableNumber,
        customerName: customerName.trim() || 'زبون',
        items: cart.map(item => ({
          _id: item._id,
          productId: item.productId || item._id,
          product: item._id,
          name: item.name,
          quantity: item.quantity,
          price: item.price,
          notes: itemNotes[item._id] || ''
        })),
        totalPrice: finalPrice,
        notes: orderNotes.trim(),
        discountPercent: selectedTable.discountPercent || 0
      };

      const success = await onSubmitOrder(orderData);
      
      if (success) {
        // مسح البيانات بعد النجاح
        onClearCart();
        setCustomerName('');
        setOrderNotes('');
        setItemNotes({});
        onTableSelect(null);
        showSuccess('تم إرسال الطلب بنجاح');
      }
    } catch (error) {
      console.error('❌ خطأ في إرسال الطلب:', error);
      showError('فشل في إرسال الطلب');
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="new-order-view">
      {/* اختيار الطاولة */}
      <div className="table-selection">
        <h3>اختيار الطاولة</h3>
        <div className="tables-grid">
          {loading ? (
            <div className="loading-text">جاري تحميل الطاولات...</div>
          ) : tables.length === 0 ? (
            <div className="empty-state">لا توجد طاولات متاحة</div>
          ) : (
            tables.map(table => (
              <button
                key={table._id}
                className={`table-btn ${selectedTable?._id === table._id ? 'selected' : ''}`}
                onClick={() => onTableSelect(table)}
              >
                <div className="table-number">طاولة {table.tableNumber}</div>
                <div className="table-info">
                  <span className="customer-name">
                    {table.customerName || 'غير محدد'}
                  </span>                  {(table.discountPercent || 0) > 0 && (
                    <span className="discount-badge">
                      خصم {table.discountPercent}%
                    </span>
                  )}
                </div>
                <div className="table-total">
                  المجموع: {table.totalAmount?.toFixed(2) || '0.00'} ريال
                </div>
              </button>
            ))
          )}
        </div>
      </div>

      {/* تفاصيل العميل */}
      <div className="customer-details">
        <h3>تفاصيل العميل</h3>
        <input
          type="text"
          placeholder="اسم العميل (اختياري)"
          value={customerName}
          onChange={(e) => setCustomerName(e.target.value)}
          className="customer-name-input"
        />
      </div>

      {/* عرض السلة */}
      <div className="cart-section">
        <div className="cart-header">
          <h3>سلة الطلبات ({cart.length})</h3>
          {cart.length > 0 && (
            <button onClick={onClearCart} className="clear-cart-btn">
              مسح الكل
            </button>
          )}
        </div>

        {cart.length === 0 ? (
          <div className="empty-cart">
            <p>السلة فارغة</p>
            <p>اذهب إلى القائمة لإضافة منتجات</p>
          </div>
        ) : (
          <div className="cart-items">
            {cart.map(item => (
              <div key={item._id} className="cart-item">
                <div className="item-info">
                  <h4 className="item-name">{item.name}</h4>
                  <p className="item-price">{item.price.toFixed(2)} ريال</p>
                </div>

                <div className="quantity-controls">
                  <button
                    onClick={() => handleQuantityChange(item._id, item.quantity - 1)}
                    className="quantity-btn decrease"
                  >
                    -
                  </button>
                  <span className="quantity-display">{item.quantity}</span>
                  <button
                    onClick={() => handleQuantityChange(item._id, item.quantity + 1)}
                    className="quantity-btn increase"
                  >
                    +
                  </button>
                </div>

                <div className="item-total">
                  {(item.price * item.quantity).toFixed(2)} ريال
                </div>

                <button
                  onClick={() => onRemoveFromCart(item._id)}
                  className="remove-item-btn"
                >
                  حذف
                </button>

                {/* ملاحظات العنصر */}
                <div className="item-notes">
                  <input
                    type="text"
                    placeholder="ملاحظات خاصة بهذا العنصر..."
                    value={itemNotes[item._id] || ''}
                    onChange={(e) => handleItemNoteChange(item._id, e.target.value)}
                    className="item-notes-input"
                  />
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* ملاحظات الطلب */}
      {cart.length > 0 && (
        <div className="order-notes">
          <h3>ملاحظات عامة للطلب</h3>
          <textarea
            placeholder="أي ملاحظات إضافية للطلب..."
            value={orderNotes}
            onChange={(e) => setOrderNotes(e.target.value)}
            className="order-notes-textarea"
            rows={3}
          />
        </div>
      )}

      {/* ملخص الطلب */}
      {cart.length > 0 && (
        <div className="order-summary">
          <div className="summary-row">
            <span>المجموع الفرعي:</span>
            <span>{subtotal.toFixed(2)} ريال</span>
          </div>
          
          {appliedDiscount > 0 && (
            <div className="summary-row discount">
              <span>الخصم ({selectedTable?.discountPercent}%):</span>
              <span>-{appliedDiscount.toFixed(2)} ريال</span>
            </div>
          )}
          
          <div className="summary-row total">
            <span>المجموع النهائي:</span>
            <span>{finalPrice.toFixed(2)} ريال</span>
          </div>

          <button
            onClick={handleSubmitOrder}
            disabled={submitting || !selectedTable}
            className="submit-order-btn"
          >
            {submitting ? 'جاري الإرسال...' : 'إرسال الطلب'}
          </button>
        </div>
      )}
    </div>
  );
};
