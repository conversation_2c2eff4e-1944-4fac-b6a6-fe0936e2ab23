// backend/models/Setting.js
const mongoose = require('mongoose');

const settingSchema = new mongoose.Schema({
  key: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    enum: ['siteName', 'currencySymbol', 'taxRate', 'defaultLanguage', 'itemsPerPage', 'isTaxEnabled', 'defaultOrderStatus', 'lowStockThreshold'] // Added 'isTaxEnabled', 'defaultOrderStatus', 'lowStockThreshold'
  },
  value: {
    type: mongoose.Schema.Types.Mixed, // Allows storing different types of values
    required: true
  },
  label: { // User-friendly label for the setting
    type: String,
    required: true
  },
  description: { // Optional description
    type: String,
    trim: true
  },
  type: { // To help render the correct input type on the frontend
    type: String,
    enum: ['text', 'number', 'boolean', 'select'],
    default: 'text'
  },
  options: [String] // For 'select' type
}, {
  timestamps: true
});

settingSchema.statics.getAllSettings = async function() {
  const settings = await this.find({});
  // Transform into a key-value object for easier use on the frontend if needed,
  // or return as an array of objects. For now, returning array.
  return settings;
};

settingSchema.statics.updateSettings = async function(settingsToUpdate) {
  const results = [];
  for (const { key, value } of settingsToUpdate) {
    const result = await this.findOneAndUpdate(
      { key },
      { value, updatedAt: new Date() },
      { new: true, upsert: true, runValidators: true }
    );
    results.push(result);
  }
  return results;
};

module.exports = mongoose.model('Setting', settingSchema);
