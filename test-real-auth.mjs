import fetch from 'node-fetch';

// Configuration
const API_BASE = 'https://deshacoffee-production.up.railway.app';

async function testRealAuth() {
  console.log('🧪 Testing Real Production Authentication...\n');
  
  const testUsers = [
    { username: '<PERSON><PERSON>', password: 'MOHAMEDmostafa123', role: 'مدير' },
    { username: 'azza', password: '253040', role: 'نادل' },
    { username: 'khaled', password: '253040', role: 'طباخ' },
    { username: 'admin', password: 'DeshaCoffee2024Admin!', role: 'أدمن' }
  ];

  for (const user of testUsers) {
    try {
      console.log(`🔐 Testing ${user.role}: ${user.username}...`);
      
      const response = await fetch(`${API_BASE}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          username: user.username,
          password: user.password
        })
      });

      if (response.ok) {
        const data = await response.json();
        console.log(`✅ SUCCESS: ${user.username} logged in successfully`);
        console.log(`   Name: ${data.user?.name || 'N/A'}`);
        console.log(`   Role: ${data.user?.role || 'N/A'}`);
        console.log(`   Token: ${data.token ? '✓ Generated' : '✗ Missing'}\n`);
      } else {
        const error = await response.text();
        console.log(`❌ FAILED: ${user.username} - ${response.status}`);
        console.log(`   Error: ${error}\n`);
      }
    } catch (error) {
      console.log(`❌ ERROR: ${user.username} - ${error.message}\n`);
    }
  }

  // Test health endpoint
  try {
    console.log('🏥 Testing health endpoint...');
    const healthResponse = await fetch(`${API_BASE}/health`);
    
    if (healthResponse.ok) {
      const healthData = await healthResponse.json();
      console.log('✅ Health check passed');
      console.log(`   Status: ${healthData.status}`);
      console.log(`   Database: ${healthData.database?.message || 'N/A'}`);
      console.log(`   Users: ${healthData.database?.userCount || 0}`);
    } else {
      console.log(`❌ Health check failed: ${healthResponse.status}`);
    }
  } catch (error) {
    console.log(`❌ Health check error: ${error.message}`);
  }
}

testRealAuth().catch(console.error);
