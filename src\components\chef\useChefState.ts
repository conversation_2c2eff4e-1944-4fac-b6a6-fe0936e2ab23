// مدير الحالة للوحة الطباخ
import { useState, useCallback } from 'react';
import type { 
  ChefState,
  ChefModalState,
  ChefOrderFilter,
  ChefSettings,
  ChefOrder,
  ChefStats,
  ChefNotification,
  PreparationTimer
} from '../../models/ChefModels';

// الحالة الأولية للفلاتر
const initialFilter: ChefOrderFilter = {
  status: 'all',
  priority: 'all',
  timeRange: 'all',
  searchTerm: ''
};

// الحالة الأولية للنوافذ المنبثقة
const initialModalState: ChefModalState = {
  showOrderModal: false,
  showStatsModal: false,
  showNotificationsModal: false,
  showPreparationTimer: false,
  selectedOrderForModal: null
};

// الحالة الأولية للإحصائيات
const initialStats: ChefStats = {
  todayOrders: 0,
  completedOrders: 0,
  pendingOrders: 0,
  preparingOrders: 0,
  readyOrders: 0,
  averagePreparationTime: 0,
  totalPreparationTime: 0,
  ordersPerHour: 0
};

// الحالة الأولية للإعدادات
const initialSettings: ChefSettings = {
  soundEnabled: true,
  autoRefresh: true,
  refreshInterval: 30000, // 30 ثانية
  showNotifications: true,
  defaultPreparationTime: 15, // 15 دقيقة
  priorityAlerts: true,
  language: 'ar'
};

export function useChefState() {
  // الحالات الأساسية
  const [currentView, setCurrentView] = useState<ChefState['currentView']>('orders');
  const [loading, setLoading] = useState<boolean>(false);
  const [sidebarOpen, setSidebarOpen] = useState<boolean>(false);
  
  // حالات البيانات
  const [orders, setOrders] = useState<ChefOrder[]>([]);
  const [stats, setStats] = useState<ChefStats>(initialStats);
  const [notifications, setNotifications] = useState<ChefNotification[]>([]);
  
  // حالات التحديد
  const [selectedOrder, setSelectedOrder] = useState<ChefOrder | null>(null);

  // حالات الفلاتر والإعدادات
  const [currentFilter, setCurrentFilter] = useState<ChefOrderFilter>(initialFilter);
  const [settings, setSettings] = useState<ChefSettings>(initialSettings);
  
  // حالات النوافذ المنبثقة
  const [modalState, setModalState] = useState<ChefModalState>(initialModalState);

  // حالة مؤقتات التحضير
  const [preparationTimers, setPreparationTimers] = useState<PreparationTimer[]>([]);

  // دوال تحديث البيانات
  const updateOrders = useCallback((newOrders: ChefOrder[]) => {
    setOrders(newOrders);
  }, []);

  const updateStats = useCallback((newStats: ChefStats) => {
    setStats(newStats);
  }, []);

  const updateNotifications = useCallback((newNotifications: ChefNotification[]) => {
    setNotifications(newNotifications);
  }, []);

  // دوال تحديث الفلاتر
  const updateFilter = useCallback(<K extends keyof ChefOrderFilter>(
    key: K, 
    value: ChefOrderFilter[K]
  ) => {
    setCurrentFilter(prev => ({
      ...prev,
      [key]: value
    }));
  }, []);

  const resetFilters = useCallback(() => {
    setCurrentFilter(initialFilter);
  }, []);

  // دوال تحديث الإعدادات
  const updateSetting = useCallback(<K extends keyof ChefSettings>(
    key: K, 
    value: ChefSettings[K]
  ) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  }, []);

  // دوال تحديث النوافذ المنبثقة
  const updateModal = useCallback(<K extends keyof ChefModalState>(
    key: K, 
    value: ChefModalState[K]
  ) => {
    setModalState(prev => ({
      ...prev,
      [key]: value
    }));
  }, []);

  const closeAllModals = useCallback(() => {
    setModalState(initialModalState);
  }, []);

  // دوال مؤقتات التحضير
  const startPreparationTimer = useCallback((order: ChefOrder, estimatedTime: number) => {
    const timer: PreparationTimer = {
      orderId: order._id,
      orderNumber: order.orderNumber,
      startTime: new Date(),
      estimatedTime,
      status: 'running'
    };

    setPreparationTimers(prev => {
      // إزالة أي مؤقت سابق لنفس الطلب
      const filtered = prev.filter(t => t.orderId !== order._id);
      return [...filtered, timer];
    });
  }, []);

  const pausePreparationTimer = useCallback((orderId: string) => {
    setPreparationTimers(prev =>
      prev.map(timer =>
        timer.orderId === orderId
          ? { ...timer, status: 'paused' as const }
          : timer
      )
    );
  }, []);

  const resumePreparationTimer = useCallback((orderId: string) => {
    setPreparationTimers(prev =>
      prev.map(timer =>
        timer.orderId === orderId
          ? { ...timer, status: 'running' as const }
          : timer
      )
    );
  }, []);

  const completePreparationTimer = useCallback((orderId: string) => {
    setPreparationTimers(prev =>
      prev.map(timer => {
        if (timer.orderId === orderId) {
          const actualTime = (new Date().getTime() - timer.startTime.getTime()) / (1000 * 60);
          return {
            ...timer,
            status: 'completed' as const,
            actualTime: Math.round(actualTime)
          };
        }
        return timer;
      })
    );
  }, []);

  const cancelPreparationTimer = useCallback((orderId: string) => {
    setPreparationTimers(prev =>
      prev.filter(timer => timer.orderId !== orderId)
    );
  }, []);

  // دوال مساعدة للفلترة
  const getFilteredOrders = useCallback(() => {
    let filtered = [...orders];

    // فلتر الحالة
    if (currentFilter.status !== 'all') {
      filtered = filtered.filter(order => order.status === currentFilter.status);
    }

    // فلتر الأولوية
    if (currentFilter.priority !== 'all') {
      filtered = filtered.filter(order => order.priority === currentFilter.priority);
    }

    // فلتر الوقت
    if (currentFilter.timeRange !== 'all') {
      const now = new Date();
      let cutoffTime: Date;

      switch (currentFilter.timeRange) {
        case 'today':
          cutoffTime = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          break;
        case 'last_hour':
          cutoffTime = new Date(now.getTime() - 60 * 60 * 1000);
          break;
        case 'last_3_hours':
          cutoffTime = new Date(now.getTime() - 3 * 60 * 60 * 1000);
          break;
        default:
          cutoffTime = new Date(0);
      }

      filtered = filtered.filter(order => new Date(order.createdAt) >= cutoffTime);
    }

    // فلتر البحث
    if (currentFilter.searchTerm) {
      const searchTerm = currentFilter.searchTerm.toLowerCase();
      filtered = filtered.filter(order =>
        order.orderNumber.toLowerCase().includes(searchTerm) ||
        order.customerName?.toLowerCase().includes(searchTerm) ||
        order.waiterName.toLowerCase().includes(searchTerm) ||
        order.tableNumber.toString().includes(searchTerm)
      );
    }

    return filtered;
  }, [orders, currentFilter]);

  // حساب الطلبات حسب الحالة
  const getOrdersByStatus = useCallback((status: ChefOrder['status']) => {
    return orders.filter(order => order.status === status);
  }, [orders]);

  // حساب الطلبات حسب الأولوية
  const getOrdersByPriority = useCallback((priority: ChefOrder['priority']) => {
    return orders.filter(order => order.priority === priority);
  }, [orders]);

  // الحصول على الإشعارات غير المقروءة
  const getUnreadNotifications = useCallback(() => {
    return notifications.filter(notification => !notification.isRead);
  }, [notifications]);

  // الحصول على الطلبات العاجلة
  const getUrgentOrders = useCallback(() => {
    return orders.filter(order => order.priority === 'urgent');
  }, [orders]);

  // الحصول على الطلبات المتأخرة
  const getOverdueOrders = useCallback(() => {
    const now = new Date();
    return orders.filter(order => {
      if (order.status === 'completed' || order.status === 'delivered') return false;
      
      const orderTime = new Date(order.createdAt);
      const minutesOld = (now.getTime() - orderTime.getTime()) / (1000 * 60);
      
      // اعتبار الطلب متأخر إذا مر عليه أكثر من 25 دقيقة
      return minutesOld > 25;
    });
  }, [orders]);

  // إضافة إشعار جديد
  const addNotification = useCallback((notification: ChefNotification) => {
    setNotifications(prev => [notification, ...prev]);
  }, []);

  // تحديد إشعار كمقروء
  const markNotificationAsRead = useCallback((notificationId: string) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification._id === notificationId
          ? { ...notification, isRead: true }
          : notification
      )
    );
  }, []);

  // تحديد جميع الإشعارات كمقروءة
  const markAllNotificationsAsRead = useCallback(() => {
    setNotifications(prev =>
      prev.map(notification => ({ ...notification, isRead: true }))
    );
  }, []);

  return {
    // الحالات
    currentView,
    loading,
    sidebarOpen,
    orders,
    stats,
    notifications,
    selectedOrder,
    currentFilter,
    settings,
    modalState,
    preparationTimers,

    // دوال التحديث الأساسية
    setCurrentView,
    setLoading,
    setSidebarOpen,
    setSelectedOrder,
    updateOrders,
    updateStats,
    updateNotifications,

    // دوال الفلاتر
    updateFilter,
    resetFilters,
    getFilteredOrders,
    getOrdersByStatus,
    getOrdersByPriority,

    // دوال الإعدادات
    updateSetting,

    // دوال النوافذ المنبثقة
    updateModal,
    closeAllModals,

    // دوال مؤقتات التحضير
    startPreparationTimer,
    pausePreparationTimer,
    resumePreparationTimer,
    completePreparationTimer,
    cancelPreparationTimer,

    // دوال الإشعارات
    addNotification,
    markNotificationAsRead,
    markAllNotificationsAsRead,
    getUnreadNotifications,

    // دوال مساعدة
    getUrgentOrders,
    getOverdueOrders
  };
}
