import { useState, useEffect } from 'react';
import Button from './Button';
import { getApiUrl } from '../config/app.config';

interface Category {
  _id: string;
  name: string;
  description?: string;
  active: boolean;
  order: number;
  color: string;
}

interface MenuItem {
  _id: string;
  name: string;
  price: number;
  categories: string[];
  categoryDetails?: Category[];
  description?: string;
  available: boolean;
  order?: number;
}

interface OrderItem {
  name: string;
  quantity: number;
  price: number;
}

interface DrinkSelectorProps {
  onAddToOrder: (item: OrderItem) => void;
}

export default function DrinkSelector({ onAddToOrder }: DrinkSelectorProps) {
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [quantities, setQuantities] = useState<Record<string, number>>({});
  const [categories, setCategories] = useState<Category[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  useEffect(() => {
    fetchMenuItems();
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      const response = await fetch(getApiUrl('/api/categories'));
      if (!response.ok) throw new Error('فشل في جلب الفئات');
      const data = await response.json();
      setCategories(data);
    } catch (error) {
      setCategories([]);
    }
  };

  const fetchMenuItems = async () => {
    try {
      setLoading(true);
      setError('');
      const response = await fetch(getApiUrl('/api/menu'));
      if (!response.ok) {
        throw new Error('فشل في جلب قائمة المشروبات');
      }
      const data = await response.json();
      setMenuItems(data); // عرض جميع المشروبات بدون تصفية
    } catch (error) {
      console.error('خطأ في جلب قائمة المشروبات:', error);
      setError('حدث خطأ أثناء جلب قائمة المشروبات');
    } finally {
      setLoading(false);
    }
  };

  const toggleItemSelection = (itemId: string) => {
    const newSelection = new Set(selectedItems);
    if (newSelection.has(itemId)) {
      newSelection.delete(itemId);
      const newQuantities = { ...quantities };
      delete newQuantities[itemId];
      setQuantities(newQuantities);
    } else {
      newSelection.add(itemId);
      setQuantities(prev => ({ ...prev, [itemId]: 1 }));
    }
    setSelectedItems(newSelection);
  };

  const updateQuantity = (itemId: string, value: number) => {
    if (value > 0) {
      setQuantities(prev => ({ ...prev, [itemId]: value }));
    }
  };

  const handleConfirmSelection = () => {
    const selectedDrinks = menuItems
      .filter(item => selectedItems.has(item._id))
      .map(item => ({
        name: item.name,
        quantity: quantities[item._id] || 1,
        price: item.price
      }));

    selectedDrinks.forEach(drink => onAddToOrder(drink));
    setSelectedItems(new Set());
    setQuantities({});
  };

  if (loading) {
    return <div>جاري تحميل قائمة المشروبات...</div>;
  }

  if (error) {
    return <div style={{ color: '#f44336' }}>{error}</div>;
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
      <div style={{ marginBottom: '1rem' }}>
        <h4 style={{ color: '#6d4c41', marginBottom: '0.5rem' }}>قائمة المشروبات</h4>
        <div style={{ display: 'flex', gap: 8, flexWrap: 'wrap', margin: '0.5rem 0' }}>
          <Button
            variant={selectedCategory === 'all' ? 'primary' : 'secondary'}
            onClick={() => setSelectedCategory('all')}
            style={{ minWidth: 80 }}
          >
            الكل
          </Button>
          {categories.map(cat => (
            <Button
              key={cat._id}
              variant={selectedCategory === cat._id ? 'primary' : 'secondary'}
              onClick={() => setSelectedCategory(cat._id)}
              style={{ minWidth: 80 }}
            >
              {cat.name}
            </Button>
          ))}
        </div>
      </div>

      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))', gap: '1rem' }}>
        {menuItems
          .filter(item =>
            selectedCategory === 'all' ||
            item.categories.map(String).includes(selectedCategory)
          )
          .sort((a, b) => (a.order || 0) - (b.order || 0))
          .map(item => (
            <div
              key={item._id}
              style={{
                padding: '1rem',
                borderRadius: '8px',
                border: '1px solid #e0e0e0',
                backgroundColor: item.available ? '#fff' : '#f5f5f5',
                opacity: item.available ? 1 : 0.7,
                transition: 'all 0.2s'
              }}
            >
              <div style={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', marginBottom: '0.5rem' }}>
                <div style={{ flex: 1 }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                    <input
                      type="checkbox"
                      checked={selectedItems.has(item._id)}
                      onChange={() => item.available && toggleItemSelection(item._id)}
                      disabled={!item.available}
                      style={{ width: '18px', height: '18px' }}
                    />
                    <h5 style={{ margin: 0, color: '#333', fontSize: '1.1rem' }}>{item.name}</h5>
                  </div>
                  <p style={{ margin: '0.5rem 0', color: '#666', fontSize: '0.9rem' }}>{item.description}</p>
                  <div style={{ color: item.available ? '#4caf50' : '#f44336', fontSize: '0.9rem' }}>
                    {item.available ? 'متوفر' : 'غير متوفر حالياً'}
                  </div>
                </div>
                <div style={{ textAlign: 'left', marginLeft: '1rem' }}>
                  <div style={{ fontWeight: 'bold', color: '#6d4c41', fontSize: '1.1rem' }}>
                    {item.price} جنيه
                  </div>
                </div>
              </div>

              {selectedItems.has(item._id) && (
                <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginTop: '0.5rem' }}>
                  <label style={{ fontSize: '0.9rem' }}>الكمية:</label>
                  <Button
                    variant="secondary"
                    onClick={() => updateQuantity(item._id, (quantities[item._id] || 1) - 1)}
                    style={{ padding: '0.25rem 0.5rem', minWidth: 'auto' }}
                  >-</Button>
                  <span style={{ margin: '0 0.5rem' }}>{quantities[item._id] || 1}</span>
                  <Button
                    variant="secondary"
                    onClick={() => updateQuantity(item._id, (quantities[item._id] || 1) + 1)}
                    style={{ padding: '0.25rem 0.5rem', minWidth: 'auto' }}
                  >+</Button>
                </div>
              )}
            </div>
          ))}
      </div>

      {selectedItems.size > 0 && (
        <div style={{
          position: 'sticky',
          bottom: '1rem',
          backgroundColor: '#fff',
          padding: '1rem',
          borderRadius: '8px',
          boxShadow: '0 -2px 10px rgba(0,0,0,0.1)',
          marginTop: '1rem',
          textAlign: 'center'
        }}>
          <Button
            variant="success"
            onClick={handleConfirmSelection}
            style={{ minWidth: '200px' }}
          >
            <i className="fas fa-check" style={{ marginLeft: '0.5rem' }}></i>
            تأكيد اختيار {selectedItems.size} مشروب
          </Button>
        </div>
      )}
    </div>
  );
}
