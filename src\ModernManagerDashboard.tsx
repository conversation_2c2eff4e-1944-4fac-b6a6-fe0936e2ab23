// filepath: c:\\Users\\<USER>\\OneDrive\\Desktop\\PRINT\\Coffee\\Coffee\\src\\ModernManagerDashboard.tsx
import React, { useState, useEffect, useMemo } from 'react';
import { FaUsers, FaBoxOpen, FaShoppingCart, FaDollarSign, FaCog, FaTachometerAlt, FaTags, FaSearch } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import i18n from './i18n';
import Toast from './components/Toast';
import type { User, Product, Order, Category, InventoryStats, InventoryItem, ApiResponse, Setting } from './utils/api'; // ADDED InventoryItem
import api from './utils/api'; // ADDED api import
import { APP_CONFIG } from './config/app.config'; // Corrected import path

// Import section components
import UsersSection from './components/dashboardSections/UsersSection'; // ADDED
import ProductsSection from './components/dashboardSections/ProductsSection'; // ADDED
import CategoriesSection from './components/dashboardSections/CategoriesSection'; // ADDED
import OrdersSection from './components/dashboardSections/OrdersSection';
import SettingsSection from './components/dashboardSections/SettingsSection';
import DashboardSection from './components/dashboardSections/DashboardSection';

// Import UI components
import Button from './components/Button'; // ADDED
import Modal from './components/Modal'; // ADDED
import Loading from './components/Loading'; // ADDED
import Alert from './components/Alert'; // ADDED


const initialUserFormData: Partial<User> & { password?: string; confirmPassword?: string } = {
  name: '',
  email: '',
  role: APP_CONFIG.ROLES.WAITER, // Default role
  password: '',
  confirmPassword: '',
};

const initialProductFormData: Partial<Product> = {
  name: '',
  description: '',
  price: 0,
  category: '', // This will be category ID/name depending on API
  stock: 0,
  isAvailable: true,
  imageUrl: '',
};

const initialCategoryFormData: Partial<Category> = {
  name: '',
  description: '',
};

// REMOVED initialSettingsFormData as SettingsSection will manage its own form state initialization

interface ToastMessage {
  id: string; // Changed from number to string
  message: string;
  type: 'success' | 'error' | 'info';
}

const ModernManagerDashboard: React.FC = () => {
  const { t } = useTranslation(['manager', 'common']);
  const [activeSection, setActiveSection] = useState('dashboard');
  const [users, setUsers] = useState<User[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [orders, setOrders] = useState<Order[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [inventoryStats, setInventoryStats] = useState<InventoryStats | null>(null);
  const [settings, setSettings] = useState<Setting[]>([]);
  // REMOVED settingsFormData, setSettingsFormData, settingsError, setSettingsError - managed by SettingsSection
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeToast, setActiveToast] = useState<ToastMessage | null>(null);

  // Function to show a toast message
  const showToast = (message: string, type: 'success' | 'error' | 'info') => {
    setActiveToast({ id: Date.now().toString(), message, type }); // Ensure id is a string
  };

  const handleRemoveToast = () => {
    setActiveToast(null);
  };

  // Modal states for Order Details (still managed here)
  const [isOrderDetailsModalOpen, setIsOrderDetailsModalOpen] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);

  // REMOVED Modal states for User, Product (Inventory), Category - managed by respective sections
  // const [isUserModalOpen, setIsUserModalOpen] = useState(false);
  // const [isInventoryModalOpen, setIsInventoryModalOpen] = useState(false);
  // const [isCategoryModalOpen, setIsCategoryModalOpen] = useState(false);
  // const [selectedUser, setSelectedUser] = useState<User | null>(null);
  // const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  // const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);

  // REMOVED State for User Form Data - managed by UsersSection
  // const [userFormData, setUserFormData] = useState<Partial<User> & { password?: string; confirmPassword?: string }>(initialUserFormData);
  // const [userModalError, setUserModalError] = useState<string | null>(null);

  // REMOVED State for Product Form Data - managed by ProductsSection
  // const [productFormData, setProductFormData] = useState<Partial<Product>>(initialProductFormData);
  // const [productModalError, setProductModalError] = useState<string | null>(null);

  // REMOVED State for Category Form Data - managed by CategoriesSection
  // const [categoryFormData, setCategoryFormData] = useState<Partial<Category>>(initialCategoryFormData);
  // const [categoryModalError, setCategoryModalError] = useState<string | null>(null);

  // State for Delete Confirmation Modal (still managed here)
  const [isConfirmDeleteModalOpen, setIsConfirmDeleteModalOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState<Category | Product | User | null>(null);
  const [itemTypeToDelete, setItemTypeToDelete] = useState<'Category' | 'Product' | 'User' | null>(null);
  const [deleteError, setDeleteError] = useState<string | null>(null);

  // Helper function to calculate inventory statistics
  const calculateInventoryStats = (items: InventoryItem[]): InventoryStats => {
    const lowStockThreshold = APP_CONFIG.LIMITS.LOW_STOCK_THRESHOLD;
    let totalStockUnits = 0;
    let lowStockProducts = 0;
    let outOfStockProducts = 0;

    items.forEach(item => {
      totalStockUnits += item.stock || 0; // Assuming item.stock exists
      if ((item.stock || 0) === 0) {
        outOfStockProducts++;
      } else if ((item.stock || 0) < lowStockThreshold) {
        lowStockProducts++;
      }
    });

    return {
      distinctProducts: items.length,
      totalStockUnits,
      lowStockProducts,
      outOfStockProducts,
      // Populate other fields of InventoryStats as defined in your types
    };
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null); // Reset error before fetching
        // Ensure all API calls are correctly defined in `api.ts` and imported
        const [usersResponse, productsResponse, ordersResponse, categoriesResponse, inventoryResponse, settingsResponse] = await Promise.all([
          api.getUsers(),
          api.getProducts(), 
          api.getOrders(),
          api.getCategories(),
          api.getInventoryItems(), // CORRECTED: Changed getInventoryStats to getInventoryItems
          api.getSettings() 
        ]);

        if (usersResponse.success && usersResponse.data) setUsers(usersResponse.data);
        else setError(prev => prev ? prev + `\\n${usersResponse.error || 'Failed to fetch users'}` : (usersResponse.error || 'Failed to fetch users'));

        if (productsResponse.success && productsResponse.data) setProducts(productsResponse.data);
        else setError(prev => prev ? prev + `\\n${productsResponse.error || 'Failed to fetch products'}` : (productsResponse.error || 'Failed to fetch products'));

        if (ordersResponse.success && ordersResponse.data) setOrders(ordersResponse.data);
        else setError(prev => prev ? prev + `\\n${ordersResponse.error || 'Failed to fetch orders'}` : (ordersResponse.error || 'Failed to fetch orders'));

        if (categoriesResponse.success && categoriesResponse.data) setCategories(categoriesResponse.data);
        else setError(prev => prev ? prev + `\\n${categoriesResponse.error || 'Failed to fetch categories'}` : (categoriesResponse.error || 'Failed to fetch categories'));
        
        if (inventoryResponse.success && inventoryResponse.data) {
          // Assuming inventoryResponse.data is InventoryItem[]
          const items: InventoryItem[] = inventoryResponse.data;
          const stats = calculateInventoryStats(items);
          setInventoryStats(stats);
        } else {
          setError(prev => prev ? prev + `\\n${inventoryResponse.error || 'Failed to fetch inventory stats'}` : (inventoryResponse.error || 'Failed to fetch inventory stats'));
        }

        if (settingsResponse.success && settingsResponse.data) {
          setSettings(settingsResponse.data);
          // REMOVED: SettingsSection will initialize its own form data based on the 'settings' prop
          // const initialForm: { [key: string]: any } = {};
          // settingsResponse.data.forEach(setting => {
          //   initialForm[setting.key] = setting.value;
          // });
          // setSettingsFormData(initialForm);
        } else {
          setError(prev => prev ? prev + `\\n${settingsResponse.error || 'Failed to fetch settings'}` : (settingsResponse.error || 'Failed to fetch settings'));
        }

      } catch (err: any) {
        setError('Failed to fetch data. Please try again later.');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, []);

  const CURRENCY_SYMBOL = APP_CONFIG.APP.CURRENCY_SYMBOL || '$';

  // Memoized filtered data
  const filteredUsers = useMemo(() => users.filter(user => user.name.toLowerCase().includes(searchTerm.toLowerCase()) || user.email.toLowerCase().includes(searchTerm.toLowerCase())), [users, searchTerm]);
  const filteredProducts = useMemo(() => products.filter(product => product.name.toLowerCase().includes(searchTerm.toLowerCase())), [products, searchTerm]);
  const filteredOrders = useMemo(() => orders.filter(order => order.id.toString().includes(searchTerm) || (order.customerInfo && order.customerInfo.name.toLowerCase().includes(searchTerm.toLowerCase()))), [orders, searchTerm]);
  const filteredCategories = useMemo(() => categories.filter(category => category.name.toLowerCase().includes(searchTerm.toLowerCase())), [categories, searchTerm]);


  // REMOVED Modal handlers for User modal - managed by UsersSection
  // const handleOpenUserModal = (user?: User) => { ... };
  // const closeUserModal = () => { ... };
  // const handleUserFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => { ... };
  // const handleUserFormSubmit = async (e: React.FormEvent) => { ... };

  // Modal handlers for Order Details (still managed here)
  const handleOpenOrderDetailsModal = (order: Order) => {
    setSelectedOrder(order);
    setIsOrderDetailsModalOpen(true);
  };
  const closeOrderDetailsModal = () => setIsOrderDetailsModalOpen(false);

  // REMOVED Modal handlers for Product (Inventory) modal - managed by ProductsSection
  // const closeInventoryModal = () => { ... };
  // const handleOpenInventoryModal = (product?: Product) => { ... };
  // const handleProductFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => { ... };
  // const handleProductFormSubmit = async (e: React.FormEvent) => { ... };
  
  // REMOVED Modal handlers for Category modal - managed by CategoriesSection
  // const handleOpenCategoryModal = (category?: Category) => { ... };
  // const closeCategoryModal = () => { ... };
  // const handleCategoryFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => { ... };
  // const handleCategoryFormSubmit = async (e: React.FormEvent) => { ... };

  // REMOVED Settings form handlers - managed by SettingsSection


  // Delete Confirmation Modal Handlers (still managed here)
  const handleOpenConfirmDeleteModal = (item: Category | Product | User, type: 'Category' | 'Product' | 'User') => {
    setItemToDelete(item);
    setItemTypeToDelete(type);
    setIsConfirmDeleteModalOpen(true);
    setDeleteError(null); // Clear previous errors
  };

  const handleCloseConfirmDeleteModal = () => {
    setIsConfirmDeleteModalOpen(false);
    setItemToDelete(null);
    setItemTypeToDelete(null);
    setDeleteError(null);
  };

  const handleConfirmDelete = async () => {
    if (!itemToDelete || !itemTypeToDelete) {      setDeleteError(t('noItemToDelete'));
      return;
    }

    let response: ApiResponse | null = null; // Now uses imported ApiResponse
    let success = false;

    try {
      if (itemTypeToDelete === 'Category' && itemToDelete.id) {
        response = await api.deleteCategory(itemToDelete.id);
      } else if (itemTypeToDelete === 'Product' && itemToDelete.id) {
        response = await api.deleteProduct(itemToDelete.id);
      } else if (itemTypeToDelete === 'User' && itemToDelete.id) {
        response = await api.deleteUser(itemToDelete.id);
      }

      if (response && response.success) {
        success = true;
        // Refresh data based on type
        if (itemTypeToDelete === 'Category') {
          const categoriesResponse = await api.getCategories();
          if (categoriesResponse.success && categoriesResponse.data) setCategories(categoriesResponse.data);
          showToast(t('categoryDeleted', { name: itemToDelete.name }), 'success');
        } else if (itemTypeToDelete === 'Product') {
          const productsResponse = await api.getProducts();
          if (productsResponse.success && productsResponse.data) setProducts(productsResponse.data);
          showToast(t('productDeleted', { name: itemToDelete.name }), 'success');
        } else if (itemTypeToDelete === 'User') {
          const usersResponse = await api.getUsers();
          if (usersResponse.success && usersResponse.data) setUsers(usersResponse.data);
          showToast(t('userDeleted', { name: itemToDelete.name }), 'success');
        }
        handleCloseConfirmDeleteModal();
      } else {
        setDeleteError(response?.error || t('deleteError', { itemType: t(itemTypeToDelete.toLowerCase()) }));
        showToast(response?.error || t('deleteError', { itemType: t(itemTypeToDelete.toLowerCase()) }), 'error');
      }
    } catch (errCatch: any) {
      setDeleteError(t('unexpectedDeleteError', { itemType: t(itemTypeToDelete.toLowerCase()) }));
      showToast(t('unexpectedDeleteError', { itemType: t(itemTypeToDelete.toLowerCase()) }), 'error');
      console.error(errCatch);
    }
  };


  const renderContent = () => {
    if (loading && activeSection !== 'settings' && activeSection !== 'dashboard') return <Loading />;
    // For dashboard and settings, loading is handled within their respective components if needed for initial data display
    if (error && activeSection !== 'settings') return <Alert type="error" onClose={() => setError(null)}>{error}</Alert>; 
    // Settings section handles its own errors primarily.

    switch (activeSection) {
      case 'dashboard':
        return (
          <DashboardSection 
            users={users}
            products={products}
            orders={orders}
            inventoryStats={inventoryStats}
            currencySymbol={CURRENCY_SYMBOL}
          />
        );
      case 'users':
        return (
          <UsersSection
            users={filteredUsers}
            setUsers={setUsers} 
            api={api as any} // CORRECTED: Cast api to any to resolve type mismatch temporarily. Consider creating a more specific type for the passed api object.
            addToast={showToast}
            handleOpenConfirmDeleteModal={(user) => handleOpenConfirmDeleteModal(user, 'User')}
            APP_CONFIG={APP_CONFIG}
            initialUserFormData={initialUserFormData}
            searchTerm={searchTerm}
            loading={loading} // Pass loading for empty state message
            // Removed props that are now internal to UsersSection: 
            // onOpenModal, userFormData, onFormChange, onFormSubmit, isModalOpen, onCloseModal, modalError, setModalError
            // The `roles` prop was also an issue, APP_CONFIG.ROLES should be used directly in UsersSection if needed.
          />
        );
      case 'products':
        return (
          <ProductsSection
            products={filteredProducts}
            setProducts={setProducts}
            categories={categories}
            api={api as any} // CORRECTED: Cast api to any to resolve type mismatch temporarily.
            addToast={showToast}
            handleOpenConfirmDeleteModal={(product) => handleOpenConfirmDeleteModal(product, 'Product')}
            APP_CONFIG={APP_CONFIG}
            initialProductFormData={initialProductFormData}
            searchTerm={searchTerm}
            loading={loading}
            CURRENCY_SYMBOL={CURRENCY_SYMBOL}
            // Removed props now internal to ProductsSection
          />
        );
      case 'orders':
        return (
          <OrdersSection
            orders={filteredOrders}
            products={products}
            currencySymbol={CURRENCY_SYMBOL}
            onOpenDetailsModal={handleOpenOrderDetailsModal} // This modal is still in ModernManagerDashboard
            onCloseDetailsModal={closeOrderDetailsModal} // This modal is still in ModernManagerDashboard
            selectedOrder={selectedOrder} // This modal is still in ModernManagerDashboard
            isDetailsModalOpen={isOrderDetailsModalOpen} // This modal is still in ModernManagerDashboard
            loading={loading} 
            addToast={showToast} // Add addToast prop to OrdersSectionProps if not already there
          />
        );
      case 'categories':
        return (
          <CategoriesSection
            categories={filteredCategories} 
            setCategories={setCategories} 
            api={api as any} 
            addToast={showToast}
            handleOpenConfirmDeleteModal={(category) => handleOpenConfirmDeleteModal(category, 'Category')}
            APP_CONFIG={APP_CONFIG} 
            initialCategoryFormData={initialCategoryFormData} 
            searchTerm={searchTerm} // ADDED searchTerm prop
            loading={loading} 
          />
        );
      case 'settings':
        return (
          <SettingsSection 
            settings={settings} 
            setSettings={setSettings} 
            api={api as any} // CORRECTED: Cast api to any to resolve type mismatch temporarily
            addToast={showToast} 
            initialLoading={loading} 
          />
        );
      default:        return <p>{t('sectionNotFound')}</p>;
    }
  };

  return (
    <div className={`p-6 bg-gray-100 min-h-screen ${i18n.language === 'ar' ? 'rtl' : ''}`}>
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">{t('controlPanel')}</h1>
        <button 
          onClick={() => i18n.changeLanguage(i18n.language === 'en' ? 'ar' : 'en')} 
          className="language-toggle-btn px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          {i18n.language === 'en' ? 'العربية (Ar)' : 'English (En)'}
        </button>
      </div>
      <div className="mb-4 flex flex-wrap items-center justify-between">
        <div className="flex flex-wrap items-center">
          <Button onClick={() => setActiveSection('dashboard')} variant={activeSection === 'dashboard' ? 'primary' : 'secondary'} className="mr-2 mb-2">
            <FaTachometerAlt className="inline-block mr-1" />
            {t('dashboard')}
          </Button>
          <Button onClick={() => setActiveSection('users')} variant={activeSection === 'users' ? 'primary' : 'secondary'} className="mr-2 mb-2">
            <FaUsers className="inline-block mr-1" />
            {t('users')}
          </Button>
          <Button onClick={() => setActiveSection('products')} variant={activeSection === 'products' ? 'primary' : 'secondary'} className="mr-2 mb-2">
            <FaBoxOpen className="inline-block mr-1" />
            {t('products')}
          </Button>
          <Button onClick={() => setActiveSection('orders')} variant={activeSection === 'orders' ? 'primary' : 'secondary'} className="mr-2 mb-2">
            <FaShoppingCart className="inline-block mr-1" />
            {t('orders')}
          </Button>
          <Button onClick={() => setActiveSection('categories')} variant={activeSection === 'categories' ? 'primary' : 'secondary'} className="mr-2 mb-2">
            <FaTags className="inline-block mr-1" /> 
            {t('categories')}
          </Button>
          <Button onClick={() => setActiveSection('settings')} variant={activeSection === 'settings' ? 'primary' : 'secondary'} className="mr-2 mb-2">
            <FaCog className="inline-block mr-1" />
            {t('settings')}
          </Button>
        </div>
        {/* Global Search Input */}
        {['users', 'products', 'orders', 'categories'].includes(activeSection) && (
          <div className="relative">
            <input
              type="text"
              placeholder={t('search')}
              className="pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          </div>
        )}
      </div>
      {renderContent()}

      {/* Toast Container */}
      {activeToast && (
        <Toast 
          toast={activeToast} 
          onRemove={handleRemoveToast} // Use the corrected handler
        />
      )}

      {/* Confirmation Delete Modal */}
      {isConfirmDeleteModalOpen && itemToDelete && (
        <Modal
          isOpen={isConfirmDeleteModalOpen}
          onClose={handleCloseConfirmDeleteModal}          title={t('confirmDelete', { 
            itemType: itemTypeToDelete === 'User' ? t('user') : 
                     itemTypeToDelete === 'Product' ? t('product') : 
                     t('category') 
          })}
        >
          <p>{t('confirmDeleteMessage', {
            itemType: itemTypeToDelete === 'User' ? t('user') : 
                     itemTypeToDelete === 'Product' ? t('product') : 
                     t('category'),
            itemName: itemToDelete.name
          })}</p>
          {deleteError && <Alert type="error" onClose={() => setDeleteError(null)}>{deleteError}</Alert>}
          <div className="mt-4 flex justify-end space-x-2 rtl:space-x-reverse">
            <Button onClick={handleCloseConfirmDeleteModal} variant="secondary">
              {t('cancel')}
            </Button>
            <Button onClick={handleConfirmDelete} variant="error">
              {t('delete')}
            </Button>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default ModernManagerDashboard;