import { describe, test, expect } from '@jest/globals';

// Basic test to ensure CI/CD pipeline passes
describe('Basic Tests', () => {
  test('should pass basic test', () => {
    expect(1 + 1).toBe(2);
  });

  test('should have correct environment', () => {
    expect(typeof window).toBe('object');
  });

  test('should pass simple assertion', () => {
    // Simple test without any external dependencies
    const testValue = 'test';
    expect(testValue).toBe('test');
    expect(Array.isArray([])).toBe(true);
    expect(typeof 'string').toBe('string');
  });

  test('should handle basic math operations', () => {
    expect(2 * 3).toBe(6);
    expect(10 / 2).toBe(5);
    expect(5 + 5).toBe(10);
  });
});
