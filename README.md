# ☕ نظام إدارة مقهى ديشة (Desha Coffee Management System)

نظام إدارة مقهى متكامل وحديث مبني بتقنيات React و Node.js لإدارة المقاهي والمطاعم بكفاءة عالية.

## 🌟 المميزات الرئيسية

- **💼 إدارة المستخدمين**: نظام أدوار متقدم (مدير، طباخ، نادل)
- **📋 إدارة الطلبات**: تتبع وإدارة الطلبات في الوقت الفعلي
- **🍽️ إدارة القائمة**: إضافة وتعديل المنتجات والفئات
- **📊 التقارير**: تقارير مفصلة للمبيعات والأداء
- **📱 تصميم متجاوب**: يعمل بسلاسة على جميع الأجهزة
- **🔄 الوقت الفعلي**: تحديثات فورية باستخدام Socket.IO

## 🚀 النشر المباشر

### Frontend (Vercel)
🌐 **الرابط**: [https://desha-coffee.vercel.app](https://desha-coffee.vercel.app)

### Backend (Railway)
🔗 **API**: [https://deshacoffee-production.up.railway.app](https://deshacoffee-production.up.railway.app)

### Database (MongoDB Atlas)
📊 **قاعدة البيانات**: MongoDB Atlas المدارة

## 📁 هيكل المشروع

```
Coffee/
├── src/                    # Frontend React Application
│   ├── components/         # المكونات المشتركة
│   ├── pages/             # صفحات التطبيق
│   ├── config/            # إعدادات التطبيق
│   └── assets/            # الملفات الثابتة
├── backend/               # Backend Node.js API
│   ├── models/            # نماذج قاعدة البيانات
│   ├── routes/            # مسارات API
│   ├── middleware/        # وسطاء Express
│   └── config/            # إعدادات الخادم
└── public/                # الملفات العامة
```

## 🛠️ التقنيات المستخدمة

### Frontend
- **React 19** - مكتبة واجهة المستخدم
- **TypeScript** - لغة برمجة مطورة
- **Vite** - أداة البناء السريعة
- **Socket.IO Client** - للاتصال الفوري

### Backend
- **Node.js** - بيئة تشغيل JavaScript
- **Express.js** - إطار عمل الخادم
- **MongoDB** - قاعدة البيانات
- **Socket.IO** - للاتصال الفوري
- **JWT** - المصادقة الآمنة

### النشر والبنية التحتية
- **Vercel** - استضافة Frontend
- **Railway** - استضافة Backend
- **MongoDB Atlas** - قاعدة البيانات المدارة

## 🚦 بدء التشغيل المحلي

### متطلبات النظام
- Node.js 18+ 
- npm أو yarn
- MongoDB (للتطوير المحلي)

### 1. تحميل المشروع
```bash
git clone https://github.com/MediaFuture/DeshaCoffee.git
cd DeshaCoffee
```

### 2. تثبيت الحزم
```bash
# تثبيت حزم Frontend
npm install --legacy-peer-deps

# تثبيت حزم Backend
cd backend
npm install
cd ..
```

### 3. إعداد متغيرات البيئة
انسخ ملف `.env.example` إلى `.env` وقم بتحديث القيم:

```bash
cp .env.example .env
```

### 4. تشغيل التطبيق
```bash
# تشغيل Frontend و Backend معاً
npm run dev:all

# أو تشغيل كل واحد منفصل
npm run dev              # Frontend على المنفذ 5176
npm run backend:dev      # Backend على المنفذ 4003
```

## 🔧 إعدادات النشر

### متغيرات البيئة المطلوبة

#### Frontend (Vercel)
```env
VITE_API_URL=https://deshacoffee-production.up.railway.app
VITE_SOCKET_URL=https://deshacoffee-production.up.railway.app
NODE_ENV=production
```

#### Backend (Railway) 
```env
MONGODB_URI=mongodb+srv://besomustafa:<EMAIL>/deshacoffee?retryWrites=true&w=majority&appName=MyCoffeChop
JWT_SECRET=desha-coffee-super-secret-jwt-key-production-2024-arabic-cafe-system-v1.0
PORT=4003
NODE_ENV=production
FRONTEND_URL=https://desha-coffee.vercel.app
```

### بيانات الدخول للإنتاج
```
المدير العام: Beso / MOHAMEDmostafa123
النادل: azza / 253040
الطباخ: khaled / 253040
الأدمن: admin / DeshaCoffee2024Admin!
```

## 👥 الأدوار والصلاحيات

### 🔑 مدير (Manager)
- إدارة المستخدمين والموظفين
- عرض التقارير والإحصائيات
- إدارة المنتجات والفئات
- إدارة الطاولات والحسابات

### 👨‍🍳 طباخ (Cook/Chef)
- عرض الطلبات الجديدة
- تحديث حالة الطلبات
- إدارة المخزون

### 🍽️ نادل (Waiter)
- إنشاء طلبات جديدة
- إدارة الطاولات
- عرض حالة الطلبات

## 📊 إحصائيات المشروع

- **📁 الملفات**: +100 ملف
- **📝 أسطر الكود**: +15,000 سطر
- **🎨 المكونات**: +30 مكون React
- **🔌 APIs**: +25 نقطة نهاية
- **📱 صفحات**: +15 صفحة

## 🤝 المساهمة

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push إلى الفرع (`git push origin feature/amazing-feature`)
5. إنشاء Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 👨‍💻 الفريق

تم تطوير هذا المشروع بواسطة فريق MediaFuture

## 📞 الدعم

للحصول على الدعم، يرجى التواصل معنا عبر:
- GitHub Issues
- البريد الإلكتروني: <EMAIL>

---

**تم تطوير هذا المشروع بـ ❤️ في فلسطين 🇵🇸**