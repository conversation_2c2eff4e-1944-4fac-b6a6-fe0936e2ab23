import React from 'react';
import type { Order, Product } from '../../utils/api'; // Changed to type-only import
import Button from '../Button';
import Modal from '../Modal';
import Loading from '../Loading';

interface OrdersSectionProps {
  orders: Order[];
  products: Product[]; // Needed to display product names in order details
  currencySymbol: string;
  onOpenDetailsModal: (order: Order) => void;
  isDetailsModalOpen: boolean;
  onCloseDetailsModal: () => void;
  selectedOrder: Order | null;
  loading: boolean; // To show loading state for the section or table
  addToast: (message: string, type: 'success' | 'error' | 'info') => void; // ADDED
}

const OrdersSection: React.FC<OrdersSectionProps> = ({
  orders,
  products,
  currencySymbol,
  onOpenDetailsModal,
  isDetailsModalOpen,
  onCloseDetailsModal,
  selectedOrder,
  loading,
  addToast, // ADDED
}) => {
  if (loading && orders.length === 0) {
    return <Loading />;
  }

  return (
    <div>
      <h2 className="text-2xl font-semibold mb-4">إدارة الطلبات</h2>
      {/* Search and filter controls can be added here if needed, passed as props or managed internally */}
      {orders.length === 0 && !loading && (
        <p className="text-center text-gray-500 my-4">لا توجد طلبات لعرضها حاليًا.</p>
      )}
      {orders.length > 0 && (
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white shadow-md rounded-lg">
            <thead>
              <tr className="bg-gray-200 text-gray-600 uppercase text-sm leading-normal">
                <th className="py-3 px-6 text-left">معرف الطلب</th>
                <th className="py-3 px-6 text-left">الزبون</th>
                <th className="py-3 px-6 text-left">التاريخ</th>
                <th className="py-3 px-6 text-right">الإجمالي</th>
                <th className="py-3 px-6 text-center">الحالة</th>
                <th className="py-3 px-6 text-center">الإجراءات</th>
              </tr>
            </thead>
            <tbody className="text-gray-600 text-sm font-light">
              {orders.map(order => (
                <tr key={order.id} className="border-b border-gray-200 hover:bg-gray-100">
                  <td className="py-3 px-6 text-left">{order.id}</td>
                  <td className="py-3 px-6 text-left">{order.customerInfo ? order.customerInfo.name : 'N/A'}</td>
                  <td className="py-3 px-6 text-left">{new Date(order.date).toLocaleDateString()}</td>
                  <td className="py-3 px-6 text-right">{currencySymbol}{order.totalAmount.toFixed(2)}</td>
                  <td className="py-3 px-6 text-center">
                    <span className={`py-1 px-3 rounded-full text-xs ${
                      order.status === 'Completed' ? 'bg-green-200 text-green-700' :
                      order.status === 'Pending' ? 'bg-yellow-200 text-yellow-700' :
                      'bg-red-200 text-red-700' // For 'Cancelled' or other statuses
                    }`}>
                      {order.status}
                    </span>
                  </td>
                  <td className="py-3 px-6 text-center">
                    <Button onClick={() => onOpenDetailsModal(order)} size="sm" variant="info">عرض التفاصيل</Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
      {selectedOrder && (
        <Modal isOpen={isDetailsModalOpen} onClose={onCloseDetailsModal} title={`تفاصيل الطلب - #${selectedOrder.id}`}>
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-semibold">معلومات العميل:</h3>
              <p>الاسم: {selectedOrder.customerInfo ? selectedOrder.customerInfo.name : 'N/A'}</p>
              {selectedOrder.customerInfo?.email && <p>البريد الإلكتروني: {selectedOrder.customerInfo.email}</p>}
              {selectedOrder.customerInfo?.phone && <p>الهاتف: {selectedOrder.customerInfo.phone}</p>}
            </div>
            <div>
              <h3 className="text-lg font-semibold">تفاصيل الطلب:</h3>
              <p>تاريخ الطلب: {new Date(selectedOrder.date).toLocaleString()}</p>
              <p>الحالة: {selectedOrder.status}</p>
            </div>
            <div>
              <h3 className="text-lg font-semibold">العناصر المطلوبة:</h3>
              {selectedOrder.items && selectedOrder.items.length > 0 ? (
                <ul className="list-disc pl-5 space-y-2">
                  {selectedOrder.items.map((item, index) => {
                    const product = products.find(p => p.id === item.productId);
                    return (
                      <li key={index} className="flex justify-between">
                        <span>
                          {product ? product.name : `منتج غير معروف (ID: ${item.productId})`} (x{item.quantity})
                        </span>
                        <span>{currencySymbol}{(item.price * item.quantity).toFixed(2)}</span>
                      </li>
                    );
                  })}
                </ul>
              ) : (
                <p>لا توجد عناصر في هذا الطلب.</p>
              )}
            </div>
            <div className="text-right font-bold text-xl">
              الإجمالي: {currencySymbol}{selectedOrder.totalAmount.toFixed(2)}
            </div>
          </div>
          <div className="mt-6 flex justify-end">
            <Button onClick={onCloseDetailsModal} variant="secondary">إغلاق</Button>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default OrdersSection;
