// Production System Test - Railway Backend + Vercel Frontend + MongoDB
// Tests the complete workflow using real production environment

import https from 'https';
import http from 'http';

// Production URLs
const PRODUCTION_BACKEND = 'https://deshacoffee-production.up.railway.app';
const PRODUCTION_FRONTEND = 'https://desha-coffee.vercel.app';

// Real user credentials from production
const REAL_USERS = {
  manager: { username: '<PERSON><PERSON>', password: 'MOHAMEDmostafa123', role: 'مدير' },
  waiter1: { username: 'azza', password: '253040', role: 'نادل' },
  waiter2: { username: '<PERSON><PERSON>', password: '253040', role: 'نادل' },
  chef: { username: 'khaled', password: '253040', role: 'طباخ' }
};

class ProductionSystemTester {
  constructor() {
    this.testResults = {
      passed: 0,
      failed: 0,
      tests: [],
      timing: {}
    };
    this.tokens = {};
    this.testStartTime = Date.now();
  }

  // HTTP request helper for Node.js
  async makeRequest(url, options = {}) {
    return new Promise((resolve, reject) => {
      const isHttps = url.startsWith('https');
      const client = isHttps ? https : http;
      
      const requestOptions = {
        method: options.method || 'GET',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'ProductionTester/1.0',
          ...options.headers
        }
      };

      const req = client.request(url, requestOptions, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          try {
            const result = {
              status: res.statusCode,
              headers: res.headers,
              data: data ? JSON.parse(data) : null
            };
            resolve(result);
          } catch (error) {
            resolve({
              status: res.statusCode,
              headers: res.headers,
              data: data
            });
          }
        });
      });

      req.on('error', reject);
      
      if (options.body) {
        req.write(JSON.stringify(options.body));
      }
      
      req.end();
    });
  }

  async runTest(testName, testFunction) {
    const startTime = Date.now();
    console.log(`🧪 Testing: ${testName}`);
    
    try {
      await testFunction();
      const duration = Date.now() - startTime;
      this.testResults.passed++;
      this.testResults.tests.push({ name: testName, status: 'PASSED', duration });
      this.testResults.timing[testName] = duration;
      console.log(`✅ ${testName} - PASSED (${duration}ms)`);
    } catch (error) {
      const duration = Date.now() - startTime;
      this.testResults.failed++;
      this.testResults.tests.push({ 
        name: testName, 
        status: 'FAILED', 
        error: error.message,
        duration 
      });
      this.testResults.timing[testName] = duration;
      console.error(`❌ ${testName} - FAILED: ${error.message} (${duration}ms)`);
    }
  }

  // Test 1: Production Backend Health
  async testProductionBackendHealth() {
    const response = await this.makeRequest(`${PRODUCTION_BACKEND}/health`);
    
    if (response.status !== 200) {
      throw new Error(`Backend health check failed with status ${response.status}`);
    }
    
    if (!response.data || response.data.status !== 'OK') {
      throw new Error('Backend health response invalid');
    }

    console.log(`🟢 Backend healthy (${response.data.version})`);
    console.log(`🗄️ Database: ${response.data.database.message}`);
    console.log(`🌍 Environment: ${response.data.environment}`);
  }

  // Test 2: Production Frontend Accessibility
  async testProductionFrontendHealth() {
    const response = await this.makeRequest(PRODUCTION_FRONTEND);
    
    if (response.status !== 200) {
      throw new Error(`Frontend not accessible, status: ${response.status}`);
    }

    console.log(`🌐 Frontend accessible at Vercel`);
  }

  // Test 3: Authentication for all user roles
  async testUserAuthentication() {
    for (const [userType, credentials] of Object.entries(REAL_USERS)) {
      const response = await this.makeRequest(`${PRODUCTION_BACKEND}/api/auth/login`, {
        method: 'POST',
        body: credentials
      });

      if (response.status !== 200 || !response.data.success) {
        throw new Error(`Authentication failed for ${userType}: ${response.data?.message || 'Unknown error'}`);
      }

      this.tokens[userType] = response.data.token;
      const user = response.data.user;
      console.log(`🔐 ${userType}: ${user.name} (${user.role}) ✅`);
    }
  }
  // Test 4: Get production products
  async testProductsAPI() {
    const response = await this.makeRequest(`${PRODUCTION_BACKEND}/api/products`, {
      headers: {
        'Authorization': `Bearer ${this.tokens.waiter1}`
      }
    });

    if (response.status !== 200 || !Array.isArray(response.data)) {
      throw new Error('Products API failed');
    }

    const products = response.data;
    if (products.length === 0) {
      throw new Error('No products found in production');
    }

    this.productId = products[0]._id;
    this.productData = products[0]; // Store complete product data
    console.log(`📦 Found ${products.length} products. First product: ${products[0].name} (${this.productId})`);
  }
  // Test 5: Create order with table validation
  async testOrderCreation() {
    if (!this.productId || !this.productData) {
      throw new Error('No product data available for testing');
    }

    const orderData = {
      tableNumber: '15', // Pass as string
      customerName: 'عميل اختبار الإنتاج',
      items: [{
        product: this.productId,
        name: this.productData.name,
        quantity: 1,
        price: this.productData.price,
        notes: 'اختبار النظام الإنتاجي'
      }],
      notes: 'طلب اختبار من النظام الإنتاجي'
    };

    const response = await this.makeRequest(`${PRODUCTION_BACKEND}/api/orders`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.tokens.waiter1}`
      },
      body: orderData
    });    if (response.status !== 201 && response.status !== 200) {
      throw new Error(`Order creation failed: ${response.data?.message || 'Unknown error'}`);
    }

    if (!response.data.data) {
      throw new Error('Order creation response invalid');
    }

    this.testOrderId = response.data.data._id;
    console.log(`📋 Order created: ${response.data.data.orderNumber} (Table ${response.data.data.table.number})`);
  }

  // Test 6: Chef order management
  async testChefOrderManagement() {
    if (!this.testOrderId) {
      throw new Error('No test order available');
    }

    // Chef updates order to preparing
    const preparingResponse = await this.makeRequest(`${PRODUCTION_BACKEND}/api/orders/${this.testOrderId}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${this.tokens.chef}`
      },
      body: { status: 'preparing' }
    });

    if (preparingResponse.status !== 200) {
      throw new Error('Chef cannot update order to preparing');
    }

    // Chef marks order as ready
    const readyResponse = await this.makeRequest(`${PRODUCTION_BACKEND}/api/orders/${this.testOrderId}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${this.tokens.chef}`
      },
      body: { status: 'ready' }
    });

    if (readyResponse.status !== 200) {
      throw new Error('Chef cannot mark order as ready');
    }

    console.log(`👨‍🍳 Chef successfully managed order lifecycle`);
  }

  // Test 7: Manager access to reports
  async testManagerAccess() {
    const ordersResponse = await this.makeRequest(`${PRODUCTION_BACKEND}/api/orders`, {
      headers: {
        'Authorization': `Bearer ${this.tokens.manager}`
      }
    });

    if (ordersResponse.status !== 200) {
      throw new Error('Manager cannot access orders');
    }

    const orders = ordersResponse.data;
    console.log(`📊 Manager access verified: ${orders.length} orders visible`);

    // Test table accounts access
    const tablesResponse = await this.makeRequest(`${PRODUCTION_BACKEND}/api/table-accounts`, {
      headers: {
        'Authorization': `Bearer ${this.tokens.manager}`
      }
    });

    if (tablesResponse.status !== 200) {
      throw new Error('Manager cannot access table accounts');
    }

    console.log(`🏓 Manager can access table management`);
  }

  // Test 8: Order validation (missing table number)
  async testOrderValidation() {
    const invalidOrderData = {
      // Missing tableNumber - should fail
      customerName: 'عميل بدون طاولة',
      items: [{
        product: this.productId,
        name: 'منتج اختبار',
        quantity: 1,
        price: 25
      }]
    };

    const response = await this.makeRequest(`${PRODUCTION_BACKEND}/api/orders`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.tokens.waiter1}`
      },
      body: invalidOrderData
    });

    if (response.status === 200 || response.status === 201) {
      throw new Error('Order validation failed - order created without table number');
    }

    console.log(`✅ Order validation working - rejected order without table number`);
  }

  // Run all production tests
  async runAllTests() {
    console.log('🚀 Starting Production System Test');
    console.log('==================================');
    console.log(`📡 Backend: ${PRODUCTION_BACKEND}`);
    console.log(`🌐 Frontend: ${PRODUCTION_FRONTEND}`);
    console.log(`🗄️ Database: MongoDB Atlas`);
    console.log('');

    await this.runTest('Production Backend Health', () => this.testProductionBackendHealth());
    await this.runTest('Production Frontend Health', () => this.testProductionFrontendHealth());
    await this.runTest('User Authentication (All Roles)', () => this.testUserAuthentication());
    await this.runTest('Products API', () => this.testProductsAPI());
    await this.runTest('Order Validation', () => this.testOrderValidation());
    await this.runTest('Order Creation', () => this.testOrderCreation());
    await this.runTest('Chef Order Management', () => this.testChefOrderManagement());
    await this.runTest('Manager Access', () => this.testManagerAccess());

    // Results Summary
    const totalTime = Date.now() - this.testStartTime;
    console.log('\n📊 PRODUCTION TEST RESULTS');
    console.log('===========================');
    console.log(`✅ Passed: ${this.testResults.passed}`);
    console.log(`❌ Failed: ${this.testResults.failed}`);
    console.log(`📋 Total: ${this.testResults.tests.length}`);
    console.log(`⏱️ Total Time: ${totalTime}ms`);

    if (this.testResults.failed > 0) {
      console.log('\n❌ FAILED TESTS:');
      this.testResults.tests
        .filter(test => test.status === 'FAILED')
        .forEach(test => {
          console.log(`  - ${test.name}: ${test.error}`);
        });
    }

    const successRate = (this.testResults.passed / this.testResults.tests.length) * 100;
    console.log(`\n🎯 Success Rate: ${successRate.toFixed(1)}%`);

    if (successRate === 100) {
      console.log('\n🏆 ALL PRODUCTION TESTS PASSED! ✅');
      console.log('🎉 Production system is fully functional!');
      console.log('\n🌟 PRODUCTION SYSTEM STATUS:');
      console.log('✅ Railway Backend: Operational');
      console.log('✅ Vercel Frontend: Accessible');
      console.log('✅ MongoDB Database: Connected');
      console.log('✅ Authentication: Working for all roles');
      console.log('✅ Order Management: Complete workflow');
      console.log('✅ Role-based Access: Properly configured');
      console.log('✅ Data Validation: Working correctly');
    } else if (successRate >= 75) {
      console.log('\n⚠️ Most tests passed, minor issues detected');
    } else {
      console.log('\n🚨 Multiple failures detected - system needs attention');
    }

    // Performance Summary
    console.log('\n⏱️ PERFORMANCE METRICS:');
    Object.entries(this.testResults.timing).forEach(([test, time]) => {
      const status = time > 5000 ? '🐌' : time > 2000 ? '⚡' : '🚀';
      console.log(`${status} ${test}: ${time}ms`);
    });

    return this.testResults;
  }
}

// Execute the production system test
const tester = new ProductionSystemTester();
tester.runAllTests()
  .then(results => {
    // Save results to file
    import('fs').then(fs => {
      fs.writeFileSync('production-workflow-test-results.json', JSON.stringify(results, null, 2));
      console.log('\n💾 Results saved to production-workflow-test-results.json');
    });
  })
  .catch(console.error);