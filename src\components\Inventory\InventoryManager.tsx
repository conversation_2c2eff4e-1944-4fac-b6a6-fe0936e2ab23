import React, { useState, useEffect } from 'react';
import { authenticatedGet, authenticatedPost, authenticatedPut } from '../../utils/apiHelpers';
import { useToast } from '../../hooks/useToast';
import socket from '../../socket';
import './InventoryManager.css';

interface InventoryItem {
  _id: string;
  name: string;
  category: string;
  currentStock: number;
  minStock: number;
  maxStock: number;
  unit: string;
  costPerUnit: number;
  supplier: string;
  lastRestocked: string;
  expiryDate?: string;
  status: 'in_stock' | 'low_stock' | 'out_of_stock' | 'expired';
  movements: Array<{
    type: 'in' | 'out' | 'adjustment';
    quantity: number;
    reason: string;
    date: string;
    user: string;
  }>;
}

interface InventoryManagerProps {
  userRole: 'waiter' | 'chef' | 'manager';
}

const InventoryManager: React.FC<InventoryManagerProps> = ({ userRole }) => {
  const [inventory, setInventory] = useState<InventoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedItem, setSelectedItem] = useState<InventoryItem | null>(null);
  const [showItemModal, setShowItemModal] = useState(false);
  const [showMovementModal, setShowMovementModal] = useState(false);
  const [filterCategory, setFilterCategory] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');

  // Movement form data
  const [movementData, setMovementData] = useState({
    type: 'in' as 'in' | 'out' | 'adjustment',
    quantity: 0,
    reason: ''
  });

  const { showSuccess, showError, showInfo } = useToast();

  // جلب المخزون
  const fetchInventory = async () => {
    try {
      setLoading(true);
      const response = await authenticatedGet('/api/inventory');
      const inventoryData = response.data || [];

      // حساب حالة كل عنصر
      const processedInventory = inventoryData.map((item: InventoryItem) => {
        let status: InventoryItem['status'] = 'in_stock';

        if (item.expiryDate && new Date(item.expiryDate) < new Date()) {
          status = 'expired';
        } else if (item.currentStock <= 0) {
          status = 'out_of_stock';
        } else if (item.currentStock <= item.minStock) {
          status = 'low_stock';
        }

        return { ...item, status };
      });

      setInventory(processedInventory);
    } catch (error) {
      console.error('خطأ في جلب المخزون:', error);
      showError('فشل في تحميل المخزون');

      // بيانات تجريبية
      setInventory([
        {
          _id: '1',
          name: 'حبوب قهوة عربية',
          category: 'قهوة',
          currentStock: 25,
          minStock: 10,
          maxStock: 100,
          unit: 'كيلو',
          costPerUnit: 150,
          supplier: 'مورد القهوة المتميزة',
          lastRestocked: '2024-01-10',
          status: 'in_stock',
          movements: []
        },
        {
          _id: '2',
          name: 'حليب طازج',
          category: 'ألبان',
          currentStock: 5,
          minStock: 8,
          maxStock: 50,
          unit: 'لتر',
          costPerUnit: 12,
          supplier: 'مزرعة الألبان',
          lastRestocked: '2024-01-12',
          expiryDate: '2024-01-20',
          status: 'low_stock',
          movements: []
        },
        {
          _id: '3',
          name: 'سكر أبيض',
          category: 'محليات',
          currentStock: 0,
          minStock: 5,
          maxStock: 30,
          unit: 'كيلو',
          costPerUnit: 8,
          supplier: 'شركة السكر',
          lastRestocked: '2024-01-05',
          status: 'out_of_stock',
          movements: []
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  // إضافة/تعديل عنصر مخزون
  const saveInventoryItem = async (itemData: Partial<InventoryItem>) => {
    try {
      let response;

      if (selectedItem) {
        // تعديل عنصر موجود
        response = await authenticatedPut(`/api/inventory/${selectedItem._id}`, itemData);
      } else {
        // إضافة عنصر جديد
        response = await authenticatedPost('/api/inventory', itemData);
      }

      if (response.success) {
        showSuccess(selectedItem ? 'تم تحديث العنصر بنجاح' : 'تم إضافة العنصر بنجاح');

        // إرسال إشعار Socket.IO
        if (socket) {
          socket.emit('inventory-updated', {
            action: selectedItem ? 'updated' : 'added',
            item: response.data
          });
        }

        fetchInventory();
        setShowItemModal(false);
        setSelectedItem(null);
      }
    } catch (error) {
      console.error('خطأ في حفظ العنصر:', error);
      showError('فشل في حفظ العنصر');
    }
  };

  // تسجيل حركة مخزون
  const recordMovement = async () => {
    if (!selectedItem || !movementData.quantity || !movementData.reason) {
      showError('يرجى ملء جميع البيانات المطلوبة');
      return;
    }

    try {
      const movement = {
        ...movementData,
        date: new Date().toISOString(),
        user: 'المستخدم الحالي' // يجب الحصول على اسم المستخدم الفعلي
      };

      const response = await authenticatedPost(`/api/inventory/${selectedItem._id}/movement`, movement);

      if (response.success) {
        showSuccess('تم تسجيل الحركة بنجاح');

        // إرسال إشعار Socket.IO
        if (socket) {
          socket.emit('inventory-movement', {
            itemId: selectedItem._id,
            movement
          });
        }

        fetchInventory();
        setShowMovementModal(false);
        setMovementData({ type: 'in', quantity: 0, reason: '' });
        setSelectedItem(null);
      }
    } catch (error) {
      console.error('خطأ في تسجيل الحركة:', error);
      showError('فشل في تسجيل الحركة');
    }
  };

  // إعداد Socket.IO
  useEffect(() => {
    if (socket) {
      socket.on('inventory-updated', () => {
        fetchInventory();
      });

      socket.on('inventory-movement', () => {
        fetchInventory();
      });

      socket.on('low-stock-alert', (data) => {
        showInfo(`تنبيه: ${data.itemName} أوشك على النفاد (${data.currentStock} ${data.unit})`);
      });
    }

    return () => {
      if (socket) {
        socket.off('inventory-updated');
        socket.off('inventory-movement');
        socket.off('low-stock-alert');
      }
    };
  }, []);

  // تحميل البيانات الأولية
  useEffect(() => {
    fetchInventory();
  }, []);

  // فلترة المخزون
  const getFilteredInventory = () => {
    let filtered = inventory;

    // فلترة حسب الفئة
    if (filterCategory !== 'all') {
      filtered = filtered.filter(item => item.category === filterCategory);
    }

    // فلترة حسب الحالة
    if (filterStatus !== 'all') {
      filtered = filtered.filter(item => item.status === filterStatus);
    }

    // فلترة حسب البحث
    if (searchTerm) {
      filtered = filtered.filter(item =>
        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.supplier.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    return filtered;
  };

  // الحصول على الفئات المتاحة
  const getCategories = () => {
    const categories = [...new Set(inventory.map(item => item.category))];
    return categories.filter(Boolean);
  };

  // الحصول على لون الحالة
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'in_stock': return '#27ae60';
      case 'low_stock': return '#f39c12';
      case 'out_of_stock': return '#e74c3c';
      case 'expired': return '#8e44ad';
      default: return '#2c3e50';
    }
  };

  // الحصول على نص الحالة
  const getStatusText = (status: string) => {
    switch (status) {
      case 'in_stock': return 'متوفر';
      case 'low_stock': return 'مخزون منخفض';
      case 'out_of_stock': return 'نفد المخزون';
      case 'expired': return 'منتهي الصلاحية';
      default: return status;
    }
  };

  // تنسيق التاريخ
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-EG');
  };

  if (loading) {
    return (
      <div className="inventory-manager loading">
        <div className="loading-spinner">
          <i className="fas fa-spinner fa-spin"></i>
          <span>جاري تحميل المخزون...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="inventory-manager">
      {/* Header */}
      <div className="inventory-header">
        <div className="header-left">
          <h1>
            <i className="fas fa-boxes"></i>
            إدارة المخزون
          </h1>
          <p>تتبع وإدارة مستويات المخزون مع التنبيهات التلقائية</p>
        </div>

        <div className="header-actions">
          {userRole === 'manager' && (
            <button
              className="btn-new-item"
              onClick={() => {
                setSelectedItem(null);
                setShowItemModal(true);
              }}
            >
              <i className="fas fa-plus"></i>
              إضافة عنصر جديد
            </button>
          )}
        </div>
      </div>

      {/* Stats Cards */}
      <div className="inventory-stats">
        <div className="stat-card total">
          <div className="stat-icon">
            <i className="fas fa-boxes"></i>
          </div>
          <div className="stat-content">
            <h3>{inventory.length}</h3>
            <p>إجمالي الأصناف</p>
          </div>
        </div>

        <div className="stat-card in-stock">
          <div className="stat-icon">
            <i className="fas fa-check-circle"></i>
          </div>
          <div className="stat-content">
            <h3>{inventory.filter(item => item.status === 'in_stock').length}</h3>
            <p>متوفر</p>
          </div>
        </div>

        <div className="stat-card low-stock">
          <div className="stat-icon">
            <i className="fas fa-exclamation-triangle"></i>
          </div>
          <div className="stat-content">
            <h3>{inventory.filter(item => item.status === 'low_stock').length}</h3>
            <p>مخزون منخفض</p>
          </div>
        </div>

        <div className="stat-card out-of-stock">
          <div className="stat-icon">
            <i className="fas fa-times-circle"></i>
          </div>
          <div className="stat-content">
            <h3>{inventory.filter(item => item.status === 'out_of_stock').length}</h3>
            <p>نفد المخزون</p>
          </div>
        </div>

        <div className="stat-card expired">
          <div className="stat-icon">
            <i className="fas fa-calendar-times"></i>
          </div>
          <div className="stat-content">
            <h3>{inventory.filter(item => item.status === 'expired').length}</h3>
            <p>منتهي الصلاحية</p>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="inventory-filters">
        <div className="search-group">
          <i className="fas fa-search"></i>
          <input
            type="text"
            placeholder="البحث في المخزون..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <div className="filter-group">
          <label>الفئة:</label>
          <select
            value={filterCategory}
            onChange={(e) => setFilterCategory(e.target.value)}
          >
            <option value="all">جميع الفئات</option>
            {getCategories().map(category => (
              <option key={category} value={category}>{category}</option>
            ))}
          </select>
        </div>

        <div className="filter-group">
          <label>الحالة:</label>
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
          >
            <option value="all">جميع الحالات</option>
            <option value="in_stock">متوفر</option>
            <option value="low_stock">مخزون منخفض</option>
            <option value="out_of_stock">نفد المخزون</option>
            <option value="expired">منتهي الصلاحية</option>
          </select>
        </div>

        <button
          className="refresh-btn"
          onClick={fetchInventory}
          title="تحديث البيانات"
        >
          <i className="fas fa-sync-alt"></i>
          تحديث
        </button>
      </div>

      {/* Inventory List */}
      <div className="inventory-list">
        {getFilteredInventory().length === 0 ? (
          <div className="no-inventory">
            <i className="fas fa-boxes"></i>
            <p>لا توجد أصناف تطابق الفلاتر المحددة</p>
          </div>
        ) : (
          <div className="inventory-grid">
            {getFilteredInventory().map(item => (
              <div
                key={item._id}
                className={`inventory-card ${item.status}`}
                onClick={() => {
                  setSelectedItem(item);
                  setShowItemModal(true);
                }}
              >
                <div className="item-header">
                  <div className="item-name">
                    <i className="fas fa-box"></i>
                    {item.name}
                  </div>
                  <div
                    className="item-status"
                    style={{ backgroundColor: getStatusColor(item.status) }}
                  >
                    {getStatusText(item.status)}
                  </div>
                </div>

                <div className="item-info">
                  <div className="info-row">
                    <i className="fas fa-layer-group"></i>
                    <span>{item.category}</span>
                  </div>
                  <div className="info-row">
                    <i className="fas fa-warehouse"></i>
                    <span>{item.currentStock} {item.unit}</span>
                  </div>
                  <div className="info-row">
                    <i className="fas fa-exclamation-triangle"></i>
                    <span>الحد الأدنى: {item.minStock} {item.unit}</span>
                  </div>
                  <div className="info-row">
                    <i className="fas fa-truck"></i>
                    <span>{item.supplier}</span>
                  </div>
                </div>

                <div className="item-details">
                  <div className="stock-level">
                    <div className="stock-bar">
                      <div
                        className="stock-fill"
                        style={{
                          width: `${Math.min((item.currentStock / item.maxStock) * 100, 100)}%`,
                          backgroundColor: getStatusColor(item.status)
                        }}
                      ></div>
                    </div>
                    <span className="stock-text">
                      {item.currentStock} / {item.maxStock} {item.unit}
                    </span>
                  </div>

                  <div className="item-cost">
                    <i className="fas fa-money-bill-wave"></i>
                    <span>{item.costPerUnit.toFixed(2)} ج.م / {item.unit}</span>
                  </div>
                </div>

                <div className="item-dates">
                  <div className="last-restocked">
                    <i className="fas fa-calendar-plus"></i>
                    <span>آخر تموين: {formatDate(item.lastRestocked)}</span>
                  </div>
                  {item.expiryDate && (
                    <div className="expiry-date">
                      <i className="fas fa-calendar-times"></i>
                      <span>انتهاء الصلاحية: {formatDate(item.expiryDate)}</span>
                    </div>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="item-actions" onClick={(e) => e.stopPropagation()}>
                  {userRole === 'manager' && (
                    <button
                      className="action-btn movement"
                      onClick={() => {
                        setSelectedItem(item);
                        setShowMovementModal(true);
                      }}
                    >
                      <i className="fas fa-exchange-alt"></i>
                      حركة مخزون
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default InventoryManager;