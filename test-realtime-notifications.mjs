#!/usr/bin/env node

import { io } from 'socket.io-client';
import fetch from 'node-fetch';

console.log('🔄 اختبار الإشعارات المباشرة بين الواجهات');
console.log('=' .repeat(60));

const API_BASE = 'https://deshacoffee-production.up.railway.app/api';
const SOCKET_URL = 'https://deshacoffee-production.up.railway.app';

async function testRealTimeNotifications() {
    try {
        // 1. Test API connectivity
        console.log('1️⃣ اختبار الاتصال بـ API...');
        const ordersResponse = await fetch(`${API_BASE}/orders`);
        if (!ordersResponse.ok) {
            throw new Error(`API Error: ${ordersResponse.status}`);
        }
        const orders = await ordersResponse.json();
        console.log(`✅ API متصل - عدد الطلبات: ${orders.length}`);

        // Find a pending order to test with
        const pendingOrder = orders.find(order => order.status === 'pending');
        if (!pendingOrder) {
            console.log('⚠️ لا توجد طلبات في الانتظار للاختبار');
            return;
        }

        console.log(`🎯 سيتم اختبار الطلب: ${pendingOrder._id}`);

        // 2. Setup Socket.IO connections
        console.log('2️⃣ إعداد اتصالات Socket.IO...');
        
        // Chef connection
        const chefSocket = io(SOCKET_URL, {
            transports: ['websocket', 'polling']
        });

        // Waiter connection
        const waiterSocket = io(SOCKET_URL, {
            transports: ['websocket', 'polling']
        });

        // 3. Setup event listeners
        let chefReceivedUpdate = false;
        let waiterReceivedUpdate = false;

        chefSocket.on('connect', () => {
            console.log('✅ طباخ متصل');
            chefSocket.emit('register-user', {
                userId: 'test-chef-001',
                userType: 'chef',
                name: 'Chef Test'
            });
        });

        waiterSocket.on('connect', () => {
            console.log('✅ نادل متصل');
            waiterSocket.emit('register-user', {
                userId: 'test-waiter-001',
                userType: 'waiter',
                name: 'Waiter Test'
            });
        });

        // Listen for order status updates
        chefSocket.on('order-status-update', (data) => {
            console.log('🔔 الطباخ استلم تحديث:', data.status);
            chefReceivedUpdate = true;
        });

        waiterSocket.on('order-status-update', (data) => {
            console.log('🔔 النادل استلم تحديث:', data.status);
            waiterReceivedUpdate = true;
        });

        // Wait for connections
        await new Promise(resolve => setTimeout(resolve, 2000));

        // 4. Test order status update
        console.log('3️⃣ اختبار تحديث حالة الطلب...');
        
        const updateResponse = await fetch(`${API_BASE}/orders/${pendingOrder._id}/status`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                status: 'preparing',
                updatedBy: 'test-chef-001'
            })
        });

        if (updateResponse.ok) {
            console.log('✅ تحديث الحالة نجح');
            
            // Wait for real-time updates
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            // Check if both received updates
            if (chefReceivedUpdate && waiterReceivedUpdate) {
                console.log('🎉 الإشعارات المباشرة تعمل بنجاح!');
            } else {
                console.log('⚠️ بعض الإشعارات لم تصل:');
                console.log(`   - الطباخ: ${chefReceivedUpdate ? '✅' : '❌'}`);
                console.log(`   - النادل: ${waiterReceivedUpdate ? '✅' : '❌'}`);
            }
        } else {
            console.log('❌ فشل تحديث الحالة');
        }

        // 5. Revert order status
        console.log('4️⃣ إعادة الحالة الأصلية...');
        await fetch(`${API_BASE}/orders/${pendingOrder._id}/status`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                status: 'pending',
                updatedBy: 'test-system'
            })
        });

        // Close connections
        chefSocket.disconnect();
        waiterSocket.disconnect();
        
        console.log('✅ اختبار الإشعارات المباشرة مكتمل');

    } catch (error) {
        console.error('❌ خطأ في الاختبار:', error.message);
    }
}

// Run the test
testRealTimeNotifications();
