// مكون شاشة الطاولات البديل للوحة المدير
import React from 'react';
import type { TableAccount } from '../../models/ManagerModels';

interface SimpleTablesScreenProps {
  tableAccounts: TableAccount[];
  onRefresh: () => void;
}

export function SimpleTablesScreen({ tableAccounts, onRefresh }: SimpleTablesScreenProps) {
  // التأكد من وجود البيانات وتوفير قيم افتراضية آمنة
  const safeTableAccounts = tableAccounts || [];

  // إحصائيات الطاولات
  const openTables = safeTableAccounts.filter(table => table && table.isOpen);
  const closedTables = safeTableAccounts.filter(table => table && !table.isOpen);
  const totalRevenue = openTables.reduce((sum, table) => {
    if (!table || typeof table.totalAmount !== 'number') return sum;
    return sum + table.totalAmount;
  }, 0);

  // ترتيب الطاولات: المفتوحة أولاً ثم المغلقة
  const sortedTables = [...openTables, ...closedTables];

  return (
    <div className="tables-screen">
      <div className="tables-header">
        <h1>
          <i className="fas fa-table"></i>
          إدارة الطاولات
        </h1>
        <div className="header-actions">
          <button
            className="refresh-btn"
            onClick={onRefresh}
            title="تحديث قائمة الطاولات"
          >
            <i className="fas fa-sync-alt"></i>
            تحديث
          </button>
        </div>
      </div>

      {/* إحصائيات الطاولات */}
      <div className="tables-stats">
        <div className="stat-card open-tables">
          <div className="stat-icon">
            <i className="fas fa-table"></i>
          </div>
          <div className="stat-content">
            <div className="stat-number">{openTables.length}</div>
            <div className="stat-label">طاولات مفتوحة</div>
          </div>
        </div>

        <div className="stat-card closed-tables">
          <div className="stat-icon">
            <i className="fas fa-lock"></i>
          </div>
          <div className="stat-content">
            <div className="stat-number">{closedTables.length}</div>
            <div className="stat-label">طاولات مغلقة</div>
          </div>
        </div>

        <div className="stat-card total-revenue">
          <div className="stat-icon">
            <i className="fas fa-money-bill-wave"></i>
          </div>
          <div className="stat-content">
            <div className="stat-number">{totalRevenue.toFixed(2)}</div>
            <div className="stat-label">إجمالي الإيرادات (ج.م)</div>
          </div>
        </div>

        <div className="stat-card total-tables">
          <div className="stat-icon">
            <i className="fas fa-list"></i>
          </div>
          <div className="stat-content">
            <div className="stat-number">{safeTableAccounts.length}</div>
            <div className="stat-label">إجمالي الطاولات</div>
          </div>
        </div>
      </div>

      {/* قائمة الطاولات */}
      <div className="tables-grid">
        {sortedTables.length === 0 ? (
          <div className="empty-state">
            <i className="fas fa-table"></i>
            <h3>لا توجد طاولات</h3>
            <p>لم يتم فتح أي طاولة بعد</p>
          </div>
        ) : (
          sortedTables.map(table => {
            // التحقق من وجود بيانات الطاولة
            if (!table || !table._id) return null;
            
            return (
              <div 
                key={table._id} 
                className={`table-card ${table.isOpen ? 'open' : 'closed'}`}
              >
                {/* رأس الطاولة */}
                <div className="table-header">
                  <div className="table-number">
                    <i className="fas fa-table"></i>
                    طاولة {table.tableNumber || 'غير محدد'}
                  </div>
                  <div className={`table-status ${table.status || 'unknown'}`}>
                    <i className={`fas ${table.isOpen ? 'fa-unlock' : 'fa-lock'}`}></i>
                    {table.isOpen ? 'مفتوحة' : 'مغلقة'}
                  </div>
                </div>

                {/* معلومات النادل */}
                <div className="table-waiter">
                  <div className="waiter-info">
                    <i className="fas fa-user-tie"></i>
                    <span className="waiter-name">{table.waiterName || 'غير محدد'}</span>
                  </div>
                </div>

                {/* إجمالي المبلغ */}
                <div className="table-total">
                  <div className="total-label">إجمالي المبلغ:</div>
                  <div className="total-amount">
                    <i className="fas fa-money-bill-wave"></i>
                    {(table.totalAmount || 0).toFixed(2)} ج.م
                  </div>
                </div>

                {/* معلومات الطلبات */}
                <div className="table-orders-info">
                  <div className="orders-count">
                    <i className="fas fa-shopping-cart"></i>
                    {(table.orders && Array.isArray(table.orders) ? table.orders.length : 0)} طلب
                  </div>
                  <div className="table-time">
                    <i className="fas fa-clock"></i>
                    {table.createdAt ? new Date(table.createdAt).toLocaleString('ar-SA', {
                      day: '2-digit',
                      month: '2-digit',
                      hour: '2-digit',
                      minute: '2-digit'
                    }) : 'غير محدد'}
                  </div>
                </div>

                {/* تفاصيل الطلبات للطاولات المفتوحة */}
                {table.isOpen && table.orders && table.orders.length > 0 && (
                  <div className="table-orders-details">
                    <div className="orders-header">
                      <h4>الطلبات:</h4>
                    </div>
                    <div className="orders-list">
                      {table.orders.slice(0, 3).map(order => {
                        if (!order || !order._id) return null;
                        return (
                          <div key={order._id} className="table-order-item">
                            <div className="order-number">#{order.orderNumber || 'غير محدد'}</div>
                            <div className={`order-status ${order.status || 'pending'}`}>
                              {order.status === 'pending' && (
                                <>
                                  <i className="fas fa-clock"></i>
                                  انتظار
                                </>
                              )}
                              {order.status === 'preparing' && (
                                <>
                                  <i className="fas fa-fire"></i>
                                  تحضير
                                </>
                              )}
                              {order.status === 'ready' && (
                                <>
                                  <i className="fas fa-bell"></i>
                                  جاهز
                                </>
                              )}
                              {order.status === 'completed' && (
                                <>
                                  <i className="fas fa-check"></i>
                                  مكتمل
                                </>
                              )}
                              {!order.status && (
                                <>
                                  <i className="fas fa-clock"></i>
                                  انتظار
                                </>
                              )}
                            </div>
                            <div className="order-items-count">
                              {(order.items && Array.isArray(order.items) ? order.items.length : 0)} صنف
                            </div>
                          </div>
                        );
                      }).filter(Boolean)}
                      {table.orders.length > 3 && (
                        <div className="more-orders">
                          <i className="fas fa-ellipsis-h"></i>
                          +{table.orders.length - 3} طلب آخر
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* ملخص للطاولات المغلقة */}
                {!table.isOpen && (
                  <div className="table-closed-summary">
                    <div className="closed-info">
                      <i className="fas fa-check-circle"></i>
                      <span className="closed-label">مغلقة -</span>
                      <span className="closed-orders">
                        {(table.orders && Array.isArray(table.orders) ? table.orders.length : 0)} طلب مكتمل
                      </span>
                    </div>
                  </div>
                )}
              </div>
            );
          }).filter(Boolean)
        )}
      </div>

      {/* معلومات إضافية */}
      {safeTableAccounts.length > 0 && (
        <div className="tables-summary">
          <div className="summary-info">
            <p>
              <strong>ملخص:</strong> 
              {openTables.length} طاولة مفتوحة من أصل {safeTableAccounts.length} طاولة
              {totalRevenue > 0 && ` • إجمالي الإيرادات: ${totalRevenue.toFixed(2)} ج.م`}
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
