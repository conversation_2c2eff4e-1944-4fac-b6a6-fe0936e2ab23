.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }
  
  .modal-content {
    background-color: #fff;
    padding: 25px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    width: 90%;
    max-width: 500px;
    position: relative;
    animation: fadeIn 0.3s ease-out;
  }
  
  .modal-overlay.rtl .modal-content {
    direction: rtl;
    text-align: right;
  }
  
  .close-button {
    position: absolute;
    top: 10px;
    right: 15px;
    background: none;
    border: none;
    font-size: 1.8rem;
    cursor: pointer;
    color: #555;
  }
  .modal-overlay.rtl .close-button {
    right: auto;
    left: 15px;
  }
  
  .modal-content h2 {
    margin-top: 0;
    color: #333;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
    margin-bottom: 20px;
  }
  
  .modal-content p {
    margin: 10px 0;
    line-height: 1.6;
    color: #444;
  }
  
  .modal-content p strong {
    color: #222;
  }
  
  .order-items-list-modal {
    list-style: none;
    padding: 0;
    margin-top: 15px;
    max-height: 200px; /* Or adjust as needed */
    overflow-y: auto;
  }
  
  .order-item-modal {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
  }
  
  .order-item-modal:last-child {
    border-bottom: none;
  }
  
  .order-item-modal span {
    font-size: 0.95rem;
  }
  
  .customizations-display-modal {
    display: block;
    font-size: 0.8rem;
    color: #666;
    margin-left: 10px; /* Indent customizations slightly */
  }
  .modal-overlay.rtl .customizations-display-modal {
    margin-left: 0;
    margin-right: 10px;
  }
  
  .modal-action-button {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    margin-top: 20px;
    transition: background-color 0.2s ease;
  }
  
  .modal-action-button:hover {
    background-color: #0056b3;
  }
  
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  