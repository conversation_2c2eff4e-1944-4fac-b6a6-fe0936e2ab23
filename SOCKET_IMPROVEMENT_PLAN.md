# 🔧 تقرير تحسين نظام Socket.IO والإشعارات

## 🎯 المشكلة المحددة

بعد الفحص الشامل للتطبيق، تم اكتشاف أن نظام Socket.IO يحتاج لتحسينات لضمان عمل الإشعارات بشكل مثالي.

### 🔍 المشاكل المكتشفة:

1. **عدم وجود handlers مخصصة في الـ server** للأحداث المُرسلة من Frontend
2. **الاعتماد على `simulate` events** بدلاً من أحداث حقيقية مرتبطة بقاعدة البيانات
3. **عدم وجود تجمعات (rooms) منظمة** للنوادل والطباخين والمديرين

---

## 💡 الحلول المقترحة

### 1. 🛠️ إضافة Socket Handlers في Backend

إنشاء ملف منفصل لإدارة Socket.IO events:

```javascript
// backend/socket/socketHandlers.js
const socketHandlers = (io) => {
  const connectedUsers = new Map(); // تتبع المستخدمين المتصلين

  io.on('connection', (socket) => {
    console.log('👤 User connected:', socket.id);

    // تسجيل المستخدم
    socket.on('register', (userData) => {
      const { role, name } = userData;
      
      connectedUsers.set(socket.id, { role, name, socketId: socket.id });
      
      // انضمام للـ room حسب الدور
      if (role === 'طباخ' || role === 'chef') {
        socket.join('chefs');
      } else if (role === 'waiter' || role === 'نادل') {
        socket.join('waiters');
      } else if (role === 'مدير' || role === 'manager') {
        socket.join('managers');
      }
      
      socket.emit('registered', { 
        success: true, 
        role, 
        name,
        message: `تم تسجيلك كـ ${role}` 
      });
      
      console.log(`✅ ${name} registered as ${role}`);
    });

    // طلب جديد من النادل
    socket.on('simulateNewOrder', (orderData) => {
      console.log('📤 New order from waiter:', orderData);
      
      // إشعار جميع الطباخين
      socket.broadcast.to('chefs').emit('newOrder', {
        type: 'new_order',
        title: 'طلب جديد',
        message: `طلب جديد رقم ${orderData.orderNumber} من النادل ${orderData.waiterName}`,
        orderData,
        timestamp: new Date().toISOString()
      });
      
      // إشعار المديرين
      socket.broadcast.to('managers').emit('newOrder', {
        type: 'new_order',
        title: 'طلب جديد',
        message: `طلب جديد رقم ${orderData.orderNumber}`,
        orderData,
        timestamp: new Date().toISOString()
      });
    });

    // قبول الطلب من الطباخ
    socket.on('simulateOrderAccepted', (data) => {
      console.log('📤 Order accepted by chef:', data);
      
      // إشعار جميع النوادل
      socket.broadcast.to('waiters').emit('orderAccepted', {
        type: 'order_accepted',
        title: 'تم قبول الطلب',
        message: `تم قبول الطلب رقم ${data.orderNumber} من الطباخ ${data.chefName}`,
        orderData: data,
        timestamp: new Date().toISOString()
      });
      
      // إشعار المديرين
      socket.broadcast.to('managers').emit('orderUpdate', {
        type: 'order_accepted',
        title: 'تم قبول طلب',
        message: `الطباخ ${data.chefName} قبل الطلب رقم ${data.orderNumber}`,
        orderData: data,
        timestamp: new Date().toISOString()
      });
    });

    // إنهاء التحضير من الطباخ
    socket.on('simulateOrderReady', (data) => {
      console.log('📤 Order ready from chef:', data);
      
      // إشعار جميع النوادل
      socket.broadcast.to('waiters').emit('orderReady', {
        type: 'order_ready',
        title: 'الطلب جاهز',
        message: `الطلب رقم ${data.orderNumber} جاهز للتسليم!`,
        orderData: data,
        timestamp: new Date().toISOString()
      });
      
      // إشعار المديرين
      socket.broadcast.to('managers').emit('orderUpdate', {
        type: 'order_ready',
        title: 'طلب جاهز',
        message: `الطلب رقم ${data.orderNumber} جاهز للتسليم`,
        orderData: data,
        timestamp: new Date().toISOString()
      });
    });

    // طلب خصم جديد
    socket.on('newDiscountRequest', (data) => {
      console.log('📤 New discount request:', data);
      
      // إشعار جميع المديرين
      socket.broadcast.to('managers').emit('newDiscountRequest', {
        type: 'discount_request',
        title: 'طلب خصم جديد',
        message: `طلب خصم بقيمة ${data.amount} للطلب رقم ${data.orderNumber}`,
        requestData: data,
        timestamp: new Date().toISOString()
      });
    });

    // رد على طلب الخصم
    socket.on('discountRequestProcessed', (data) => {
      console.log('📤 Discount request processed:', data);
      
      const message = data.approved 
        ? `تم الموافقة على طلب الخصم للطلب رقم ${data.orderNumber}`
        : `تم رفض طلب الخصم للطلب رقم ${data.orderNumber}`;
      
      // إشعار النادل الذي طلب الخصم
      socket.broadcast.to('waiters').emit('discountRequestProcessed', {
        type: data.approved ? 'discount_approved' : 'discount_rejected',
        title: data.approved ? 'تمت الموافقة على الخصم' : 'تم رفض الخصم',
        message,
        requestData: data,
        timestamp: new Date().toISOString()
      });
    });

    // قطع الاتصال
    socket.on('disconnect', () => {
      const user = connectedUsers.get(socket.id);
      if (user) {
        console.log(`👤 ${user.name} (${user.role}) disconnected`);
        connectedUsers.delete(socket.id);
      } else {
        console.log('👤 User disconnected:', socket.id);
      }
    });

    // إرسال قائمة المستخدمين المتصلين للمديرين
    socket.on('getConnectedUsers', () => {
      const users = Array.from(connectedUsers.values());
      socket.emit('connectedUsersList', users);
    });
  });

  return { connectedUsers };
};

module.exports = socketHandlers;
```

### 2. 🔧 تحديث Server.js

```javascript
// backend/server.js
const socketHandlers = require('./socket/socketHandlers');

// بعد إنشاء io
const socketManager = socketHandlers(io);

// باقي الكود...
```

### 3. 📱 تحسين Frontend Socket Integration

تحديث مكون useNotifications:

```typescript
// src/hooks/useNotifications.ts
export const useNotifications = (options: NotificationHookOptions) => {
  // ... الكود الموجود

  useEffect(() => {
    // تسجيل محسن
    const registerUser = () => {
      socket.emit('register', { 
        role: options.role, 
        name: options.userName 
      });
    };

    // أحداث الاتصال
    socket.on('connect', () => {
      console.log('🔌 Connected to Socket.IO');
      setIsConnected(true);
      registerUser();
    });

    socket.on('disconnect', () => {
      console.log('❌ Disconnected from Socket.IO');
      setIsConnected(false);
    });

    socket.on('registered', (data) => {
      console.log('✅ Successfully registered:', data);
      setIsConnected(true);
    });

    // أحداث الإشعارات المحسنة
    socket.on('newOrder', (notification) => {
      handleNotification(notification);
      playNotificationSound('new_order');
    });

    socket.on('orderAccepted', (notification) => {
      handleNotification(notification);
      playNotificationSound('order_accepted');
    });

    socket.on('orderReady', (notification) => {
      handleNotification(notification);
      playNotificationSound('order_ready');
    });

    socket.on('newDiscountRequest', (notification) => {
      handleNotification(notification);
      playNotificationSound('discount_request');
    });

    socket.on('discountRequestProcessed', (notification) => {
      handleNotification(notification);
      playNotificationSound(notification.type);
    });

    // تنظيف
    return () => {
      socket.off('connect');
      socket.off('disconnect');
      socket.off('registered');
      socket.off('newOrder');
      socket.off('orderAccepted');
      socket.off('orderReady');
      socket.off('newDiscountRequest');
      socket.off('discountRequestProcessed');
    };
  }, []);

  // ... باقي الكود
};
```

### 4. 🗄️ ربط الإشعارات بقاعدة البيانات

إضافة middleware في نماذج البيانات:

```javascript
// backend/models/Order.js
const io = require('../socket/socketInstance'); // singleton instance

orderSchema.post('save', function(doc, next) {
  const order = this;
  
  // إرسال إشعارات عند تغيير الحالة
  if (order.isModified('status')) {
    const eventData = {
      orderId: order._id,
      orderNumber: order.orderNumber,
      status: order.status,
      waiterName: order.staff?.waiter?.name,
      chefName: order.staff?.chef?.name,
      timestamp: new Date().toISOString()
    };

    switch (order.status) {
      case 'pending':
        // طلب جديد - إشعار للطباخين
        io.to('chefs').emit('newOrder', {
          type: 'new_order',
          title: 'طلب جديد',
          message: `طلب جديد رقم ${order.orderNumber}`,
          orderData: eventData
        });
        break;

      case 'preparing':
        // تم قبول الطلب - إشعار للنوادل
        io.to('waiters').emit('orderAccepted', {
          type: 'order_accepted',
          title: 'تم قبول الطلب',
          message: `تم قبول الطلب رقم ${order.orderNumber}`,
          orderData: eventData
        });
        break;

      case 'ready':
        // الطلب جاهز - إشعار للنوادل
        io.to('waiters').emit('orderReady', {
          type: 'order_ready',
          title: 'الطلب جاهز',
          message: `الطلب رقم ${order.orderNumber} جاهز للتسليم`,
          orderData: eventData
        });
        break;
    }

    // إشعار المديرين بجميع التحديثات
    io.to('managers').emit('orderUpdate', {
      type: 'order_update',
      title: 'تحديث طلب',
      message: `تم تحديث حالة الطلب رقم ${order.orderNumber} إلى ${order.status}`,
      orderData: eventData
    });
  }
  
  next();
});
```

### 5. 🛡️ إضافة آلية إعادة الاتصال

```typescript
// src/socket.ts
import io from 'socket.io-client';

const socket = io(getSocketUrl(), {
  ...APP_CONFIG.SOCKET.OPTIONS,
  autoConnect: true,
  reconnection: true,
  reconnectionDelay: 1000,
  reconnectionAttempts: 5,
  timeout: 20000
});

// معالجة إعادة الاتصال
socket.on('connect', () => {
  console.log('🔌 Socket connected successfully');
});

socket.on('disconnect', (reason) => {
  console.log('❌ Socket disconnected:', reason);
  
  if (reason === 'io server disconnect') {
    // إعادة الاتصال يدوياً إذا قطع الخادم الاتصال
    socket.connect();
  }
});

socket.on('reconnect', (attemptNumber) => {
  console.log('🔄 Socket reconnected after', attemptNumber, 'attempts');
});

socket.on('reconnect_error', (error) => {
  console.error('❌ Socket reconnection failed:', error);
});

export default socket;
```

---

## 📊 خطة التنفيذ

### المرحلة 1: إعداد Socket Handlers (1 يوم)
- [ ] إنشاء ملف `backend/socket/socketHandlers.js`
- [ ] إضافة جميع event handlers المطلوبة
- [ ] تحديث `server.js` لاستخدام الـ handlers الجديدة

### المرحلة 2: تحسين Frontend Integration (1 يوم)
- [ ] تحديث `useNotifications.ts`
- [ ] إضافة آلية إعادة الاتصال في `socket.ts`
- [ ] تحسين معالجة الأخطاء

### المرحلة 3: ربط قاعدة البيانات (1 يوم)
- [ ] إضافة middleware في نماذج البيانات
- [ ] إنشاء singleton instance للـ Socket
- [ ] اختبار الإشعارات التلقائية

### المرحلة 4: اختبار شامل (1 يوم)
- [ ] اختبار جميع سيناريوهات الإشعارات
- [ ] اختبار إعادة الاتصال
- [ ] اختبار تحت ضغط (multiple users)

---

## 🧪 سكريبت اختبار الإشعارات

```javascript
// test-notifications.js
const io = require('socket.io-client');

class NotificationTester {
  constructor() {
    this.waiterSocket = null;
    this.chefSocket = null;
    this.managerSocket = null;
  }

  async connectUsers() {
    // اتصال النادل
    this.waiterSocket = io('http://localhost:5000');
    this.waiterSocket.emit('register', { role: 'waiter', name: 'test-waiter' });

    // اتصال الطباخ
    this.chefSocket = io('http://localhost:5000');
    this.chefSocket.emit('register', { role: 'طباخ', name: 'test-chef' });

    // اتصال المدير
    this.managerSocket = io('http://localhost:5000');
    this.managerSocket.emit('register', { role: 'مدير', name: 'test-manager' });

    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  async testNewOrderNotification() {
    console.log('🧪 Testing new order notification...');

    // الطباخ يستمع للطلبات الجديدة
    this.chefSocket.on('newOrder', (data) => {
      console.log('✅ Chef received new order notification:', data.message);
    });

    // المدير يستمع للطلبات الجديدة
    this.managerSocket.on('newOrder', (data) => {
      console.log('✅ Manager received new order notification:', data.message);
    });

    // النادل يرسل طلب جديد
    this.waiterSocket.emit('simulateNewOrder', {
      orderNumber: 'TEST-001',
      waiterName: 'test-waiter',
      tableNumber: '5',
      items: [{ name: 'قهوة', quantity: 1 }]
    });

    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  async testOrderAcceptedNotification() {
    console.log('🧪 Testing order accepted notification...');

    // النادل يستمع لقبول الطلبات
    this.waiterSocket.on('orderAccepted', (data) => {
      console.log('✅ Waiter received order accepted notification:', data.message);
    });

    // الطباخ يقبل الطلب
    this.chefSocket.emit('simulateOrderAccepted', {
      orderNumber: 'TEST-001',
      chefName: 'test-chef',
      waiterName: 'test-waiter'
    });

    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  async testOrderReadyNotification() {
    console.log('🧪 Testing order ready notification...');

    // النادل يستمع لجاهزية الطلبات
    this.waiterSocket.on('orderReady', (data) => {
      console.log('✅ Waiter received order ready notification:', data.message);
    });

    // الطباخ ينهي الطلب
    this.chefSocket.emit('simulateOrderReady', {
      orderNumber: 'TEST-001',
      chefName: 'test-chef',
      waiterName: 'test-waiter'
    });

    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  async runAllTests() {
    try {
      await this.connectUsers();
      await this.testNewOrderNotification();
      await this.testOrderAcceptedNotification();
      await this.testOrderReadyNotification();
      
      console.log('🎉 All notification tests completed!');
    } catch (error) {
      console.error('❌ Test failed:', error);
    } finally {
      this.waiterSocket?.disconnect();
      this.chefSocket?.disconnect();
      this.managerSocket?.disconnect();
    }
  }
}

// تشغيل الاختبارات
const tester = new NotificationTester();
tester.runAllTests();
```

---

## 📋 الخلاصة

هذه التحسينات ستضمن:
- ✅ عمل نظام الإشعارات بشكل مثالي
- ✅ تنظيم المستخدمين في مجموعات (rooms)
- ✅ إشعارات تلقائية مرتبطة بقاعدة البيانات
- ✅ استقرار الاتصال وإعادة الاتصال التلقائي
- ✅ سهولة الاختبار والصيانة

بعد تطبيق هذه التحسينات، سيكون نظام الإشعارات مكتملاً 100% ويعمل بكفاءة عالية.
