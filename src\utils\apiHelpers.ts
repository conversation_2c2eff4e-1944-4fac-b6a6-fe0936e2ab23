// API Helper functions for authenticated requests
// دوال مساعدة لطلبات API مع المصادقة

import { getApiUrl } from '../config/app.config';

// Get auth token from localStorage
export const getAuthToken = (): string | null => {
  return localStorage.getItem('token') || localStorage.getItem('authToken');
};

// Get auth headers with token
export const getAuthHeaders = (): HeadersInit => {
  const token = getAuthToken();
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  return headers;
};

// Make authenticated API request
export const makeAuthenticatedRequest = async (
  endpoint: string,
  options: RequestInit = {}
): Promise<Response> => {
  const url = getApiUrl(endpoint);
  const headers = {
    ...getAuthHeaders(),
    ...options.headers,
  };

  return fetch(url, {
    ...options,
    headers,
  });
};

// GET request with authentication and enhanced rate limiting
export const authenticatedGet = async (endpoint: string): Promise<any> => {
  try {
    // Strict rate limit check
    if (!rateLimiter.canMakeRequest(endpoint)) {
      const waitTime = rateLimiter.getWaitTime(endpoint);
      console.warn(`🚫 Rate limit exceeded for ${endpoint}. Must wait ${waitTime}ms`);

      // Force wait - no exceptions
      if (waitTime > 0) {
        await new Promise(resolve => setTimeout(resolve, Math.min(waitTime, 10000)));
      }

      // Check again after waiting
      if (!rateLimiter.canMakeRequest(endpoint)) {
        throw new Error(`Rate limit still exceeded for ${endpoint}`);
      }
    }

    console.log(`🔄 Making request to ${endpoint}`);
    const response = await makeAuthenticatedRequest(endpoint);

    if (!response.ok) {
      // Special handling for 429 errors
      if (response.status === 429) {
        console.error(`🚫 Server rate limited ${endpoint}. Waiting 10 seconds...`);
        await new Promise(resolve => setTimeout(resolve, 10000));
        throw new Error(`HTTP ${response.status}: Too Many Requests`);
      }

      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    console.log(`✅ Request successful for ${endpoint}`);
    return await response.json();
  } catch (error) {
    console.error(`❌ Authenticated GET request failed for ${endpoint}:`, error);
    throw error;
  }
};

// POST request with authentication
export const authenticatedPost = async (
  endpoint: string,
  data: any
): Promise<any> => {
  try {
    const response = await makeAuthenticatedRequest(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Authenticated POST request failed:', error);
    throw error;
  }
};

// PUT request with authentication
export const authenticatedPut = async (
  endpoint: string,
  data: any
): Promise<any> => {
  try {
    const response = await makeAuthenticatedRequest(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
    
    if (response.status === 401) {
      console.error('❌ Authentication failed - Token may be expired');
      // Clear invalid token
      localStorage.removeItem('token');
      localStorage.removeItem('authToken');
      // Redirect to login
      window.location.href = '/login';
      throw new Error('Authentication required - redirecting to login');
    }
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ HTTP ${response.status} error:`, errorText);
      throw new Error(`HTTP ${response.status}: ${errorText || response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Authenticated PUT request failed:', error);
    throw error;
  }
};

// DELETE request with authentication
export const authenticatedDelete = async (endpoint: string): Promise<any> => {
  try {
    const response = await makeAuthenticatedRequest(endpoint, {
      method: 'DELETE',
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Authenticated DELETE request failed:', error);
    throw error;
  }
};

// Check if user is authenticated
export const isAuthenticated = (): boolean => {
  const token = getAuthToken();
  const user = localStorage.getItem('user');
  return !!(token && user);
};

// Get current user from localStorage
export const getCurrentUser = (): any | null => {
  try {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  } catch (error) {
    console.error('Error parsing user data:', error);
    return null;
  }
};

// Logout user (clear all auth data)
export const logoutUser = (): void => {
  localStorage.removeItem('token');
  localStorage.removeItem('authToken');
  localStorage.removeItem('user');
  localStorage.removeItem('username');
  localStorage.removeItem('userRole');
};

// Enhanced Rate limiting mechanism - محسن للسرعة
class RateLimiter {
  private requests: Map<string, number[]> = new Map();
  private readonly maxRequests: number = 10; // زيادة الحد الأقصى لتحسين السرعة
  private readonly windowMs: number = 60000; // 1 minute window
  private readonly minInterval: number = 1000; // تقليل الانتظار إلى ثانية واحدة

  canMakeRequest(endpoint: string): boolean {
    const now = Date.now();
    const requests = this.requests.get(endpoint) || [];

    // Remove old requests outside the window
    const validRequests = requests.filter(time => now - time < this.windowMs);

    // Check if we've exceeded the max requests
    if (validRequests.length >= this.maxRequests) {
      console.warn(`🚫 Rate limit exceeded for ${endpoint}: ${validRequests.length}/${this.maxRequests}`);
      return false;
    }

    // Check minimum interval between requests
    if (validRequests.length > 0) {
      const lastRequest = Math.max(...validRequests);
      if (now - lastRequest < this.minInterval) {
        console.warn(`⏱️ Too soon for ${endpoint}: ${now - lastRequest}ms < ${this.minInterval}ms`);
        return false;
      }
    }

    // Add current request
    validRequests.push(now);
    this.requests.set(endpoint, validRequests);

    console.log(`✅ Request allowed for ${endpoint}: ${validRequests.length}/${this.maxRequests}`);
    return true;
  }

  getWaitTime(endpoint: string): number {
    const requests = this.requests.get(endpoint) || [];
    if (requests.length === 0) return 0;

    const now = Date.now();
    const lastRequest = Math.max(...requests);
    const intervalWait = this.minInterval - (now - lastRequest);

    if (requests.length >= this.maxRequests) {
      const oldestRequest = Math.min(...requests);
      const windowWait = this.windowMs - (now - oldestRequest);
      return Math.max(intervalWait, windowWait);
    }

    return Math.max(0, intervalWait);
  }
}

const rateLimiter = new RateLimiter();

// Request queue to prevent concurrent requests to same endpoint
const activeRequests = new Map<string, Promise<any>>();

// Handle API errors with user-friendly messages
export const handleApiError = (error: any): string => {
  if (error.message?.includes('401')) {
    return 'انتهت صلاحية جلسة العمل. الرجاء تسجيل الدخول مرة أخرى.';
  }

  if (error.message?.includes('403')) {
    return 'ليس لديك صلاحية للوصول إلى هذا المورد.';
  }

  if (error.message?.includes('404')) {
    return 'المورد المطلوب غير موجود.';
  }

  if (error.message?.includes('409')) {
    return 'هذه الطاولة مفتوحة بالفعل من قبل نادل آخر. لا يمكن فتحها مرة أخرى حتى يتم إنهاء الحساب الحالي.';
  }

  if (error.message?.includes('429')) {
    return 'تم إرسال طلبات كثيرة جداً. الرجاء الانتظار قليلاً ثم المحاولة مرة أخرى.';
  }

  if (error.message?.includes('500')) {
    return 'خطأ في الخادم. الرجاء المحاولة لاحقاً.';
  }

  if (error.message?.includes('Network')) {
    return 'خطأ في الاتصال بالشبكة. تحقق من اتصالك بالإنترنت.';
  }

  return error.message || 'حدث خطأ غير متوقع.';
};

// Enhanced retry mechanism for failed requests with exponential backoff
export const retryRequest = async (
  requestFn: () => Promise<any>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<any> => {
  let lastError: any;

  for (let i = 0; i < maxRetries; i++) {
    try {
      return await requestFn();
    } catch (error: any) {
      lastError = error;

      // Don't retry on certain errors
      if (error.message?.includes('401') || error.message?.includes('403')) {
        throw error;
      }

      if (i < maxRetries - 1) {
        // Exponential backoff with jitter
        const delay = baseDelay * Math.pow(2, i) + Math.random() * 1000;

        // Special handling for 429 errors
        if (error.message?.includes('429')) {
          const rateLimitDelay = Math.max(delay, 5000); // At least 5 seconds for rate limits
          console.warn(`Rate limited, waiting ${rateLimitDelay}ms before retry ${i + 1}/${maxRetries}`);
          await new Promise(resolve => setTimeout(resolve, rateLimitDelay));
        } else {
          console.warn(`Request failed, retrying in ${delay}ms (${i + 1}/${maxRetries})`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
  }

  throw lastError;
};

// GET request with retry mechanism and request deduplication
export const authenticatedGetWithRetry = async (endpoint: string): Promise<any> => {
  // Check if there's already an active request for this endpoint
  if (activeRequests.has(endpoint)) {
    console.log(`🔄 Reusing active request for ${endpoint}`);
    return activeRequests.get(endpoint);
  }

  // Create new request
  const requestPromise = retryRequest(() => authenticatedGet(endpoint), 2, 2000);

  // Store in active requests
  activeRequests.set(endpoint, requestPromise);

  try {
    const result = await requestPromise;
    return result;
  } finally {
    // Remove from active requests when done
    activeRequests.delete(endpoint);
  }
};

export default {
  getAuthToken,
  getAuthHeaders,
  makeAuthenticatedRequest,
  authenticatedGet,
  authenticatedPost,
  authenticatedPut,
  authenticatedDelete,
  isAuthenticated,
  getCurrentUser,
  logoutUser,
  handleApiError,
  retryRequest,
};
