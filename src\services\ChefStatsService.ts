// خدمة إحصائيات الطباخ
import type { ChefOrder, ChefStats, DetailedChefStats } from '../models/ChefModels';

export class ChefStatsService {
  private static instance: ChefStatsService;

  // Singleton pattern
  static getInstance(): ChefStatsService {
    if (!ChefStatsService.instance) {
      ChefStatsService.instance = new ChefStatsService();
    }
    return ChefStatsService.instance;
  }

  private constructor() {}

  // حساب الإحصائيات الأساسية
  calculateBasicStats(orders: ChefOrder[], chefId: string, chefName: string): ChefStats {
    // فلترة الطلبات للطباخ الحالي أو جميع الطلبات إذا لم يحدد طباخ
    const chefOrders = orders.filter(order => 
      !chefId || order.chefId === chefId || order.chefName === chefName
    );

    // طلبات اليوم
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const todayOrders = chefOrders.filter(order => 
      new Date(order.createdAt) >= startOfDay
    );

    // تصفية الطلبات حسب الحالة
    const completedOrders = chefOrders.filter(order => order.status === 'completed');
    const pendingOrders = chefOrders.filter(order => order.status === 'pending');
    const preparingOrders = chefOrders.filter(order => order.status === 'preparing');
    const readyOrders = chefOrders.filter(order => order.status === 'ready');

    // حساب متوسط وقت التحضير
    const ordersWithTime = completedOrders.filter(order => order.preparationTime && order.preparationTime > 0);
    const averagePreparationTime = ordersWithTime.length > 0
      ? ordersWithTime.reduce((sum, order) => sum + (order.preparationTime || 0), 0) / ordersWithTime.length
      : 0;

    // حساب إجمالي وقت التحضير
    const totalPreparationTime = ordersWithTime.reduce((sum, order) => sum + (order.preparationTime || 0), 0);

    // حساب الطلبات في الساعة
    const hoursWorked = this.calculateWorkingHours(todayOrders);
    const ordersPerHour = hoursWorked > 0 ? todayOrders.length / hoursWorked : 0;

    return {
      todayOrders: todayOrders.length,
      completedOrders: completedOrders.length,
      pendingOrders: pendingOrders.length,
      preparingOrders: preparingOrders.length,
      readyOrders: readyOrders.length,
      averagePreparationTime: Math.round(averagePreparationTime),
      totalPreparationTime: Math.round(totalPreparationTime),
      ordersPerHour: Math.round(ordersPerHour * 10) / 10
    };
  }

  // حساب الإحصائيات المفصلة
  calculateDetailedStats(orders: ChefOrder[], chefId: string, chefName: string): DetailedChefStats {
    const basicStats = this.calculateBasicStats(orders, chefId, chefName);
    
    // فلترة الطلبات للطباخ
    const chefOrders = orders.filter(order => 
      !chefId || order.chefId === chefId || order.chefName === chefName
    );

    // إحصائيات كل ساعة
    const hourlyStats = this.calculateHourlyStats(chefOrders);

    // إحصائيات الفئات
    const categoryStats = this.calculateCategoryStats(chefOrders);

    // مقاييس الأداء
    const performanceMetrics = this.calculatePerformanceMetrics(chefOrders);

    return {
      basic: basicStats,
      hourlyStats,
      categoryStats,
      performanceMetrics
    };
  }

  // حساب ساعات العمل
  private calculateWorkingHours(orders: ChefOrder[]): number {
    if (orders.length === 0) return 0;

    const orderTimes = orders.map(order => new Date(order.createdAt).getTime());
    const firstOrder = Math.min(...orderTimes);
    const lastOrder = Math.max(...orderTimes);
    
    const timeDifference = lastOrder - firstOrder;
    const hoursWorked = timeDifference / (1000 * 60 * 60);
    
    return Math.max(hoursWorked, 1); // على الأقل ساعة واحدة
  }

  // حساب الإحصائيات كل ساعة
  private calculateHourlyStats(orders: ChefOrder[]): { hour: number; ordersCount: number; averageTime: number; }[] {
    const hourlyData: { [hour: number]: { count: number; totalTime: number; } } = {};

    orders.forEach(order => {
      const hour = new Date(order.createdAt).getHours();
      
      if (!hourlyData[hour]) {
        hourlyData[hour] = { count: 0, totalTime: 0 };
      }
      
      hourlyData[hour].count++;
      if (order.preparationTime) {
        hourlyData[hour].totalTime += order.preparationTime;
      }
    });

    return Object.entries(hourlyData).map(([hour, data]) => ({
      hour: parseInt(hour),
      ordersCount: data.count,
      averageTime: data.count > 0 ? Math.round(data.totalTime / data.count) : 0
    })).sort((a, b) => a.hour - b.hour);
  }

  // حساب إحصائيات الفئات
  private calculateCategoryStats(orders: ChefOrder[]): { category: string; ordersCount: number; averageTime: number; }[] {
    const categoryData: { [category: string]: { count: number; totalTime: number; } } = {};

    orders.forEach(order => {
      order.items.forEach(item => {
        const category = item.category || 'غير محدد';
        
        if (!categoryData[category]) {
          categoryData[category] = { count: 0, totalTime: 0 };
        }
        
        categoryData[category].count += item.quantity;
        if (item.preparationTime) {
          categoryData[category].totalTime += item.preparationTime * item.quantity;
        }
      });
    });

    return Object.entries(categoryData).map(([category, data]) => ({
      category,
      ordersCount: data.count,
      averageTime: data.count > 0 ? Math.round(data.totalTime / data.count) : 0
    })).sort((a, b) => b.ordersCount - a.ordersCount);
  }

  // حساب مقاييس الأداء
  private calculatePerformanceMetrics(orders: ChefOrder[]): { efficiency: number; speed: number; quality: number; } {
    const completedOrders = orders.filter(order => order.status === 'completed');
    
    // الكفاءة: نسبة الطلبات المكتملة في الوقت المحدد (افتراضياً 20 دقيقة)
    const standardTime = 20; // دقيقة
    const onTimeOrders = completedOrders.filter(order => 
      (order.preparationTime || 0) <= standardTime
    );
    const efficiency = completedOrders.length > 0 
      ? (onTimeOrders.length / completedOrders.length) * 100 
      : 0;

    // السرعة: مقارنة متوسط الوقت بالمعيار
    const ordersWithTime = completedOrders.filter(order => order.preparationTime && order.preparationTime > 0);
    const averageTime = ordersWithTime.length > 0
      ? ordersWithTime.reduce((sum, order) => sum + (order.preparationTime || 0), 0) / ordersWithTime.length
      : standardTime;
    
    const speed = standardTime > 0 ? Math.max(0, 100 - ((averageTime - standardTime) / standardTime * 100)) : 100;

    // الجودة: معيار افتراضي (يمكن تحسينه بناءً على تقييمات العملاء)
    const quality = 85; // معيار افتراضي

    return {
      efficiency: Math.round(efficiency),
      speed: Math.round(Math.max(0, Math.min(100, speed))),
      quality: Math.round(quality)
    };
  }

  // حساب الوقت المتبقي المقدر للطلب
  calculateEstimatedTime(order: ChefOrder): number {
    // حساب متوسط وقت التحضير لكل عنصر
    let totalEstimatedTime = 0;
    
    order.items.forEach(item => {
      // وقت تحضير افتراضي حسب الفئة
      const defaultTimes: { [key: string]: number } = {
        'hot_drinks': 3,     // المشروبات الساخنة
        'cold_drinks': 2,    // المشروبات الباردة
        'appetizers': 8,     // المقبلات
        'main_courses': 15,  // الأطباق الرئيسية
        'desserts': 5,       // الحلويات
        'salads': 6,         // السلطات
        'sandwiches': 10,    // السندويتشات
        'default': 8         // افتراضي
      };
      
      const categoryTime = defaultTimes[item.category || 'default'] || defaultTimes.default;
      totalEstimatedTime += categoryTime * item.quantity;
    });

    return Math.max(5, totalEstimatedTime); // على الأقل 5 دقائق
  }

  // تحديد أولوية الطلب
  calculateOrderPriority(order: ChefOrder): 'low' | 'normal' | 'high' | 'urgent' {
    const now = new Date();
    const orderTime = new Date(order.createdAt);
    const minutesOld = (now.getTime() - orderTime.getTime()) / (1000 * 60);
    const estimatedTime = this.calculateEstimatedTime(order);

    // إذا كان الطلب متأخر عن الوقت المقدر بأكثر من 10 دقائق
    if (minutesOld > estimatedTime + 10) return 'urgent';
    
    // إذا كان الطلب متأخر عن الوقت المقدر بأكثر من 5 دقائق
    if (minutesOld > estimatedTime + 5) return 'high';
    
    // إذا كان الطلب في الوقت المناسب
    if (minutesOld <= estimatedTime) return 'normal';
    
    // طلبات جديدة
    return 'low';
  }

  // حساب إحصائيات اليوم فقط
  calculateTodayStats(orders: ChefOrder[], chefId: string, chefName: string): ChefStats {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    
    const todayOrders = orders.filter(order => 
      new Date(order.createdAt) >= startOfDay &&
      (!chefId || order.chefId === chefId || order.chefName === chefName)
    );

    return this.calculateBasicStats(todayOrders, chefId, chefName);
  }

  // حساب معدل الإنتاجية
  calculateProductivityRate(orders: ChefOrder[], chefId: string, chefName: string): {
    ordersPerHour: number;
    itemsPerHour: number;
    averageOrderSize: number;
  } {
    const chefOrders = orders.filter(order => 
      !chefId || order.chefId === chefId || order.chefName === chefName
    );

    const hoursWorked = this.calculateWorkingHours(chefOrders);
    const totalItems = chefOrders.reduce((sum, order) => 
      sum + order.items.reduce((itemSum, item) => itemSum + item.quantity, 0), 0
    );

    return {
      ordersPerHour: hoursWorked > 0 ? Math.round((chefOrders.length / hoursWorked) * 10) / 10 : 0,
      itemsPerHour: hoursWorked > 0 ? Math.round((totalItems / hoursWorked) * 10) / 10 : 0,
      averageOrderSize: chefOrders.length > 0 ? Math.round((totalItems / chefOrders.length) * 10) / 10 : 0
    };
  }
}
