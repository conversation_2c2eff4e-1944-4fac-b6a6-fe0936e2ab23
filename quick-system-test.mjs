// اختبار سريع وشامل للنظام
// Quick Comprehensive System Test

const API_BASE = 'http://localhost:4003';

// بيانات المصادقة الحقيقية
const USERS = {
  waiter: { username: 'a<PERSON>', password: '253040' },
  chef: { username: 'khale<PERSON>', password: '253040' },
  manager: { username: '<PERSON><PERSON>', password: 'MOHAMEDmostafa123' }
};

// معرفات المنتجات الحقيقية
const REAL_PRODUCT_ID = '683c105102b04d587b825b60'; // موكا

class QuickSystemTest {
  constructor() {
    this.startTime = Date.now();
  }

  async authenticate(userType) {
    const response = await fetch(`${API_BASE}/api/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(USERS[userType])
    });

    const data = await response.json();
    if (!data.success) throw new Error(`Authentication failed for ${userType}`);
    return data.token;
  }

  async makeRequest(endpoint, options = {}, token) {
    const response = await fetch(`${API_BASE}${endpoint}`, {
      ...options,
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
        ...options.headers
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`);
    }

    return response.json();
  }

  async runQuickTest() {
    console.log('🚀 بدء اختبار سريع للنظام...\n');

    try {
      // 1. فحص حالة الخادم
      console.log('1️⃣ فحص حالة الخادم...');
      const health = await fetch(`${API_BASE}/health`);
      const healthData = await health.json();
      console.log(`   ✅ الخادم يعمل - ${healthData.version}`);
      console.log(`   📊 قاعدة البيانات: ${healthData.database.userCount} مستخدم\n`);

      // 2. اختبار المصادقة
      console.log('2️⃣ اختبار المصادقة...');
      const waiterToken = await this.authenticate('waiter');
      const chefToken = await this.authenticate('chef');
      const managerToken = await this.authenticate('manager');
      console.log('   ✅ جميع المستخدمين تم تسجيل دخولهم بنجاح\n');

      // 3. إنشاء طلب جديد
      console.log('3️⃣ إنشاء طلب جديد...');
      const orderData = {
        tableNumber: Math.floor(Math.random() * 50) + 1, // طاولة عشوائية
        customerName: 'عميل اختبار',
        items: [{
          product: REAL_PRODUCT_ID,
          name: 'موكا',
          quantity: 1,
          price: 50,
          notes: 'اختبار سريع'
        }],
        notes: 'طلب تجريبي'
      };

      const order = await this.makeRequest('/api/orders', {
        method: 'POST',
        body: JSON.stringify(orderData)
      }, waiterToken);

      const orderId = order.order._id;
      console.log(`   ✅ طلب تم إنشاؤه: ${order.order.orderNumber}`);
      console.log(`   📋 المجموع: ${order.order.totalPrice} ريال\n`);

      // 4. تحديث حالة الطلب بواسطة الطباخ
      console.log('4️⃣ الطباخ يعالج الطلب...');
      await this.makeRequest(`/api/orders/${orderId}`, {
        method: 'PUT',
        body: JSON.stringify({ status: 'preparing' })
      }, chefToken);
      console.log('   ✅ الطلب قيد التحضير');

      await this.makeRequest(`/api/orders/${orderId}`, {
        method: 'PUT',
        body: JSON.stringify({ status: 'ready' })
      }, chefToken);
      console.log('   ✅ الطلب جاهز\n');

      // 5. النادل يقدم الطلب
      console.log('5️⃣ النادل يقدم الطلب...');
      await this.makeRequest(`/api/orders/${orderId}`, {
        method: 'PUT',
        body: JSON.stringify({ status: 'served' })
      }, waiterToken);
      console.log('   ✅ الطلب تم تقديمه للعميل\n');

      // 6. المدير يراجع الطلبات
      console.log('6️⃣ المدير يراجع التقارير...');
      const allOrders = await this.makeRequest('/api/orders', {}, managerToken);
      console.log(`   ✅ المدير يرى ${allOrders.length} طلب إجمالي\n`);

      // 7. اختبار إدارة الطاولات
      console.log('7️⃣ اختبار إدارة الطاولات...');
      const tableAccounts = await this.makeRequest('/api/table-accounts', {}, waiterToken);
      console.log(`   ✅ ${tableAccounts.length} حساب طاولة موجود\n`);

      const duration = Date.now() - this.startTime;
      console.log('🏆 جميع الاختبارات نجحت! ✅');
      console.log(`⏱️  وقت التنفيذ: ${duration}ms (${(duration/1000).toFixed(1)} ثانية)`);
      console.log('\n📋 ملخص النظام:');
      console.log('================');
      console.log('✅ الخادم الخلفي يعمل بشكل صحيح');
      console.log('✅ قاعدة البيانات متصلة');
      console.log('✅ المصادقة تعمل لجميع الأدوار');
      console.log('✅ إنشاء الطلبات يعمل');
      console.log('✅ سير عمل الطباخ يعمل');
      console.log('✅ تقديم الطلبات يعمل');
      console.log('✅ تقارير المدير تعمل');
      console.log('✅ إدارة الطاولات تعمل');
      console.log('\n🎉 النظام جاهز للاستخدام بالكامل!');

    } catch (error) {
      console.error(`❌ خطأ في الاختبار: ${error.message}`);
      const duration = Date.now() - this.startTime;
      console.log(`⏱️  وقت التنفيذ حتى الخطأ: ${duration}ms`);
    }
  }
}

// تشغيل الاختبار
const test = new QuickSystemTest();
test.runQuickTest();
