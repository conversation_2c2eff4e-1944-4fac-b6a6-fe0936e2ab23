// Socket.IO Test Client لاختبار اتصال النظام
// تشغيل: node socket-test-client.js

const io = require('socket.io-client');

const BACKEND_URL = 'http://localhost:4003';

console.log('🔗 محاولة الاتصال بـ Socket.IO server...');
console.log('URL:', BACKEND_URL);

const socket = io(BACKEND_URL, {
  transports: ['websocket', 'polling'],
  reconnection: true,
  reconnectionDelay: 1000,
  reconnectionAttempts: 5,
  timeout: 20000
});

// Event listeners for connection
socket.on('connect', () => {
  console.log('✅ تم الاتصال بنجاح!');
  console.log('Socket ID:', socket.id);
  
  // Test user registration
  console.log('\n📝 اختبار تسجيل المستخدم...');
  socket.emit('register-user', {
    userId: 'test-user-123',
    role: 'chef',
    name: 'طباخ اختبار'
  });
});

socket.on('connect_error', (error) => {
  console.error('❌ خطأ في الاتصال:', error.message);
});

socket.on('disconnect', (reason) => {
  console.log('🔌 انقطع الاتصال:', reason);
});

socket.on('reconnect', (attemptNumber) => {
  console.log('🔄 تم إعادة الاتصال بعد', attemptNumber, 'محاولات');
});

socket.on('reconnect_error', (error) => {
  console.error('❌ خطأ في إعادة الاتصال:', error.message);
});

socket.on('reconnect_failed', () => {
  console.error('❌ فشل في إعادة الاتصال بعد جميع المحاولات');
});

// Test event listeners
socket.on('registration-confirmed', (data) => {
  console.log('✅ تأكيد التسجيل:', data);
  
  // Test order creation notification
  console.log('\n📋 اختبار إشعار طلب جديد...');
  socket.emit('order-created', {
    orderId: 'test-order-456',
    tableNumber: '5',
    waiterName: 'نادل اختبار',
    items: [
      { name: 'قهوة عربية', quantity: 2 },
      { name: 'شاي بالنعناع', quantity: 1 }
    ]
  });
});

socket.on('registration-error', (error) => {
  console.error('❌ خطأ في التسجيل:', error);
});

socket.on('new-order-notification', (orderData) => {
  console.log('📋 إشعار طلب جديد:', orderData);
  
  // Test order status update
  console.log('\n🔄 اختبار تحديث حالة الطلب...');
  setTimeout(() => {
    socket.emit('order-status-update', {
      orderId: orderData.orderId,
      newStatus: 'preparing',
      chefName: 'طباخ اختبار',
      tableNumber: orderData.tableNumber
    });
  }, 2000);
});

socket.on('order-status-update', (updateData) => {
  console.log('🔄 تحديث حالة الطلب:', updateData);
});

// Keep the script running
console.log('\n⏳ الاختبار يعمل... اضغط Ctrl+C للإيقاف');

// Cleanup on exit
process.on('SIGINT', () => {
  console.log('\n👋 إغلاق الاتصال...');
  socket.disconnect();
  process.exit(0);
});

// Auto-exit after 30 seconds
setTimeout(() => {
  console.log('\n⏰ انتهى وقت الاختبار');
  socket.disconnect();
  process.exit(0);
}, 30000);
