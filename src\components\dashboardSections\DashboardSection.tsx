import React from 'react';
import { FaUsers, FaBoxOpen, FaShoppingCart } from 'react-icons/fa';
import type { User, Product, Order, InventoryStats } from '../../utils/api';
import Card from '../Card';
import './DashboardSection.css';

interface DashboardSectionProps {
  users: User[];
  products: Product[];
  orders: Order[];
  inventoryStats: InventoryStats | null;
  currencySymbol?: string; // Added for completeness, though totalRevenue is removed
}

const DashboardSection: React.FC<DashboardSectionProps> = ({
  users,
  products,
  orders,
  inventoryStats,
  currencySymbol = '$',
}) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <Card title="إجمالي المستخدمين" icon="FaUsers" className="text-blue-500">
        {users.length.toString()}
      </Card>
      <Card title="إجمالي المنتجات" icon="FaBoxOpen" className="text-green-500">
        {products.length.toString()}
      </Card>
      <Card title="إجمالي الطلبات" icon="FaShoppingCart" className="text-purple-500">
        {orders.length.toString()}
      </Card>
      {inventoryStats && (
        <Card title="إحصائيات المخزون" icon="FaBoxOpen" className="text-yellow-500">
          <div className="dashboard-section-content">
            {inventoryStats.distinctProducts !== undefined && (
              <p>المنتجات المميزة: {inventoryStats.distinctProducts}</p>
            )}
            {inventoryStats.totalStockUnits !== undefined && (
              <p>إجمالي وحدات المخزون: {inventoryStats.totalStockUnits}</p>
            )}
            {inventoryStats.lowStockProducts !== undefined && (
              <p>
                المنتجات منخفضة المخزون:{' '}
                <span className={inventoryStats.lowStockProducts > 0 ? 'text-warning' : ''}>
                  {inventoryStats.lowStockProducts}
                </span>
              </p>
            )}
            {inventoryStats.outOfStockProducts !== undefined && (
              <p>
                المنتجات نفذت من المخزون:{' '}
                <span className={inventoryStats.outOfStockProducts > 0 ? 'text-danger' : ''}>
                  {inventoryStats.outOfStockProducts}
                </span>
              </p>
            )}
          </div>
        </Card>
      )}
      {/* Add more cards for other stats if needed, e.g., low stock items, pending orders etc. */}
    </div>
  );
};

export default DashboardSection;
