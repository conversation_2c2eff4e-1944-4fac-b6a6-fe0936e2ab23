import React, { useState, useEffect } from 'react';
import { authenticatedGet, authenticatedPost, authenticatedPut, authenticatedDelete } from '../../utils/apiHelpers';
import { useToast } from '../../hooks/useToast';
import socket from '../../socket';
import './MenuManager.css';

interface MenuItem {
  _id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  isAvailable: boolean;
  preparationTime: number;
  ingredients: string[];
  image?: string;
  createdAt: string;
  updatedAt: string;
}

interface MenuManagerProps {
  userRole: 'waiter' | 'chef' | 'manager';
}

const MenuManager: React.FC<MenuManagerProps> = ({ userRole }) => {
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedItem, setSelectedItem] = useState<MenuItem | null>(null);
  const [showItemModal, setShowItemModal] = useState(false);
  const [filterCategory, setFilterCategory] = useState<string>('all');
  const [filterAvailability, setFilterAvailability] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');

  const { showSuccess, showError } = useToast();

  // جلب عناصر القائمة
  const fetchMenuItems = async () => {
    try {
      setLoading(true);
      const response = await authenticatedGet('/api/menu-items');
      const items = response.data || [];
      setMenuItems(items);
      
      // استخراج الفئات
      const uniqueCategories = [...new Set(items.map((item: MenuItem) => item.category))];
      setCategories(uniqueCategories.filter(Boolean));
      
    } catch (error) {
      console.error('خطأ في جلب القائمة:', error);
      showError('فشل في تحميل القائمة');
      
      // بيانات تجريبية
      const mockItems = [
        {
          _id: '1',
          name: 'قهوة تركية',
          description: 'قهوة تركية أصيلة محضرة بالطريقة التقليدية',
          price: 15,
          category: 'قهوة ساخنة',
          isAvailable: true,
          preparationTime: 5,
          ingredients: ['قهوة تركية', 'ماء', 'سكر'],
          createdAt: '2024-01-01',
          updatedAt: '2024-01-01'
        },
        {
          _id: '2',
          name: 'كابتشينو',
          description: 'كابتشينو كريمي مع رغوة الحليب الطازجة',
          price: 20,
          category: 'قهوة ساخنة',
          isAvailable: true,
          preparationTime: 3,
          ingredients: ['إسبريسو', 'حليب', 'رغوة حليب'],
          createdAt: '2024-01-01',
          updatedAt: '2024-01-01'
        },
        {
          _id: '3',
          name: 'شاي أحمر',
          description: 'شاي أحمر طازج مع النعناع',
          price: 10,
          category: 'مشروبات ساخنة',
          isAvailable: false,
          preparationTime: 2,
          ingredients: ['شاي أحمر', 'ماء', 'نعناع'],
          createdAt: '2024-01-01',
          updatedAt: '2024-01-01'
        }
      ];
      
      setMenuItems(mockItems);
      setCategories(['قهوة ساخنة', 'مشروبات ساخنة']);
    } finally {
      setLoading(false);
    }
  };

  // حفظ عنصر القائمة
  const saveMenuItem = async (itemData: Partial<MenuItem>) => {
    try {
      let response;
      
      if (selectedItem) {
        response = await authenticatedPut(`/api/menu-items/${selectedItem._id}`, itemData);
      } else {
        response = await authenticatedPost('/api/menu-items', itemData);
      }
      
      if (response.success) {
        showSuccess(selectedItem ? 'تم تحديث العنصر بنجاح' : 'تم إضافة العنصر بنجاح');
        
        if (socket) {
          socket.emit('menu-updated', {
            action: selectedItem ? 'updated' : 'added',
            item: response.data
          });
        }
        
        fetchMenuItems();
        setShowItemModal(false);
        setSelectedItem(null);
      }
    } catch (error) {
      console.error('خطأ في حفظ العنصر:', error);
      showError('فشل في حفظ العنصر');
    }
  };

  // حذف عنصر القائمة
  const deleteMenuItem = async (itemId: string) => {
    if (!window.confirm('هل أنت متأكد من حذف هذا العنصر؟')) {
      return;
    }

    try {
      const response = await authenticatedDelete(`/api/menu-items/${itemId}`);
      
      if (response.success) {
        showSuccess('تم حذف العنصر بنجاح');
        
        if (socket) {
          socket.emit('menu-updated', {
            action: 'deleted',
            itemId
          });
        }
        
        fetchMenuItems();
      }
    } catch (error) {
      console.error('خطأ في حذف العنصر:', error);
      showError('فشل في حذف العنصر');
    }
  };

  // تبديل حالة التوفر
  const toggleAvailability = async (item: MenuItem) => {
    try {
      const response = await authenticatedPut(`/api/menu-items/${item._id}`, {
        isAvailable: !item.isAvailable
      });
      
      if (response.success) {
        showSuccess(`تم ${!item.isAvailable ? 'تفعيل' : 'إلغاء'} العنصر`);
        
        if (socket) {
          socket.emit('menu-updated', {
            action: 'availability_changed',
            item: response.data
          });
        }
        
        fetchMenuItems();
      }
    } catch (error) {
      console.error('خطأ في تغيير حالة التوفر:', error);
      showError('فشل في تغيير حالة التوفر');
    }
  };

  // إعداد Socket.IO
  useEffect(() => {
    if (socket) {
      socket.on('menu-updated', () => {
        fetchMenuItems();
      });
    }

    return () => {
      if (socket) {
        socket.off('menu-updated');
      }
    };
  }, []);

  // تحميل البيانات الأولية
  useEffect(() => {
    fetchMenuItems();
  }, []);

  // فلترة عناصر القائمة
  const getFilteredItems = () => {
    let filtered = menuItems;

    if (filterCategory !== 'all') {
      filtered = filtered.filter(item => item.category === filterCategory);
    }

    if (filterAvailability !== 'all') {
      const isAvailable = filterAvailability === 'available';
      filtered = filtered.filter(item => item.isAvailable === isAvailable);
    }

    if (searchTerm) {
      filtered = filtered.filter(item => 
        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    return filtered;
  };

  if (loading) {
    return (
      <div className="menu-manager loading">
        <div className="loading-spinner">
          <i className="fas fa-spinner fa-spin"></i>
          <span>جاري تحميل القائمة...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="menu-manager">
      {/* Header */}
      <div className="menu-header">
        <div className="header-left">
          <h1>
            <i className="fas fa-coffee"></i>
            {userRole === 'manager' ? 'إدارة القائمة' : 'قائمة المشروبات'}
          </h1>
          <p>
            {userRole === 'manager' 
              ? 'إدارة عناصر القائمة والأسعار والتوفر'
              : 'عرض قائمة المشروبات المتاحة'
            }
          </p>
        </div>
        
        <div className="header-actions">
          {userRole === 'manager' && (
            <button 
              className="btn-new-item"
              onClick={() => {
                setSelectedItem(null);
                setShowItemModal(true);
              }}
            >
              <i className="fas fa-plus"></i>
              إضافة عنصر جديد
            </button>
          )}
        </div>
      </div>

      {/* Stats */}
      <div className="menu-stats">
        <div className="stat-card total">
          <div className="stat-icon">
            <i className="fas fa-coffee"></i>
          </div>
          <div className="stat-content">
            <h3>{menuItems.length}</h3>
            <p>إجمالي العناصر</p>
          </div>
        </div>

        <div className="stat-card available">
          <div className="stat-icon">
            <i className="fas fa-check-circle"></i>
          </div>
          <div className="stat-content">
            <h3>{menuItems.filter(item => item.isAvailable).length}</h3>
            <p>متاح</p>
          </div>
        </div>

        <div className="stat-card unavailable">
          <div className="stat-icon">
            <i className="fas fa-times-circle"></i>
          </div>
          <div className="stat-content">
            <h3>{menuItems.filter(item => !item.isAvailable).length}</h3>
            <p>غير متاح</p>
          </div>
        </div>

        <div className="stat-card categories">
          <div className="stat-icon">
            <i className="fas fa-tags"></i>
          </div>
          <div className="stat-content">
            <h3>{categories.length}</h3>
            <p>الفئات</p>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="menu-filters">
        <div className="search-group">
          <i className="fas fa-search"></i>
          <input
            type="text"
            placeholder="البحث في القائمة..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <div className="filter-group">
          <label>الفئة:</label>
          <select 
            value={filterCategory} 
            onChange={(e) => setFilterCategory(e.target.value)}
          >
            <option value="all">جميع الفئات</option>
            {categories.map(category => (
              <option key={category} value={category}>{category}</option>
            ))}
          </select>
        </div>

        <div className="filter-group">
          <label>التوفر:</label>
          <select 
            value={filterAvailability} 
            onChange={(e) => setFilterAvailability(e.target.value)}
          >
            <option value="all">الكل</option>
            <option value="available">متاح</option>
            <option value="unavailable">غير متاح</option>
          </select>
        </div>

        <button 
          className="refresh-btn"
          onClick={fetchMenuItems}
          title="تحديث البيانات"
        >
          <i className="fas fa-sync-alt"></i>
          تحديث
        </button>
      </div>

      {/* Menu Items */}
      <div className="menu-items">
        {getFilteredItems().length === 0 ? (
          <div className="no-items">
            <i className="fas fa-coffee"></i>
            <p>لا توجد عناصر تطابق الفلاتر المحددة</p>
          </div>
        ) : (
          <div className="items-grid">
            {getFilteredItems().map(item => (
              <div 
                key={item._id} 
                className={`menu-item-card ${!item.isAvailable ? 'unavailable' : ''}`}
              >
                <div className="item-header">
                  <div className="item-name">
                    <h3>{item.name}</h3>
                    <span className="category">{item.category}</span>
                  </div>
                  <div className="item-price">
                    {item.price.toFixed(2)} ج.م
                  </div>
                </div>

                <div className="item-description">
                  <p>{item.description}</p>
                </div>

                <div className="item-details">
                  <div className="detail-item">
                    <i className="fas fa-clock"></i>
                    <span>{item.preparationTime} دقيقة</span>
                  </div>
                  <div className="detail-item">
                    <i className="fas fa-list"></i>
                    <span>{item.ingredients.length} مكون</span>
                  </div>
                  <div className={`availability-status ${item.isAvailable ? 'available' : 'unavailable'}`}>
                    <i className={`fas ${item.isAvailable ? 'fa-check-circle' : 'fa-times-circle'}`}></i>
                    <span>{item.isAvailable ? 'متاح' : 'غير متاح'}</span>
                  </div>
                </div>

                <div className="item-ingredients">
                  <h4>المكونات:</h4>
                  <div className="ingredients-list">
                    {item.ingredients.map((ingredient, index) => (
                      <span key={index} className="ingredient-tag">
                        {ingredient}
                      </span>
                    ))}
                  </div>
                </div>

                {userRole === 'manager' && (
                  <div className="item-actions">
                    <button 
                      className="action-btn edit"
                      onClick={() => {
                        setSelectedItem(item);
                        setShowItemModal(true);
                      }}
                    >
                      <i className="fas fa-edit"></i>
                      تعديل
                    </button>
                    
                    <button 
                      className={`action-btn toggle ${item.isAvailable ? 'disable' : 'enable'}`}
                      onClick={() => toggleAvailability(item)}
                    >
                      <i className={`fas ${item.isAvailable ? 'fa-eye-slash' : 'fa-eye'}`}></i>
                      {item.isAvailable ? 'إلغاء' : 'تفعيل'}
                    </button>
                    
                    <button 
                      className="action-btn delete"
                      onClick={() => deleteMenuItem(item._id)}
                    >
                      <i className="fas fa-trash"></i>
                      حذف
                    </button>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Item Modal */}
      {showItemModal && (
        <div className="modal-overlay" onClick={() => setShowItemModal(false)}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>
                <i className="fas fa-coffee"></i>
                {selectedItem ? 'تعديل العنصر' : 'إضافة عنصر جديد'}
              </h2>
              <button 
                className="modal-close"
                onClick={() => setShowItemModal(false)}
              >
                <i className="fas fa-times"></i>
              </button>
            </div>
            
            <div className="modal-body">
              <form onSubmit={(e) => {
                e.preventDefault();
                const formData = new FormData(e.target as HTMLFormElement);
                const ingredients = (formData.get('ingredients') as string)
                  .split(',')
                  .map(ing => ing.trim())
                  .filter(Boolean);
                
                const itemData = {
                  name: formData.get('name') as string,
                  description: formData.get('description') as string,
                  price: parseFloat(formData.get('price') as string),
                  category: formData.get('category') as string,
                  preparationTime: parseInt(formData.get('preparationTime') as string),
                  ingredients,
                  isAvailable: formData.get('isAvailable') === 'on'
                };
                
                saveMenuItem(itemData);
              }}>
                <div className="form-row">
                  <div className="form-group">
                    <label htmlFor="name">اسم العنصر:</label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      defaultValue={selectedItem?.name || ''}
                      required
                      placeholder="أدخل اسم العنصر"
                    />
                  </div>
                  
                  <div className="form-group">
                    <label htmlFor="category">الفئة:</label>
                    <input
                      type="text"
                      id="category"
                      name="category"
                      defaultValue={selectedItem?.category || ''}
                      required
                      placeholder="أدخل فئة العنصر"
                    />
                  </div>
                </div>

                <div className="form-group">
                  <label htmlFor="description">الوصف:</label>
                  <textarea
                    id="description"
                    name="description"
                    defaultValue={selectedItem?.description || ''}
                    required
                    placeholder="أدخل وصف العنصر"
                    rows={3}
                  />
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label htmlFor="price">السعر (ج.م):</label>
                    <input
                      type="number"
                      id="price"
                      name="price"
                      step="0.01"
                      min="0"
                      defaultValue={selectedItem?.price || ''}
                      required
                      placeholder="0.00"
                    />
                  </div>
                  
                  <div className="form-group">
                    <label htmlFor="preparationTime">وقت التحضير (دقيقة):</label>
                    <input
                      type="number"
                      id="preparationTime"
                      name="preparationTime"
                      min="1"
                      defaultValue={selectedItem?.preparationTime || ''}
                      required
                      placeholder="5"
                    />
                  </div>
                </div>

                <div className="form-group">
                  <label htmlFor="ingredients">المكونات (مفصولة بفاصلة):</label>
                  <input
                    type="text"
                    id="ingredients"
                    name="ingredients"
                    defaultValue={selectedItem?.ingredients.join(', ') || ''}
                    required
                    placeholder="قهوة, حليب, سكر"
                  />
                </div>

                <div className="form-group checkbox-group">
                  <label className="checkbox-label">
                    <input
                      type="checkbox"
                      name="isAvailable"
                      defaultChecked={selectedItem?.isAvailable ?? true}
                    />
                    <span className="checkmark"></span>
                    متاح للطلب
                  </label>
                </div>
                
                <div className="modal-footer">
                  <button type="submit" className="btn-confirm">
                    <i className="fas fa-save"></i>
                    {selectedItem ? 'تحديث' : 'إضافة'}
                  </button>
                  <button 
                    type="button" 
                    className="btn-cancel"
                    onClick={() => setShowItemModal(false)}
                  >
                    إلغاء
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MenuManager;
