import React, { useState, useEffect, useMemo } from 'react';
import { FaEdit, FaTrash, FaPlus } from 'react-icons/fa';
import { APP_CONFIG } from '../../config/app.config';
import Button from '../Button';
import Modal from '../Modal';
import Alert from '../Alert';
import type { Category, ApiResponse } from '../../utils/api'; // Keep ApiResponse if used
import * as apiUtil from '../../utils/api'; // For API calls

interface CategoriesSectionProps {
  categories: Category[]; // All categories
  setCategories: React.Dispatch<React.SetStateAction<Category[]>>; // To update list after CRUD
  api: typeof apiUtil; // API utility
  addToast: (message: string, type: 'success' | 'error' | 'info') => void;
  handleOpenConfirmDeleteModal: (item: Category, type: 'Category') => void; // For delete confirmation
  APP_CONFIG: typeof APP_CONFIG; // If needed for any config values
  initialCategoryFormData: Partial<Category>;
  searchTerm: string;
  loading: boolean; // For loading state message
  // Removed: filteredCategories (derive internally or use `categories` and `searchTerm`)
  // Removed: onOpenCategoryModal (will be internal)
  // Removed: onDeleteCategory (use handleOpenConfirmDeleteModal)
}

// initialCategoryFormData is already defined in ModernManagerDashboard.tsx and passed as a prop.
// const initialCategoryFormData: Partial<Category> = {
// name: '',
// description: '',
// };

const CategoriesSection: React.FC<CategoriesSectionProps> = ({
  categories, // Use this for display, filter with searchTerm internally
  setCategories,
  api,
  addToast,
  handleOpenConfirmDeleteModal,
  APP_CONFIG,
  initialCategoryFormData,
  searchTerm,
  loading,
}) => {
  const [isCategoryModalOpen, setIsCategoryModalOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);
  const [categoryFormData, setCategoryFormData] = useState<Partial<Category>>(initialCategoryFormData);
  const [categoryModalError, setCategoryModalError] = useState<string | null>(null);

  // Memoized filtered categories for rendering
  const filteredCategories = useMemo(() =>
    categories.filter(category =>
      category.name.toLowerCase().includes(searchTerm.toLowerCase())
    ), [categories, searchTerm]);

  const handleOpenCategoryModal = (category?: Category) => {
    setIsCategoryModalOpen(true);
    setSelectedCategory(category || null);
    setCategoryFormData(category ? { ...category } : initialCategoryFormData);
    setCategoryModalError(null);
  };

  const closeCategoryModal = () => {
    setIsCategoryModalOpen(false);
    setSelectedCategory(null);
    setCategoryFormData(initialCategoryFormData);
    setCategoryModalError(null);
  };

  const handleCategoryFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setCategoryFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleCategoryFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setCategoryModalError(null);

    if (!categoryFormData.name) {
      setCategoryModalError("يرجى إدخال اسم الفئة.");
      addToast("يرجى إدخال اسم الفئة.", 'error');
      return;
    }

    try {
      let response: ApiResponse<Category>;
      if (selectedCategory && selectedCategory.id) {
        response = await api.updateCategory(selectedCategory.id, categoryFormData as Category);
      } else {
        response = await api.createCategory(categoryFormData as Omit<Category, 'id'>);
      }

      if (response.success) {
        const categoriesResponse = await api.getCategories();
        if (categoriesResponse.success && categoriesResponse.data) setCategories(categoriesResponse.data);
        closeCategoryModal();
        addToast(selectedCategory ? 'تم تحديث الفئة بنجاح!' : 'تم إنشاء الفئة بنجاح!', 'success');
      } else {
        setCategoryModalError(response.error || 'فشل في معالجة الطلب.');
        addToast(response.error || 'فشل في معالجة الطلب.', 'error');
      }
    } catch (errCatch: any) {
      setCategoryModalError('حدث خطأ غير متوقع.');
      addToast('حدث خطأ غير متوقع.', 'error');
      console.error(errCatch);
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-semibold">إدارة الفئات</h2>
        <Button onClick={() => handleOpenCategoryModal()} icon="fa-plus" variant="primary"> {/* MODIFIED */}
          إضافة فئة
        </Button>
      </div>

      {loading && filteredCategories.length === 0 && <p>جارٍ تحميل الفئات...</p>} {/* Show loading only if no categories yet */}
      {!loading && filteredCategories.length === 0 && (
        <p className="text-center text-gray-500 my-4">
          {searchTerm ? `لا توجد نتائج بحث تطابق "${searchTerm}"` : 'لا توجد فئات لعرضها حاليًا.'} {/* MODIFIED */}
        </p>
      )}

      {!loading && filteredCategories.length > 0 && (
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white shadow-md rounded-lg">
            <thead className="bg-gray-200 text-gray-600 uppercase text-sm leading-normal">
              <tr>
                <th className="py-3 px-6 text-left">الاسم</th>
                <th className="py-3 px-6 text-left">الوصف</th>
                <th className="py-3 px-6 text-center">الإجراءات</th>
              </tr>
            </thead>
            <tbody className="text-gray-600 text-sm font-light">
              {filteredCategories.map(category => (
                <tr key={category.id} className="border-b border-gray-200 hover:bg-gray-100">
                  <td className="py-3 px-6 text-left whitespace-nowrap">
                    <div className="flex items-center">
                      <span className="font-medium">{category.name}</span>
                    </div>
                  </td>
                  <td className="py-3 px-6 text-left">
                    {category.description || '-'}
                  </td>
                  <td className="py-3 px-6 text-center">
                    <div className="flex item-center justify-center">
                      <Button
                        onClick={() => handleOpenCategoryModal(category)} 
                        className="w-8 h-8 p-0 flex items-center justify-center mr-2 text-blue-500 hover:text-blue-700" // Added styling classes
                        aria-label={`تعديل ${category.name}`}
                      >
                        <FaEdit /> {/* Removed text-blue-500 from here, applied to button */}
                      </Button>
                      <Button
                        onClick={() => handleOpenConfirmDeleteModal(category, 'Category')} 
                        className="w-8 h-8 p-0 flex items-center justify-center text-red-500 hover:text-red-700" // Added styling classes
                        aria-label={`حذف ${category.name}`}
                      >
                        <FaTrash /> {/* Removed text-red-500 from here, applied to button */}
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
      
      {/* Modal for adding/editing categories */}
      <Modal isOpen={isCategoryModalOpen} onClose={closeCategoryModal} title={selectedCategory ? "تعديل الفئة" : "إضافة فئة جديدة"}>
        <form onSubmit={handleCategoryFormSubmit}>
          {categoryModalError && <Alert type="error" onClose={() => setCategoryModalError(null)}>{categoryModalError}</Alert>}
          <div className="mb-4">
            <label htmlFor="categoryName" className="block text-sm font-medium text-gray-700">اسم الفئة</label>
            <input
              type="text"
              name="name"
              id="categoryName"
              value={categoryFormData.name || ''}
              onChange={handleCategoryFormChange}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              required
            />
          </div>
          <div className="mb-4">
            <label htmlFor="categoryDescription" className="block text-sm font-medium text-gray-700">وصف الفئة (اختياري)</label>
            <textarea
              name="description"
              id="categoryDescription"
              value={categoryFormData.description || ''}
              onChange={handleCategoryFormChange}
              rows={3}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            />
          </div>
          <div className="flex justify-end space-x-2 rtl:space-x-reverse">
            <Button type="button" variant="secondary" onClick={closeCategoryModal}>إلغاء</Button>
            <Button type="submit" variant="primary">
              {selectedCategory ? 'حفظ التعديلات' : 'إنشاء فئة'}
            </Button>
          </div>
        </form>
      </Modal>
    </div>
  );
};

export default CategoriesSection;
