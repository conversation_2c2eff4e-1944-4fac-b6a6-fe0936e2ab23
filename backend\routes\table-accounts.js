// Backend route for tables management
// مسارات إدارة الطاولات
const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const Table = require('../models/Table');
const Order = require('../models/Order');
const User = require('../models/User');

/**
 * @route   GET /api/table-accounts
 * @desc    Get all table accounts with real-time data
 * @access  Private
 */
router.get('/', authenticateToken, async (req, res) => {
  try {
    // جلب جميع الطاولات مع معلومات الطلبات النشطة
    const tables = await Table.find({ isActive: true })
      .populate('assignedWaiter', 'name username')
      .populate({
        path: 'currentOrder',
        populate: {
          path: 'items.product',
          select: 'name price'
        }
      })
      .sort({ section: 1, number: 1 });

    // تحويل البيانات لتتناسب مع واجهة المستخدم الحالية
    const tableAccounts = [];

    for (const table of tables) {
      let totalAmount = 0;
      let orders = [];
      let customer = null;
      let openTime = null;
      let closeTime = null;

      // إذا كانت الطاولة مشغولة، جلب تفاصيل الطلب
      if (table.status === 'occupied' && table.currentOrder) {
        const order = table.currentOrder;
        totalAmount = order.total || 0;
        openTime = order.timing.orderTime;
        
        // تفاصيل العميل
        customer = {
          name: order.customer.name,
          phone: order.customer.phone || 'غير محدد'
        };

        // تفاصيل الطلبات
        orders = order.items.map(item => ({
          id: item._id,
          productName: item.productName,
          quantity: item.quantity,
          price: item.price,
          subtotal: item.subtotal
        }));
      }

      // إنشاء كائن حساب الطاولة
      const tableAccount = {
        id: `table-${table.number}`,
        tableNumber: table.number,
        section: table.section,
        status: table.status === 'occupied' ? 'active' : 'closed',
        isOpen: table.status === 'occupied',
        customer: customer || {
          name: 'غير محدد',
          phone: 'غير محدد'
        },
        customerName: customer?.name || 'غير محدد',
        orders: orders,
        totalAmount: totalAmount,
        openTime: openTime,
        closeTime: table.status !== 'occupied' ? new Date() : closeTime,
        waiter: {
          id: table.assignedWaiter?._id || null,
          name: table.assignedWaiter?.name || 'غير محدد',
          username: table.assignedWaiter?.username || 'غير محدد'
        },
        waiterName: table.assignedWaiter?.username || 'غير محدد',
        capacity: table.capacity,
        features: table.features,
        // إضافة معلومات إضافية
        occupationTime: table.occupationTime,
        isAvailable: table.isAvailable,
        displayName: table.displayName,
        createdAt: openTime
      };

      tableAccounts.push(tableAccount);
    }

    res.json(tableAccounts);
  } catch (error) {
    console.error('خطأ في جلب حسابات الطاولات:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم أثناء جلب حسابات الطاولات',
      error: error.message
    });
  }
});

/**
 * @route   GET /api/table-accounts/long-running
 * @desc    Get tables that have been occupied for more than 1 hour
 * @access  Private
 */
router.get('/long-running', authenticateToken, async (req, res) => {
  try {
    // جلب الطاولات المشغولة لأكثر من ساعة
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    
    const longRunningTables = await Table.find({
      status: 'occupied',
      'stats.lastUsed': { $lte: oneHourAgo },
      isActive: true
    })
    .populate('assignedWaiter', 'name username')
    .populate({
      path: 'currentOrder',
      populate: {
        path: 'items.product',
        select: 'name price'
      }
    });

    // تحويل البيانات
    const formattedTables = longRunningTables.map(table => {
      const order = table.currentOrder;
      const occupationMinutes = Math.round((Date.now() - table.stats.lastUsed.getTime()) / (1000 * 60));

      return {
        id: `table-${table.number}`,
        tableNumber: table.number,
        section: table.section,
        status: 'active',
        customer: {
          name: order?.customer.name || 'غير محدد',
          phone: order?.customer.phone || 'غير محدد'
        },
        orders: order?.items || [],
        totalAmount: order?.total || 0,
        openTime: table.stats.lastUsed,
        closeTime: null,
        waiter: {
          id: table.assignedWaiter?._id || null,
          name: table.assignedWaiter?.name || 'غير محدد',
          username: table.assignedWaiter?.username || 'غير محدد' // إضافة اسم المستخدم هنا
        },
        duration: occupationMinutes,
        waiterName: table.assignedWaiter?.name || 'غير محدد'
      };
    });

    res.json(formattedTables);
  } catch (error) {
    console.error('خطأ في جلب الطاولات طويلة المدى:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم أثناء جلب الطاولات طويلة المدى',
      error: error.message
    });
  }
});

/**
 * @route   POST /api/table-accounts
 * @desc    Create a new table account (occupy a table)
 * @access  Private
 */
router.post('/', authenticateToken, async (req, res) => {
  try {
    const { tableNumber, section, customer } = req.body;

    // البحث عن الطاولة
    const table = await Table.findOne({ 
      number: tableNumber, 
      section: section,
      isActive: true 
    });

    if (!table) {
      return res.status(404).json({
        success: false,
        message: 'الطاولة غير موجودة'
      });
    }

    if (table.status !== 'available') {
      return res.status(400).json({
        success: false,
        message: 'الطاولة غير متاحة حالياً'
      });
    }

    // إنشاء طلب جديد للطاولة
    const newOrder = new Order({
      customer: {
        name: customer.name,
        phone: customer.phone
      },
      orderType: 'dine-in',
      table: {
        number: tableNumber,
        section: section
      },
      items: [],
      total: 0,
      status: 'pending'
    });

    await newOrder.save();

    // ربط الطاولة بالطلب
    await table.occupy(newOrder._id, req.user.userId);

    // تحضير الرد
    const tableAccount = {
      id: `table-${table.number}`,
      tableNumber: table.number,
      section: table.section,
      status: 'active',
      customer: customer,
      orders: [],
      totalAmount: 0,
      openTime: new Date(),
      closeTime: null,
      waiter: {
        id: req.user.userId,
        name: req.user.name || 'النادل'
      }
    };    res.status(201).json({
      success: true,
      message: 'تم إنشاء حساب الطاولة بنجاح',
      data: tableAccount
    });

    // Send Socket notifications for table opening
    if (global.socketHandlers) {
      try {
        const waiterUser = await User.findById(req.user.userId);
        const waiterDisplayName = waiterUser ? (waiterUser.name || waiterUser.username) : 'نادل';

        // Emit table status change event
        global.socketHandlers.io.emit('table-status-change', {
          tableNumber: table.number,
          isOpen: true,
          waiterName: waiterDisplayName,
          action: 'opened',
          customer: customer,
          timestamp: new Date().toISOString()
        });

        console.log(`📡 تم إرسال إشعار فتح الطاولة ${table.number} بواسطة ${waiterDisplayName}`);
      } catch (socketError) {
        console.error('خطأ في إرسال إشعار Socket:', socketError);
      }
    }
  } catch (error) {
    console.error('خطأ في إنشاء حساب الطاولة:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم أثناء إنشاء حساب الطاولة',
      error: error.message
    });
  }
});

/**
 * @route   PUT /api/table-accounts/:tableId/close
 * @desc    Close a table account
 * @access  Private
 */
router.put('/:tableId/close', authenticateToken, async (req, res) => {
  try {
    const tableId = req.params.tableId;
    const tableNumber = parseInt(tableId.replace('table-', ''));

    // البحث عن الطاولة
    const table = await Table.findOne({ number: tableNumber })
      .populate('currentOrder');

    if (!table) {
      return res.status(404).json({
        success: false,
        message: 'الطاولة غير موجودة'
      });
    }

    // تحديث حالة الطلب إلى مكتمل
    if (table.currentOrder) {
      const order = table.currentOrder;
      order.status = 'completed';
      order.timing.completedAt = new Date();
      await order.save();

      // تحديث إحصائيات الطاولة
      await table.updateStats(order.total || 0);
    }

    // تحرير الطاولة
    await table.release();    res.json({
      success: true,
      message: 'تم إغلاق حساب الطاولة بنجاح'
    });

    // Send Socket notifications for table closing
    if (global.socketHandlers) {
      try {
        const waiterUser = await User.findById(table.assignedWaiter);
        const waiterDisplayName = waiterUser ? (waiterUser.name || waiterUser.username) : 'نادل';

        // Emit table status change event
        global.socketHandlers.io.emit('table-status-change', {
          tableNumber: table.number,
          isOpen: false,
          waiterName: waiterDisplayName,
          action: 'closed',
          totalAmount: table.currentOrder?.total || 0,
          timestamp: new Date().toISOString()
        });

        console.log(`📡 تم إرسال إشعار إغلاق الطاولة ${table.number} بواسطة ${waiterDisplayName}`);
      } catch (socketError) {
        console.error('خطأ في إرسال إشعار Socket:', socketError);
      }
    }
  } catch (error) {
    console.error('خطأ في إغلاق حساب الطاولة:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم أثناء إغلاق حساب الطاولة',
      error: error.message
    });
  }
});

/**
 * @route   GET /api/table-accounts/stats
 * @desc    Get tables statistics
 * @access  Private
 */
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    const stats = {
      total: await Table.countDocuments({ isActive: true }),
      available: await Table.countDocuments({ status: 'available', isActive: true }),
      occupied: await Table.countDocuments({ status: 'occupied', isActive: true }),
      reserved: await Table.countDocuments({ status: 'reserved', isActive: true }),
      cleaning: await Table.countDocuments({ status: 'cleaning', isActive: true }),
      maintenance: await Table.countDocuments({ status: 'maintenance', isActive: true }),
      sections: {}
    };

    // إحصائيات حسب القسم
    const sections = ['أ', 'ب', 'ج', 'VIP'];
    for (const section of sections) {
      stats.sections[section] = {
        total: await Table.countDocuments({ section, isActive: true }),
        available: await Table.countDocuments({ section, status: 'available', isActive: true }),
        occupied: await Table.countDocuments({ section, status: 'occupied', isActive: true })
      };
    }

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('خطأ في جلب إحصائيات الطاولات:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم أثناء جلب الإحصائيات',
      error: error.message
    });
  }
});

/**
 * @route   GET /api/table-accounts/check
 * @desc    Check if table account exists for specific table and waiter
 * @access  Private
 */
router.get('/check', authenticateToken, async (req, res) => {
  try {
    const { tableNumber, waiterName } = req.query;

    if (!tableNumber) {
      return res.status(400).json({
        success: false,
        message: 'رقم الطاولة مطلوب'
      });
    }

    // البحث عن الطاولة
    const table = await Table.findOne({ 
      number: parseInt(tableNumber),
      status: 'occupied',
      isActive: true 
    })
    .populate('assignedWaiter', 'name username')
    .populate({
      path: 'currentOrder',
      populate: {
        path: 'items.product',
        select: 'name price'
      }
    });

    if (!table || !table.currentOrder) {
      return res.json({
        success: true,
        exists: false,
        account: null
      });
    }

    // تحضير معلومات الحساب
    const order = table.currentOrder;
    const tableAccount = {
      _id: table._id,
      tableNumber: table.number,
      section: table.section,
      status: 'active',
      customer: {
        name: order.customer.name,
        phone: order.customer.phone || 'غير محدد'
      },
      orders: order.items.map(item => ({
        productName: item.productName,
        quantity: item.quantity,
        price: item.price,
        subtotal: item.subtotal
      })),
      totalAmount: order.total || 0,
      openTime: order.timing.orderTime,
      closeTime: null,
      waiter: {
        id: table.assignedWaiter?._id || null,
        name: table.assignedWaiter?.name || 'غير محدد',
        username: table.assignedWaiter?.username || 'غير محدد' // إضافة اسم المستخدم هنا
      },
      waiterName: table.assignedWaiter?.name || 'غير محدد',
      createdAt: order.timing.orderTime
    };

    res.json({
      success: true,
      exists: true,
      account: tableAccount
    });
  } catch (error) {
    console.error('خطأ في التحقق من حساب الطاولة:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم أثناء التحقق من حساب الطاولة',
      error: error.message
    });
  }
});

module.exports = router;
