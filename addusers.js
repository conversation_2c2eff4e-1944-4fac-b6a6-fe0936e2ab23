// add-users.js
const mongoose = require('mongoose');
const bcrypt = require('bcrypt');
const saltRounds = 10;

// استخدم نفس URI الذي تستخدمه في Railway
const MONGODB_URI = 'mongodb+srv://besomustafa:<EMAIL>/deshacoffee?retryWrites=true&w=majority&appName=MyCoffeChop';

// تعريف نموذج المستخدم (مشابه لما هو موجود في User.js)
const userSchema = new mongoose.Schema({
  username: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  role: { type: String, required: true, enum: ['manager', 'waiter', 'chef'] },
  active: { type: Boolean, default: true }
});

const User = mongoose.model('User', userSchema);

// قائمة المستخدمين المراد إضافتهم
const usersToAdd = [
  { username: 'Be<PERSON>', password: 'MOHAMEDmostafa123', role: 'manager' },
  { username: 'azz', password: '253040', role: 'waiter' },
  { username: 'Bosy', password: '253040', role: 'waiter' },
  { username: 'khaled', password: '253040', role: 'chef' }
];

// وظيفة لإضافة المستخدمين
async function addUsers() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('🔌 متصل بقاعدة البيانات بنجاح');

    for (const userData of usersToAdd) {
      const existingUser = await User.findOne({ username: userData.username });
      
      // تشفير كلمة المرور
      const hashedPassword = await bcrypt.hash(userData.password, saltRounds);
      
      if (existingUser) {
        // تحديث المستخدم الموجود
        existingUser.password = hashedPassword;
        existingUser.role = userData.role;
        existingUser.active = true;
        await existingUser.save();
        console.log(`✅ تم تحديث المستخدم: ${userData.username}`);
      } else {
        // إنشاء مستخدم جديد
        const newUser = new User({
          username: userData.username,
          password: hashedPassword,
          role: userData.role,
          active: true
        });
        await newUser.save();
        console.log(`✅ تم إضافة المستخدم الجديد: ${userData.username}`);
      }
    }

    console.log('🎉 تمت إضافة/تحديث جميع المستخدمين بنجاح');
  } catch (error) {
    console.error('❌ خطأ:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تنفيذ الوظيفة
addUsers();