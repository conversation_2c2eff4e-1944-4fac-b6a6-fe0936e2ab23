/* لوحة الطباخ - تصميم حديث ومتجاوب */

.chef-dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  direction: rtl;
  font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Header */
.chef-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 1rem 2rem;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.sidebar-toggle {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  color: #333;
}

.sidebar-toggle:hover {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.chef-header h1 {
  margin: 0;
  color: #333;
  font-size: 1.8rem;
  font-weight: 700;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.chef-name {
  color: #666;
  font-weight: 500;
}

.logout-btn {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.logout-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(238, 90, 82, 0.4);
}

/* Dashboard Content */
.dashboard-content {
  display: flex;
  min-height: calc(100vh - 80px);
}

/* Sidebar */
.chef-sidebar {
  width: 280px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-left: 1px solid rgba(255, 255, 255, 0.2);
  padding: 2rem 1rem;
  transition: all 0.3s ease;
  overflow-y: auto;
}

.chef-sidebar.open {
  transform: translateX(0);
}

.sidebar-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chef-profile {
  text-align: center;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 15px;
  color: white;
}

.chef-avatar {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.chef-profile h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.chef-profile p {
  margin: 0;
  opacity: 0.9;
  font-size: 0.9rem;
}

/* Filter Navigation */
.filter-nav {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.7);
  border: 2px solid transparent;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: right;
  font-size: 1rem;
  color: #333;
}

.filter-btn:hover {
  background: rgba(102, 126, 234, 0.1);
  border-color: rgba(102, 126, 234, 0.3);
  transform: translateX(-5px);
}

.filter-btn.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-color: #667eea;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.filter-btn .filter-icon {
  font-size: 1.2rem;
  margin-left: 0.5rem;
}

.filter-btn .filter-text {
  flex: 1;
  font-weight: 500;
}

.filter-count {
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  padding: 0.25rem 0.5rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  min-width: 24px;
  text-align: center;
}

.filter-btn.active .filter-count {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

/* Main Content */
.chef-main {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
}

.orders-section {
  max-width: 1200px;
  margin: 0 auto;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.section-header h2 {
  margin: 0;
  color: #333;
  font-size: 1.5rem;
  font-weight: 600;
}

.orders-count {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
}

/* Loading & No Orders */
.loading, .no-orders {
  text-align: center;
  padding: 3rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  color: #666;
}

.no-orders-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

/* Orders Grid */
.orders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

/* Order Card */
.order-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.order-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.order-card.pending {
  border-color: #ff9500;
  box-shadow: 0 4px 20px rgba(255, 149, 0, 0.2);
}

.order-card.preparing {
  border-color: #007bff;
  box-shadow: 0 4px 20px rgba(0, 123, 255, 0.2);
}

.order-card.ready {
  border-color: #28a745;
  box-shadow: 0 4px 20px rgba(40, 167, 69, 0.2);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.order-number {
  font-size: 1.1rem;
  font-weight: 700;
  color: #333;
}

.order-status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.order-status.pending {
  background: #fff3cd;
  color: #856404;
}

.order-status.preparing {
  background: #cce5ff;
  color: #004085;
}

.order-status.ready {
  background: #d4edda;
  color: #155724;
}

.order-status.completed {
  background: #e2e3e5;
  color: #383d41;
}

.order-info {
  margin-bottom: 1rem;
}

.order-info > div {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
  color: #666;
  font-size: 0.9rem;
}

.order-info .icon {
  margin-left: 0.5rem;
  font-size: 1rem;
}

.order-items {
  margin-bottom: 1rem;
  padding: 1rem;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 10px;
}

.items-count {
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.items-preview {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.item-name {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  padding: 0.25rem 0.5rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
}

.more-items {
  background: rgba(0, 0, 0, 0.1);
  color: #666;
  padding: 0.25rem 0.5rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-style: italic;
}

.order-time {
  display: flex;
  align-items: center;
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

.order-time .icon {
  margin-left: 0.5rem;
}

/* Order Actions */
.order-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.order-actions button {
  flex: 1;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  min-width: 100px;
}

.details-btn {
  background: linear-gradient(135deg, #6c757d, #5a6268);
  color: white;
}

.details-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(108, 117, 125, 0.4);
}

.accept-btn {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
}

.accept-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
}

.complete-btn {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
}

.complete-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.4);
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-content {
  background: white;
  border-radius: 15px;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-radius: 15px 15px 0 0;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.modal-body {
  padding: 1.5rem;
}

.order-details {
  margin-bottom: 1.5rem;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.detail-row .label {
  font-weight: 600;
  color: #333;
}

.detail-row .value {
  color: #666;
}

.detail-row .value.total {
  font-weight: 700;
  color: #28a745;
  font-size: 1.1rem;
}

.order-items-details h4 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.1rem;
}

.items-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.item-detail {
  padding: 1rem;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 10px;
}

.item-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.item-name {
  font-weight: 600;
  color: #333;
}

.item-quantity {
  color: #667eea;
  font-weight: 500;
}

.item-price {
  color: #28a745;
  font-weight: 600;
  text-align: left;
}

.item-notes {
  color: #666;
  font-size: 0.9rem;
  font-style: italic;
  margin-top: 0.5rem;
  padding-top: 0.5rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.order-notes {
  margin-top: 1.5rem;
  padding: 1rem;
  background: rgba(255, 193, 7, 0.1);
  border-radius: 10px;
  border-right: 4px solid #ffc107;
}

.order-notes h4 {
  margin: 0 0 0.5rem 0;
  color: #333;
}

.order-notes p {
  margin: 0;
  color: #666;
  line-height: 1.5;
}

.modal-actions {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.02);
  border-radius: 0 0 15px 15px;
}

.modal-actions button {
  flex: 1;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.close-modal-btn {
  background: #6c757d;
  color: white;
}

.close-modal-btn:hover {
  background: #5a6268;
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .chef-sidebar {
    position: fixed;
    top: 80px;
    right: -280px;
    height: calc(100vh - 80px);
    z-index: 200;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
  }

  .chef-sidebar.open {
    right: 0;
  }

  .chef-main {
    padding: 1rem;
  }

  .orders-grid {
    grid-template-columns: 1fr;
  }

  .header-content {
    padding: 0 1rem;
  }

  .chef-header {
    padding: 1rem;
  }

  .modal-content {
    margin: 1rem;
    max-height: calc(100vh - 2rem);
  }

  .order-actions {
    flex-direction: column;
  }

  .modal-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .chef-header h1 {
    font-size: 1.4rem;
  }

  .chef-name {
    display: none;
  }

  .section-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .order-card {
    padding: 1rem;
  }
}