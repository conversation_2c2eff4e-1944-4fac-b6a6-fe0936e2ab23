import { useState, useEffect } from 'react';
import socket from '../socket';
import { getApiUrl } from '../config/app.config';
import type { Order } from '../types/Order';
import { getOrderTotal } from '../types/Order';

// تم نقل interface Order إلى src/types/Order.ts

export default function EnhancedReports() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  // جلب البيانات
  const fetchOrders = async () => {
    setLoading(true);
    try {
      const response = await fetch(getApiUrl('/api/orders'));
      if (response.ok) {
        const data = await response.json();
        setOrders(data);
        setLastUpdate(new Date());
      }
    } catch (error) {
      console.error('Error fetching orders:', error);
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchOrders();

    // إعداد Socket.IO للتحديثات الفورية
    socket.emit('register', { role: 'مدير', name: localStorage.getItem('username') || 'مدير' });

    const handleUpdate = () => {
      fetchOrders();
    };

    socket.on('orderUpdate', handleUpdate);
    socket.on('newOrder', handleUpdate);
    // استقبال أحداث Socket.IO المحدثة
    socket.on('order-status-update', handleUpdate);
    socket.on('new-order-notification', handleUpdate);

    // تحديث كل 5 دقائق بدلاً من 30 ثانية (تحسين الأداء)
    const interval = setInterval(fetchOrders, 300000);

    return () => {
      socket.off('orderUpdate', handleUpdate);
      socket.off('newOrder', handleUpdate);
      socket.off('order-status-update', handleUpdate);
      socket.off('new-order-notification', handleUpdate);
      clearInterval(interval);
    };
  }, []);

  // حساب الإحصائيات المحسنة (دمج جاهز ومكتمل)
  const completedOrders = orders.filter(o => o.status === 'delivered' || o.status === 'ready');
  const pendingOrders = orders.filter(o => o.status !== 'delivered' && o.status !== 'ready' && o.status !== 'cancelled');

  const stats = {
    totalOrders: orders.length,
    completedOrders: completedOrders.length,
    pendingOrders: pendingOrders.length,
    cancelledOrders: orders.filter(o => o.status === 'cancelled').length,

    // المبيعات الصحيحة (فقط الطلبات المُسلمة فعلياً)
    totalSales: orders.filter(o => o.status === 'delivered').reduce((sum, order) => sum + getOrderTotal(order), 0),
    pendingSales: pendingOrders.reduce((sum, order) => sum + getOrderTotal(order), 0),
    averageOrderValue: orders.filter(o => o.status === 'delivered').length > 0 ? orders.filter(o => o.status === 'delivered').reduce((sum, order) => sum + getOrderTotal(order), 0) / orders.filter(o => o.status === 'delivered').length : 0,

    // إحصائيات اليوم
    todayOrders: orders.filter(o => {
      const today = new Date();
      const orderDate = o.createdAt ? new Date(o.createdAt) : null; // Added check for o.createdAt
      return orderDate && orderDate.toDateString() === today.toDateString();
    }),

    // إحصائيات الأسبوع
    weekOrders: orders.filter(o => {
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);
      const orderDate = o.createdAt ? new Date(o.createdAt) : null; // Added check for o.createdAt
      return orderDate && orderDate >= weekAgo;
    })
  };

  // إحصائيات النادلين المحسنة (دمج جاهز ومكتمل)
  const waiterStats = orders.reduce((acc, order) => {
    const waiter = 'waiterName' in order && order.waiterName ? order.waiterName : 'غير محدد'; // Type guard for waiterName
    if (!acc[waiter]) {
      acc[waiter] = {
        totalOrders: 0,
        completedOrders: 0,
        pendingOrders: 0,
        totalSales: 0,
        completedSales: 0,
        averageOrderValue: 0
      };
    }

    acc[waiter].totalOrders++;

    if (order.status === 'delivered' || order.status === 'ready') {
      acc[waiter].completedOrders++;
      // المبيعات فقط للطلبات المُسلمة فعلياً
      if (order.status === 'delivered') {
        const orderTotal = getOrderTotal(order);
        acc[waiter].completedSales += orderTotal;
        acc[waiter].totalSales += orderTotal;
      }
    } else if (order.status !== 'cancelled') {
      acc[waiter].pendingOrders++;
    }

    // حساب متوسط قيمة الطلب (للطلبات المُسلمة فقط)
    const deliveredCount = orders.filter(o => ('waiterName' in o && o.waiterName === waiter) && o.status === 'delivered').length; // Type guard for waiterName
    if (deliveredCount > 0) {
      acc[waiter].averageOrderValue = acc[waiter].completedSales / deliveredCount;
    }

    return acc;
  }, {} as Record<string, any>);

  return (
    <div style={{ padding: '2rem', direction: 'rtl', minHeight: '100vh', background: '#f5f5f5' }}>
      {/* Header */}
      <div style={{
        background: 'linear-gradient(135deg, #2196f3 0%, #1976d2 100%)',
        color: 'white',
        padding: '2rem',
        borderRadius: '16px',
        marginBottom: '2rem',
        boxShadow: '0 8px 32px rgba(0,0,0,0.1)'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <h1 style={{ margin: 0, fontSize: '2rem', fontWeight: '700' }}>
              <i className="fas fa-chart-bar" style={{ marginLeft: '0.5rem' }}></i>
              التقارير والإحصائيات المحسنة
            </h1>
            <p style={{ margin: '0.5rem 0 0 0', opacity: 0.9 }}>
              تحليلات شاملة ومفصلة للمبيعات والأداء مع تحديث فوري
            </p>
            <p style={{ margin: '0.25rem 0 0 0', opacity: 0.7, fontSize: '0.9rem' }}>
              آخر تحديث: {lastUpdate.toLocaleString('ar-EG')}
            </p>
          </div>
          <button
            onClick={fetchOrders}
            disabled={loading}
            style={{
              background: loading ? '#ccc' : '#4caf50',
              color: 'white',
              border: 'none',
              padding: '0.75rem 1.5rem',
              borderRadius: '8px',
              cursor: loading ? 'not-allowed' : 'pointer',
              fontSize: '0.9rem',
              fontWeight: '600'
            }}
          >
            <i className={`fas ${loading ? 'fa-spinner fa-spin' : 'fa-sync-alt'}`} style={{ marginLeft: '0.5rem' }}></i>
            {loading ? 'جاري التحديث...' : 'تحديث البيانات'}
          </button>
        </div>
      </div>

      {/* الإحصائيات الرئيسية المصححة */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
        gap: '1.5rem',
        marginBottom: '2rem'
      }}>
        <div style={{
          background: 'white',
          padding: '1.5rem',
          borderRadius: '12px',
          boxShadow: '0 4px 16px rgba(0,0,0,0.1)',
          textAlign: 'center',
          border: '3px solid #4caf50'
        }}>
          <i className="fas fa-check-double" style={{ fontSize: '2rem', color: '#4caf50', marginBottom: '0.5rem' }}></i>
          <div style={{ fontSize: '2rem', fontWeight: '700', color: '#4caf50' }}>{stats.completedOrders}</div>
          <div style={{ color: '#666', fontSize: '0.9rem' }}>الطلبات المكتملة</div>
          <div style={{ color: '#4caf50', fontSize: '1.2rem', fontWeight: '600', marginTop: '0.5rem' }}>
            {stats.totalSales.toFixed(2)} ج.م
          </div>
        </div>

        <div style={{
          background: 'white',
          padding: '1.5rem',
          borderRadius: '12px',
          boxShadow: '0 4px 16px rgba(0,0,0,0.1)',
          textAlign: 'center',
          border: '3px solid #ff9800'
        }}>
          <i className="fas fa-hourglass-half" style={{ fontSize: '2rem', color: '#ff9800', marginBottom: '0.5rem' }}></i>
          <div style={{ fontSize: '2rem', fontWeight: '700', color: '#ff9800' }}>{stats.pendingOrders}</div>
          <div style={{ color: '#666', fontSize: '0.9rem' }}>قيد المعالجة</div>
          <div style={{ color: '#ff9800', fontSize: '1.2rem', fontWeight: '600', marginTop: '0.5rem' }}>
            {stats.pendingSales.toFixed(2)} ج.م
          </div>
        </div>

        <div style={{
          background: 'white',
          padding: '1.5rem',
          borderRadius: '12px',
          boxShadow: '0 4px 16px rgba(0,0,0,0.1)',
          textAlign: 'center',
          border: '3px solid #2196f3'
        }}>
          <i className="fas fa-money-bill-wave" style={{ fontSize: '2rem', color: '#2196f3', marginBottom: '0.5rem' }}></i>
          <div style={{ fontSize: '1.8rem', fontWeight: '700', color: '#2196f3' }}>
            {stats.totalSales.toFixed(2)}
          </div>
          <div style={{ color: '#666', fontSize: '0.9rem' }}>إجمالي المبيعات الصحيح</div>
          <div style={{ color: '#2196f3', fontSize: '1rem', fontWeight: '600', marginTop: '0.5rem' }}>
            (الطلبات المكتملة فقط)
          </div>
        </div>

        <div style={{
          background: 'white',
          padding: '1.5rem',
          borderRadius: '12px',
          boxShadow: '0 4px 16px rgba(0,0,0,0.1)',
          textAlign: 'center',
          border: '3px solid #9c27b0'
        }}>
          <i className="fas fa-calculator" style={{ fontSize: '2rem', color: '#9c27b0', marginBottom: '0.5rem' }}></i>
          <div style={{ fontSize: '1.8rem', fontWeight: '700', color: '#9c27b0' }}>
            {stats.averageOrderValue.toFixed(2)}
          </div>
          <div style={{ color: '#666', fontSize: '0.9rem' }}>متوسط قيمة الطلب</div>
        </div>
      </div>

      {/* مبيعات كل نادل المحسنة */}
      <div style={{
        background: 'white',
        padding: '2rem',
        borderRadius: '16px',
        marginBottom: '2rem',
        boxShadow: '0 4px 16px rgba(0,0,0,0.1)'
      }}>
        <h3 style={{ margin: '0 0 1.5rem 0', color: '#333', fontSize: '1.5rem' }}>
          <i className="fas fa-users" style={{ marginLeft: '0.5rem', color: '#2196f3' }}></i>
          إيرادات النادلين (البيانات الصحيحة)
        </h3>

        {Object.keys(waiterStats).length === 0 ? (
          <div style={{
            textAlign: 'center',
            padding: '3rem',
            color: '#666',
            fontSize: '1.1rem'
          }}>
            <i className="fas fa-info-circle" style={{ fontSize: '3rem', marginBottom: '1rem', color: '#ddd' }}></i>
            <div>لا توجد بيانات متاحة حالياً</div>
            <div style={{ fontSize: '0.9rem', marginTop: '0.5rem' }}>
              سيتم عرض البيانات عند وجود طلبات
            </div>
          </div>
        ) : (
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
            gap: '1.5rem'
          }}>
            {Object.entries(waiterStats)
              .sort(([,a], [,b]) => b.completedSales - a.completedSales)
              .map(([waiter, stats]) => (
              <div key={waiter} style={{
                background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
                border: '2px solid #dee2e6',
                borderRadius: '12px',
                padding: '1.5rem',
                transition: 'transform 0.2s ease'
              }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  marginBottom: '1rem'
                }}>
                  <div style={{
                    background: '#2196f3',
                    color: 'white',
                    width: '50px',
                    height: '50px',
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginLeft: '1rem'
                  }}>
                    <i className="fas fa-user"></i>
                  </div>
                  <div>
                    <h4 style={{ margin: 0, color: '#333', fontSize: '1.2rem' }}>{waiter}</h4>
                    <p style={{ margin: 0, color: '#2196f3', fontSize: '1.1rem', fontWeight: '600' }}>
                      {stats.completedSales.toFixed(2)} ج.م
                    </p>
                  </div>
                </div>

                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(2, 1fr)',
                  gap: '0.75rem',
                  fontSize: '0.9rem'
                }}>
                  <div style={{ background: 'white', padding: '0.75rem', borderRadius: '8px', textAlign: 'center' }}>
                    <div style={{ color: '#666' }}>إجمالي الطلبات</div>
                    <div style={{ fontSize: '1.2rem', fontWeight: '600', color: '#333' }}>{stats.totalOrders}</div>
                  </div>
                  <div style={{ background: 'white', padding: '0.75rem', borderRadius: '8px', textAlign: 'center' }}>
                    <div style={{ color: '#666' }}>مكتملة</div>
                    <div style={{ fontSize: '1.2rem', fontWeight: '600', color: '#4caf50' }}>{stats.completedOrders}</div>
                  </div>
                  <div style={{ background: 'white', padding: '0.75rem', borderRadius: '8px', textAlign: 'center' }}>
                    <div style={{ color: '#666' }}>قيد المعالجة</div>
                    <div style={{ fontSize: '1.2rem', fontWeight: '600', color: '#ff9800' }}>{stats.pendingOrders}</div>
                  </div>
                  <div style={{ background: 'white', padding: '0.75rem', borderRadius: '8px', textAlign: 'center' }}>
                    <div style={{ color: '#666' }}>متوسط الطلب</div>
                    <div style={{ fontSize: '1rem', fontWeight: '600', color: '#9c27b0' }}>
                      {stats.averageOrderValue.toFixed(2)} ج.م
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
