/* Enhanced Waiter Dashboard Styles - OOP Version */

/* Main Dashboard Container */
.waiter-dashboard {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
  overflow: hidden;
}

/* Header Styles */
.waiter-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 1rem 2rem;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.sidebar-toggle {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #2c3e50;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.sidebar-toggle:hover {
  background: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

.header-left h1 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.8rem;
  font-weight: 600;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.notifications-badge {
  position: relative;
  background: rgba(52, 152, 219, 0.1);
  border-radius: 50%;
  width: 45px;
  height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #3498db;
  font-size: 1.2rem;
}

.notifications-badge:hover {
  background: rgba(52, 152, 219, 0.2);
  transform: scale(1.1);
}

.notifications-badge .badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #e74c3c;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: 600;
}

.waiter-name {
  color: #2c3e50;
  font-weight: 500;
  padding: 0.5rem 1rem;
  background: rgba(52, 152, 219, 0.1);
  border-radius: 20px;
}

.logout-btn {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.logout-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

/* Dashboard Content */
.dashboard-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* Sidebar */
.waiter-sidebar {
  width: 300px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  overflow-y: auto;
  box-shadow: 2px 0 20px rgba(0, 0, 0, 0.1);
}

.waiter-sidebar.open {
  width: 300px;
}

.sidebar-content {
  padding: 2rem 1.5rem;
}

/* Waiter Profile */
.waiter-profile {
  text-align: center;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: rgba(52, 152, 219, 0.1);
  border-radius: 16px;
}

.waiter-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3498db, #2980b9);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  margin: 0 auto 1rem;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.waiter-profile h3 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.waiter-profile p {
  margin: 0;
  color: #7f8c8d;
  font-size: 0.9rem;
}

/* Quick Stats */
.quick-stats {
  margin-bottom: 2rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  margin-bottom: 0.75rem;
  border-right: 4px solid #3498db;
  transition: all 0.3s ease;
}

.stat-item:hover {
  background: rgba(255, 255, 255, 1);
  transform: translateX(-5px);
}

.stat-item i {
  color: #3498db;
  font-size: 1.2rem;
  width: 20px;
  text-align: center;
}

.stat-item span {
  color: #2c3e50;
  font-weight: 500;
  font-size: 0.9rem;
}

/* Navigation */
.waiter-nav {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.nav-btn {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 1.5rem;
  background: rgba(255, 255, 255, 0.8);
  border: none;
  border-radius: 12px;
  color: #2c3e50;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  border-right: 4px solid transparent;
}

.nav-btn:hover {
  background: rgba(52, 152, 219, 0.1);
  border-right-color: #3498db;
  color: #3498db;
  transform: translateX(-5px);
}

.nav-btn.active {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  border-right-color: #2980b9;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.nav-btn i {
  font-size: 1.2rem;
  width: 20px;
  text-align: center;
}

.nav-badge {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: #e74c3c;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: 600;
}

/* Main Content */
.waiter-main {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
  background: transparent;
}

/* Loading Container */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: white;
  font-size: 1.2rem;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Tables View */
.tables-view {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.tables-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.tables-header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.8rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.tables-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
}

.table-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border-right: 4px solid #3498db;
  cursor: pointer;
}

.table-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.table-card.occupied {
  border-right-color: #e74c3c;
}

.table-card.closed {
  border-right-color: #95a5a6;
  opacity: 0.7;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.table-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2c3e50;
}

.table-status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.table-status.available {
  background: #27ae60;
  color: white;
}

.table-status.occupied {
  background: #e74c3c;
  color: white;
}

.table-status.closed {
  background: #95a5a6;
  color: white;
}

.table-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.table-detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
  color: #7f8c8d;
}

.table-detail strong {
  color: #2c3e50;
}

/* Orders View */
.orders-view {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.orders-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.orders-header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.8rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.orders-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.order-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border-right: 4px solid #3498db;
}

.order-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.order-card.pending {
  border-right-color: #f39c12;
}

.order-card.preparing {
  border-right-color: #e74c3c;
}

.order-card.ready {
  border-right-color: #9b59b6;
}

.order-card.completed {
  border-right-color: #27ae60;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.order-number {
  font-size: 1.3rem;
  font-weight: 700;
  color: #2c3e50;
}

.order-status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.order-status.pending {
  background: #f39c12;
  color: white;
}

.order-status.preparing {
  background: #e74c3c;
  color: white;
}

.order-status.ready {
  background: #9b59b6;
  color: white;
}

.order-status.completed {
  background: #27ae60;
  color: white;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: #7f8c8d;
}

.empty-state i {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-state h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
}

.empty-state p {
  margin: 0;
  font-size: 1rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .tables-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .waiter-sidebar {
    position: fixed;
    left: -300px;
    top: 0;
    height: 100vh;
    z-index: 1000;
    transition: left 0.3s ease;
  }
  
  .waiter-sidebar.open {
    left: 0;
  }
  
  .waiter-main {
    padding: 1rem;
  }
  
  .header-content {
    padding: 0 1rem;
  }
  
  .tables-grid {
    grid-template-columns: 1fr;
  }
  
  .dashboard-content {
    margin-left: 0;
  }
  
  .header-right {
    gap: 0.5rem;
  }
  
  .waiter-name {
    display: none;
  }
}

@media (max-width: 480px) {
  .waiter-main {
    padding: 0.5rem;
  }
  
  .tables-view,
  .orders-view {
    padding: 1rem;
  }
  
  .table-card,
  .order-card {
    padding: 1rem;
  }
}
