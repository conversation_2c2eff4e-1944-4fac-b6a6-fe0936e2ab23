import React, { useState, useEffect } from 'react';
import { useToast } from './hooks/useToast';
import { ToastContainer } from './components/Toast';
import { authenticatedGet, authenticatedPost, authenticatedPut, authenticatedDelete, handleApiError } from './utils/apiHelpers';

interface Item {
  _id?: string;
  name: string;
  quantity: number;
  min: number;
  price?: number;
  last_updated?: string;
  unit?: string;
}

export default function Inventory() {
  const [items, setItems] = useState<Item[]>([]);
  const [name, setName] = useState('');
  const [quantity, setQuantity] = useState(1);
  const [min, setMin] = useState(1);
  const [price, setPrice] = useState(0);
  const [unit, setUnit] = useState('قطعة');
  const [loading, setLoading] = useState(false);
  const [editingItem, setEditingItem] = useState<Item | null>(null);
  const { toasts, removeToast, showSuccess, showError } = useToast();

  // جلب الأصناف من السيرفر عند التحميل
  useEffect(() => {
    fetchItems();
  }, []);
  const fetchItems = async () => {
    try {
      const response = await authenticatedGet('/api/inventory');
      
      // Handle both response formats for compatibility
      let items = response;
      if (response && typeof response === 'object' && response.data) {
        items = response.data;
      }
      
      // Ensure we have an array
      if (Array.isArray(items)) {
        setItems(items);
      } else {
        console.error('Inventory response is not an array:', items);
        setItems([]);
        showError('تنسيق البيانات غير صحيح');
      }
    } catch (error) {
      console.error('خطأ في جلب المخزون:', error);
      const errorMessage = handleApiError(error);
      showError(errorMessage);
      setItems([]);
    }
  };
  // إضافة صنف جديد
  const handleAdd = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!name) return;
    setLoading(true);

    try {
      await authenticatedPost('/api/inventory', { name, quantity, min, price, unit });
      await fetchItems(); // إعادة جلب البيانات
      setName('');
      setQuantity(1);
      setMin(1);
      setPrice(0);
      setUnit('قطعة');
      showSuccess('تم إضافة الصنف بنجاح');
    } catch (error) {
      console.error('خطأ في إضافة الصنف:', error);
      const errorMessage = handleApiError(error);
      showError(errorMessage);
    }
    setLoading(false);
  };
  // تحديث صنف
  const handleUpdate = async (item: Item) => {
    setLoading(true);
    try {
      await authenticatedPut(`/api/inventory/${item._id}`, {
        name: item.name,
        quantity: item.quantity,
        min: item.min,
        unit: item.unit
      });
      await fetchItems();
      setEditingItem(null);
      showSuccess('تم تحديث الصنف بنجاح');
    } catch (error) {
      console.error('خطأ في تحديث الصنف:', error);
      const errorMessage = handleApiError(error);
      showError(errorMessage);
    }
    setLoading(false);
  };
  // حذف صنف
  const handleDelete = async (id: string) => {
    if (!confirm('هل أنت متأكد من حذف هذا الصنف؟')) return;

    setLoading(true);
    try {
      await authenticatedDelete(`/api/inventory/${id}`);
      await fetchItems();
      showSuccess('تم حذف الصنف بنجاح');
    } catch (error) {
      console.error('خطأ في حذف الصنف:', error);
      const errorMessage = handleApiError(error);
      showError(errorMessage);
    }
    setLoading(false);
  };
  return (
    <div style={{ direction: 'rtl', padding: '2rem' }}>
      <div style={{
        background: '#fff',
        borderRadius: '12px',
        padding: '2rem',
        boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
        marginBottom: '2rem'
      }}>
        <h2 style={{ color: '#6d4c41', marginBottom: '0.5rem' }}>إدارة المخزون</h2>
        <p style={{ color: '#666', marginBottom: '2rem' }}>
          إضافة وإدارة أصناف المخزون مع تتبع الكميات والحد الأدنى
        </p>

        <form onSubmit={handleAdd} style={{
          marginBottom: '2rem',
          background: '#f8f9fa',
          padding: '1.5rem',
          borderRadius: '8px',
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '1rem',
          alignItems: 'end'
        }}>
          <div>
            <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 'bold' }}>
              اسم الصنف:
              <input
                value={name}
                onChange={e => setName(e.target.value)}
                required
                style={{
                  width: '100%',
                  padding: '0.5rem',
                  border: '1px solid #ddd',
                  borderRadius: '4px',
                  marginTop: '0.25rem'
                }}
                placeholder="مثال: سكر أبيض"
              />
            </label>
          </div>

          <div>
            <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 'bold' }}>
              الوحدة:
              <select
                value={unit}
                onChange={e => setUnit(e.target.value)}
                style={{
                  width: '100%',
                  padding: '0.5rem',
                  border: '1px solid #ddd',
                  borderRadius: '4px',
                  marginTop: '0.25rem'
                }}
              >
                <option value="قطعة">قطعة</option>
                <option value="كيلو">كيلو</option>
                <option value="لتر">لتر</option>
                <option value="حبة">حبة</option>
                <option value="علبة">علبة</option>
                <option value="كيس">كيس</option>
              </select>
            </label>
          </div>

          <div>
            <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 'bold' }}>
              الكمية:
              <input
                type="number"
                min={0}
                value={quantity}
                onChange={e => setQuantity(Number(e.target.value))}
                style={{
                  width: '100%',
                  padding: '0.5rem',
                  border: '1px solid #ddd',
                  borderRadius: '4px',
                  marginTop: '0.25rem'
                }}
              />
            </label>
          </div>

          <div>
            <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 'bold' }}>
              الحد الأدنى:
              <input
                type="number"
                min={1}
                value={min}
                onChange={e => setMin(Number(e.target.value))}
                style={{
                  width: '100%',
                  padding: '0.5rem',
                  border: '1px solid #ddd',
                  borderRadius: '4px',
                  marginTop: '0.25rem'
                }}
              />
            </label>
          </div>

          <button
            type="submit"
            style={{
              background: '#795548',
              color: '#fff',
              border: 'none',
              padding: '0.75rem 1.5rem',
              borderRadius: '6px',
              cursor: loading ? 'not-allowed' : 'pointer',
              fontSize: '1rem',
              fontWeight: 'bold'
            }}
            disabled={loading}
          >
            {loading ? 'جاري الإضافة...' : 'إضافة صنف'}
          </button>
        </form>

        <div style={{ overflowX: 'auto' }}>
          <table style={{ width: '100%', borderCollapse: 'collapse', background: '#fff', minWidth: '700px' }}>
            <thead>
              <tr style={{ background: '#f5f5f5' }}>
                <th style={{ padding: '1rem', textAlign: 'center', border: '1px solid #ddd' }}>الصنف</th>
                <th style={{ padding: '1rem', textAlign: 'center', border: '1px solid #ddd' }}>الوحدة</th>
                <th style={{ padding: '1rem', textAlign: 'center', border: '1px solid #ddd' }}>الكمية الحالية</th>
                <th style={{ padding: '1rem', textAlign: 'center', border: '1px solid #ddd' }}>الحد الأدنى</th>
                <th style={{ padding: '1rem', textAlign: 'center', border: '1px solid #ddd' }}>الحالة</th>
                <th style={{ padding: '1rem', textAlign: 'center', border: '1px solid #ddd' }}>آخر تحديث</th>
                <th style={{ padding: '1rem', textAlign: 'center', border: '1px solid #ddd' }}>العمليات</th>
              </tr>
            </thead>
            <tbody>
              {items.map(item => (
                <tr key={item._id} style={{
                  textAlign: 'center',
                  backgroundColor: item.quantity <= item.min ? '#ffebee' : 'transparent'
                }}>
                  <td style={{ padding: '1rem', border: '1px solid #ddd' }}>                    {editingItem?._id === item._id ? (
                      <input
                        value={editingItem?.name || ''}
                        onChange={e => editingItem && setEditingItem({...editingItem, name: e.target.value})}
                        style={{ width: '100%', padding: '0.25rem' }}
                      />
                    ) : (
                      item.name
                    )}
                  </td>
                  <td style={{ padding: '1rem', border: '1px solid #ddd' }}>                    {editingItem?._id === item._id ? (
                      <select
                        value={editingItem?.unit || 'قطعة'}
                        onChange={e => editingItem && setEditingItem({...editingItem, unit: e.target.value})}
                        style={{ width: '100%', padding: '0.25rem' }}
                      >
                        <option value="قطعة">قطعة</option>
                        <option value="كيلو">كيلو</option>
                        <option value="لتر">لتر</option>
                        <option value="حبة">حبة</option>
                        <option value="علبة">علبة</option>
                        <option value="كيس">كيس</option>
                      </select>
                    ) : (
                      item.unit || 'قطعة'
                    )}
                  </td>                  <td style={{ padding: '1rem', border: '1px solid #ddd' }}>
                    {editingItem?._id === item._id ? (
                      <input
                        type="number"
                        value={editingItem?.quantity || 0}
                        onChange={e => editingItem && setEditingItem({...editingItem, quantity: Number(e.target.value)})}
                        style={{ width: '80px', padding: '0.25rem' }}
                      />
                    ) : (
                      item.quantity
                    )}
                  </td>
                  <td style={{ padding: '1rem', border: '1px solid #ddd' }}>
                    {editingItem?._id === item._id ? (
                      <input
                        type="number"
                        value={editingItem?.min || 0}
                        onChange={e => editingItem && setEditingItem({...editingItem, min: Number(e.target.value)})}
                        style={{ width: '80px', padding: '0.25rem' }}
                      />
                    ) : (
                      item.min
                    )}
                  </td>
                  <td style={{ padding: '1rem', border: '1px solid #ddd' }}>
                    {item.quantity <= item.min ? (
                      <span style={{ color: '#d32f2f', fontWeight: 'bold' }}>⚠️ نفدت الكمية</span>
                    ) : item.quantity <= item.min * 1.5 ? (
                      <span style={{ color: '#f57c00', fontWeight: 'bold' }}>⚡ قريب من النفاد</span>
                    ) : (
                      <span style={{ color: '#388e3c', fontWeight: 'bold' }}>✅ متوفر</span>
                    )}
                  </td>
                  <td style={{ padding: '1rem', border: '1px solid #ddd' }}>
                    {item.last_updated ? new Date(item.last_updated).toLocaleString('ar-EG') : 'غير محدد'}
                  </td>
                  <td style={{ padding: '1rem', border: '1px solid #ddd' }}>
                    {editingItem?._id === item._id ? (
                      <div style={{ display: 'flex', gap: '0.5rem', justifyContent: 'center' }}>                        <button
                          onClick={() => editingItem && handleUpdate(editingItem)}
                          style={{
                            background: '#4caf50',
                            color: 'white',
                            border: 'none',
                            padding: '0.25rem 0.5rem',
                            borderRadius: '4px',
                            cursor: 'pointer'
                          }}
                        >
                          حفظ
                        </button>
                        <button
                          onClick={() => setEditingItem(null)}
                          style={{
                            background: '#f44336',
                            color: 'white',
                            border: 'none',
                            padding: '0.25rem 0.5rem',
                            borderRadius: '4px',
                            cursor: 'pointer'
                          }}
                        >
                          إلغاء
                        </button>
                      </div>
                    ) : (
                      <div style={{ display: 'flex', gap: '0.5rem', justifyContent: 'center' }}>
                        <button
                          onClick={() => setEditingItem(item)}
                          style={{
                            background: '#2196f3',
                            color: 'white',
                            border: 'none',
                            padding: '0.25rem 0.5rem',
                            borderRadius: '4px',
                            cursor: 'pointer'
                          }}
                        >
                          تعديل
                        </button>
                        <button
                          onClick={() => handleDelete(item._id!)}
                          style={{
                            background: '#f44336',
                            color: 'white',
                            border: 'none',
                            padding: '0.25rem 0.5rem',
                            borderRadius: '4px',
                            cursor: 'pointer'
                          }}
                        >
                          حذف
                        </button>
                      </div>
                    )}
                  </td>
                </tr>
              ))}
              {items.length === 0 && (
                <tr>
                  <td colSpan={7} style={{ padding: '2rem', textAlign: 'center', color: '#666' }}>
                    لا توجد أصناف في المخزون
                  </td>
                </tr>
              )}
            </tbody>          </table>
        </div>
      </div>
      
      <ToastContainer toasts={toasts} onRemove={removeToast} />
    </div>
  );
}
