{"chefDashboardTitle": "Chef Dashboard", "welcomeMessage": "Welcome, {{name}}", "chef": "Chef", "logout": "Logout", "pendingOrders": "Pending Orders", "preparingOrders": "Orders in Preparation", "readyOrders": "Ready Orders", "allOrders": "All Orders", "orderNumber": "Order Number", "table": "Table", "items": "Items", "status": "Status", "actions": "Actions", "startPreparing": "Start Preparing", "markAsReady": "<PERSON> as Ready", "orderTime": "Order Time", "preparationTime": "Preparation Time", "estimatedTime": "Estimated Time", "kitchen": "Kitchen", "priority": "Priority", "urgent": "<PERSON><PERSON>", "normal": "Normal", "low": "Low", "instructions": "Instructions", "allergens": "Allergens", "customizations": "Customizations", "orderCompleted": "Order Completed", "orderInProgress": "Order in Progress", "loadingOrders": "Loading orders...", "noOrdersFound": "No orders found", "refreshOrders": "Refresh Orders", "filterByStatus": "Filter by Status", "todaysOrders": "Today's Orders", "orderDetails": "Order Details", "ingredientsList": "Ingredients List", "preparationNotes": "Preparation Notes", "deliveryTime": "Delivery Time", "orderAcceptedSuccessfully": "Order accepted successfully", "failedToAcceptOrder": "Failed to accept order", "errorAcceptingOrder": "Error accepting order", "orderCompletedSuccessfully": "Order completed successfully", "failedToCompleteOrder": "Failed to complete order", "errorCompletingOrder": "Error completing order", "orderAcceptedByChef": "Order accepted by {{<PERSON><PERSON><PERSON>}}", "orderPreparedByChef": "Order preparation completed by {{chef<PERSON>ame}}", "newOrderReceived": "New order #{{orderNumber}} from table {{tableNumber}}", "notSpecified": "Not specified"}