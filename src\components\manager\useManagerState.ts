// مدير الحالة للوحة المدير
import { useState, useCallback } from 'react';
import type { 
  ManagerState, 
  ModalState, 
  FilterState, 
  Order, 
  Employee, 
  TableAccount, 
  DiscountRequest, 
  MenuItem, 
  Category,
  NewEmployee,
  NewProduct,
  DashboardStats 
} from '../../models/ManagerModels';

// الحالة الأولية للفلاتر
const initialFilters: FilterState = {
  orderStatus: 'all',
  waiter: 'all',
  date: 'today',
  categoryFilter: null,
  availability: 'all',
  searchTerm: ''
};

// الحالة الأولية للنوافذ المنبثقة
const initialModalState: ModalState = {
  showDiscountModal: false,
  selectedDiscountRequest: null,
  showShiftModal: false,
  selectedEmployee: null,
  showOrderDetailsModal: false,
  selectedOrder: null,
  showMenuModal: false,
  selectedMenuItem: null,
  showCategoryModal: false,
  selectedCategory: null,
  showAddEmployeeModal: false,
  showEditEmployeeModal: false,
  selectedEmployeeForEdit: null,
  showAddProductModal: false,
  showEditProductModal: false,
  selectedProductForEdit: null
};

// الحالة الأولية للإحصائيات
const initialStats: DashboardStats = {
  totalOrders: 0,
  totalSales: 0,
  activeEmployees: 0,
  activeTables: 0,
  pendingOrders: 0,
  preparingOrders: 0,
  readyOrders: 0,
  completedOrders: 0,
  averageOrderValue: 0,
  statusCounts: {
    pending: 0,
    preparing: 0,
    ready: 0,
    completed: 0
  }
};

// الحالة الأولية لبيانات الموظف الجديد
const initialNewEmployee: NewEmployee = {
  username: '',
  name: '',
  email: '',
  role: 'waiter',
  password: '',
  phone: ''
};

// الحالة الأولية لبيانات المنتج الجديد
const initialNewProduct: NewProduct = {
  name: '',
  description: '',
  price: 0,
  categories: [],
  available: true,
  stock: 0
};

export function useManagerState() {
  // الحالات الأساسية
  const [currentScreen, setCurrentScreen] = useState<ManagerState['currentScreen']>('home');
  const [loading, setLoading] = useState<boolean>(false);
  const [sidebarOpen, setSidebarOpen] = useState<boolean>(false);
  
  // حالات البيانات
  const [orders, setOrders] = useState<Order[]>([]);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [tableAccounts, setTableAccounts] = useState<TableAccount[]>([]);
  const [discountRequests, setDiscountRequests] = useState<DiscountRequest[]>([]);
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [stats, setStats] = useState<DashboardStats>(initialStats);

  // حالات الفلاتر
  const [filters, setFilters] = useState<FilterState>(initialFilters);
  
  // حالات النوافذ المنبثقة
  const [modalState, setModalState] = useState<ModalState>(initialModalState);

  // حالات النماذج
  const [newEmployee, setNewEmployee] = useState<NewEmployee>(initialNewEmployee);
  const [newProduct, setNewProduct] = useState<NewProduct>(initialNewProduct);

  // دوال تحديث الحالة
  const updateOrders = useCallback((newOrders: Order[]) => {
    setOrders(newOrders);
  }, []);

  const updateEmployees = useCallback((newEmployees: Employee[]) => {
    setEmployees(newEmployees);
  }, []);

  const updateTableAccounts = useCallback((newTableAccounts: TableAccount[]) => {
    setTableAccounts(newTableAccounts);
  }, []);

  const updateDiscountRequests = useCallback((newRequests: DiscountRequest[]) => {
    setDiscountRequests(newRequests);
  }, []);

  const updateMenuItems = useCallback((newMenuItems: MenuItem[]) => {
    setMenuItems(newMenuItems);
  }, []);

  const updateCategories = useCallback((newCategories: Category[]) => {
    setCategories(newCategories);
  }, []);

  const updateStats = useCallback((newStats: DashboardStats) => {
    setStats(newStats);
  }, []);

  // دوال تحديث الفلاتر
  const updateFilter = useCallback(<K extends keyof FilterState>(
    key: K, 
    value: FilterState[K]
  ) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  }, []);

  const resetFilters = useCallback(() => {
    setFilters(initialFilters);
  }, []);

  // دوال تحديث النوافذ المنبثقة
  const updateModal = useCallback(<K extends keyof ModalState>(
    key: K, 
    value: ModalState[K]
  ) => {
    setModalState(prev => ({
      ...prev,
      [key]: value
    }));
  }, []);

  const closeAllModals = useCallback(() => {
    setModalState(initialModalState);
  }, []);

  // دوال تحديث النماذج
  const updateNewEmployee = useCallback(<K extends keyof NewEmployee>(
    key: K, 
    value: NewEmployee[K]
  ) => {
    setNewEmployee(prev => ({
      ...prev,
      [key]: value
    }));
  }, []);

  const resetNewEmployee = useCallback(() => {
    setNewEmployee(initialNewEmployee);
  }, []);

  const updateNewProduct = useCallback(<K extends keyof NewProduct>(
    key: K, 
    value: NewProduct[K]
  ) => {
    setNewProduct(prev => ({
      ...prev,
      [key]: value
    }));
  }, []);

  const resetNewProduct = useCallback(() => {
    setNewProduct(initialNewProduct);
  }, []);

  // دوال مساعدة للفلترة
  const getFilteredOrders = useCallback(() => {
    return orders.filter(order => {
      // فلتر حالة الطلب
      if (filters.orderStatus !== 'all' && order.status !== filters.orderStatus) {
        return false;
      }

      // فلتر النادل
      if (filters.waiter !== 'all' && order.waiterName !== filters.waiter) {
        return false;
      }

      // فلتر التاريخ
      const orderDate = new Date(order.createdAt);
      const now = new Date();
      
      switch (filters.date) {
        case 'today':
          if (orderDate.toDateString() !== now.toDateString()) {
            return false;
          }
          break;
        case 'week':
          const weekStart = new Date(now);
          weekStart.setDate(now.getDate() - now.getDay());
          if (orderDate < weekStart) {
            return false;
          }
          break;
        case 'month':
          const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
          if (orderDate < monthStart) {
            return false;
          }
          break;
      }

      return true;
    });
  }, [orders, filters]);

  const getFilteredMenuItems = useCallback(() => {
    return menuItems.filter(item => {
      // فلتر الفئة
      if (filters.categoryFilter && !item.categories?.includes(filters.categoryFilter)) {
        return false;
      }

      // فلتر التوفر
      if (filters.availability !== 'all') {
        if (filters.availability === 'available' && !item.available) {
          return false;
        }
        if (filters.availability === 'unavailable' && item.available) {
          return false;
        }
      }

      // فلتر البحث
      if (filters.searchTerm) {
        const searchTerm = filters.searchTerm.toLowerCase();
        const nameMatch = item.name.toLowerCase().includes(searchTerm);
        const descriptionMatch = item.description?.toLowerCase().includes(searchTerm);
        if (!nameMatch && !descriptionMatch) {
          return false;
        }
      }

      return true;
    });
  }, [menuItems, filters]);

  return {
    // الحالات
    currentScreen,
    loading,
    sidebarOpen,
    orders,
    employees,
    tableAccounts,
    discountRequests,
    menuItems,
    categories,
    stats,
    filters,
    modalState,
    newEmployee,
    newProduct,

    // دوال التحديث الأساسية
    setCurrentScreen,
    setLoading,
    setSidebarOpen,
    updateOrders,
    updateEmployees,
    updateTableAccounts,
    updateDiscountRequests,
    updateMenuItems,
    updateCategories,
    updateStats,

    // دوال الفلاتر
    updateFilter,
    resetFilters,
    getFilteredOrders,
    getFilteredMenuItems,

    // دوال النوافذ المنبثقة
    updateModal,
    closeAllModals,

    // دوال النماذج
    updateNewEmployee,
    resetNewEmployee,
    updateNewProduct,
    resetNewProduct
  };
}
