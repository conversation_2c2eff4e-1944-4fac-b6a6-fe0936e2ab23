import React, { useState, useMemo } from 'react';
import { FaEdit, FaTrash, FaPlus } from 'react-icons/fa';
import type { User, ApiResponse } from '../../utils/api'; // Adjusted import path
import * as apiUtil from '../../utils/api'; // Renamed to avoid conflict with api prop
import { APP_CONFIG as GlobalAppConfig } from '../../config/app.config'; // Adjusted import path and alias
import Button from '../Button'; // Adjusted import path
import Modal from '../Modal'; // Adjusted import path
import Alert from '../Alert'; // Adjusted import path

interface UsersSectionProps {
  users: User[];
  setUsers: React.Dispatch<React.SetStateAction<User[]>>;
  api: typeof apiUtil.default; // Use the aliased import for the type
  addToast: (message: string, type: 'success' | 'error' | 'info') => void;
  handleOpenConfirmDeleteModal: (item: User, type: 'User') => void;
  APP_CONFIG: typeof GlobalAppConfig; // Use the aliased import for the type
  initialUserFormData: Partial<User> & { password?: string; confirmPassword?: string };
  searchTerm: string;
  loading: boolean; // To show "no users" message correctly
}

const UsersSection: React.FC<UsersSectionProps> = ({
  users,
  setUsers,
  api,
  addToast,
  handleOpenConfirmDeleteModal,
  APP_CONFIG,
  initialUserFormData,
  searchTerm,
  loading,
}) => {
  const [isUserModalOpen, setIsUserModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [userFormData, setUserFormData] = useState<Partial<User> & { password?: string; confirmPassword?: string }>(initialUserFormData);
  const [userModalError, setUserModalError] = useState<string | null>(null);

  const filteredUsers = useMemo(() => 
    users.filter(user => 
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) || 
      user.email.toLowerCase().includes(searchTerm.toLowerCase())
    ), 
    [users, searchTerm]
  );

  const handleOpenUserModal = (user?: User) => {
    setIsUserModalOpen(true);
    setSelectedUser(user || null);
    setUserFormData(user ? { ...user } : initialUserFormData);
    setUserModalError(null); // Clear previous errors
  };

  const closeUserModal = () => {
    setIsUserModalOpen(false);
    setSelectedUser(null);
    setUserFormData(initialUserFormData);
    setUserModalError(null);
  };

  const handleUserFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setUserFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleUserFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setUserModalError(null);
    if (!userFormData.name || !userFormData.email || !userFormData.role) {
      setUserModalError("يرجى ملء جميع الحقول المطلوبة.");
      addToast("يرجى ملء جميع الحقول المطلوبة.", 'error');
      return;
    }

    if (!selectedUser) {
      if (!userFormData.password || !userFormData.confirmPassword) {
        setUserModalError("يرجى إدخال كلمة المرور وتأكيدها.");
        addToast("يرجى إدخال كلمة المرور وتأكيدها.", 'error');
        return;
      }
      if (userFormData.password !== userFormData.confirmPassword) {
        setUserModalError("كلمتا المرور غير متطابقتين.");
        addToast("كلمتا المرور غير متطابقتين.", 'error');
        return;
      }
    }

    try {
      let response: ApiResponse<User>; // Use ApiResponse<User>
      const userDataToSend: any = {
        name: userFormData.name,
        email: userFormData.email,
        role: userFormData.role,
      };

      if (!selectedUser) {
        userDataToSend.password = userFormData.password;
      }

      if (selectedUser && selectedUser.id) {
        response = await api.updateUser(selectedUser.id, userDataToSend);
      } else {
        response = await api.createUser(userDataToSend);
      }

      if (response.success) {
        const usersResponse = await api.getUsers();
        if (usersResponse.success && usersResponse.data) setUsers(usersResponse.data);
        closeUserModal();
        addToast(selectedUser ? 'تم تحديث المستخدم بنجاح!' : 'تم إنشاء المستخدم بنجاح!', 'success');
      } else {
        setUserModalError(response.error || 'فشل في معالجة الطلب.');
        addToast(response.error || 'فشل في معالجة الطلب.', 'error');
      }
    } catch (err) {
      setUserModalError('حدث خطأ غير متوقع.');
      addToast('حدث خطأ غير متوقع.', 'error');
      console.error(err);
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-semibold">إدارة المستخدمين</h2>
        <Button onClick={() => handleOpenUserModal()} icon="fa-plus" variant="primary">إضافة مستخدم</Button>
      </div>
      {filteredUsers.length === 0 && !loading && (
        <p className="text-center text-gray-500 my-4">لا يوجد مستخدمون لعرضهم حاليًا.</p>
      )}
      {filteredUsers.length > 0 && (
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white shadow-md rounded-lg">
            <thead>
              <tr className="bg-gray-200 text-gray-600 uppercase text-sm leading-normal">
                <th className="py-3 px-6 text-left">الاسم</th>
                <th className="py-3 px-6 text-left">البريد الإلكتروني</th>
                <th className="py-3 px-6 text-left">الدور</th>
                <th className="py-3 px-6 text-center">الإجراءات</th>
              </tr>
            </thead>
            <tbody className="text-gray-600 text-sm font-light">
              {filteredUsers.map(user => (
                <tr key={user.id} className="border-b border-gray-200 hover:bg-gray-100">
                  <td className="py-3 px-6 text-left whitespace-nowrap">{user.name}</td>
                  <td className="py-3 px-6 text-left">{user.email}</td>
                  <td className="py-3 px-6 text-left">{user.role}</td>
                  <td className="py-3 px-6 text-center">
                    <Button onClick={() => handleOpenUserModal(user)} icon="fa-edit" size="sm" variant="info" className="mr-2">تعديل</Button>
                    <Button onClick={() => handleOpenConfirmDeleteModal(user, 'User')} icon="fa-trash" size="sm" variant="error">حذف</Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
      <Modal isOpen={isUserModalOpen} onClose={closeUserModal} title={selectedUser ? "تعديل المستخدم" : "إضافة مستخدم"}>
        <form onSubmit={handleUserFormSubmit}>
          {userModalError && <Alert type="error" onClose={() => setUserModalError(null)}>{userModalError}</Alert>}
          <div className="mb-4">
            <label htmlFor="userName" className="block text-sm font-medium text-gray-700">الاسم</label>
            <input
              type="text"
              name="name"
              id="userName"
              value={userFormData.name || ''}
              onChange={handleUserFormChange}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              required
            />
          </div>
          <div className="mb-4">
            <label htmlFor="userEmail" className="block text-sm font-medium text-gray-700">البريد الإلكتروني</label>
            <input
              type="email"
              name="email"
              id="userEmail"
              value={userFormData.email || ''}
              onChange={handleUserFormChange}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              required
            />
          </div>
          <div className="mb-4">
            <label htmlFor="userRole" className="block text-sm font-medium text-gray-700">الدور</label>
            <select
              name="role"
              id="userRole"
              value={userFormData.role || ''}
              onChange={handleUserFormChange}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              required
            >
              {APP_CONFIG.ROLES && Object.values(APP_CONFIG.ROLES).map(role => (
                <option key={role as string} value={role as string}>{role as string}</option>
              ))}
              {(!APP_CONFIG.ROLES || Object.values(APP_CONFIG.ROLES).length === 0) && (
                <>
                  <option key="User" value="User">User</option>
                  <option key="Admin" value="Admin">Admin</option>
                  <option key="Manager" value="Manager">Manager</option>
                  <option key="Waiter" value="Waiter">Waiter</option>
                  <option key="Cook" value="Cook">Cook</option>
                </>
              )}
            </select>
          </div>
          {!selectedUser && (
            <>
              <div className="mb-4">
                <label htmlFor="userPassword" className="block text-sm font-medium text-gray-700">كلمة المرور</label>
                <input
                  type="password"
                  name="password"
                  id="userPassword"
                  value={userFormData.password || ''}
                  onChange={handleUserFormChange}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  required={!selectedUser}
                />
              </div>
              <div className="mb-4">
                <label htmlFor="userConfirmPassword" className="block text-sm font-medium text-gray-700">تأكيد كلمة المرور</label>
                <input
                  type="password"
                  name="confirmPassword"
                  id="userConfirmPassword"
                  value={userFormData.confirmPassword || ''}
                  onChange={handleUserFormChange}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  required={!selectedUser}
                />
              </div>
            </>
          )}
          <div className="mb-4">
            <div className="flex justify-end space-x-2 space-x-reverse">
              <Button type="button" variant="secondary" onClick={closeUserModal}>إلغاء</Button>
              <Button type="submit" variant="primary">{selectedUser ? 'حفظ التغييرات' : 'إضافة مستخدم'}</Button>
            </div>
          </div>
        </form>
      </Modal>
    </div>
  );
};

export default UsersSection;
