# 🔧 دليل متغيرات البيئة - نظام إدارة مقهى ديشة

## 📋 نظرة عامة

يستخدم نظام مقهى ديشة متغيرات البيئة (Environment Variables) لإدارة التكوين بشكل آمن ومرن. هذا يسمح بتشغيل النظام في بيئات مختلفة (التطوير، الاختبار، الإنتاج) دون الحاجة لتعديل الكود.

## 📁 ملفات التكوين

### 1. الملفات الرئيسية:
- `.env` - التكوين المحلي (لا يُرفع إلى Git)
- `backend/.env` - تكوين الباك إند (لا يُرفع إلى Git)
- `.env.example` - مثال التكوين (يُرفع إلى Git كمرجع)
- `.env.production` - إعدادات الإنتاج (يُرفع إلى Git)

### 2. ملفات التكوين المساعدة:
- `backend/config/environment.js` - التكوين المركزي للباك إند
- `src/config/app.config.ts` - التكوين المركزي للفرونت إند

## 🔐 المتغيرات الحيوية (Critical Variables)

### قاعدة البيانات:
```env
MONGODB_URI=mongodb+srv://username:<EMAIL>/deshacoffee
```

### الأمان:
```env
JWT_SECRET=your-super-secret-jwt-key-here-make-it-very-long-and-random
SESSION_SECRET=your-session-secret-key-here
BCRYPT_SALT_ROUNDS=12
```

### الخادم:
```env
PORT=4003
NODE_ENV=production
HOST=0.0.0.0
```

## 🌐 متغيرات الاتصال

### URLs الأساسية:
```env
FRONTEND_URL=https://desha-coffee.vercel.app
BACKEND_URL=https://deshacoffee-production.up.railway.app
VITE_API_URL=https://deshacoffee-production.up.railway.app
```

### إعدادات CORS:
```env
CORS_ORIGIN=https://desha-coffee.vercel.app
CORS_CREDENTIALS=true
```

## ⚙️ متغيرات الأداء

### Rate Limiting:
```env
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

### قاعدة البيانات:
```env
DB_MAX_POOL_SIZE=10
DB_MIN_POOL_SIZE=1
DB_MAX_IDLE_TIME_MS=30000
```

## 🎛️ متغيرات الفرونت إند

### Socket.IO:
```env
VITE_SOCKET_URL=https://deshacoffee-production.up.railway.app
VITE_SOCKET_TIMEOUT=20000
VITE_SOCKET_RECONNECTION_ATTEMPTS=5
```

### التحديث التلقائي:
```env
VITE_AUTO_REFRESH_ENABLED=true
VITE_ORDERS_REFRESH_INTERVAL=3000
VITE_TABLES_REFRESH_INTERVAL=5000
```

### الإشعارات:
```env
VITE_NOTIFICATIONS_ENABLED=true
VITE_NOTIFICATION_DURATION=5000
VITE_MAX_NOTIFICATIONS=5
```

## 🛠️ إعداد البيئة المحلية

### 1. إنشاء ملف `.env` للفرونت إند:
```bash
cp .env.example .env
```

### 2. إنشاء ملف `.env` للباك إند:
```bash
cp backend/.env.example backend/.env
```

### 3. تحديث القيم:
- أدخل connection string لقاعدة البيانات الحقيقية
- أنشئ JWT secret قوي
- حدث URLs للتطوير المحلي

## 🚀 نشر الإنتاج

### Vercel (Frontend):
```env
VITE_API_URL=https://your-backend-domain.railway.app
NODE_ENV=production
```

### Railway (Backend):
```env
MONGODB_URI=mongodb+srv://...
JWT_SECRET=your-production-secret
NODE_ENV=production
PORT=4003
```

## 🔒 أفضل الممارسات الأمنية

### 1. حماية المفاتيح:
- لا تضع مفاتيح حقيقية في `.env.example`
- استخدم مفاتيح قوية ومعقدة
- غيّر المفاتيح دوريًا

### 2. إدارة الملفات:
- أضف `.env` إلى `.gitignore`
- احتفظ بنسخ احتياطية آمنة
- استخدم خدمات إدارة الأسرار للإنتاج

### 3. التحقق من التكوين:
- يتحقق النظام من وجود المتغيرات المطلوبة
- يتوقف التشغيل إذا كانت المتغيرات الحيوية مفقودة
- يُظهر تحذيرات للمتغيرات الاختيارية المفقودة

## 🐛 استكشاف الأخطاء

### رسائل الخطأ الشائعة:

#### 1. `MONGODB_URI is required`:
- تأكد من وجود متغير `MONGODB_URI` في ملف `.env`
- تحقق من صحة connection string

#### 2. `JWT_SECRET is required`:
- أضف متغير `JWT_SECRET` قوي
- تأكد من أنه طويل ومعقد

#### 3. `Cannot connect to MongoDB`:
- تحقق من صحة connection string
- تأكد من أن IP address مُدرج في whitelist
- تحقق من صحة credentials

## 📞 الدعم

إذا واجهت مشاكل في التكوين:
1. تحقق من وجود جميع المتغيرات المطلوبة
2. راجع رسائل الخطأ في console
3. تأكد من صحة القيم المُدخلة
4. قارن مع ملف `.env.example`

---

💡 **نصيحة**: استخدم أدوات مثل `dotenv-checker` للتحقق من اكتمال متغيرات البيئة قبل النشر.
