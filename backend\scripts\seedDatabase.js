const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const config = require('../config/environment');

// Import models
const User = require('../models/User');
const Category = require('../models/Category');
const Product = require('../models/Product');
const Order = require('../models/Order');

// Connect to database
const connectDB = async () => {
  try {
    await mongoose.connect(config.database.mongoUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error);
    process.exit(1);
  }
};

// Seed data
const seedData = async () => {
  try {
    console.log('🌱 Starting database seeding...');

    // Clear existing data
    await User.deleteMany({});
    await Category.deleteMany({});
    await Product.deleteMany({});
    await Order.deleteMany({});
    console.log('🗑️ Cleared existing data');

    // Create users - Real Production credentials
    const users = [
      {
        username: 'Beso',
        email: '<EMAIL>',
        password: 'MOHAMEDmostafa123',
        name: 'Beso - مدير عام',
        role: 'manager',
        status: 'active'
      },
      {
        username: 'azza',
        email: '<EMAIL>',
        password: '253040',
        name: 'عزة - نادل',
        role: 'waiter',
        status: 'active'
      },
      {
        username: 'khaled',
        email: '<EMAIL>',
        password: '253040',
        name: 'خالد - طباخ',
        role: 'chef',
        status: 'active'
      },
      {
        username: 'admin',
        email: '<EMAIL>',
        password: 'DeshaCoffee2024Admin!',
        name: 'مدير النظام',
        role: 'admin',
        status: 'active'
      },
      {
        username: 'manager',
        email: '<EMAIL>',
        password: 'DeshaCoffee2024Manager!',
        name: 'مدير المقهى',
        role: 'manager',
        status: 'active'
      },
      {
        username: 'employee',
        email: '<EMAIL>',
        password: 'DeshaCoffee2024Employee!',
        name: 'موظف المقهى',
        role: 'employee',
        status: 'active'
      },
      {
        username: 'waiter',
        email: '<EMAIL>',
        password: 'DeshaCoffee2024Waiter!',
        name: 'نادل المقهى',
        role: 'waiter',
        status: 'active'
      },
      {
        username: 'chef',
        email: '<EMAIL>',
        password: 'DeshaCoffee2024Chef!',
        name: 'طباخ المقهى',
        role: 'chef',
        status: 'active'
      }
    ];

    const createdUsers = await User.insertMany(users);
    console.log(`👥 Created ${createdUsers.length} users`);

    // Create categories
    const categories = [
      {
        name: 'مشروبات ساخنة',
        description: 'قهوة وشاي ومشروبات ساخنة متنوعة',
        icon: '☕',
        color: '#8B4513',
        active: true,
        featured: true,
        sortOrder: 1
      },
      {
        name: 'مشروبات باردة',
        description: 'عصائر طبيعية ومشروبات منعشة',
        icon: '🥤',
        color: '#4169E1',
        active: true,
        featured: true,
        sortOrder: 2
      },
      {
        name: 'مخبوزات',
        description: 'كرواسان وكعك ومعجنات طازجة',
        icon: '🥐',
        color: '#DAA520',
        active: true,
        featured: true,
        sortOrder: 3
      },
      {
        name: 'حلويات',
        description: 'كيك وحلويات شرقية وغربية',
        icon: '🍰',
        color: '#FF69B4',
        active: true,
        featured: false,
        sortOrder: 4
      },
      {
        name: 'وجبات خفيفة',
        description: 'ساندويتش وسلطات ووجبات سريعة',
        icon: '🥪',
        color: '#32CD32',
        active: true,
        featured: false,
        sortOrder: 5
      }
    ];

    const createdCategories = await Category.insertMany(categories);
    console.log(`📂 Created ${createdCategories.length} categories`);

    // Create products
    const products = [
      {
        name: 'قهوة عربية',
        description: 'قهوة عربية أصيلة محمصة طازجة مع الهيل',
        price: 15,
        category: createdCategories[0]._id,
        categoryName: 'مشروبات ساخنة',
        image: '/images/arabic-coffee.jpg',
        available: true,
        featured: true,
        preparationTime: 5,
        tags: ['قهوة', 'عربي', 'هيل'],
        stock: { quantity: 100, unit: 'cup' }
      },
      {
        name: 'كابتشينو',
        description: 'كابتشينو إيطالي كلاسيكي مع رغوة الحليب الكريمية',
        price: 20,
        category: createdCategories[0]._id,
        categoryName: 'مشروبات ساخنة',
        image: '/images/cappuccino.jpg',
        available: true,
        featured: true,
        preparationTime: 7,
        tags: ['قهوة', 'كابتشينو', 'حليب'],
        stock: { quantity: 80, unit: 'cup' }
      },
      {
        name: 'لاتيه',
        description: 'لاتيه كريمي مع الحليب المبخر وطبقة رقيقة من الرغوة',
        price: 22,
        category: createdCategories[0]._id,
        categoryName: 'مشروبات ساخنة',
        image: '/images/latte.jpg',
        available: true,
        featured: false,
        preparationTime: 8,
        tags: ['قهوة', 'لاتيه', 'حليب'],
        stock: { quantity: 75, unit: 'cup' }
      },
      {
        name: 'عصير برتقال طازج',
        description: 'عصير برتقال طبيعي 100% بدون إضافات',
        price: 12,
        category: createdCategories[1]._id,
        categoryName: 'مشروبات باردة',
        image: '/images/orange-juice.jpg',
        available: true,
        featured: true,
        preparationTime: 3,
        tags: ['عصير', 'برتقال', 'طازج'],
        stock: { quantity: 50, unit: 'glass' }
      },
      {
        name: 'كرواسان بالجبن',
        description: 'كرواسان فرنسي طازج محشو بالجبن الكريمي',
        price: 18,
        category: createdCategories[2]._id,
        categoryName: 'مخبوزات',
        image: '/images/cheese-croissant.jpg',
        available: true,
        featured: true,
        preparationTime: 10,
        tags: ['كرواسان', 'جبن', 'مخبوزات'],
        stock: { quantity: 30, unit: 'piece' }
      }
    ];

    const createdProducts = await Product.insertMany(products);
    console.log(`🍽️ Created ${createdProducts.length} products`);

    console.log('✅ Database seeding completed successfully!');
    console.log('\n📋 Real Production login credentials:');
    console.log('Manager: Beso / MOHAMEDmostafa123');
    console.log('Waiter: azza / 253040');
    console.log('Chef: khaled / 253040');
    console.log('Admin: admin / DeshaCoffee2024Admin!');
    console.log('Manager Alt: manager / DeshaCoffee2024Manager!');
    console.log('Employee: employee / DeshaCoffee2024Employee!');
    console.log('Waiter Alt: waiter / DeshaCoffee2024Waiter!');
    console.log('Chef Alt: chef / DeshaCoffee2024Chef!');

  } catch (error) {
    console.error('❌ Error seeding database:', error);
  }
};

// Run seeding
const runSeed = async () => {
  await connectDB();
  await seedData();
  await mongoose.connection.close();
  console.log('🔒 Database connection closed');
  process.exit(0);
};

// Check if this script is run directly
if (require.main === module) {
  runSeed();
}

module.exports = { seedData };
