# ==================================================
# DESHA COFFEE BACKEND - PRODUCTION ENVIRONMENT
# ==================================================
# ⚠️ Production environment variables for Railway deployment

# ==================================================
# SERVER CONFIGURATION
# ==================================================
PORT=4003
NODE_ENV=production
HOST=0.0.0.0

# ==================================================
# DATABASE CONFIGURATION - MongoDB Atlas Production
# ==================================================
MONGODB_URI=mongodb+srv://besomustafa:<EMAIL>/deshacoffee?retryWrites=true&w=majority&appName=MyCoffeChop
DB_MAX_POOL_SIZE=10
DB_MIN_POOL_SIZE=1
DB_MAX_IDLE_TIME_MS=30000

# ==================================================
# AUTHENTICATION & SECURITY - PRODUCTION
# ==================================================
JWT_SECRET=desha-coffee-super-secret-jwt-key-production-2024-arabic-cafe-system-v1.0
JWT_EXPIRES_IN=7d
BCRYPT_SALT_ROUNDS=12
SESSION_SECRET=desha-coffee-session-secret-key-2024-arabic-system

# ==================================================
# API & CORS CONFIGURATION - PRODUCTION URLS
# ==================================================
BACKEND_URL=https://deshacoffee-production.up.railway.app
FRONTEND_URL=https://desha-coffee.vercel.app
CORS_ORIGIN=https://desha-coffee.vercel.app
CORS_CREDENTIALS=true

# ==================================================
# RATE LIMITING - PRODUCTION SETTINGS
# ==================================================
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# ==================================================
# EMAIL CONFIGURATION (Optional)
# ==================================================
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_FROM=<EMAIL>

# ==================================================
# FILE UPLOAD CONFIGURATION
# ==================================================
MAX_FILE_SIZE=10485760
UPLOAD_PATH=uploads/
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf

# ==================================================
# LOGGING & MONITORING
# ==================================================
LOG_LEVEL=info
DEBUG=false

# ==================================================
# PRODUCTION FLAGS
# ==================================================
ENABLE_SEED_DATA=false
ENABLE_TEST_ROUTES=false
