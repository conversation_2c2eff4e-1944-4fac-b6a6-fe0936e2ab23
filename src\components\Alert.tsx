import { useState } from 'react';
import type { ReactNode } from 'react';
import './Alert.css';

interface AlertProps {
  children: ReactNode;
  type?: 'info' | 'success' | 'warning' | 'error';
  dismissible?: boolean;
  icon?: boolean;
  className?: string;
}

const Alert: React.FC<AlertProps> = ({
  children,
  type = 'info',
  dismissible = false,
  icon = true,
  className = ''
}) => {
  const [isVisible, setIsVisible] = useState(true);

  if (!isVisible) return null;

  const getIcon = () => {
    switch (type) {
      case 'success':
        return 'fa-circle-check';
      case 'warning':
        return 'fa-triangle-exclamation';
      case 'error':
        return 'fa-circle-exclamation';
      case 'info':
      default:
        return 'fa-circle-info';
    }
  };

  return (
    <div className={`alert alert-${type} ${className}`}>
      {icon && (
        <div className="alert-icon">
          <i className={`fas ${getIcon()}`}></i>
        </div>
      )}
      
      <div className="alert-content">
        {children}
      </div>
      
      {dismissible && (
        <button 
          className="alert-close" 
          onClick={() => setIsVisible(false)}
          aria-label="إغلاق التنبيه"
        >
          <i className="fas fa-times"></i>
        </button>
      )}
    </div>
  );
};

export default Alert;
