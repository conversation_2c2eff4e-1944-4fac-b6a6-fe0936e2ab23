#!/usr/bin/env node

/**
 * اختبار شامل للتأكد من استخدام البيانات الحقيقية
 * Production Data Verification Test
 * Railway Backend + Vercel Frontend + MongoDB Atlas
 */

import https from 'https';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class ProductionDataVerification {
    constructor() {
        this.results = {
            environment: {},
            backend: {},
            frontend: {},
            database: {},
            connectivity: {},
            realDataOnly: true,
            issues: []
        };
    }

    async verifyEnvironmentConfiguration() {
        console.log('🔍 التحقق من إعدادات البيئة...\n');
        
        try {
            // قراءة ملف .env
            const envPath = path.join(process.cwd(), '.env');
            const envContent = fs.readFileSync(envPath, 'utf8');
            
            // التحقق من استخدام Railway للـ backend
            const railwayBackend = envContent.includes('deshacoffee-production.up.railway.app');
            this.results.environment.railwayBackend = railwayBackend;
            
            // التحقق من استخدام Vercel للـ frontend
            const vercelFrontend = envContent.includes('desha-coffee.vercel.app');
            this.results.environment.vercelFrontend = vercelFrontend;
            
            // التحقق من استخدام MongoDB Atlas
            const mongoAtlas = envContent.includes('mongodb+srv://') && envContent.includes('mycoffechop.hpr7xnl.mongodb.net');
            this.results.environment.mongoAtlas = mongoAtlas;
            
            // التحقق من عدم وجود localhost
            const hasLocalhost = envContent.includes('localhost') || envContent.includes('127.0.0.1');
            this.results.environment.noLocalhost = !hasLocalhost;
            
            if (railwayBackend && vercelFrontend && mongoAtlas && !hasLocalhost) {
                console.log('✅ إعدادات البيئة تستخدم البيانات الحقيقية فقط');
                this.results.environment.status = 'SUCCESS';
            } else {
                console.log('❌ توجد مشاكل في إعدادات البيئة:');
                if (!railwayBackend) console.log('  - لا يستخدم Railway للـ backend');
                if (!vercelFrontend) console.log('  - لا يستخدم Vercel للـ frontend');
                if (!mongoAtlas) console.log('  - لا يستخدم MongoDB Atlas');
                if (hasLocalhost) console.log('  - يحتوي على إعدادات localhost');
                this.results.environment.status = 'FAILED';
                this.results.realDataOnly = false;
            }
            
        } catch (error) {
            console.log('❌ فشل في قراءة ملف .env:', error.message);
            this.results.environment.status = 'ERROR';
            this.results.realDataOnly = false;
        }
    }

    async verifyBackendConnectivity() {
        console.log('\n🔍 التحقق من اتصال Backend Railway...\n');
        
        return new Promise((resolve) => {
            const options = {
                hostname: 'deshacoffee-production.up.railway.app',
                path: '/health',
                method: 'GET',
                timeout: 10000
            };

            const req = https.request(options, (res) => {
                let data = '';
                res.on('data', (chunk) => data += chunk);
                res.on('end', () => {
                    if (res.statusCode === 200) {
                        console.log('✅ Railway Backend متصل ويعمل');
                        this.results.backend.status = 'CONNECTED';
                        this.results.backend.railway = true;
                        
                        try {
                            const response = JSON.parse(data);
                            this.results.backend.health = response;
                            console.log(`📊 حالة الخادم: ${response.status || 'OK'}`);
                            console.log(`🌐 البيئة: ${response.environment || 'production'}`);
                            console.log(`💾 قاعدة البيانات: ${response.database?.status || 'connected'}`);
                        } catch (e) {
                            console.log('📊 الخادم يعمل بشكل صحيح');
                        }
                    } else {
                        console.log(`❌ Railway Backend خطأ: ${res.statusCode}`);
                        this.results.backend.status = 'ERROR';
                        this.results.realDataOnly = false;
                    }
                    resolve();
                });
            });

            req.on('error', (error) => {
                console.log('❌ فشل في الاتصال بـ Railway Backend:', error.message);
                this.results.backend.status = 'FAILED';
                this.results.realDataOnly = false;
                resolve();
            });

            req.on('timeout', () => {
                console.log('❌ انتهت مهلة الاتصال بـ Railway Backend');
                this.results.backend.status = 'TIMEOUT';
                this.results.realDataOnly = false;
                req.destroy();
                resolve();
            });

            req.end();
        });
    }

    async verifyDatabaseConnection() {
        console.log('\n🔍 التحقق من اتصال MongoDB Atlas...\n');
        
        return new Promise((resolve) => {
            const options = {
                hostname: 'deshacoffee-production.up.railway.app',
                path: '/api/orders',
                method: 'GET',
                timeout: 10000,
                headers: {
                    'Accept': 'application/json'
                }
            };

            const req = https.request(options, (res) => {
                let data = '';
                res.on('data', (chunk) => data += chunk);
                res.on('end', () => {
                    if (res.statusCode === 200 || res.statusCode === 401) {
                        console.log('✅ MongoDB Atlas متصل ويعمل');
                        this.results.database.status = 'CONNECTED';
                        this.results.database.mongoAtlas = true;
                        
                        try {
                            const response = JSON.parse(data);
                            if (Array.isArray(response)) {
                                console.log(`📊 عدد الطلبات في قاعدة البيانات: ${response.length}`);
                                this.results.database.ordersCount = response.length;
                                this.results.database.hasRealData = response.length > 0;
                            }
                        } catch (e) {
                            console.log('📊 قاعدة البيانات تعمل (تحتاج مصادقة)');
                            this.results.database.requiresAuth = true;
                        }
                    } else {
                        console.log(`❌ MongoDB Atlas خطأ: ${res.statusCode}`);
                        this.results.database.status = 'ERROR';
                        this.results.realDataOnly = false;
                    }
                    resolve();
                });
            });

            req.on('error', (error) => {
                console.log('❌ فشل في الاتصال بـ MongoDB Atlas:', error.message);
                this.results.database.status = 'FAILED';
                this.results.realDataOnly = false;
                resolve();
            });

            req.on('timeout', () => {
                console.log('❌ انتهت مهلة الاتصال بـ MongoDB Atlas');
                this.results.database.status = 'TIMEOUT';
                this.results.realDataOnly = false;
                req.destroy();
                resolve();
            });

            req.end();
        });
    }

    async verifyFrontendConfiguration() {
        console.log('\n🔍 التحقق من إعدادات Frontend...\n');
        
        try {
            // التحقق من ملف app.config.ts
            const configPath = path.join(process.cwd(), 'src', 'config', 'app.config.ts');
            const configContent = fs.readFileSync(configPath, 'utf8');
            
            const usesRailway = configContent.includes('deshacoffee-production.up.railway.app');
            const noLocalhost = !configContent.includes('localhost') && !configContent.includes('127.0.0.1');
            
            this.results.frontend.usesRailway = usesRailway;
            this.results.frontend.noLocalhost = noLocalhost;
            
            // التحقق من ملف socket.ts
            const socketPath = path.join(process.cwd(), 'src', 'socket.ts');
            const socketContent = fs.readFileSync(socketPath, 'utf8');
            
            const socketUsesRailway = socketContent.includes('getSocketUrl');
            this.results.frontend.socketUsesRailway = socketUsesRailway;
            
            if (usesRailway && noLocalhost && socketUsesRailway) {
                console.log('✅ Frontend مُعد لاستخدام البيانات الحقيقية فقط');
                this.results.frontend.status = 'SUCCESS';
            } else {
                console.log('❌ مشاكل في إعدادات Frontend:');
                if (!usesRailway) console.log('  - لا يستخدم Railway API');
                if (!noLocalhost) console.log('  - يحتوي على localhost');
                if (!socketUsesRailway) console.log('  - Socket.IO لا يستخدم Railway');
                this.results.frontend.status = 'FAILED';
                this.results.realDataOnly = false;
            }
            
        } catch (error) {
            console.log('❌ فشل في التحقق من إعدادات Frontend:', error.message);
            this.results.frontend.status = 'ERROR';
            this.results.realDataOnly = false;
        }
    }

    async verifyChefDashboardConfiguration() {
        console.log('\n🔍 التحقق من إعدادات لوحة الطباخ...\n');
        
        try {
            const chefDashboardPath = path.join(process.cwd(), 'src', 'ChefDashboard.tsx');
            const chefDashboardContent = fs.readFileSync(chefDashboardPath, 'utf8');
            
            // التحقق من استخدام البيانات الحقيقية
            const usesRealAPI = chefDashboardContent.includes('authenticatedGet') && 
                              chefDashboardContent.includes('authenticatedPut');
            
            const usesRealSocket = chefDashboardContent.includes('socket.emit') && 
                                  chefDashboardContent.includes('socket.on');
            
            const hasChefWorkflow = chefDashboardContent.includes('takeOrder') && 
                                   chefDashboardContent.includes('completeOrder');
            
            const usesRealData = chefDashboardContent.includes('convertToChefOrder') &&
                               chefDashboardContent.includes('ChefOrder');
            
            this.results.chefDashboard = {
                usesRealAPI,
                usesRealSocket,
                hasChefWorkflow,
                usesRealData,
                status: usesRealAPI && usesRealSocket && hasChefWorkflow && usesRealData ? 'SUCCESS' : 'FAILED'
            };
            
            if (this.results.chefDashboard.status === 'SUCCESS') {
                console.log('✅ لوحة الطباخ مُعدة لاستخدام البيانات الحقيقية');
            } else {
                console.log('❌ مشاكل في إعدادات لوحة الطباخ');
                this.results.realDataOnly = false;
            }
            
        } catch (error) {
            console.log('❌ فشل في التحقق من لوحة الطباخ:', error.message);
            this.results.chefDashboard = { status: 'ERROR' };
            this.results.realDataOnly = false;
        }
    }

    generateReport() {
        console.log('\n' + '='.repeat(60));
        console.log('📊 تقرير التحقق من البيانات الحقيقية');
        console.log('='.repeat(60));
        
        const sections = [
            { name: 'إعدادات البيئة', result: this.results.environment },
            { name: 'Backend Railway', result: this.results.backend },
            { name: 'قاعدة البيانات MongoDB Atlas', result: this.results.database },
            { name: 'Frontend Configuration', result: this.results.frontend },
            { name: 'لوحة الطباخ', result: this.results.chefDashboard }
        ];
        
        sections.forEach(section => {
            const status = section.result.status;
            const icon = status === 'SUCCESS' ? '✅' : status === 'FAILED' ? '❌' : '⚠️';
            console.log(`${icon} ${section.name}: ${status || 'UNKNOWN'}`);
        });
        
        console.log('\n📈 النتيجة الإجمالية:');
        if (this.results.realDataOnly) {
            console.log('🎉 النظام يستخدم البيانات الحقيقية فقط!');
            console.log('✅ Railway Backend + Vercel Frontend + MongoDB Atlas');
        } else {
            console.log('⚠️ النظام يحتاج إلى تعديلات لاستخدام البيانات الحقيقية فقط');
        }
        
        console.log('\n📋 التفاصيل:');
        console.log(`🔗 Backend URL: ${this.results.environment.railwayBackend ? 'Railway ✅' : 'Local ❌'}`);
        console.log(`🌐 Frontend URL: ${this.results.environment.vercelFrontend ? 'Vercel ✅' : 'Local ❌'}`);
        console.log(`💾 Database: ${this.results.environment.mongoAtlas ? 'MongoDB Atlas ✅' : 'Local ❌'}`);
        console.log(`🚫 No Localhost: ${this.results.environment.noLocalhost ? 'Yes ✅' : 'Found ❌'}`);
        
        // حفظ التقرير
        this.saveReport();
    }

    async saveReport() {
        const reportData = {
            timestamp: new Date().toISOString(),
            summary: {
                realDataOnly: this.results.realDataOnly,
                backend: 'Railway',
                frontend: 'Vercel',
                database: 'MongoDB Atlas',
                status: this.results.realDataOnly ? 'PRODUCTION_READY' : 'NEEDS_CONFIGURATION'
            },
            details: this.results,
            recommendations: this.results.realDataOnly ? [
                'النظام جاهز للإنتاج',
                'جميع الخدمات تستخدم البيانات الحقيقية',
                'لوحة الطباخ جاهزة للاستخدام'
            ] : [
                'تحديث ملف .env لاستخدام البيانات الحقيقية فقط',
                'إزالة جميع مراجع localhost',
                'التأكد من استخدام Railway للـ backend',
                'التأكد من استخدام MongoDB Atlas'
            ]
        };
        
        try {
            fs.writeFileSync('production-data-verification.json', JSON.stringify(reportData, null, 2));
            console.log('\n💾 تم حفظ التقرير في production-data-verification.json');
        } catch (error) {
            console.log('\n⚠️ فشل في حفظ التقرير:', error.message);
        }
    }

    async runFullVerification() {
        console.log('🔍 بدء التحقق الشامل من استخدام البيانات الحقيقية\n');
        
        await this.verifyEnvironmentConfiguration();
        await this.verifyBackendConnectivity();
        await this.verifyDatabaseConnection();
        await this.verifyFrontendConfiguration();
        await this.verifyChefDashboardConfiguration();
        
        this.generateReport();
    }
}

// تشغيل التحقق
const verifier = new ProductionDataVerification();
verifier.runFullVerification().catch(console.error);
