// خدمة إدارة البيانات للوحة الطباخ
import { authenticatedGet, authenticatedPost, authenticatedPut, authenticatedDelete } from '../utils/apiHelpers';
import type { 
  ChefOrder, 
  ChefNotification,
  OrderUpdateData,
  ChefOrderFilter
} from '../models/ChefModels';

export class ChefDataService {
  private static instance: ChefDataService;

  // Singleton pattern
  static getInstance(): ChefDataService {
    if (!ChefDataService.instance) {
      ChefDataService.instance = new ChefDataService();
    }
    return ChefDataService.instance;
  }

  private constructor() {}

  // جلب جميع الطلبات
  async getAllOrders(): Promise<ChefOrder[]> {
    return ChefDataService.fetchAllOrders();
  }

  // تحديث حالة الطلب
  async updateOrderStatus(orderData: OrderUpdateData): Promise<boolean> {
    const result = await ChefDataService.updateOrder(orderData);
    return result.success;
  }

  // قبول الطلب
  async acceptOrder(orderId: string, chefId: string, chefName: string): Promise<boolean> {
    const result = await ChefDataService.acceptOrderForPreparation(orderId, chefId, chefName);
    return result.success;
  }

  // جلب الإشعارات
  async getNotifications(chefId: string): Promise<ChefNotification[]> {
    return ChefDataService.fetchChefNotifications(chefId);
  }

  // تحديد الإشعار كمقروء
  async markNotificationAsRead(notificationId: string): Promise<boolean> {
    const result = await ChefDataService.markNotificationAsRead(notificationId);
    return result.success;
  }

  // جلب الطلبات (alias for getAllOrders)
  static async getOrders(): Promise<ChefOrder[]> {
    return this.fetchAllOrders();
  }

  // جلب الإحصائيات
  static async getStats(): Promise<any> {
    try {
      const orders = await this.getOrders();
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      const todayOrders = orders.filter(order => new Date(order.createdAt) >= today);
      
      return {
        todayOrders: todayOrders.length,
        completedOrders: orders.filter(o => o.status === 'completed').length,
        pendingOrders: orders.filter(o => o.status === 'pending').length,
        preparingOrders: orders.filter(o => o.status === 'preparing').length,
        readyOrders: orders.filter(o => o.status === 'ready').length,
        averagePreparationTime: 15, // يمكن حسابه بدقة أكبر لاحقاً
        totalPreparationTime: 0,
        ordersPerHour: todayOrders.length / Math.max(1, (new Date().getHours() || 1))
      };
    } catch (error) {
      console.error('❌ خطأ في جلب الإحصائيات:', error);
      return {
        todayOrders: 0,
        completedOrders: 0,
        pendingOrders: 0,
        preparingOrders: 0,
        readyOrders: 0,
        averagePreparationTime: 0,
        totalPreparationTime: 0,
        ordersPerHour: 0
      };
    }
  }

  // جلب الإشعارات (alias)
  static async getNotifications(chefId?: string): Promise<ChefNotification[]> {
    if (chefId) {
      return this.fetchChefNotifications(chefId);
    }
    // إذا لم يتم تمرير chefId، نجلب جميع الإشعارات
    return [];
  }

  // تحديث حالة الطلب (مبسط)
  static async updateOrderStatus(orderId: string, newStatus: string): Promise<boolean> {
    try {
      const response = await authenticatedPut(`/api/orders/${orderId}`, {
        status: newStatus,
        updatedAt: new Date().toISOString()
      });

      return response.success || response.status === 'success' || response.message === 'Order updated successfully';
    } catch (error) {
      console.error('❌ خطأ في تحديث حالة الطلب:', error);
      return false;
    }
  }

  // حذف الإشعار
  static async deleteNotification(notificationId: string): Promise<boolean> {
    try {
      const response = await authenticatedDelete(`/api/notifications/${notificationId}`);
      return response.success || true; // نفترض النجاح إذا لم يكن هناك خطأ
    } catch (error) {
      console.error('❌ خطأ في حذف الإشعار:', error);
      return false;
    }
  }

  // تحديد جميع الإشعارات كمقروءة
  static async markAllNotificationsAsRead(chefId?: string): Promise<boolean> {
    try {
      const endpoint = chefId ? `/api/notifications/chef/${chefId}/mark-all-read` : '/api/notifications/mark-all-read';
      const response = await authenticatedPut(endpoint, {
        isRead: true,
        readAt: new Date().toISOString()
      });

      return response.success || true;
    } catch (error) {
      console.error('❌ خطأ في تحديد جميع الإشعارات كمقروءة:', error);
      return false;
    }
  }

  // جلب جميع الطلبات
  static async fetchAllOrders(): Promise<ChefOrder[]> {
    try {
      const response = await authenticatedGet('/api/orders');
      console.log('📊 استجابة API للطلبات (الطباخ):', response);

      let ordersData: any[] = [];
      if (Array.isArray(response)) {
        ordersData = response;
      } else if (response.success && Array.isArray(response.data)) {
        ordersData = response.data;
      } else if (response.data && Array.isArray(response.data)) {
        ordersData = response.data;
      }

      console.log('✅ تم جلب الطلبات للطباخ:', ordersData.length, 'طلب');
      return ordersData;
    } catch (error) {
      console.error('❌ خطأ في جلب الطلبات:', error);
      throw new Error('فشل في جلب الطلبات');
    }
  }

  // تحديث حالة الطلب
  static async updateOrder(orderData: OrderUpdateData): Promise<{ success: boolean; message?: string }> {
    try {
      console.log('🔄 تحديث حالة الطلب:', orderData);

      const response = await authenticatedPut(`/api/orders/${orderData.orderId}`, {
        status: orderData.status,
        chefName: orderData.chefName,
        chefId: orderData.chefId,
        preparationTime: orderData.preparationTime,
        notes: orderData.notes,
        updatedAt: new Date().toISOString()
      });

      if (response.success || response.status === 'success' || response.message === 'Order updated successfully') {
        console.log('✅ تم تحديث حالة الطلب بنجاح');
        return { success: true };
      } else {
        console.error('❌ فشل في تحديث حالة الطلب:', response);
        return { success: false, message: response.message || 'فشل في تحديث حالة الطلب' };
      }
    } catch (error) {
      console.error('❌ خطأ في تحديث حالة الطلب:', error);
      return { success: false, message: 'فشل في تحديث حالة الطلب' };
    }
  }

  // قبول الطلب للتحضير
  static async acceptOrderForPreparation(orderId: string, chefId: string, chefName: string): Promise<{ success: boolean; message?: string }> {
    try {
      console.log('👨‍🍳 قبول الطلب للتحضير:', { orderId, chefId, chefName });

      const response = await authenticatedPut(`/api/orders/${orderId}`, {
        status: 'preparing',
        chefId: chefId,
        chefName: chefName,
        updatedAt: new Date().toISOString()
      });

      if (response.success || response.status === 'success' || response.message === 'Order updated successfully') {
        console.log('✅ تم قبول الطلب للتحضير بنجاح');
        return { success: true };
      } else {
        console.error('❌ فشل في قبول الطلب:', response);
        return { success: false, message: response.message || 'فشل في قبول الطلب' };
      }
    } catch (error) {
      console.error('❌ خطأ في قبول الطلب:', error);
      return { success: false, message: 'فشل في قبول الطلب' };
    }
  }

  // تحديد الطلب كجاهز
  static async markOrderAsReady(orderId: string, chefId: string, chefName: string, preparationTime?: number): Promise<{ success: boolean; message?: string }> {
    try {
      console.log('✅ تحديد الطلب كجاهز:', { orderId, chefId, chefName, preparationTime });

      const response = await authenticatedPut(`/api/orders/${orderId}`, {
        status: 'ready',
        chefId: chefId,
        chefName: chefName,
        preparationTime: preparationTime,
        updatedAt: new Date().toISOString()
      });

      if (response.success || response.status === 'success' || response.message === 'Order updated successfully') {
        console.log('✅ تم تحديد الطلب كجاهز بنجاح');
        return { success: true };
      } else {
        console.error('❌ فشل في تحديد الطلب كجاهز:', response);
        return { success: false, message: response.message || 'فشل في تحديد الطلب كجاهز' };
      }
    } catch (error) {
      console.error('❌ خطأ في تحديد الطلب كجاهز:', error);
      return { success: false, message: 'فشل في تحديد الطلب كجاهز' };
    }
  }

  // إرسال إشعار للنادل
  static async notifyWaiter(orderId: string, orderNumber: string, waiterName: string, message: string): Promise<{ success: boolean }> {
    try {
      console.log('🔔 إرسال إشعار للنادل:', { orderId, orderNumber, waiterName, message });

      const response = await authenticatedPost('/api/notifications', {
        type: 'order_ready',
        title: 'طلب جاهز',
        message: message,
        orderId: orderId,
        orderNumber: orderNumber,
        recipientType: 'waiter',
        recipientName: waiterName,
        createdAt: new Date().toISOString()
      });

      if (response.success) {
        console.log('✅ تم إرسال الإشعار للنادل');
        return { success: true };
      } else {
        console.log('⚠️ لا يوجد نظام إشعارات، سيتم استخدام Socket');
        return { success: true }; // نعتبرها ناجحة حتى لو لم يوجد API
      }
    } catch (error) {
      console.log('⚠️ خطأ في إرسال الإشعار، سيتم استخدام Socket:', error);
      return { success: true }; // نعتبرها ناجحة ونعتمد على Socket
    }
  }

  // جلب إشعارات الطباخ
  static async fetchChefNotifications(chefId: string): Promise<ChefNotification[]> {
    try {
      const response = await authenticatedGet(`/api/notifications/chef/${chefId}`);
      
      let notificationsData: any[] = [];
      if (Array.isArray(response)) {
        notificationsData = response;
      } else if (response.success && Array.isArray(response.data)) {
        notificationsData = response.data;
      }

      console.log('🔔 تم جلب إشعارات الطباخ:', notificationsData.length);
      return notificationsData;
    } catch (error) {
      console.error('❌ خطأ في جلب الإشعارات:', error);
      return [];
    }
  }

  // تحديد الإشعار كمقروء
  static async markNotificationAsRead(notificationId: string): Promise<{ success: boolean }> {
    try {
      const response = await authenticatedPut(`/api/notifications/${notificationId}`, {
        isRead: true,
        readAt: new Date().toISOString()
      });

      if (response.success) {
        console.log('✅ تم تحديد الإشعار كمقروء');
        return { success: true };
      } else {
        console.log('⚠️ فشل في تحديد الإشعار كمقروء');
        return { success: false };
      }
    } catch (error) {
      console.error('❌ خطأ في تحديد الإشعار كمقروء:', error);
      return { success: false };
    }
  }

  // فلترة الطلبات
  static filterOrders(orders: ChefOrder[], filter: ChefOrderFilter): ChefOrder[] {
    let filtered = [...orders];

    // فلتر الحالة
    if (filter.status !== 'all') {
      filtered = filtered.filter(order => order.status === filter.status);
    }

    // فلتر الأولوية
    if (filter.priority !== 'all') {
      filtered = filtered.filter(order => order.priority === filter.priority);
    }

    // فلتر الوقت
    if (filter.timeRange !== 'all') {
      const now = new Date();
      let cutoffTime: Date;

      switch (filter.timeRange) {
        case 'today':
          cutoffTime = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          break;
        case 'last_hour':
          cutoffTime = new Date(now.getTime() - 60 * 60 * 1000);
          break;
        case 'last_3_hours':
          cutoffTime = new Date(now.getTime() - 3 * 60 * 60 * 1000);
          break;
        default:
          cutoffTime = new Date(0);
      }

      filtered = filtered.filter(order => new Date(order.createdAt) >= cutoffTime);
    }

    // فلتر البحث
    if (filter.searchTerm) {
      const searchTerm = filter.searchTerm.toLowerCase();
      filtered = filtered.filter(order =>
        order.orderNumber.toLowerCase().includes(searchTerm) ||
        order.customerName?.toLowerCase().includes(searchTerm) ||
        order.waiterName.toLowerCase().includes(searchTerm) ||
        order.tableNumber.toString().includes(searchTerm)
      );
    }

    return filtered;
  }

  // حساب الأولوية التلقائية
  static calculateOrderPriority(order: ChefOrder): 'low' | 'normal' | 'high' | 'urgent' {
    const now = new Date();
    const orderTime = new Date(order.createdAt);
    const minutesOld = (now.getTime() - orderTime.getTime()) / (1000 * 60);

    // طلبات أكثر من 30 دقيقة = عاجل
    if (minutesOld > 30) return 'urgent';
    
    // طلبات أكثر من 20 دقيقة = عالي
    if (minutesOld > 20) return 'high';
    
    // طلبات أكثر من 10 دقائق = عادي
    if (minutesOld > 10) return 'normal';
    
    // طلبات جديدة = منخفض
    return 'low';
  }
}
