import React, { useState, useEffect } from 'react';
import { useChefState, KitchenView, OrdersView, StatsView, NotificationsView } from './components/chef';
import { ChefDataService } from './services/ChefDataService';
import { useToast } from './hooks/useToast';
import { notificationSound } from './utils/notificationSound';
import socket from './socket';
import type { ChefOrder } from './models/ChefModels';
import './ChefDashboard.css';

interface ChefDashboardNewProps {
  user?: any;
  onLogout?: () => void;
}

type ViewType = 'kitchen' | 'orders' | 'stats' | 'notifications';
type FilterType = 'all' | 'pending' | 'preparing' | 'ready' | 'completed';

export default function ChefDashboardNew({ user: propUser, onLogout }: ChefDashboardNewProps) {
  const [currentView, setCurrentView] = useState<ViewType>('kitchen');
  const [currentFilter, setCurrentFilter] = useState<FilterType>('all');
  const [selectedOrder, setSelectedOrder] = useState<ChefOrder | null>(null);
  const [showOrderModal, setShowOrderModal] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [user, setUser] = useState(propUser);

  const { showSuccess, showError } = useToast();
  
  // استخدام hook إدارة الحالة
  const {
    orders,
    stats,
    notifications,
    loading,
    updateOrders,
    updateStats,
    updateNotifications,
    markNotificationAsRead,
    markAllNotificationsAsRead,
    getFilteredOrders,
    updateFilter
  } = useChefState();

  // فلترة الطلبات حسب الفلتر الحالي
  const getFilteredOrdersByStatus = () => {
    if (currentFilter === 'all') {
      return orders;
    }
    return orders.filter(order => order.status === currentFilter);
  };

  // تحديث البيانات
  const refreshData = async () => {
    try {
      const [ordersData, statsData, notificationsData] = await Promise.all([
        ChefDataService.getOrders(),
        ChefDataService.getStats(),
        ChefDataService.getNotifications()
      ]);

      updateOrders(ordersData);
      updateStats(statsData);
      updateNotifications(notificationsData);
    } catch (error) {
      console.error('خطأ في تحديث البيانات:', error);
    }
  };

  // تحميل البيانات عند البدء
  useEffect(() => {
    if (user) {
      refreshData();
    }
  }, [user]);

  // إعداد Socket.IO للإشعارات
  useEffect(() => {
    if (!user) return;

    console.log('إعداد Socket.IO للطباخ:', user.name);

    // الاستماع للطلبات الجديدة
    socket.on('new-order-notification', (data) => {
      console.log('إشعار طلب جديد للطباخ:', data);
      notificationSound.playNotification();
      showSuccess(`طلب جديد: ${data.orderNumber}`);
      refreshData();
    });

    // الاستماع لتحديثات الطلبات
    socket.on('order-status-updated', (data) => {
      console.log('تحديث حالة الطلب:', data);
      if (data.status === 'cancelled') {
        notificationSound.playNotification();
        showError(`تم إلغاء الطلب: ${data.orderNumber}`);
      }
      refreshData();
    });

    // تنظيف المستمعين عند الخروج
    return () => {
      socket.off('new-order-notification');
      socket.off('order-status-updated');
    };
  }, [user, showSuccess, showError]);

  // معالجة تحديث حالة الطلب
  const handleUpdateOrderStatus = async (orderId: string, newStatus: string) => {
    try {
      await ChefDataService.updateOrderStatus(orderId, newStatus);
      
      // إشعار النادل بالتحديث
      socket.emit('order-status-update', {
        orderId,
        status: newStatus,
        chefName: user?.name
      });

      showSuccess(`تم تحديث حالة الطلب إلى: ${getStatusText(newStatus)}`);
      
      // تشغيل الصوت للحالات المهمة
      if (newStatus === 'ready') {
        notificationSound.playNotification();
      }

      // تحديث البيانات
      refreshData();
    } catch (error) {
      console.error('خطأ في تحديث حالة الطلب:', error);
      showError('فشل في تحديث حالة الطلب');
    }
  };

  // معالجة حذف الإشعار
  const handleDeleteNotification = async (notificationId: string) => {
    try {
      await ChefDataService.deleteNotification(notificationId);
      refreshData();
      showSuccess('تم حذف الإشعار');
    } catch (error) {
      console.error('خطأ في حذف الإشعار:', error);
      showError('فشل في حذف الإشعار');
    }
  };

  // معالجة تحديد جميع الإشعارات كمقروءة
  const handleMarkAllAsRead = async () => {
    try {
      await ChefDataService.markAllNotificationsAsRead();
      markAllNotificationsAsRead();
      showSuccess('تم تحديد جميع الإشعارات كمقروءة');
    } catch (error) {
      console.error('خطأ في تحديد الإشعارات كمقروءة:', error);
      showError('فشل في تحديد الإشعارات كمقروءة');
    }
  };

  // معالجة تحديد إشعار كمقروء
  const handleMarkAsRead = async (notificationId: string) => {
    try {
      await ChefDataService.markNotificationAsRead(notificationId);
      markNotificationAsRead(notificationId);
    } catch (error) {
      console.error('خطأ في تحديد الإشعار كمقروء:', error);
    }
  };

  // معالجة عرض تفاصيل الطلب
  const handleSelectOrder = (order: ChefOrder) => {
    setSelectedOrder(order);
    setShowOrderModal(true);
  };

  // معالجة إغلاق نافذة الطلب
  const handleCloseOrderModal = () => {
    setSelectedOrder(null);
    setShowOrderModal(false);
  };

  // الحصول على نص الحالة
  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'في الانتظار';
      case 'preparing': return 'قيد التحضير';
      case 'ready': return 'جاهز';
      case 'completed': return 'مكتمل';
      case 'delivered': return 'تم التسليم';
      default: return status;
    }
  };

  // معالجة تسجيل الخروج
  const handleLogout = () => {
    if (onLogout) {
      onLogout();
    }
  };

  return (
    <div className="chef-dashboard">
      {/* شريط علوي */}
      <header className="dashboard-header">
        <div className="header-left">
          <button
            className="sidebar-toggle"
            onClick={() => setSidebarOpen(!sidebarOpen)}
          >
            ☰
          </button>
          <h1>لوحة الطباخ</h1>
        </div>
        
        <div className="header-center">
          {/* فلاتر سريعة */}
          <div className="quick-filters">
            <button
              className={`filter-btn ${currentFilter === 'all' ? 'active' : ''}`}
              onClick={() => setCurrentFilter('all')}
            >
              الكل ({orders.length})
            </button>
            <button
              className={`filter-btn ${currentFilter === 'pending' ? 'active' : ''}`}
              onClick={() => setCurrentFilter('pending')}
            >
              في الانتظار ({orders.filter(o => o.status === 'pending').length})
            </button>
            <button
              className={`filter-btn ${currentFilter === 'preparing' ? 'active' : ''}`}
              onClick={() => setCurrentFilter('preparing')}
            >
              قيد التحضير ({orders.filter(o => o.status === 'preparing').length})
            </button>
            <button
              className={`filter-btn ${currentFilter === 'ready' ? 'active' : ''}`}
              onClick={() => setCurrentFilter('ready')}
            >
              جاهز ({orders.filter(o => o.status === 'ready').length})
            </button>
          </div>
        </div>

        <div className="header-right">
          <div className="user-info">
            <span>أهلاً، {user?.name}</span>
            <button onClick={handleLogout} className="btn btn-outline">
              تسجيل خروج
            </button>
          </div>
        </div>
      </header>

      <div className="dashboard-content">
        {/* الشريط الجانبي */}
        <nav className={`sidebar ${sidebarOpen ? 'open' : ''}`}>
          <div className="sidebar-content">
            <div className="nav-section">
              <h3>القوائم الرئيسية</h3>
              <ul className="nav-list">
                <li>
                  <button
                    className={`nav-item ${currentView === 'kitchen' ? 'active' : ''}`}
                    onClick={() => setCurrentView('kitchen')}
                  >
                    🍳 المطبخ
                  </button>
                </li>
                <li>
                  <button
                    className={`nav-item ${currentView === 'orders' ? 'active' : ''}`}
                    onClick={() => setCurrentView('orders')}
                  >
                    📋 الطلبات
                  </button>
                </li>
                <li>
                  <button
                    className={`nav-item ${currentView === 'stats' ? 'active' : ''}`}
                    onClick={() => setCurrentView('stats')}
                  >
                    📊 الإحصائيات
                  </button>
                </li>
                <li>
                  <button
                    className={`nav-item ${currentView === 'notifications' ? 'active' : ''}`}
                    onClick={() => setCurrentView('notifications')}
                  >
                    🔔 الإشعارات
                    {notifications.filter(n => !n.isRead).length > 0 && (
                      <span className="notification-badge">
                        {notifications.filter(n => !n.isRead).length}
                      </span>
                    )}
                  </button>
                </li>
              </ul>
            </div>
          </div>
        </nav>

        {/* المحتوى الرئيسي */}
        <main className="main-content">
          {currentView === 'kitchen' && (
            <KitchenView
              orders={getFilteredOrdersByStatus()}
              onUpdateOrderStatus={handleUpdateOrderStatus}
              onSelectOrder={handleSelectOrder}
              loading={loading}
            />
          )}

          {currentView === 'orders' && (
            <OrdersView
              orders={getFilteredOrdersByStatus()}
              onUpdateOrderStatus={handleUpdateOrderStatus}
              onSelectOrder={handleSelectOrder}
              loading={loading}
              currentFilter={{ status: currentFilter }}
              onFilterChange={(key, value) => {
                if (key === 'status' && typeof value === 'string') {
                  setCurrentFilter(value as FilterType);
                }
              }}
            />
          )}

          {currentView === 'stats' && (
            <StatsView
              stats={stats}
              loading={loading}
            />
          )}

          {currentView === 'notifications' && (
            <NotificationsView
              notifications={notifications}
              onMarkAsRead={handleMarkAsRead}
              onMarkAllAsRead={handleMarkAllAsRead}
              onDeleteNotification={handleDeleteNotification}
              loading={loading}
            />
          )}
        </main>
      </div>

      {/* نافذة تفاصيل الطلب */}
      {showOrderModal && selectedOrder && (
        <div className="modal-overlay" onClick={handleCloseOrderModal}>
          <div className="modal-content order-modal" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>تفاصيل الطلب #{selectedOrder.orderNumber}</h2>
              <button
                className="modal-close"
                onClick={handleCloseOrderModal}
              >
                ✕
              </button>
            </div>

            <div className="modal-body">
              <div className="order-details">
                <div className="order-info-grid">
                  <div className="info-item">
                    <strong>طاولة:</strong> {selectedOrder.tableNumber}
                  </div>
                  <div className="info-item">
                    <strong>النادل:</strong> {selectedOrder.waiterName}
                  </div>
                  <div className="info-item">
                    <strong>الحالة:</strong> {getStatusText(selectedOrder.status)}
                  </div>
                  <div className="info-item">
                    <strong>وقت الطلب:</strong> {new Date(selectedOrder.createdAt).toLocaleString('ar-EG')}
                  </div>
                </div>

                {selectedOrder.notes && (
                  <div className="order-notes">
                    <strong>ملاحظات:</strong>
                    <p>{selectedOrder.notes}</p>
                  </div>
                )}

                <div className="order-items">
                  <h3>العناصر:</h3>
                  <div className="items-list">
                    {selectedOrder.items.map((item, index) => (
                      <div key={index} className="item-card">
                        <div className="item-info">
                          <h4>{item.name}</h4>
                          <span className="item-quantity">الكمية: {item.quantity}</span>
                          <span className="item-price">{item.price} جنيه</span>
                        </div>
                        {item.notes && (
                          <div className="item-notes">
                            <strong>ملاحظات:</strong> {item.notes}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                <div className="order-total">
                  <strong>المجموع: {selectedOrder.totalAmount} جنيه</strong>
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <div className="order-actions">
                {selectedOrder.status === 'pending' && (
                  <button
                    className="btn btn-primary"
                    onClick={() => {
                      handleUpdateOrderStatus(selectedOrder._id, 'preparing');
                      handleCloseOrderModal();
                    }}
                  >
                    بدء التحضير
                  </button>
                )}
                {selectedOrder.status === 'preparing' && (
                  <button
                    className="btn btn-success"
                    onClick={() => {
                      handleUpdateOrderStatus(selectedOrder._id, 'ready');
                      handleCloseOrderModal();
                    }}
                  >
                    جاهز
                  </button>
                )}
                {selectedOrder.status === 'ready' && (
                  <button
                    className="btn btn-complete"
                    onClick={() => {
                      handleUpdateOrderStatus(selectedOrder._id, 'completed');
                      handleCloseOrderModal();
                    }}
                  >
                    مكتمل
                  </button>
                )}
                <button
                  className="btn btn-secondary"
                  onClick={handleCloseOrderModal}
                >
                  إغلاق
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
