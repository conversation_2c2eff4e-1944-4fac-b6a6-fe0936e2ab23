import React, { useState, useEffect } from 'react';
import { authenticatedGet, authenticatedPost, authenticatedPut } from '../../utils/apiHelpers';
import { useToast } from '../../hooks/useToast';
import socket from '../../socket';
import './TablesManager.css';

interface Table {
  _id: string;
  tableNumber: string;
  capacity: number;
  status: 'available' | 'occupied' | 'reserved' | 'maintenance';
  waiterName?: string;
  customerName?: string;
  orders: Array<{
    _id: string;
    orderNumber: string;
    totalPrice: number;
    status: string;
    createdAt: string;
  }>;
  totalAmount: number;
  openedAt?: string;
  lastActivity?: string;
  closeRequests?: Array<{
    requestedBy: string;
    requestedAt: string;
    reason: string;
    status: 'pending' | 'approved' | 'rejected';
  }>;
}

interface TablesManagerProps {
  userRole: 'waiter' | 'chef' | 'manager';
  userName: string;
}

const TablesManager: React.FC<TablesManagerProps> = ({ userRole, userName }) => {
  const [tables, setTables] = useState<Table[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedTable, setSelectedTable] = useState<Table | null>(null);
  const [showTableModal, setShowTableModal] = useState(false);
  const [showCloseRequestModal, setShowCloseRequestModal] = useState(false);
  const [closeReason, setCloseReason] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [filterStatus, setFilterStatus] = useState<string>('all');

  const { showSuccess, showError, showInfo } = useToast();

  // جلب الطاولات
  const fetchTables = async () => {
    try {
      setLoading(true);
      const response = await authenticatedGet('/api/table-accounts');
      const tablesData = response.data || [];
      
      // فلترة الطاولات حسب الدور
      let filteredTables = tablesData;
      if (userRole === 'waiter') {
        filteredTables = tablesData.filter((table: Table) => 
          table.waiterName === userName || table.status === 'available'
        );
      }
      
      setTables(filteredTables);
    } catch (error) {
      console.error('خطأ في جلب الطاولات:', error);
      showError('فشل في تحميل الطاولات');
    } finally {
      setLoading(false);
    }
  };

  // فتح طاولة
  const openTable = async (tableNumber: string, customerName: string) => {
    try {
      const tableData = {
        tableNumber,
        customerName,
        waiterName: userName,
        status: 'occupied',
        openedAt: new Date().toISOString(),
        totalAmount: 0,
        orders: []
      };

      const response = await authenticatedPost('/api/table-accounts', tableData);
      
      if (response.success) {
        showSuccess(`تم فتح الطاولة ${tableNumber} بنجاح`);
        
        // إرسال إشعار Socket.IO
        if (socket) {
          socket.emit('table-opened', {
            tableNumber,
            customerName,
            waiterName: userName,
            openedAt: tableData.openedAt
          });
        }
        
        fetchTables();
        setShowTableModal(false);
      }
    } catch (error) {
      console.error('خطأ في فتح الطاولة:', error);
      showError('فشل في فتح الطاولة');
    }
  };

  // طلب إغلاق طاولة
  const requestCloseTable = async (table: Table) => {
    try {
      const closeRequest = {
        requestedBy: userName,
        requestedAt: new Date().toISOString(),
        reason: closeReason,
        status: 'pending'
      };

      const response = await authenticatedPut(`/api/table-accounts/${table._id}/close-request`, {
        closeRequest
      });
      
      if (response.success) {
        showSuccess('تم إرسال طلب إغلاق الطاولة');
        
        // إرسال إشعار Socket.IO
        if (socket) {
          socket.emit('table-close-requested', {
            tableId: table._id,
            tableNumber: table.tableNumber,
            requestedBy: userName,
            reason: closeReason
          });
        }
        
        fetchTables();
        setShowCloseRequestModal(false);
        setCloseReason('');
      }
    } catch (error) {
      console.error('خطأ في طلب إغلاق الطاولة:', error);
      showError('فشل في إرسال طلب الإغلاق');
    }
  };

  // الموافقة على إغلاق طاولة (للمدير)
  const approveCloseRequest = async (tableId: string, requestIndex: number) => {
    try {
      const response = await authenticatedPut(`/api/table-accounts/${tableId}/close-request/${requestIndex}/approve`, {
        approvedBy: userName
      });
      
      if (response.success) {
        showSuccess('تم الموافقة على إغلاق الطاولة');
        
        // إرسال إشعار Socket.IO
        if (socket) {
          socket.emit('table-close-approved', {
            tableId,
            approvedBy: userName
          });
        }
        
        fetchTables();
      }
    } catch (error) {
      console.error('خطأ في الموافقة على إغلاق الطاولة:', error);
      showError('فشل في الموافقة على الإغلاق');
    }
  };

  // رفض طلب إغلاق طاولة (للمدير)
  const rejectCloseRequest = async (tableId: string, requestIndex: number) => {
    try {
      const response = await authenticatedPut(`/api/table-accounts/${tableId}/close-request/${requestIndex}/reject`, {
        rejectedBy: userName
      });
      
      if (response.success) {
        showSuccess('تم رفض طلب إغلاق الطاولة');
        fetchTables();
      }
    } catch (error) {
      console.error('خطأ في رفض طلب إغلاق الطاولة:', error);
      showError('فشل في رفض طلب الإغلاق');
    }
  };

  // إعداد Socket.IO
  useEffect(() => {
    if (socket) {
      socket.on('table-updated', () => {
        fetchTables();
      });

      socket.on('table-opened', () => {
        fetchTables();
      });

      socket.on('table-close-requested', () => {
        fetchTables();
      });

      socket.on('table-close-approved', () => {
        fetchTables();
      });
    }

    return () => {
      if (socket) {
        socket.off('table-updated');
        socket.off('table-opened');
        socket.off('table-close-requested');
        socket.off('table-close-approved');
      }
    };
  }, []);

  // تحميل البيانات الأولية
  useEffect(() => {
    fetchTables();
  }, [userRole, userName]);

  // فلترة الطاولات
  const getFilteredTables = () => {
    if (filterStatus === 'all') return tables;
    return tables.filter(table => table.status === filterStatus);
  };

  // الحصول على لون الحالة
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available': return '#27ae60';
      case 'occupied': return '#e74c3c';
      case 'reserved': return '#f39c12';
      case 'maintenance': return '#95a5a6';
      default: return '#2c3e50';
    }
  };

  // الحصول على نص الحالة
  const getStatusText = (status: string) => {
    switch (status) {
      case 'available': return 'متاحة';
      case 'occupied': return 'مشغولة';
      case 'reserved': return 'محجوزة';
      case 'maintenance': return 'صيانة';
      default: return status;
    }
  };

  // تنسيق الوقت
  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('ar-EG', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // حساب مدة الجلسة
  const getSessionDuration = (openedAt: string) => {
    const opened = new Date(openedAt);
    const now = new Date();
    const diffMs = now.getTime() - opened.getTime();
    const hours = Math.floor(diffMs / (1000 * 60 * 60));
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours > 0) {
      return `${hours}س ${minutes}د`;
    }
    return `${minutes}د`;
  };

  if (loading) {
    return (
      <div className="tables-manager loading">
        <div className="loading-spinner">
          <i className="fas fa-spinner fa-spin"></i>
          <span>جاري تحميل الطاولات...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="tables-manager">
      {/* Header */}
      <div className="tables-header">
        <div className="header-left">
          <h1>
            <i className="fas fa-table"></i>
            إدارة الطاولات
          </h1>
          <p>خريطة تفاعلية لجميع الطاولات مع التحديث الفوري</p>
        </div>
        
        <div className="header-actions">
          {userRole === 'waiter' && (
            <button 
              className="btn-new-table"
              onClick={() => setShowTableModal(true)}
            >
              <i className="fas fa-plus"></i>
              فتح طاولة جديدة
            </button>
          )}
          
          <div className="view-controls">
            <button 
              className={`view-btn ${viewMode === 'grid' ? 'active' : ''}`}
              onClick={() => setViewMode('grid')}
            >
              <i className="fas fa-th"></i>
            </button>
            <button 
              className={`view-btn ${viewMode === 'list' ? 'active' : ''}`}
              onClick={() => setViewMode('list')}
            >
              <i className="fas fa-list"></i>
            </button>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="tables-filters">
        <div className="filter-group">
          <label>حالة الطاولة:</label>
          <select 
            value={filterStatus} 
            onChange={(e) => setFilterStatus(e.target.value)}
          >
            <option value="all">جميع الحالات</option>
            <option value="available">متاحة</option>
            <option value="occupied">مشغولة</option>
            <option value="reserved">محجوزة</option>
            <option value="maintenance">صيانة</option>
          </select>
        </div>

        <button 
          className="refresh-btn"
          onClick={fetchTables}
          title="تحديث البيانات"
        >
          <i className="fas fa-sync-alt"></i>
          تحديث
        </button>
      </div>

      {/* Tables Display */}
      <div className={`tables-display ${viewMode}`}>
        {getFilteredTables().length === 0 ? (
          <div className="no-tables">
            <i className="fas fa-table"></i>
            <p>لا توجد طاولات تطابق الفلاتر المحددة</p>
          </div>
        ) : (
          <div className={`tables-${viewMode}`}>
            {getFilteredTables().map(table => (
              <div 
                key={table._id} 
                className={`table-card ${table.status}`}
                onClick={() => {
                  setSelectedTable(table);
                  setShowTableModal(true);
                }}
              >
                <div className="table-header">
                  <div className="table-number">
                    <i className="fas fa-table"></i>
                    طاولة {table.tableNumber}
                  </div>
                  <div 
                    className="table-status"
                    style={{ backgroundColor: getStatusColor(table.status) }}
                  >
                    {getStatusText(table.status)}
                  </div>
                </div>

                <div className="table-info">
                  <div className="info-row">
                    <i className="fas fa-users"></i>
                    <span>السعة: {table.capacity} أشخاص</span>
                  </div>
                  
                  {table.customerName && (
                    <div className="info-row">
                      <i className="fas fa-user"></i>
                      <span>{table.customerName}</span>
                    </div>
                  )}
                  
                  {table.waiterName && (
                    <div className="info-row">
                      <i className="fas fa-user-tie"></i>
                      <span>{table.waiterName}</span>
                    </div>
                  )}
                  
                  {table.openedAt && (
                    <div className="info-row">
                      <i className="fas fa-clock"></i>
                      <span>مفتوحة منذ: {getSessionDuration(table.openedAt)}</span>
                    </div>
                  )}
                </div>

                <div className="table-summary">
                  <div className="orders-count">
                    <i className="fas fa-shopping-cart"></i>
                    <span>{table.orders.length} طلب</span>
                  </div>
                  <div className="table-total">
                    <i className="fas fa-money-bill-wave"></i>
                    <span>{table.totalAmount.toFixed(2)} ج.م</span>
                  </div>
                </div>

                {/* Close Requests Alert */}
                {table.closeRequests && table.closeRequests.some(req => req.status === 'pending') && (
                  <div className="close-request-alert">
                    <i className="fas fa-exclamation-triangle"></i>
                    <span>طلب إغلاق معلق</span>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="table-actions" onClick={(e) => e.stopPropagation()}>
                  {userRole === 'waiter' && table.status === 'occupied' && table.waiterName === userName && (
                    <button 
                      className="action-btn close-request"
                      onClick={() => {
                        setSelectedTable(table);
                        setShowCloseRequestModal(true);
                      }}
                    >
                      <i className="fas fa-times"></i>
                      طلب إغلاق
                    </button>
                  )}
                  
                  {userRole === 'manager' && table.closeRequests && table.closeRequests.some(req => req.status === 'pending') && (
                    <div className="manager-actions">
                      <button 
                        className="action-btn approve"
                        onClick={() => {
                          const pendingIndex = table.closeRequests!.findIndex(req => req.status === 'pending');
                          if (pendingIndex !== -1) {
                            approveCloseRequest(table._id, pendingIndex);
                          }
                        }}
                      >
                        <i className="fas fa-check"></i>
                        موافقة
                      </button>
                      <button 
                        className="action-btn reject"
                        onClick={() => {
                          const pendingIndex = table.closeRequests!.findIndex(req => req.status === 'pending');
                          if (pendingIndex !== -1) {
                            rejectCloseRequest(table._id, pendingIndex);
                          }
                        }}
                      >
                        <i className="fas fa-times"></i>
                        رفض
                      </button>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* New Table Modal */}
      {showTableModal && !selectedTable && (
        <div className="modal-overlay" onClick={() => setShowTableModal(false)}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>
                <i className="fas fa-plus"></i>
                فتح طاولة جديدة
              </h2>
              <button 
                className="modal-close"
                onClick={() => setShowTableModal(false)}
              >
                <i className="fas fa-times"></i>
              </button>
            </div>
            
            <div className="modal-body">
              <form onSubmit={(e) => {
                e.preventDefault();
                const formData = new FormData(e.target as HTMLFormElement);
                const tableNumber = formData.get('tableNumber') as string;
                const customerName = formData.get('customerName') as string;
                
                if (tableNumber && customerName) {
                  openTable(tableNumber, customerName);
                }
              }}>
                <div className="form-group">
                  <label htmlFor="tableNumber">رقم الطاولة:</label>
                  <input
                    type="text"
                    id="tableNumber"
                    name="tableNumber"
                    required
                    placeholder="أدخل رقم الطاولة"
                  />
                </div>
                
                <div className="form-group">
                  <label htmlFor="customerName">اسم العميل:</label>
                  <input
                    type="text"
                    id="customerName"
                    name="customerName"
                    required
                    placeholder="أدخل اسم العميل"
                  />
                </div>
                
                <div className="modal-footer">
                  <button type="submit" className="btn-confirm">
                    <i className="fas fa-plus"></i>
                    فتح الطاولة
                  </button>
                  <button 
                    type="button" 
                    className="btn-cancel"
                    onClick={() => setShowTableModal(false)}
                  >
                    إلغاء
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Close Request Modal */}
      {showCloseRequestModal && selectedTable && (
        <div className="modal-overlay" onClick={() => setShowCloseRequestModal(false)}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>
                <i className="fas fa-times"></i>
                طلب إغلاق الطاولة {selectedTable.tableNumber}
              </h2>
              <button 
                className="modal-close"
                onClick={() => setShowCloseRequestModal(false)}
              >
                <i className="fas fa-times"></i>
              </button>
            </div>
            
            <div className="modal-body">
              <div className="table-summary-modal">
                <h3>ملخص الطاولة:</h3>
                <div className="summary-item">
                  <span>العميل:</span>
                  <span>{selectedTable.customerName}</span>
                </div>
                <div className="summary-item">
                  <span>عدد الطلبات:</span>
                  <span>{selectedTable.orders.length}</span>
                </div>
                <div className="summary-item">
                  <span>الإجمالي:</span>
                  <span>{selectedTable.totalAmount.toFixed(2)} ج.م</span>
                </div>
                {selectedTable.openedAt && (
                  <div className="summary-item">
                    <span>مدة الجلسة:</span>
                    <span>{getSessionDuration(selectedTable.openedAt)}</span>
                  </div>
                )}
              </div>
              
              <div className="form-group">
                <label htmlFor="closeReason">سبب الإغلاق:</label>
                <textarea
                  id="closeReason"
                  value={closeReason}
                  onChange={(e) => setCloseReason(e.target.value)}
                  placeholder="أدخل سبب طلب إغلاق الطاولة..."
                  rows={3}
                  required
                />
              </div>
            </div>
            
            <div className="modal-footer">
              <button 
                className="btn-confirm close"
                onClick={() => requestCloseTable(selectedTable)}
                disabled={!closeReason.trim()}
              >
                <i className="fas fa-paper-plane"></i>
                إرسال طلب الإغلاق
              </button>
              <button 
                className="btn-cancel"
                onClick={() => setShowCloseRequestModal(false)}
              >
                إلغاء
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Table Details Modal */}
      {showTableModal && selectedTable && (
        <div className="modal-overlay" onClick={() => setShowTableModal(false)}>
          <div className="modal-content table-details-modal" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>
                <i className="fas fa-table"></i>
                تفاصيل الطاولة {selectedTable.tableNumber}
              </h2>
              <button 
                className="modal-close"
                onClick={() => setShowTableModal(false)}
              >
                <i className="fas fa-times"></i>
              </button>
            </div>
            
            <div className="modal-body">
              {/* Table Info */}
              <div className="table-details-info">
                <div className="info-section">
                  <h3>معلومات الطاولة</h3>
                  <div className="info-grid">
                    <div className="info-item">
                      <span className="label">رقم الطاولة:</span>
                      <span className="value">{selectedTable.tableNumber}</span>
                    </div>
                    <div className="info-item">
                      <span className="label">السعة:</span>
                      <span className="value">{selectedTable.capacity} أشخاص</span>
                    </div>
                    <div className="info-item">
                      <span className="label">الحالة:</span>
                      <span 
                        className="value status"
                        style={{ color: getStatusColor(selectedTable.status) }}
                      >
                        {getStatusText(selectedTable.status)}
                      </span>
                    </div>
                    {selectedTable.customerName && (
                      <div className="info-item">
                        <span className="label">اسم العميل:</span>
                        <span className="value">{selectedTable.customerName}</span>
                      </div>
                    )}
                    {selectedTable.waiterName && (
                      <div className="info-item">
                        <span className="label">النادل:</span>
                        <span className="value">{selectedTable.waiterName}</span>
                      </div>
                    )}
                    {selectedTable.openedAt && (
                      <div className="info-item">
                        <span className="label">وقت الفتح:</span>
                        <span className="value">
                          {formatTime(selectedTable.openedAt)} - مدة الجلسة: {getSessionDuration(selectedTable.openedAt)}
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Orders */}
                {selectedTable.orders.length > 0 && (
                  <div className="orders-section">
                    <h3>الطلبات ({selectedTable.orders.length})</h3>
                    <div className="orders-list">
                      {selectedTable.orders.map((order, index) => (
                        <div key={index} className="order-item">
                          <div className="order-info">
                            <span className="order-number">#{order.orderNumber}</span>
                            <span className="order-status">{order.status}</span>
                          </div>
                          <div className="order-details">
                            <span className="order-time">{formatTime(order.createdAt)}</span>
                            <span className="order-price">{order.totalPrice.toFixed(2)} ج.م</span>
                          </div>
                        </div>
                      ))}
                    </div>
                    
                    <div className="orders-total">
                      <span className="total-label">إجمالي الطاولة:</span>
                      <span className="total-value">{selectedTable.totalAmount.toFixed(2)} ج.م</span>
                    </div>
                  </div>
                )}

                {/* Close Requests */}
                {selectedTable.closeRequests && selectedTable.closeRequests.length > 0 && (
                  <div className="close-requests-section">
                    <h3>طلبات الإغلاق</h3>
                    <div className="close-requests-list">
                      {selectedTable.closeRequests.map((request, index) => (
                        <div key={index} className={`close-request-item ${request.status}`}>
                          <div className="request-info">
                            <div className="request-meta">
                              <span>طلب من: {request.requestedBy}</span>
                              <span>{formatTime(request.requestedAt)}</span>
                            </div>
                            <div className="request-reason">
                              <span>{request.reason}</span>
                            </div>
                          </div>
                          
                          <div className="request-status">
                            <span className={`status-badge ${request.status}`}>
                              {request.status === 'pending' ? 'معلق' : 
                               request.status === 'approved' ? 'موافق عليه' : 'مرفوض'}
                            </span>
                          </div>
                          
                          {userRole === 'manager' && request.status === 'pending' && (
                            <div className="request-actions">
                              <button 
                                className="approve-btn"
                                onClick={() => approveCloseRequest(selectedTable._id, index)}
                              >
                                <i className="fas fa-check"></i>
                                موافقة
                              </button>
                              <button 
                                className="reject-btn"
                                onClick={() => rejectCloseRequest(selectedTable._id, index)}
                              >
                                <i className="fas fa-times"></i>
                                رفض
                              </button>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
            
            <div className="modal-footer">
              <button 
                className="btn-close"
                onClick={() => setShowTableModal(false)}
              >
                إغلاق
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TablesManager;
