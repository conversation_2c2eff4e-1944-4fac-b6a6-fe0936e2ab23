import type { ReactNode } from 'react';
import './Card.css';

interface CardProps {
  title?: string;
  icon?: string;
  children: ReactNode;
  className?: string;
  onClick?: () => void;
  footer?: ReactNode;
  hoverable?: boolean;
}

const Card: React.FC<CardProps> = ({
  title,
  icon,
  children,
  className = '',
  onClick,
  footer,
  hoverable = false
}) => {
  return (
    <div 
      className={`card ${hoverable ? 'card-hoverable' : ''} ${className}`}
      onClick={onClick}
    >
      {(title || icon) && (
        <div className="card-header">
          {icon && <i className={`card-icon ${icon}`}></i>}
          {title && <h3 className="card-title">{title}</h3>}
        </div>
      )}
      
      <div className="card-body">
        {children}
      </div>
      
      {footer && (
        <div className="card-footer">
          {footer}
        </div>
      )}
    </div>
  );
};

export default Card;
