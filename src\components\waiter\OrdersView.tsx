// مكون شاشة الطلبات للوحة النادل
import React from 'react';
import type { WaiterOrder, WaiterFilters } from '../../models/WaiterModels';

interface OrdersViewProps {
  orders: WaiterOrder[];
  filters: WaiterFilters;
  onFilterChange: (key: keyof WaiterFilters, value: any) => void;
  onOrderSelect: (order: WaiterOrder) => void;
  onOrderStatusUpdate: (orderId: string, status: string) => void;
  onDiscountRequest: (order: WaiterOrder) => void;
  onRefresh: () => void;
}

export function OrdersView({
  orders,
  filters,
  onFilterChange,
  onOrderSelect,
  onOrderStatusUpdate,
  onDiscountRequest,
  onRefresh
}: OrdersViewProps) {

  // إحصائيات الطلبات
  const statusCounts = {
    pending: orders.filter(order => order.status === 'pending').length,
    preparing: orders.filter(order => order.status === 'preparing').length,
    ready: orders.filter(order => order.status === 'ready').length,
    completed: orders.filter(order => order.status === 'completed').length
  };

  // الطلبات المفلترة
  const filteredOrders = orders.filter(order => {
    // فلتر حالة الطلب
    if (filters.orderStatus !== 'all' && order.status !== filters.orderStatus) {
      return false;
    }

    // فلتر البحث
    if (filters.searchTerm) {
      const searchTerm = filters.searchTerm.toLowerCase();
      const orderMatch = order.orderNumber.toLowerCase().includes(searchTerm);
      const tableMatch = order.tableNumber.toString().includes(searchTerm);
      const customerMatch = order.customerName?.toLowerCase().includes(searchTerm);
      if (!orderMatch && !tableMatch && !customerMatch) return false;
    }

    return true;
  });

  // ترتيب الطلبات: حسب الحالة ثم التاريخ
  const sortedOrders = [...filteredOrders].sort((a, b) => {
    // ترتيب حسب الأولوية
    const statusPriority = {
      'ready': 1,
      'preparing': 2,
      'pending': 3,
      'completed': 4,
      'delivered': 5
    };
    
    const aPriority = statusPriority[a.status as keyof typeof statusPriority] || 6;
    const bPriority = statusPriority[b.status as keyof typeof statusPriority] || 6;
    
    if (aPriority !== bPriority) {
      return aPriority - bPriority;
    }
    
    // ترتيب حسب التاريخ (الأحدث أولاً)
    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
  });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return 'fa-clock';
      case 'preparing': return 'fa-fire';
      case 'ready': return 'fa-bell';
      case 'completed': return 'fa-check';
      case 'delivered': return 'fa-check-double';
      default: return 'fa-question';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'قيد الانتظار';
      case 'preparing': return 'قيد التحضير';
      case 'ready': return 'جاهز للتسليم';
      case 'completed': return 'مكتمل';
      case 'delivered': return 'تم التسليم';
      default: return 'غير معروف';
    }
  };

  const canUpdateStatus = (order: WaiterOrder) => {
    return order.status === 'ready' || order.status === 'completed';
  };

  const getNextStatus = (currentStatus: string) => {
    switch (currentStatus) {
      case 'ready': return 'delivered';
      case 'completed': return 'delivered';
      default: return null;
    }
  };

  return (
    <div className="orders-view">
      <div className="orders-header">
        <h1>
          <i className="fas fa-shopping-cart"></i>
          طلباتي
        </h1>
        <div className="header-actions">
          <button
            className="refresh-btn"
            onClick={onRefresh}
            title="تحديث قائمة الطلبات"
          >
            <i className="fas fa-sync-alt"></i>
            تحديث
          </button>
        </div>
      </div>

      {/* إحصائيات سريعة */}
      <div className="orders-stats">
        <div className="stat-card pending">
          <div className="stat-icon">
            <i className="fas fa-clock"></i>
          </div>
          <div className="stat-content">
            <div className="stat-number">{statusCounts.pending}</div>
            <div className="stat-label">قيد الانتظار</div>
          </div>
        </div>

        <div className="stat-card preparing">
          <div className="stat-icon">
            <i className="fas fa-fire"></i>
          </div>
          <div className="stat-content">
            <div className="stat-number">{statusCounts.preparing}</div>
            <div className="stat-label">قيد التحضير</div>
          </div>
        </div>

        <div className="stat-card ready">
          <div className="stat-icon">
            <i className="fas fa-bell"></i>
          </div>
          <div className="stat-content">
            <div className="stat-number">{statusCounts.ready}</div>
            <div className="stat-label">جاهز للتسليم</div>
          </div>
        </div>

        <div className="stat-card completed">
          <div className="stat-icon">
            <i className="fas fa-check"></i>
          </div>
          <div className="stat-content">
            <div className="stat-number">{statusCounts.completed}</div>
            <div className="stat-label">مكتمل</div>
          </div>
        </div>
      </div>

      {/* فلاتر الطلبات */}
      <div className="orders-filters">
        <div className="filter-group">
          <label>حالة الطلب:</label>
          <select 
            value={filters.orderStatus} 
            onChange={(e) => onFilterChange('orderStatus', e.target.value)}
            className="status-filter"
          >
            <option value="all">جميع الطلبات</option>
            <option value="pending">قيد الانتظار</option>
            <option value="preparing">قيد التحضير</option>
            <option value="ready">جاهز للتسليم</option>
            <option value="completed">مكتمل</option>
          </select>
        </div>

        <div className="filter-group">
          <label>البحث:</label>
          <div className="search-input-group">
            <i className="fas fa-search"></i>
            <input
              type="text"
              placeholder="البحث في الطلبات أو الطاولات..."
              value={filters.searchTerm}
              onChange={(e) => onFilterChange('searchTerm', e.target.value)}
              className="search-input"
            />
            {filters.searchTerm && (
              <button 
                className="clear-search"
                onClick={() => onFilterChange('searchTerm', '')}
                title="مسح البحث"
              >
                <i className="fas fa-times"></i>
              </button>
            )}
          </div>
        </div>
      </div>

      {/* نتائج الفلترة */}
      {filteredOrders.length !== orders.length && (
        <div className="filter-results">
          <span>عرض {filteredOrders.length} من {orders.length} طلب</span>
          <button 
            className="clear-filters"
            onClick={() => {
              onFilterChange('orderStatus', 'all');
              onFilterChange('searchTerm', '');
            }}
          >
            <i className="fas fa-times"></i>
            إلغاء الفلاتر
          </button>
        </div>
      )}

      {/* قائمة الطلبات */}
      <div className="orders-grid">
        {sortedOrders.length === 0 ? (
          <div className="empty-state">
            <i className="fas fa-shopping-cart"></i>
            <h3>لا توجد طلبات</h3>
            <p>
              {orders.length === 0 
                ? 'لم تقم بإنشاء أي طلبات بعد'
                : 'لا توجد طلبات تطابق معايير البحث المحددة'
              }
            </p>
          </div>
        ) : (
          sortedOrders.map(order => (
            <div 
              key={order._id} 
              className={`order-card ${order.status}`}
              onClick={() => onOrderSelect(order)}
            >
              {/* رأس الطلب */}
              <div className="order-header">
                <div className="order-number">
                  <i className="fas fa-hashtag"></i>
                  {order.orderNumber}
                </div>
                <div className={`order-status ${order.status}`}>
                  <i className={`fas ${getStatusIcon(order.status)}`}></i>
                  {getStatusText(order.status)}
                </div>
              </div>

              {/* معلومات الطلب */}
              <div className="order-info">
                <div className="order-detail">
                  <i className="fas fa-table"></i>
                  <span>طاولة {order.tableNumber}</span>
                </div>
                {order.customerName && (
                  <div className="order-detail">
                    <i className="fas fa-user"></i>
                    <span>{order.customerName}</span>
                  </div>
                )}
                {order.chefName && (
                  <div className="order-detail">
                    <i className="fas fa-chef-hat"></i>
                    <span>{order.chefName}</span>
                  </div>
                )}
              </div>

              {/* ملخص الطلب */}
              <div className="order-summary">
                <div className="items-count">
                  <i className="fas fa-list"></i>
                  {order.items.length} صنف
                </div>
                <div className="total-amount">
                  <i className="fas fa-money-bill-wave"></i>
                  {order.totalAmount.toFixed(2)} ج.م
                </div>
              </div>

              {/* وقت الطلب */}
              <div className="order-time">
                <i className="fas fa-clock"></i>
                <span>
                  {new Date(order.createdAt).toLocaleString('ar-SA', {
                    day: '2-digit',
                    month: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </span>
              </div>

              {/* أزرار العمليات */}
              <div className="order-actions">
                <button
                  className="details-btn"
                  onClick={(e) => {
                    e.stopPropagation();
                    onOrderSelect(order);
                  }}
                  title="عرض التفاصيل"
                >
                  <i className="fas fa-eye"></i>
                  التفاصيل
                </button>

                {canUpdateStatus(order) && (
                  <button
                    className={`status-update-btn ${order.status}`}
                    onClick={(e) => {
                      e.stopPropagation();
                      const nextStatus = getNextStatus(order.status);
                      if (nextStatus) {
                        onOrderStatusUpdate(order._id, nextStatus);
                      }
                    }}
                    title={`تغيير إلى ${getNextStatus(order.status) === 'delivered' ? 'تم التسليم' : ''}`}
                  >
                    <i className="fas fa-arrow-right"></i>
                    {order.status === 'ready' && 'تسليم'}
                    {order.status === 'completed' && 'تسليم'}
                  </button>
                )}

                {(order.status === 'pending' || order.status === 'preparing') && (
                  <button
                    className="discount-btn"
                    onClick={(e) => {
                      e.stopPropagation();
                      onDiscountRequest(order);
                    }}
                    title="طلب خصم"
                  >
                    <i className="fas fa-percentage"></i>
                    خصم
                  </button>
                )}
              </div>

              {/* ملاحظات إضافية */}
              {order.notes && (
                <div className="order-notes">
                  <i className="fas fa-sticky-note"></i>
                  <span>{order.notes}</span>
                </div>
              )}
            </div>
          ))
        )}
      </div>
    </div>
  );
}
