/* Menu Manager Styles */
.menu-manager {
  padding: 1.5rem;
  max-width: 1400px;
  margin: 0 auto;
  background: var(--light-bg, #f8f9fa);
  min-height: 100vh;
}

/* Loading State */
.menu-manager.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  color: var(--primary-color, #2c3e50);
}

.loading-spinner i {
  font-size: 2rem;
  color: var(--primary-color, #2c3e50);
}

/* Header */
.menu-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  gap: 2rem;
}

.header-left h1 {
  margin: 0 0 0.5rem 0;
  color: var(--primary-color, #2c3e50);
  font-size: 2rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.header-left p {
  color: var(--text-muted, #95a5a6);
  font-size: 1.1rem;
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.btn-new-item {
  background: var(--success-color, #27ae60);
  color: var(--white, #ffffff);
  border: none;
  border-radius: 0.5rem;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-new-item:hover {
  background: #229954;
  transform: translateY(-1px);
}

/* Stats Cards */
.menu-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: var(--white, #ffffff);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: var(--white, #ffffff);
}

.stat-card.total .stat-icon {
  background: var(--primary-color, #2c3e50);
}

.stat-card.available .stat-icon {
  background: var(--success-color, #27ae60);
}

.stat-card.unavailable .stat-icon {
  background: var(--error-color, #e74c3c);
}

.stat-card.categories .stat-icon {
  background: var(--secondary-color, #f39c12);
}

.stat-content h3 {
  margin: 0 0 0.25rem 0;
  font-size: 1.75rem;
  font-weight: bold;
  color: var(--primary-color, #2c3e50);
}

.stat-content p {
  margin: 0;
  color: var(--text-muted, #95a5a6);
  font-size: 0.875rem;
}

/* Filters */
.menu-filters {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
  padding: 1rem;
  background: var(--white, #ffffff);
  border-radius: 0.75rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.search-group {
  position: relative;
  flex: 1;
  max-width: 300px;
}

.search-group i {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted, #95a5a6);
}

.search-group input {
  width: 100%;
  padding: 0.75rem 2.5rem 0.75rem 1rem;
  border: 1px solid var(--border-color, #ecf0f1);
  border-radius: 0.5rem;
  font-size: 0.9rem;
  background: var(--white, #ffffff);
  color: var(--text-color, #2c3e50);
}

.search-group input:focus {
  outline: none;
  border-color: var(--primary-color, #2c3e50);
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-group label {
  font-weight: 500;
  color: var(--text-color, #2c3e50);
  white-space: nowrap;
}

.filter-group select {
  padding: 0.5rem 1rem;
  border: 1px solid var(--border-color, #ecf0f1);
  border-radius: 0.375rem;
  background: var(--white, #ffffff);
  color: var(--text-color, #2c3e50);
  font-size: 0.9rem;
  cursor: pointer;
}

.refresh-btn {
  background: var(--info-color, #3498db);
  color: var(--white, #ffffff);
  border: none;
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.refresh-btn:hover {
  background: #2980b9;
}

/* Menu Items */
.menu-items {
  margin-bottom: 2rem;
}

.items-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.menu-item-card {
  background: var(--white, #ffffff);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  position: relative;
  border-left: 4px solid var(--success-color, #27ae60);
}

.menu-item-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.menu-item-card.unavailable {
  border-left-color: var(--error-color, #e74c3c);
  opacity: 0.7;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.item-name h3 {
  margin: 0 0 0.25rem 0;
  color: var(--primary-color, #2c3e50);
  font-size: 1.25rem;
}

.item-name .category {
  background: var(--secondary-color, #f39c12);
  color: var(--white, #ffffff);
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.item-price {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--success-color, #27ae60);
}

.item-description {
  margin-bottom: 1rem;
}

.item-description p {
  margin: 0;
  color: var(--text-muted, #95a5a6);
  line-height: 1.5;
}

.item-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: var(--light-bg, #f8f9fa);
  border-radius: 0.5rem;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: var(--text-color, #2c3e50);
}

.detail-item i {
  color: var(--secondary-color, #f39c12);
}

.availability-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
}

.availability-status.available {
  color: var(--success-color, #27ae60);
}

.availability-status.unavailable {
  color: var(--error-color, #e74c3c);
}

.item-ingredients {
  margin-bottom: 1.5rem;
}

.item-ingredients h4 {
  margin: 0 0 0.75rem 0;
  color: var(--primary-color, #2c3e50);
  font-size: 0.9rem;
}

.ingredients-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.ingredient-tag {
  background: var(--border-color, #ecf0f1);
  color: var(--text-color, #2c3e50);
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.item-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.action-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.375rem;
  flex: 1;
  justify-content: center;
}

.action-btn.edit {
  background: var(--info-color, #3498db);
  color: var(--white, #ffffff);
}

.action-btn.edit:hover {
  background: #2980b9;
}

.action-btn.toggle.enable {
  background: var(--success-color, #27ae60);
  color: var(--white, #ffffff);
}

.action-btn.toggle.enable:hover {
  background: #229954;
}

.action-btn.toggle.disable {
  background: var(--warning-color, #f39c12);
  color: var(--white, #ffffff);
}

.action-btn.toggle.disable:hover {
  background: #e67e22;
}

.action-btn.delete {
  background: var(--error-color, #e74c3c);
  color: var(--white, #ffffff);
}

.action-btn.delete:hover {
  background: #c0392b;
}

/* No Items */
.no-items {
  text-align: center;
  padding: 4rem 2rem;
  color: var(--text-muted, #95a5a6);
}

.no-items i {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.no-items p {
  font-size: 1.1rem;
  margin: 0;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-content {
  background: var(--white, #ffffff);
  border-radius: 1rem;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color, #ecf0f1);
}

.modal-header h2 {
  margin: 0;
  color: var(--primary-color, #2c3e50);
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: var(--text-muted, #95a5a6);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: var(--light-bg, #f8f9fa);
  color: var(--text-color, #2c3e50);
}

.modal-body {
  padding: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-color, #2c3e50);
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color, #ecf0f1);
  border-radius: 0.5rem;
  font-family: inherit;
  font-size: 0.9rem;
  transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color, #2c3e50);
}

.checkbox-group {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-weight: 500;
  color: var(--text-color, #2c3e50);
}

.checkbox-label input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-color, #ecf0f1);
  border-radius: 4px;
  position: relative;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
  background: var(--success-color, #27ae60);
  border-color: var(--success-color, #27ae60);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--white, #ffffff);
  font-size: 12px;
  font-weight: bold;
}

.modal-footer {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid var(--border-color, #ecf0f1);
}

.btn-confirm {
  flex: 1;
  background: var(--success-color, #27ae60);
  color: var(--white, #ffffff);
  border: none;
  border-radius: 0.5rem;
  padding: 0.75rem 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-confirm:hover {
  background: #229954;
}

.btn-cancel {
  flex: 1;
  background: var(--border-color, #ecf0f1);
  color: var(--text-color, #2c3e50);
  border: none;
  border-radius: 0.5rem;
  padding: 0.75rem 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-cancel:hover {
  background: #bdc3c7;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .menu-manager {
    padding: 1rem;
  }

  .menu-header {
    flex-direction: column;
    gap: 1rem;
  }

  .header-actions {
    width: 100%;
    justify-content: center;
  }

  .menu-filters {
    flex-wrap: wrap;
    gap: 1rem;
  }

  .search-group {
    max-width: none;
    width: 100%;
  }

  .filter-group {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .refresh-btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .menu-manager {
    padding: 0.75rem;
  }

  .header-left h1 {
    font-size: 1.5rem;
  }

  .menu-stats {
    grid-template-columns: 1fr;
  }

  .stat-card {
    padding: 1rem;
  }

  .stat-icon {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }

  .stat-content h3 {
    font-size: 1.5rem;
  }

  .items-grid {
    grid-template-columns: 1fr;
  }

  .menu-item-card {
    padding: 1rem;
  }

  .item-header {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }

  .item-details {
    flex-direction: column;
    gap: 0.5rem;
  }

  .item-actions {
    flex-direction: column;
  }

  .form-row {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .menu-manager {
    padding: 0.5rem;
  }

  .header-left h1 {
    font-size: 1.25rem;
  }

  .header-left p {
    font-size: 0.9rem;
  }

  .stat-card {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }

  .menu-item-card {
    padding: 0.75rem;
  }

  .ingredients-list {
    justify-content: center;
  }

  .modal-content {
    margin: 0.5rem;
    max-height: 95vh;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 1rem;
  }

  .modal-footer {
    flex-direction: column;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .menu-manager {
    background: #1a1a1a;
  }

  .stat-card,
  .menu-filters,
  .menu-item-card,
  .modal-content {
    background: #2d2d2d;
    color: #ffffff;
  }

  .item-details {
    background: #3d3d3d;
  }

  .ingredient-tag {
    background: #555555;
    color: #ffffff;
  }

  .search-group input,
  .filter-group select,
  .form-group input,
  .form-group textarea {
    background: #3d3d3d;
    color: #ffffff;
    border-color: #555555;
  }

  .checkmark {
    border-color: #555555;
  }
}
