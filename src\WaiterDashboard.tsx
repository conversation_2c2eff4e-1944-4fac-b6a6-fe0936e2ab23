import { APP_CONFIG } from './config/app.config';
import { authenticatedGet, authenticatedPost, authenticatedPut, authenticatedGetWithRetry, handleApiError } from './utils/apiHelpers';
import { useToast } from './hooks/useToast';
import { getOrderFinalPrice } from './utils/orderHelpers';
import { globalPerformanceOptimizer, globalAPIHandler } from './utils/performanceOptimizer';
import React, { useState, useEffect, useCallback, useRef } from 'react';
import './WaiterDashboard.css';
import socket from './socket';

// Define interfaces used in the component
interface MenuItem {
  _id: string;
  name: string;
  price: number;
  description?: string;
  categories?: string[];
  categoryDetails?: Category[];
  available: boolean;
  notes?: string; 
}

interface CartItem extends MenuItem {
  quantity: number;
}

interface Category {
  _id: string;
  name: string;
  color: string;
  icon?: string;
}

interface OrderItem {
  product: string;
  name: string;
  quantity: number;
  price: number;
  notes?: string;
  id?: string;
}

interface Order {
  _id: string;
  orderNumber: string;
  waiterName: string;
  items: OrderItem[];
  totalPrice: number;
  tableNumber: string;
  customerName?: string;
  status: 'pending' | 'preparing' | 'ready' | 'delivered' | 'cancelled';
  createdAt: string;
  tableAccountId?: string;
  discountStatus?: 'pending' | 'approved' | 'rejected';
  discountAmount?: number;
}

interface TableAccount {
  _id: string;
  tableNumber: string;
  waiterId: string;
  waiterName: string;
  waiter?: {
    id: string;
    name?: string;
    username?: string;
  };
  orders: Order[];
  totalAmount: number;
  isOpen: boolean;
  status: 'active' | 'closed';
  createdAt: string;
  updatedAt: string;
  lastActivityAt?: string;
  customerName?: string; 
  paymentMethod?: string;
  discountApplied?: number;
  notes?: string;
}

export default function WaiterDashboard() {
  // State definitions
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [cart, setCart] = useState<CartItem[]>([]);
  const [tableNumber, setTableNumber] = useState('');
  const [customerName, setCustomerName] = useState('');
  const [loading, setLoading] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  
  // Toast notifications
  const {
    toasts,
    removeToast,
    showSuccess,
    showError,
    showInfo
  } = useToast();
  
  // Orders state
  const [orders, setOrders] = useState<Order[]>([]);
  const [orderStatusFilter, setOrderStatusFilter] = useState<'all' | 'pending' | 'preparing' | 'ready' | 'delivered'>('all');
  
  // Screen state
  const [currentScreen, setCurrentScreen] = useState<'drinks' | 'orders' | 'tables' | 'cart'>('drinks');

  // Discount request states
  const [showDiscountModal, setShowDiscountModal] = useState(false);
  const [selectedOrderForDiscount, setSelectedOrderForDiscount] = useState<Order | null>(null);
  const [discountAmount, setDiscountAmount] = useState('');
  const [discountReason, setDiscountReason] = useState('');

  // Table account states
  const [showTableAccountModal, setShowTableAccountModal] = useState(false);
  const [tableAccounts, setTableAccounts] = useState<TableAccount[]>([]);
  const [existingTableAccount, setExistingTableAccount] = useState<any>(null);

  // Order details modal states
  const [showOrderDetailsModal, setShowOrderDetailsModal] = useState(false);
  const [selectedOrderDetails, setSelectedOrderDetails] = useState<Order | null>(null);

  // Table details modal states
  const [showTableDetailsModal, setShowTableDetailsModal] = useState(false);
  const [selectedTableAccountDetails, setSelectedTableAccountDetails] = useState<TableAccount | null>(null);
  // Cache timestamps to prevent excessive API calls
  const [lastFetch, setLastFetch] = useState({
    orders: 0,
    tableAccounts: 0,
    categories: 0,
    menuItems: 0
  });

  const CACHE_DURATION = 30000; // 30 seconds cache

  // Rate limiting refs
  const ordersFetching = useRef(false);
  const tableAccountsFetching = useRef(false);

  // Sidebar states
  const [sidebarOpen, setSidebarOpen] = useState(window.innerWidth > 1024);
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 1024);

  // States for item notes
  const [showNotesModal, setShowNotesModal] = useState(false);
  const [selectedItemForNotes, setSelectedItemForNotes] = useState<MenuItem | null>(null);
  const [itemNotes, setItemNotes] = useState('');

  // Socket.IO state for real-time updates
  const [socket, setSocket] = useState<any>(null);
  const [isConnected, setIsConnected] = useState(false);

  // Handle resize effect
  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth <= 1024;
      setIsMobile(mobile);
      if (!mobile) setSidebarOpen(true);
    };
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Sidebar control functions
  const openSidebar = () => {
    setSidebarOpen(true);
    if (isMobile) {
      document.body.classList.add('sidebar-open');
    }
  };
  
  const closeSidebar = () => {
    setSidebarOpen(false);
    if (isMobile) {
      document.body.classList.remove('sidebar-open');
    }
  };
  
  const toggleSidebar = () => {
    const newState = !sidebarOpen;
    setSidebarOpen(newState);
    if (isMobile) {
      if (newState) {
        document.body.classList.add('sidebar-open');
      } else {
        document.body.classList.remove('sidebar-open');
      }
    }
  };

  // Session management functions
  const saveCartToSession = () => {
    const sessionData = {
      cart,
      tableNumber,
      customerName,
      timestamp: new Date().toISOString()
    };
    localStorage.setItem('waiterSession', JSON.stringify(sessionData));
    localStorage.setItem('waiterCart', JSON.stringify(cart));
  };

  const loadCartFromSession = () => {
    try {
      const savedCart = localStorage.getItem('waiterCart');
      if (savedCart) {
        const cartData = JSON.parse(savedCart);
        if (cartData && cartData.length > 0) {
          setCart(cartData);
        }
      }

      const sessionData = localStorage.getItem('waiterSession');
      if (sessionData) {
        const { cart: savedCart, tableNumber: savedTable, customerName: savedCustomer, timestamp } = JSON.parse(sessionData);
        const sessionAge = new Date().getTime() - new Date(timestamp).getTime();
        const maxAge = 4 * 60 * 60 * 1000; // 4 hours

        if (sessionAge < maxAge) {
          if (!savedCart || savedCart.length === 0) {
            if (savedCart && savedCart.length > 0 && (!cart || cart.length === 0)) {
              setCart(savedCart);
            }
          }
          setTableNumber(savedTable || '');
          setCustomerName(savedCustomer || '');
          return true;
        }
      }
    } catch (error) {
      console.error('Error loading session:', error);
    }
    return false;
  };  // Fetch functions مع Rate Limiting محسن وPerformance Optimizer
  const fetchOrders = useCallback(async (forceRefresh = false) => {
    return globalAPIHandler.makeRequest(
      'fetch-orders',
      async () => {
        const now = Date.now();
        if (!forceRefresh && now - lastFetch.orders < CACHE_DURATION) {
          console.log('📊 استخدام cache للطلبات');
          return;
        }

        // حماية من الطلبات المتكررة
        if (ordersFetching.current) {
          console.log('⏳ طلب آخر قيد التنفيذ للطلبات');
          return;
        }

        try {
          ordersFetching.current = true;
          setLoading(true);
          
          // انتظار قليل لتجنب Rate Limiting
          await new Promise(resolve => setTimeout(resolve, 500));
          
          const data = await authenticatedGetWithRetry('/api/orders');
          setOrders(data);
          setLastFetch(prev => ({ ...prev, orders: now }));
        } catch (error) {
          console.error('خطأ في جلب الطلبات:', error);
          const errorMessage = handleApiError(error);
          showError(errorMessage);
        } finally {
          ordersFetching.current = false;
          setLoading(false);
        }
      },
      {
        debounceMs: 2000, // منع الطلبات المتكررة لمدة ثانيتين
        preventDuplicate: true
      }
    );
  }, [showError, lastFetch.orders, CACHE_DURATION, setLastFetch]);const fetchTableAccounts = useCallback(async (forceRefresh = false) => {
    const now = Date.now();
    if (!forceRefresh && now - lastFetch.tableAccounts < CACHE_DURATION) {
      console.log('📊 استخدام cache للطاولات');
      return;
    }

    // حماية من الطلبات المتكررة
    if (tableAccountsFetching.current) {
      console.log('⏳ طلب آخر قيد التنفيذ للطاولات');
      return;
    }

    try {
      tableAccountsFetching.current = true;
      
      // تقليل وقت الانتظار لتحسين السرعة
      await new Promise(resolve => setTimeout(resolve, 200));
      
      const response = await authenticatedGetWithRetry('/api/table-accounts');

      console.log('📊 استجابة API للطاولات:', response);

      let tableAccountsData: any[] = [];

      // تحقق من أن الاستجابة مصفوفة مباشرة أو تحتوي على data
      if (Array.isArray(response)) {
        tableAccountsData = response;
      } else if (response.success && Array.isArray(response.data)) {
        tableAccountsData = response.data;
      } else if (response.data && Array.isArray(response.data)) {
        tableAccountsData = response.data;
      } else {
        console.error('❌ خطأ في تنسيق بيانات الطاولات:', response);
        setTableAccounts([]);
        return;
      }

      // تحسين معالجة الطاولات - بدون انتظار طويل
      const enrichedTableAccounts = tableAccountsData.map((account) => {
        try {
          // استخدام الطلبات المحملة مسبقاً بدلاً من جلب جديد
          const tableOrders = orders.filter(order =>
            order.tableNumber === account.tableNumber.toString()
          );

          console.log(`🏓 طاولة ${account.tableNumber}: ${tableOrders.length} طلب`);

          // حساب المبلغ الإجمالي من الطلبات
          const calculatedTotal = tableOrders.reduce((sum, order) =>
            sum + (order.totalPrice || 0), 0
          );

          return {
            ...account,
            orders: tableOrders,
            totalAmount: calculatedTotal > 0 ? calculatedTotal : account.totalAmount || 0
          };
        } catch (error) {
          console.error(`خطأ في معالجة طاولة ${account.tableNumber}:`, error);
          return {
            ...account,
            orders: [],
            totalAmount: account.totalAmount || 0
          };
        }
      });setTableAccounts(enrichedTableAccounts);
      setLastFetch(prev => ({ ...prev, tableAccounts: now }));
      console.log('✅ تم جلب وإثراء الطاولات بنجاح:', enrichedTableAccounts.length, 'طاولة');

    } catch (error) {
      console.error('❌ خطأ في جلب حسابات الطاولات:', error);
      const errorMessage = handleApiError(error);
      showError(errorMessage);
      setTableAccounts([]);
    } finally {
      tableAccountsFetching.current = false;
    }
  }, [showError, orders, lastFetch.tableAccounts, CACHE_DURATION, setLastFetch]);
  const fetchMenuItems = useCallback(async () => {
    try {
      // انتظار قليل لتجنب Rate Limiting
      await new Promise(resolve => setTimeout(resolve, 300));
      const data = await authenticatedGetWithRetry('/api/menu-items');
      setMenuItems(data.filter((item: MenuItem) => item.available));
    } catch (error) {
      console.error('خطأ في جلب عناصر القائمة:', error);
    }
  }, []);

  const fetchCategories = useCallback(async () => {
    try {
      // انتظار قليل لتجنب Rate Limiting
      await new Promise(resolve => setTimeout(resolve, 200));
      const data = await authenticatedGetWithRetry('/api/categories');
      setCategories(data);
    } catch (error) {
      console.error('خطأ في جلب الفئات:', error);
    }
  }, []);
  const checkExistingTableAccount = async (tableNumber: string) => {
    try {
      // انتظار قليل لتجنب Rate Limiting
      await new Promise(resolve => setTimeout(resolve, 1000));
      const data = await authenticatedGetWithRetry(`/api/table-accounts/check?tableNumber=${tableNumber}`);
      return data;
    } catch (error) {
      console.error('Error checking table account:', error);
      let errorMessage = 'Network error checking table account. Please try again.';
      if (error instanceof Error && error.message.startsWith('HTTP')) {
        errorMessage = `Error checking table: ${error.message}`;
      } else if (typeof error === 'object' && error !== null && 'message' in error) {
        errorMessage = (error as {message: string}).message;
      }
      
      showError(errorMessage);
      return { exists: false, account: null, tableStatus: 'unknown', waiterName: null, waiterUsername: null };
    }
  };

  // Cart functions
  const addToCart = (item: MenuItem, notes?: string) => {
    const existingItem = cart.find(cartItem => cartItem._id === item._id && cartItem.notes === notes);
    let updatedCart;
    if (existingItem) {
      updatedCart = cart.map(cartItem =>
        cartItem._id === item._id && cartItem.notes === notes
          ? { ...cartItem, quantity: cartItem.quantity + 1 }
          : cartItem
      );
    } else {
      updatedCart = [...cart, { ...item, quantity: 1, notes: notes || '' }];
    }
    setCart(updatedCart);
    localStorage.setItem('waiterCart', JSON.stringify(updatedCart));
    showSuccess(`تم إضافة ${item.name} إلى السلة${notes ? ' مع ملاحظات' : ''}`);
  };

  const updateCartQuantity = (itemId: string, quantity: number) => {
    let updatedCart;
    if (quantity <= 0) {
      updatedCart = cart.filter(item => item._id !== itemId);
    } else {
      updatedCart = cart.map(item =>
        item._id === itemId ? { ...item, quantity } : item
      );
    }
    setCart(updatedCart);
    localStorage.setItem('waiterCart', JSON.stringify(updatedCart));
  };

  const clearCart = () => {
    setCart([]);
    setTableNumber('');
    setCustomerName('');
    localStorage.removeItem('waiterCart');
    localStorage.removeItem('waiterSession');
  };

  // دالة لفتح مودال الملاحظات
  const openNotesModal = (item: MenuItem) => {
    setSelectedItemForNotes(item);
    setItemNotes('');
    setShowNotesModal(true);
  };

  // دالة لإضافة العنصر مع الملاحظات
  const addItemWithNotes = () => {
    if (selectedItemForNotes) {
      addToCart(selectedItemForNotes, itemNotes);
      setShowNotesModal(false);
      setSelectedItemForNotes(null);
      setItemNotes('');
    }
  };

  // دالة لإعادة محاولة اتصال Socket.IO
  const retrySocketConnection = async () => {
    try {
      // أولاً تحقق من حالة الخادم
      showInfo('🔍 فحص حالة الخادم...');

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const response = await fetch(APP_CONFIG.API.BASE_URL + '/health', {
        method: 'GET',
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        console.log('✅ الخادم يعمل بشكل طبيعي');
        showSuccess('الخادم متاح - محاولة إعادة الاتصال...');

        if (socket && typeof socket.connect === 'function') {
          console.log('🔄 محاولة إعادة اتصال Socket.IO...');
          socket.connect();
        } else {
          showError('Socket.IO غير متوفر');
        }
      } else {
        showError('الخادم غير متاح حالياً');
      }
    } catch (error) {
      console.error('❌ خطأ في فحص حالة الخادم:', error);
      showError('لا يمكن الوصول للخادم');

      // محاولة الاتصال رغم الخطأ
      if (socket && typeof socket.connect === 'function') {
        console.log('🔄 محاولة إعادة اتصال Socket.IO رغم الخطأ...');
        socket.connect();
      }
    }
  };

  // Order submission function
  const submitOrder = async () => {
    const waiterName = localStorage.getItem('waiterName');
    const waiterId = localStorage.getItem('waiterId');
    const waiterUsername = localStorage.getItem('username');

    if (!waiterName || !waiterId || !waiterUsername) {
      showError('تفاصيل النادل غير موجودة. يرجى تسجيل الخروج ثم الدخول مرة أخرى.');
      return;
    }    if (cart.length === 0) {
      showError('السلة فارغة. يرجى إضافة أصناف قبل إرسال الطلب.');
      return;
    }

    if (!tableNumber) {
      showError('يرجى تحديد رقم الطاولة.');
      return;
    }

    setLoading(true);
    try {
      const existingAccountResponse = await checkExistingTableAccount(tableNumber);
      const existingAccount = existingAccountResponse.account;
      const tableIsAvailable = !existingAccountResponse.exists || !existingAccount?.isOpen;
      const currentTableWaiterUsername = existingAccountResponse.waiterUsername;

      if (!tableIsAvailable && currentTableWaiterUsername !== waiterUsername) {
        showError(
          `الطاولة رقم ${tableNumber} مُدارة حاليًا بواسطة نادل آخر: ${existingAccountResponse.waiterName || 'غير معروف'}. لا يمكنك إضافة طلبات لهذه الطاولة.`
        );
        setLoading(false);
        return;
      }

      const orderData = {
        waiterName: localStorage.getItem('username') || 'waiter',
        items: cart.map(item => ({
          product: item._id,
          name: item.name,
          quantity: item.quantity,
          price: item.price,
          notes: item.notes || ''
        })),
        totalPrice: cart.reduce((sum, item) => sum + (item.price * item.quantity), 0),
        tableNumber,
        customerName: customerName || '',
        status: 'pending'
      };      const result = await authenticatedPost('/api/orders', orderData);

      // إرسال إشعار Socket.IO للطباخين والإدارة
      if (socket && socket.connected) {
        socket.emit('order-created', {
          orderId: result._id || `ORD-${Date.now()}`,
          orderNumber: result.orderNumber || `ORD-${Date.now()}`,
          tableNumber: tableNumber,
          waiterName: localStorage.getItem('username') || 'waiter',
          items: cart.map(item => ({
            name: item.name,
            quantity: item.quantity,
            price: item.price,
            notes: item.notes || ''
          })),
          status: 'pending',
          customer: {
            name: customerName || 'عميل',
            tableNumber: tableNumber
          },
          timestamp: new Date().toISOString()
        });
        console.log('📤 تم إرسال إشعار الطلب الجديد للطباخين');
      }

      if (existingAccount) {
        showSuccess(`تم إضافة الطلب إلى حساب الطاولة ${tableNumber} بنجاح!`);
      } else {
        showSuccess(`تم إرسال الطلب وفتح حساب جديد للطاولة ${tableNumber} بنجاح!`);
      }

      clearCart();
      setCurrentScreen('orders');
      // فرض تحديث البيانات بعد إرسال طلب جديد
      setTimeout(() => {
        fetchOrders(true);
        fetchTableAccounts(true);
      }, 1000);
    } catch (error) {
      console.error('خطأ في إرسال الطلب:', error);
      const errorMessage = handleApiError(error);
      showError(errorMessage);
    }
    setLoading(false);
  };

  // Order status update functions
  const handleMarkOrderDelivered = async (orderId: string) => {
    try {
      await authenticatedPut(`/api/orders/${orderId}`, { status: 'delivered' });

      // Update local orders list
      setOrders(prevOrders => 
        prevOrders.map(order =>
          order._id === orderId 
            ? { ...order, status: 'delivered' as const }
            : order
        )
      );

      const deliveredOrder = orders.find(order => order._id === orderId);
      
      showSuccess(`تم تسليم الطلب من الطاولة رقم ${deliveredOrder?.tableNumber || 'غير محدد'} للعميل ${deliveredOrder?.customerName || 'غير محدد'} بنجاح`);
      fetchOrders();
      fetchTableAccounts();
    } catch (error) {
      console.error('خطأ في تحديث حالة الطلب:', error);
      showError('حدث خطأ أثناء تحديث حالة الطلب');
    }
  };

  // إنهاء حساب الطاولة وإغلاقها
  const handleCloseTable = async (account: any) => {
    const confirmClose = window.confirm(
      `هل أنت متأكد من إنهاء حساب الطاولة #${account.tableNumber}؟\n` +
      `المبلغ الإجمالي: ${account.totalAmount?.toFixed(2) || '0.00'} ج.م\n` +
      `عدد الطلبات: ${account.orders?.length || 0}\n\n` +
      `ملاحظة: سيتم إخفاء الطاولة من قائمتك وإرسال طلب للمدير لإنهاء الحساب نهائياً.`
    );

    if (!confirmClose) return;

    try {
      setLoading(true);

      // محاولة إغلاق حساب الطاولة باستخدام endpoints مختلفة
      let closeSuccess = false;

      try {
        // أولاً: محاولة استخدام endpoint table-accounts مع PUT
        await authenticatedPut(`/api/table-accounts/${account._id}`, {
          status: 'closed',
          closedAt: new Date().toISOString(),
          closedBy: localStorage.getItem('username') || 'waiter'
        });
        closeSuccess = true;
      } catch (firstError) {
        console.log('فشل PUT table-accounts، جاري المحاولة مع endpoint إغلاق...');

        // ثانياً: محاولة استخدام endpoint مخصص للإغلاق
        try {
          await authenticatedPost(`/api/table-accounts/${account._id}/close`, {
            closedBy: localStorage.getItem('username') || 'waiter'
          });
          closeSuccess = true;
        } catch (secondError) {
          console.log('فشل endpoint الإغلاق، جاري المحاولة مع tables...');

          // ثالثاً: محاولة استخدام PUT مع endpoint tables
          try {
            await authenticatedPut(`/api/tables/${account.tableNumber}/close`, {
              accountId: account._id,
              closedBy: localStorage.getItem('username') || 'waiter'
            });
            closeSuccess = true;
          } catch (thirdError) {
            console.log('فشل جميع endpoints، سيتم الإخفاء المحلي...');
          }
        }
      }

      if (closeSuccess) {
        showSuccess(`تم إنهاء حساب الطاولة #${account.tableNumber} بنجاح وإغلاقها`);
      } else {
        // إخفاء الطاولة محلياً وإرسال طلب للمدير
        const closedTables = JSON.parse(localStorage.getItem('closedTables') || '[]');
        closedTables.push({
          accountId: account._id,
          tableNumber: account.tableNumber,
          totalAmount: account.totalAmount,
          ordersCount: account.orders?.length || 0,
          closedBy: localStorage.getItem('username') || 'waiter',
          closedAt: new Date().toISOString()
        });
        localStorage.setItem('closedTables', JSON.stringify(closedTables));

        showSuccess(`تم إخفاء الطاولة #${account.tableNumber} من قائمتك. سيتم إرسال طلب للمدير لإنهاء الحساب نهائياً.`);
      }

      // تحديث قائمة الطاولات
      await fetchTableAccounts(true); // فرض التحديث

    } catch (error: any) {
      console.error('خطأ في إنهاء حساب الطاولة:', error);
      showError('حدث خطأ أثناء محاولة إنهاء الحساب. يرجى الاتصال بالمدير.');
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    const confirmLogout = window.confirm('هل أنت متأكد من تسجيل الخروج؟');

    if (confirmLogout) {
      localStorage.removeItem('username');
      localStorage.removeItem('jobTitle');
      localStorage.removeItem('isLoggedIn');
      localStorage.removeItem('user');
      localStorage.removeItem('userRole');
      localStorage.removeItem('waiterCart');
      localStorage.removeItem('waiterSession');

      alert('تم تسجيل الخروج بنجاح!');
      window.location.href = '/';
    }
  };

  // Helper functions
  const getCategoryIcon = (categoryName: string) => {
    switch (categoryName.toLowerCase()) {
      case 'قهوة': case 'coffee': return 'fa-coffee';
      case 'شاي': case 'tea': return 'fa-leaf';
      case 'عصائر': case 'juices': return 'fa-glass-whiskey';
      case 'مشروبات باردة': case 'cold drinks': return 'fa-snowflake';
      case 'مشروبات ساخنة': case 'hot drinks': return 'fa-fire';
      case 'حلويات': case 'desserts': return 'fa-birthday-cake';
      case 'وجبات خفيفة': case 'snacks': return 'fa-cookie-bite';
      default: return 'fa-utensils';
    }
  };

  const getOrderStatusArabic = (status: string) => {
    switch (status) {
      case 'pending': return 'معلق';
      case 'preparing': return 'قيد التحضير';
      case 'ready': return 'جاهز';
      case 'delivered': return 'تم التسليم';
      case 'cancelled': return 'ملغي';
      default: return status;
    }
  };

  const getOrderStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return '#f39c12';
      case 'preparing': return '#3498db';
      case 'ready': return '#27ae60';
      case 'delivered': return '#95a5a6';
      case 'cancelled': return '#e74c3c';
      default: return '#95a5a6';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return 'fa-clock';
      case 'preparing': return 'fa-utensils';
      case 'ready': return 'fa-check-circle';
      case 'delivered': return 'fa-truck';
      case 'cancelled': return 'fa-times-circle';
      default: return 'fa-question-circle';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'في الانتظار';
      case 'preparing': return 'قيد التحضير';
      case 'ready': return 'جاهز';
      case 'delivered': return 'تم التسليم';
      case 'cancelled': return 'ملغي';
      default: return 'غير محدد';
    }
  };

  // دالة مساعدة لتنسيق التاريخ بشكل آمن
  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return 'غير محدد';

    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return 'غير محدد';
      }
      return date.toLocaleString('ar-EG', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      console.error('خطأ في تنسيق التاريخ:', error);
      return 'غير محدد';
    }
  };

  // Initialize data on component mount  npm install mongoose bcrypt - تحميل البيانات الأساسية فقط
  useEffect(() => {
    const loadData = async () => {
      try {
        console.log('🔄 تحميل البيانات الأولية...');
        await new Promise(resolve => setTimeout(resolve, 1000)); // انتظار ثانية
        await fetchCategories();
        await new Promise(resolve => setTimeout(resolve, 2000)); // انتظار ثانيتين
        await fetchMenuItems();
        console.log('✅ تم تحميل البيانات الأولية');
      } catch (error) {
        console.error('❌ خطأ في تحميل البيانات:', error);
      }
    };

    loadData();
    loadCartFromSession();
  }, []); // تحميل مرة واحدة فقط

  // تحميل البيانات عند تغيير الشاشة - محسن للسرعة
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    if (currentScreen === 'orders') {
      console.log('🔄 تحميل شاشة الطلبات...');
      timeoutId = setTimeout(async () => {
        try {
          await fetchOrders(false); // استخدام cache
          console.log('✅ تم تحميل شاشة الطلبات');
        } catch (error) {
          console.error('❌ خطأ في تحميل الطلبات:', error);
        }
      }, 500); // تقليل الانتظار إلى نصف ثانية
    } else if (currentScreen === 'tables') {
      console.log('🔄 تحميل شاشة الطاولات...');
      timeoutId = setTimeout(async () => {
        try {
          // تحميل متوازي للطلبات والطاولات لتحسين السرعة
          const [ordersResult] = await Promise.allSettled([
            fetchOrders(false)
          ]);

          if (ordersResult.status === 'fulfilled') {
            // انتظار أقل قبل جلب الطاولات
            await new Promise(resolve => setTimeout(resolve, 1000));
            await fetchTableAccounts(false);
          }

          console.log('✅ تم تحميل شاشة الطاولات');
        } catch (error) {
          console.error('❌ خطأ في تحميل الطاولات:', error);
        }
      }, 500); // تقليل الانتظار إلى نصف ثانية
    }

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [currentScreen]);

  // تم إزالة التحديث التلقائي للطاولات لتجنب الطلبات المفرطة
  // سيتم تحديث الطاولات يدوياً عند الحاجة فقط

  // Socket.IO connection for real-time updates
  useEffect(() => {
    console.log('🔗 إعداد اتصال Socket.IO للتحديث الفوري');
    console.log('Socket object:', socket);
    console.log('Socket type:', typeof socket);

    // التحقق من وجود socket قبل الاستخدام
    if (!socket || typeof socket.on !== 'function') {
      console.warn('⚠️ Socket.IO غير متوفر، سيتم تعطيل التحديث الفوري');
      setIsConnected(false);
      return;
    }

    try {
      // التحقق من حالة الاتصال الحالية
      if (socket.connected) {
        console.log('✅ Socket.IO متصل بالفعل');
        setIsConnected(true);
      }      socket.on('connect', () => {
        console.log('🔗 متصل بالخادم للتحديث الفوري');
        setIsConnected(true);

        // تسجيل النادل في النظام
        const waiterName = localStorage.getItem('username');
        const waiterId = localStorage.getItem('waiterId') || `waiter-${waiterName}`;
        
        if (waiterName) {
          // تسجيل المستخدم مع الدور والمعلومات
          socket.emit('register-user', {
            userId: waiterId,
            role: 'waiter',
            name: waiterName
          });
          
          console.log(`👤 تسجيل النادل: ${waiterName} (${waiterId})`);
        }
      });      socket.on('connect_error', (error: any) => {
        console.error('❌ خطأ في اتصال Socket.IO:', error);
        setIsConnected(false);
        showError('فشل في الاتصال للتحديث الفوري');
      });

    socket.on('disconnect', () => {
      console.log('❌ انقطع الاتصال مع الخادم');
      setIsConnected(false);
    });

    // تأكيد تسجيل النادل
    socket.on('registration-confirmed', (data: any) => {
      console.log('✅ تم تسجيل النادل بنجاح:', data);
      showSuccess('🔗 متصل للتحديث الفوري');
    });    // استقبال تحديثات الطلبات الفورية (من الخادم الخلفي) مع تحسين الأداء
    // استخدام throttle بدلاً من debounce للحصول على تحديثات فورية محسنة
    socket.on('order-status-update', globalPerformanceOptimizer.throttle(
      'order-status-update',
      (orderUpdate: any) => {
        console.log('📦 تحديث حالة طلب فوري:', orderUpdate);
        
        // تحديث الطلب في القائمة المحلية
        setOrders(prevOrders =>
          prevOrders.map(order =>
            order._id === orderUpdate.orderId ? { ...order, status: orderUpdate.newStatus } : order
          )
        );

        // تحديث حسابات الطاولات أيضاً
        fetchTableAccounts();

        // إشعار للنادل حسب الحالة الجديدة
        if (orderUpdate.newStatus === 'ready') {
          showSuccess(`الطلب #${orderUpdate.orderNumber || orderUpdate.orderId} جاهز للتسليم!`);
        } else if (orderUpdate.newStatus === 'preparing') {
          showInfo(`الطلب #${orderUpdate.orderNumber || orderUpdate.orderId} قيد التحضير`);
        } else if (orderUpdate.newStatus === 'delivered') {
          showSuccess(`تم تسليم الطلب #${orderUpdate.orderNumber || orderUpdate.orderId}`);
        }
      },
      1000 // throttle كل ثانية بدلاً من debounce
    ));

    // استقبال إشعارات الطلبات الجديدة (من الطباخ للنادل) مع تحسين
    socket.on('new-order-notification', globalPerformanceOptimizer.throttle(
      'new-order-notification',
      (orderNotification: any) => {
        console.log('🆕 إشعار طلب جديد:', orderNotification);
        
        // تحديث قائمة الطلبات عند الحاجة فقط
        if (currentScreen === 'orders') {
          fetchOrders(true);
        }
        
        showInfo(`طلب جديد #${orderNotification.orderNumber || orderNotification.orderId} تم إضافته`);
      },
      1000 // throttle لثانية واحدة
    ));    // استقبال تحديثات حالة الطاولات مع تحسين
    socket.on('table-status-updated', globalPerformanceOptimizer.throttle(
      'table-status-updated',
      (tableUpdate: any) => {
        console.log('🏓 تحديث حالة طاولة فوري:', tableUpdate);
        
        // تحديث قائمة الطاولات عند الحاجة فقط
        if (currentScreen === 'tables') {
          fetchTableAccounts(true);
        }
        
        showInfo(tableUpdate.message || `تم تحديث حالة الطاولة ${tableUpdate.tableNumber}`);
      },
      1000 // throttle لثانية واحدة بدلاً من debounce
    ));

      setSocket(socket);      return () => {
        if (socket && typeof socket.off === 'function') {
          socket.off('connect');
          socket.off('disconnect');
          socket.off('connect_error');
          socket.off('registration-confirmed');
          socket.off('order-status-update');
          socket.off('new-order-notification'); 
          socket.off('table-status-updated');
        }
      };
    } catch (error) {
      console.error('❌ خطأ في إعداد Socket.IO:', error);
      setIsConnected(false);
    }
  }, []);
  // إشعار حالة الاتصال
  useEffect(() => {
    if (isConnected) {
      console.log('✅ التحديث الفوري متاح');
    } else {
      showInfo('⚠️ التحديث الفوري غير متوفر - سيتم التحديث يدوياً');
    }  }, [isConnected, showInfo]);
  // تحديث دوري للبيانات مع حماية من Rate Limiting
  useEffect(() => {
    if (!isConnected && (currentScreen === 'orders' || currentScreen === 'tables')) {
      console.log('🔄 بدء التحديث الدوري للبيانات (كل 4 دقائق)');

      const interval = setInterval(() => {
        console.log('🔄 تحديث دوري للبيانات...');
        if (currentScreen === 'orders') {
          fetchOrders();
        } else if (currentScreen === 'tables') {
          fetchTableAccounts();
        }
      }, 240000); // كل 4 دقائق بدلاً من دقيقتين (تحسين إضافي)

      return () => {
        console.log('⏹️ إيقاف التحديث الدوري');
        clearInterval(interval);
      };
    }
  }, [isConnected, currentScreen, fetchOrders, fetchTableAccounts]);

  useEffect(() => {
    saveCartToSession();
  }, [cart, tableNumber, customerName]);

  // Render functions
  const renderDrinksScreen = () => {
    const filteredItems = menuItems.filter(item => {
      const matchesCategory = selectedCategory === 'all' ||
        (item.categories && Array.isArray(item.categories) && item.categories.includes(selectedCategory));
      const matchesSearch = searchTerm === '' ||
        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (item.description && item.description.toLowerCase().includes(searchTerm.toLowerCase()));
      return matchesCategory && matchesSearch;
    });

    return (
      <div className="content-container">
        <div className="screen-header">
          <h1 className="screen-title">
            <i className="fas fa-coffee"></i>
            قائمة المشروبات
          </h1>
          <p className="screen-subtitle">اختر المشروبات المطلوبة وأضفها إلى السلة</p>
        </div>

        <div className="search-section">
          <div className="search-input-group">
            <i className="fas fa-search search-icon"></i>
            <input 
              type="text" 
              className="search-input" 
              placeholder="ابحث عن المشروبات..." 
              value={searchTerm} 
              onChange={(e) => setSearchTerm(e.target.value)} 
            />
            {searchTerm && (
              <button className="clear-search" onClick={() => setSearchTerm('')}>
                <i className="fas fa-times"></i>
              </button>
            )}
          </div>
        </div>

        <div className="category-filter">
          <button 
            className={`category-btn ${selectedCategory === 'all' ? 'active' : ''}`}
            onClick={() => setSelectedCategory('all')}
          >
            <i className="fas fa-th"></i>
            جميع الفئات
          </button>
          {categories.map(category => (
            <button 
              key={category._id}
              className={`category-btn ${selectedCategory === category._id ? 'active' : ''}`}
              onClick={() => setSelectedCategory(category._id)}
              style={{ borderColor: category.color }}
            >
              <i className={`fas ${getCategoryIcon(category.name)}`}></i>
              {category.name}
            </button>
          ))}
        </div>

        <div className="menu-grid">
          {filteredItems.map(item => (
            <div key={item._id} className="menu-item-card">
              <div className="menu-item-header">
                <h3 className="menu-item-name">{item.name}</h3>
                <span className="menu-item-price">{item.price} ج.م</span>
              </div>
              
              {item.description && (
                <p className="menu-item-description">{item.description}</p>
              )}
              
              <div className="menu-item-categories">
                {item.categoryDetails?.map(cat => (
                  <span 
                    key={cat._id} 
                    className="category-tag"
                    style={{ backgroundColor: cat.color }}
                  >
                    <i className={`fas ${getCategoryIcon(cat.name)}`}></i>
                    {cat.name}
                  </span>
                ))}
              </div>
              
              <div className="item-actions">
                <button
                  className="add-to-cart-btn"
                  onClick={() => addToCart(item)}
                >
                  <i className="fas fa-plus"></i>
                  إضافة للسلة
                </button>

                <button
                  className="add-with-notes-btn"
                  onClick={() => openNotesModal(item)}
                  title="إضافة مع ملاحظات"
                >
                  <i className="fas fa-sticky-note"></i>
                  ملاحظات
                </button>
              </div>
            </div>
          ))}
        </div>

        {filteredItems.length === 0 && (
          <div className="empty-state">
            <i className="fas fa-coffee"></i>
            <h3>لا توجد مشروبات متاحة</h3>
            <p>لا توجد مشروبات في هذه الفئة حالياً</p>
          </div>
        )}
      </div>
    );
  };

  const renderOrdersScreen = () => {
    const currentWaiterName = localStorage.getItem('username') || 'waiter';

    // تصفية الطلبات للنادل الحالي فقط
    const waiterOrders = orders.filter(order => order.waiterName === currentWaiterName);

    const filteredOrders = orderStatusFilter === 'all'
      ? waiterOrders
      : waiterOrders.filter(order => order.status === orderStatusFilter);

    return (
      <div className="content-container">
        <div className="screen-header">
          <h1 className="screen-title">
            <i className="fas fa-receipt"></i>
            إدارة الطلبات
          </h1>
          <p className="screen-subtitle">تتبع ومتابعة جميع الطلبات</p>
        </div>

        <div className="filter-section">
          <div className="status-filters">
            {['all', 'pending', 'preparing', 'ready', 'delivered'].map(status => (
              <button
                key={status}
                className={`status-filter ${orderStatusFilter === status ? 'active' : ''}`}
                onClick={() => setOrderStatusFilter(status as any)}
              >
                <i className={`fas ${getStatusIcon(status)}`}></i>
                <span>{getStatusText(status)}</span>
                <span className="count">({status === 'all' ? waiterOrders.length : waiterOrders.filter(o => o.status === status).length})</span>
              </button>
            ))}
          </div>
        </div>

        <div className="orders-list">
          {filteredOrders.length === 0 ? (
            <div className="empty-state">
              <i className="fas fa-receipt empty-icon"></i>
              <h3>لا توجد طلبات</h3>
              <p>لم يتم العثور على طلبات مطابقة للفلتر المحدد</p>
            </div>
          ) : (
            <div className="orders-grid">
              {filteredOrders.map(order => (
                <div key={order._id} className="order-card">
                  <div className="order-header">
                    <div className="order-number">
                      <i className="fas fa-receipt"></i>
                      طلب #{order.orderNumber || order._id?.slice(-6) || 'غير محدد'}
                    </div>
                    <div className={`order-status ${order.status}`}>
                      <i className={`fas ${getStatusIcon(order.status)}`}></i>
                      {getStatusText(order.status)}
                    </div>
                  </div>

                  <div className="order-info">
                    <div className="info-row">
                      <span className="label">
                        <i className="fas fa-table"></i>
                        الطاولة:
                      </span>
                      <span className="value">{order.tableNumber}</span>
                    </div>
                    
                    <div className="info-row">
                      <span className="label">
                        <i className="fas fa-user"></i>
                        العميل:
                      </span>
                      <span className="value">{order.customerName || 'غير محدد'}</span>
                    </div>
                    
                    <div className="info-row">
                      <span className="label">
                        <i className="fas fa-money-bill-wave"></i>
                        المبلغ:
                      </span>
                      <span className="value">{order.totalPrice} ج.م</span>
                    </div>
                    
                    <div className="info-row">
                      <span className="label">
                        <i className="fas fa-clock"></i>
                        الوقت:
                      </span>
                      <span className="value">{formatDate(order.createdAt)}</span>
                    </div>
                  </div>

                  <div className="order-actions">
                    <button 
                      className="btn-details"
                      onClick={() => {
                        setSelectedOrderDetails(order);
                        setShowOrderDetailsModal(true);
                      }}
                    >
                      <i className="fas fa-info-circle"></i>
                      التفاصيل
                    </button>
                    
                    {order.status === 'ready' && (
                      <button 
                        className="btn-deliver"
                        onClick={() => handleMarkOrderDelivered(order._id)}
                      >
                        <i className="fas fa-check"></i>
                        تم التسليم
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    );
  };

  const renderTablesScreen = () => {
    // تصفية الطاولات النشطة (المفتوحة) والتي تخص النادل الحالي
    const currentWaiterName = localStorage.getItem('username') || 'waiter';
    const closedTables = JSON.parse(localStorage.getItem('closedTables') || '[]');
    const closedTableIds = closedTables.map((table: any) => table.accountId);

    console.log('🔍 تصفية الطاولات:');
    console.log('- اسم النادل الحالي:', currentWaiterName);
    console.log('- إجمالي الطاولات:', tableAccounts.length);
    console.log('- الطاولات المغلقة محلياً:', closedTableIds.length);

    // تصفية أكثر مرونة للطاولات مع تحسين الأداء
    const activeTableAccounts = tableAccounts.filter(account => {
      const isActive = account.status === 'active' || account.isOpen;
      const belongsToWaiter = account.waiterName === currentWaiterName ||
                             account.waiter?.name === currentWaiterName ||
                             !account.waiterName; // إذا لم يكن هناك نادل محدد
      const isNotClosedLocally = !closedTableIds.includes(account._id);

      return isActive && belongsToWaiter && isNotClosedLocally;
    });

    console.log('✅ الطاولات النشطة المفلترة:', activeTableAccounts.length);

    // دالة لحساب الإجمالي بسرعة (بدون تفاصيل الطلبات)
    const getQuickTotal = (account: any) => {
      // استخدام totalAmount إذا كان متوفراً، وإلا حساب سريع
      if (account.totalAmount && account.totalAmount > 0) {
        return account.totalAmount.toFixed(2);
      }

      // حساب سريع من عدد الطلبات × متوسط سعر تقديري
      const orderCount = account.orders?.length || 0;
      const estimatedTotal = orderCount * 25; // متوسط 25 ج.م للطلب
      return estimatedTotal.toFixed(2);
    };

    return (
      <div className="content-container">
        <div className="screen-header">
          <h1 className="screen-title">
            <i className="fas fa-table"></i>
            إدارة الطاولات
          </h1>
          <p className="screen-subtitle">متابعة حسابات الطاولات النشطة</p>
        </div>

        <div className="stats-grid">
          <div className="stat-card">
            <div className="stat-icon">
              <i className="fas fa-table"></i>
            </div>
            <div className="stat-content">
              <div className="stat-number">{activeTableAccounts.length}</div>
              <div className="stat-label">طاولات نشطة</div>
            </div>
          </div>
          
          <div className="stat-card">
            <div className="stat-icon">
              <i className="fas fa-money-bill-wave"></i>
            </div>
            <div className="stat-content">              <div className="stat-number">
                {activeTableAccounts.reduce((total, account) => {                  // حساب المبلغ من الطلبات مباشرة بطريقة أكثر دقة
                  const accountTotal = account.totalAmount && account.totalAmount > 0
                    ? account.totalAmount
                    : (account.orders?.reduce((sum, order) => {
                        // التأكد من وجود السعر الصحيح للطلب
                        const orderPrice = order.totalPrice || 0;
                        return sum + orderPrice;
                      }, 0) || 0);
                  return total + accountTotal;
                }, 0).toFixed(2)} ج.م
              </div>
              <div className="stat-label">إجمالي المبيعات</div>
            </div>
          </div>
          
          <div className="stat-card">
            <div className="stat-icon">
              <i className="fas fa-receipt"></i>
            </div>
            <div className="stat-content">
              <div className="stat-number">
                {activeTableAccounts.reduce((total, account) => {
                  // عد الطلبات الفعلي وليس عدد الأصناف
                  const ordersCount = account.orders?.length || 0;
                  return total + ordersCount;
                }, 0)}
              </div>
              <div className="stat-label">إجمالي الطلبات</div>
            </div>
          </div>
        </div>

        <div className="tables-list">
          {activeTableAccounts.length === 0 ? (
            <div className="empty-state">
              <i className="fas fa-table empty-icon"></i>
              <h3>لا توجد طاولات نشطة</h3>
              <p>جميع الطاولات مغلقة حاليًا</p>
            </div>
          ) : (
            <div className="tables-grid">
              {activeTableAccounts.map(account => (
                <div key={account._id} className="table-card">
                  <div className="table-header">
                    <div className="table-number">
                      <i className="fas fa-table"></i>
                      طاولة #{account.tableNumber}
                    </div>
                    <div className={`table-status ${account.status}`}>
                      <i className="fas fa-circle"></i>
                      {account.status === 'active' ? 'نشطة' : 'مغلقة'}
                    </div>
                  </div>

                  <div className="table-info">
                    <div className="info-row">
                      <span className="label">
                        <i className="fas fa-user"></i>
                        العميل:
                      </span>
                      <span className="value">{account.customerName || 'غير محدد'}</span>
                    </div>
                    
                    <div className="info-row">
                      <span className="label">
                        <i className="fas fa-user-tie"></i>
                        النادل:
                      </span>
                      <span className="value">{account.waiterName}</span>
                    </div>
                    
                    <div className="info-row">
                      <span className="label">
                        <i className="fas fa-receipt"></i>
                        عدد الطلبات:
                      </span>
                      <span className="value">{account.orders?.length || 0}</span>
                    </div>
                    
                    <div className="info-row">
                      <span className="label">
                        <i className="fas fa-money-bill-wave"></i>
                        إجمالي المبلغ:
                      </span>
                      <span className="value">
                        {getQuickTotal(account)} ج.م
                      </span>
                    </div>
                    
                    <div className="info-row">
                      <span className="label">
                        <i className="fas fa-clock"></i>
                        وقت الفتح:
                      </span>
                      <span className="value">{formatDate(account.createdAt)}</span>
                    </div>
                  </div>

                  <div className="table-actions">
                    <button
                      className="btn-details"
                      onClick={() => {
                        setSelectedTableAccountDetails(account);
                        setShowTableDetailsModal(true);
                      }}
                    >
                      <i className="fas fa-info-circle"></i>
                      التفاصيل
                    </button>

                    <button
                      className="btn-close-table"
                      onClick={() => handleCloseTable(account)}
                      title="إنهاء الحساب وإغلاق الطاولة"
                    >
                      <i className="fas fa-times-circle"></i>
                      إنهاء الحساب
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    );
  };

  const renderCartScreen = () => (
    <div className="content-container">
      <div className="screen-header">
        <h1 className="screen-title">
          <i className="fas fa-shopping-cart"></i>
          سلة المشتريات ({cart.length})
        </h1>
        <p className="screen-subtitle">مراجعة وإدارة عناصر السلة</p>
      </div>

      {cart.length === 0 ? (
        <div className="empty-cart-state">
          <div className="empty-cart-icon">
            <i className="fas fa-shopping-cart"></i>
          </div>
          <h3>سلة المشتريات فارغة</h3>
          <p>لم تقم بإضافة أي عناصر إلى السلة بعد</p>
          <button 
            className="browse-menu-btn"
            onClick={() => setCurrentScreen('drinks')}
          >
            <i className="fas fa-coffee"></i>
            تصفح المشروبات
          </button>
        </div>
      ) : (
        <div className="cart-content">          <div className="cart-items">
            {cart.map((item, index) => (
              <div key={`${item._id}-${index}`} className="cart-item">
                <div className="cart-item-info">
                  <h4>{item.name}</h4>
                  <span className="cart-item-price">{item.price} ج.م</span>
                  
                  {/* ملاحظات للطباخ */}
                  <div className="cart-item-notes">
                    <label className="notes-label">
                      <i className="fas fa-sticky-note"></i>
                      ملاحظات للطباخ (اختياري):
                    </label>
                    <textarea
                      className="notes-input"
                      placeholder="أدخل ملاحظات خاصة للطباخ..."
                      value={item.notes || ''}
                      onChange={(e) => {
                        const updatedCart = cart.map((cartItem, cartIndex) =>
                          cartIndex === index 
                            ? { ...cartItem, notes: e.target.value }
                            : cartItem
                        );
                        setCart(updatedCart);
                        localStorage.setItem('waiterCart', JSON.stringify(updatedCart));
                      }}
                      rows={2}
                    />
                  </div>
                </div>
                
                <div className="cart-item-controls">
                  <div className="quantity-controls">
                    <button 
                      className="quantity-btn"
                      onClick={() => updateCartQuantity(item._id, item.quantity - 1)}
                    >
                      <i className="fas fa-minus"></i>
                    </button>
                    <span className="quantity">{item.quantity}</span>
                    <button 
                      className="quantity-btn"
                      onClick={() => updateCartQuantity(item._id, item.quantity + 1)}
                    >
                      <i className="fas fa-plus"></i>
                    </button>
                  </div>
                  
                  <button 
                    className="remove-item-btn"
                    onClick={() => updateCartQuantity(item._id, 0)}
                    title="حذف العنصر"
                  >
                    <i className="fas fa-trash"></i>
                  </button>
                </div>
              </div>
            ))}
          </div>

          <div className="cart-form">
            <div className="form-group">
              <label>رقم الطاولة</label>
              <input 
                type="text" 
                value={tableNumber}
                onChange={(e) => setTableNumber(e.target.value)}
                placeholder="أدخل رقم الطاولة"
                required
              />
            </div>
            
            <div className="form-group">
              <label>اسم العميل (اختياري)</label>
              <input 
                type="text" 
                value={customerName}
                onChange={(e) => setCustomerName(e.target.value)}
                placeholder="أدخل اسم العميل"
              />
            </div>
          </div>

          <div className="cart-total">
            <span>المجموع: {cart.reduce((sum, item) => sum + (item.price * item.quantity), 0)} ج.م</span>
          </div>

          <div className="cart-actions">
            <button className="clear-cart-btn" onClick={clearCart}>
              <i className="fas fa-trash"></i>
              مسح السلة
            </button>
            <button 
              className="submit-order-btn"
              onClick={submitOrder}
              disabled={loading || !tableNumber}
            >
              <i className="fas fa-paper-plane"></i>
              {loading ? 'جاري الإرسال...' : 'إرسال الطلب'}
            </button>
          </div>
        </div>
      )}
    </div>
  );

  // Sidebar Component
  const Sidebar = () => (
    <>
      {isMobile && sidebarOpen && (
        <div className="sidebar-overlay active" onClick={closeSidebar}></div>
      )}
      <div className={`dashboard-sidebar${sidebarOpen ? ' visible' : ''}${isMobile && !sidebarOpen ? ' hidden' : ''}`}
        style={isMobile ? { right: sidebarOpen ? 0 : '-100vw', transition: 'right 0.3s' } : {}}>
        
        <div className="sidebar-header">
          <div className="sidebar-logo">
            <i className="fas fa-coffee"></i>
            <span>DeshaCoffee</span>
          </div>
          {isMobile && (
            <button className="sidebar-close-btn" onClick={closeSidebar}>
              <i className="fas fa-times"></i>
            </button>
          )}
        </div>

        <nav className="sidebar-nav">
          <ul className="nav-menu">
            <li className="nav-item">
              <button 
                className={`nav-link ${currentScreen === 'drinks' ? 'active' : ''}`}
                onClick={() => { setCurrentScreen('drinks'); if(isMobile) closeSidebar(); }}
              >
                <i className="fas fa-coffee nav-icon"></i>
                <span className="nav-text">المشروبات</span>
              </button>
            </li>
            <li className="nav-item">
              <button 
                className={`nav-link ${currentScreen === 'orders' ? 'active' : ''}`}
                onClick={() => { setCurrentScreen('orders'); if(isMobile) closeSidebar(); }}
              >
                <i className="fas fa-receipt nav-icon"></i>
                <span className="nav-text">الطلبات</span>
              </button>
            </li>
            <li className="nav-item">
              <button 
                className={`nav-link ${currentScreen === 'cart' ? 'active' : ''}`}
                onClick={() => { setCurrentScreen('cart'); if(isMobile) closeSidebar(); }}
              >
                <i className="fas fa-shopping-cart nav-icon"></i>
                <span className="nav-text">سلة المشتريات ({cart.length})</span>
              </button>
            </li>
            <li className="nav-item">
              <button 
                className={`nav-link ${currentScreen === 'tables' ? 'active' : ''}`}
                onClick={() => { setCurrentScreen('tables'); if(isMobile) closeSidebar(); }}
              >
                <i className="fas fa-table nav-icon"></i>
                <span className="nav-text">الطاولات</span>
              </button>
            </li>
          </ul>
        </nav>

        <div className="sidebar-stats">
          <div className="stats-title">
            <i className="fas fa-chart-line"></i>
            إحصائيات سريعة
            <div className={`connection-status ${isConnected ? 'connected' : 'disconnected'}`}>
              <i className={`fas ${isConnected ? 'fa-wifi' : 'fa-wifi-slash'}`}></i>
              <span>{isConnected ? 'متصل' : 'غير متصل'}</span>
              {!isConnected && (
                <button
                  className="retry-connection-btn"
                  onClick={retrySocketConnection}
                  title="إعادة محاولة الاتصال"
                >
                  <i className="fas fa-redo"></i>
                </button>
              )}
            </div>
          </div>
          <div className="stat-item">
            <div className="stat-icon">
              <i className="fas fa-shopping-cart"></i>
            </div>
            <div className="stat-details">
              <span className="stat-value">{cart.length}</span>
              <span className="stat-label">عناصر في السلة</span>
            </div>
          </div>
          <div className="stat-item">
            <div className="stat-icon">
              <i className="fas fa-money-bill-wave"></i>
            </div>
            <div className="stat-details">
              <span className="stat-value">{cart.reduce((sum, item) => sum + (item.price * item.quantity), 0)}</span>
              <span className="stat-label">ج.م - المجموع</span>
            </div>
          </div>
          <div className="stat-item">
            <div className="stat-icon">
              <i className="fas fa-clock"></i>
            </div>
            <div className="stat-details">
              <span className="stat-value">{orders.filter(order => order.waiterName === localStorage.getItem('username')).length}</span>
              <span className="stat-label">طلباتي اليوم</span>
            </div>
          </div>
          <div className="stat-item">
            <div className="stat-icon">
              <i className="fas fa-table"></i>
            </div>
            <div className="stat-details">
              <span className="stat-value">{tableAccounts.filter(account => account.waiterName === localStorage.getItem('username') && account.status === 'active').length}</span>
              <span className="stat-label">طاولاتي النشطة</span>
            </div>
          </div>
          <div className="stat-item">
            <div className="stat-icon">
              <i className="fas fa-money-bill-wave"></i>
            </div>
            <div className="stat-details">
              <span className="stat-value">
                {(() => {
                  const myOrders = orders.filter(order => order.waiterName === localStorage.getItem('username'));
                  const myTableAccounts = tableAccounts.filter(account => account.waiterName === localStorage.getItem('username'));
                  const ordersTotal = myOrders.reduce((sum, order) => sum + (order.totalPrice || 0), 0);
                  const tablesTotal = myTableAccounts.reduce((sum, account) => sum + (account.totalAmount || 0), 0);
                  return Math.max(ordersTotal, tablesTotal).toFixed(2);
                })()}
              </span>
              <span className="stat-label">ج.م - إجمالي مبيعاتي</span>
            </div>
          </div>
        </div>

        <div className="sidebar-footer">
          <button className="logout-btn" onClick={handleLogout}>
            <i className="fas fa-sign-out-alt"></i>
            <span>تسجيل الخروج</span>
          </button>
        </div>
      </div>
    </>
  );

  // Main component render
  return (
    <div className="waiter-dashboard">
      {isMobile && !sidebarOpen && (
        <button className="mobile-menu-toggle" onClick={openSidebar}>
          <i className="fas fa-bars"></i>
        </button>
      )}
      
      <Sidebar />
      
      <main className="dashboard-main">
        <div className="content-card">
          {currentScreen === 'drinks' && renderDrinksScreen()}
          {currentScreen === 'orders' && renderOrdersScreen()}
          {currentScreen === 'tables' && renderTablesScreen()}
          {currentScreen === 'cart' && renderCartScreen()}
        </div>
        
        {/* Order Details Modal */}
        {showOrderDetailsModal && selectedOrderDetails && (
          <div className="modal-overlay" onClick={() => setShowOrderDetailsModal(false)}>
            <div className="modal-content order-details-modal" onClick={(e) => e.stopPropagation()}>
              <div className="modal-header">
                <h2>
                  <i className="fas fa-receipt"></i>
                  تفاصيل الطلب #{selectedOrderDetails.orderNumber || selectedOrderDetails._id?.slice(-6) || 'غير محدد'}
                </h2>
                <button 
                  className="modal-close"
                  onClick={() => setShowOrderDetailsModal(false)}
                >
                  <i className="fas fa-times"></i>
                </button>
              </div>
              
              <div className="modal-body">
                <div className="order-info-section">
                  <h3>
                    <i className="fas fa-info-circle"></i>
                    معلومات الطلب
                  </h3>
                  <div className="order-info-grid">
                    <div className="info-item">
                      <span className="label">الطاولة:</span>
                      <span className="value">{selectedOrderDetails.tableNumber}</span>
                    </div>
                    <div className="info-item">
                      <span className="label">العميل:</span>
                      <span className="value">{selectedOrderDetails.customerName || 'غير محدد'}</span>
                    </div>
                    <div className="info-item">
                      <span className="label">النادل:</span>
                      <span className="value">{selectedOrderDetails.waiterName}</span>
                    </div>
                    <div className="info-item">
                      <span className="label">الحالة:</span>
                      <span className={`status-badge ${selectedOrderDetails.status}`}>
                        <i className={`fas ${getStatusIcon(selectedOrderDetails.status)}`}></i>
                        {getStatusText(selectedOrderDetails.status)}
                      </span>
                    </div>
                    <div className="info-item">
                      <span className="label">وقت الطلب:</span>
                      <span className="value">{formatDate(selectedOrderDetails.createdAt)}</span>
                    </div>
                    <div className="info-item">
                      <span className="label">المبلغ الإجمالي:</span>
                      <span className="value total-amount">{selectedOrderDetails.totalPrice} ج.م</span>
                    </div>
                  </div>
                </div>

                <div className="order-items-section">
                  <h3>
                    <i className="fas fa-list"></i>
                    الأصناف المطلوبة ({selectedOrderDetails.items?.length || 0})
                  </h3>
                  <div className="items-list">
                    {(selectedOrderDetails.items || []).map((item, index) => (
                      <div key={index} className="item-card">
                        <div className="item-info">
                          <div className="item-name">{item.name}</div>
                          {item.notes && (
                            <div className="item-notes">
                              <i className="fas fa-sticky-note"></i>
                              ملاحظة: {item.notes}
                            </div>
                          )}
                        </div>
                        <div className="item-details">
                          <div className="item-quantity">
                            <i className="fas fa-times"></i>
                            {item.quantity}
                          </div>
                          <div className="item-price">{item.price} ج.م</div>
                          <div className="item-total">{(item.price * item.quantity).toFixed(2)} ج.م</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="discount-section">
                  <h3>
                    <i className="fas fa-percentage"></i>
                    إدارة الخصم
                  </h3>
                  {selectedOrderDetails.discountStatus ? (
                    <div className={`discount-status ${selectedOrderDetails.discountStatus}`}>
                      <i className="fas fa-info-circle"></i>
                      <span>
                        {selectedOrderDetails.discountStatus === 'pending' && 'طلب خصم قيد المراجعة'}
                        {selectedOrderDetails.discountStatus === 'approved' && `تم الموافقة على خصم ${selectedOrderDetails.discountAmount} ج.م`}
                        {selectedOrderDetails.discountStatus === 'rejected' && 'تم رفض طلب الخصم'}
                      </span>
                    </div>
                  ) : (
                    <button 
                      className="btn-discount"
                      onClick={() => {
                        setSelectedOrderForDiscount(selectedOrderDetails);
                        setShowDiscountModal(true);
                        setShowOrderDetailsModal(false);
                      }}
                    >
                      <i className="fas fa-percentage"></i>
                      طلب خصم
                    </button>
                  )}
                </div>
              </div>
              
              <div className="modal-footer">
                {selectedOrderDetails.status === 'ready' && (
                  <button 
                    className="btn-deliver"
                    onClick={() => {
                      handleMarkOrderDelivered(selectedOrderDetails._id);
                      setShowOrderDetailsModal(false);
                    }}
                  >
                    <i className="fas fa-check"></i>
                    تم التسليم
                  </button>
                )}
                <button 
                  className="btn-close"
                  onClick={() => setShowOrderDetailsModal(false)}
                >
                  إغلاق
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Table Details Modal */}
        {showTableDetailsModal && selectedTableAccountDetails && (
          <div className="modal-overlay" onClick={() => setShowTableDetailsModal(false)}>
            <div className="modal-content table-details-modal" onClick={(e) => e.stopPropagation()}>
              <div className="modal-header">
                <h2>
                  <i className="fas fa-table"></i>
                  تفاصيل الطاولة #{selectedTableAccountDetails.tableNumber}
                </h2>
                <button 
                  className="modal-close"
                  onClick={() => setShowTableDetailsModal(false)}
                >
                  <i className="fas fa-times"></i>
                </button>
              </div>
              
              <div className="modal-body">
                <div className="table-info-section">
                  <h3>
                    <i className="fas fa-info-circle"></i>
                    معلومات الطاولة
                  </h3>
                  <div className="table-info-grid">
                    <div className="info-item">
                      <span className="label">رقم الطاولة:</span>
                      <span className="value">{selectedTableAccountDetails.tableNumber}</span>
                    </div>
                    <div className="info-item">
                      <span className="label">العميل:</span>
                      <span className="value">{selectedTableAccountDetails.customerName || 'غير محدد'}</span>
                    </div>
                    <div className="info-item">
                      <span className="label">النادل:</span>
                      <span className="value">{selectedTableAccountDetails.waiterName}</span>
                    </div>
                    <div className="info-item">
                      <span className="label">الحالة:</span>
                      <span className={`status-badge ${selectedTableAccountDetails.status}`}>
                        <i className="fas fa-circle"></i>
                        {selectedTableAccountDetails.status === 'active' ? 'نشطة' : 'مغلقة'}
                      </span>
                    </div>
                    <div className="info-item">
                      <span className="label">وقت الفتح:</span>
                      <span className="value">{formatDate(selectedTableAccountDetails.createdAt)}</span>
                    </div>
                    <div className="info-item">
                      <span className="label">آخر نشاط:</span>
                      <span className="value">{formatDate(selectedTableAccountDetails.updatedAt)}</span>
                    </div>
                    <div className="info-item">
                      <span className="label">المبلغ الإجمالي:</span>
                      <span className="value total-amount">
                        {(selectedTableAccountDetails.totalAmount > 0
                          ? selectedTableAccountDetails.totalAmount
                          : (selectedTableAccountDetails.orders?.reduce((sum, order) => sum + (order.totalPrice || 0), 0) || 0)
                        ).toFixed(2)} ج.م
                      </span>
                    </div>
                  </div>
                </div>

                <div className="table-orders-section">
                  <h3>
                    <i className="fas fa-receipt"></i>
                    طلبات الطاولة ({selectedTableAccountDetails.orders?.length || 0})
                  </h3>
                  <div className="orders-list">
                    {(selectedTableAccountDetails.orders || []).map((order, index) => (
                      <div key={index} className="order-summary-card">
                        <div className="order-summary-header">
                          <div className="order-number">طلب #{order.orderNumber || order._id?.slice(-6) || 'غير محدد'}</div>
                          <div className={`order-status ${order.status}`}>
                            <i className={`fas ${getStatusIcon(order.status)}`}></i>
                            {getStatusText(order.status)}
                          </div>
                        </div>
                        <div className="order-summary-info">
                          <div className="summary-item">
                            <span>الأصناف: {order.items?.length || 0}</span>
                          </div>                          <div className="summary-item">
                            <span>المبلغ: {(order.totalPrice || 0).toFixed(2)} ج.م</span>
                          </div>
                          <div className="summary-item">
                            <span>الوقت: {formatDate(order.createdAt)}</span>
                          </div>
                        </div>
                        <button 
                          className="btn-order-details"
                          onClick={() => {
                            setSelectedOrderDetails(order);
                            setShowTableDetailsModal(false);
                            setShowOrderDetailsModal(true);
                          }}
                        >
                          <i className="fas fa-eye"></i>
                          عرض التفاصيل
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
              
              <div className="modal-footer">
                <button 
                  className="btn-close"
                  onClick={() => setShowTableDetailsModal(false)}
                >
                  إغلاق
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Discount Request Modal */}
        {showDiscountModal && selectedOrderForDiscount && (
          <div className="modal-overlay" onClick={() => setShowDiscountModal(false)}>
            <div className="modal-content discount-modal" onClick={(e) => e.stopPropagation()}>
              <div className="modal-header">
                <h2>
                  <i className="fas fa-percentage"></i>
                  طلب خصم للطلب #{selectedOrderForDiscount.orderNumber}
                </h2>
                <button 
                  className="modal-close"
                  onClick={() => setShowDiscountModal(false)}
                >
                  <i className="fas fa-times"></i>
                </button>
              </div>
              
              <div className="modal-body">
                <div className="order-summary">
                  <h3>ملخص الطلب</h3>
                  <div className="summary-info">
                    <div>الطاولة: {selectedOrderForDiscount.tableNumber}</div>
                    <div>العميل: {selectedOrderForDiscount.customerName || 'غير محدد'}</div>
                    <div>المبلغ الأصلي: {selectedOrderForDiscount.totalPrice} ج.م</div>
                  </div>
                </div>

                <div className="discount-form">
                  <div className="form-group">
                    <label htmlFor="discount-amount">مبلغ الخصم (ج.م)</label>
                    <input
                      id="discount-amount"
                      type="number"
                      value={discountAmount}
                      onChange={(e) => setDiscountAmount(e.target.value)}
                      placeholder="أدخل مبلغ الخصم"
                      min="0"
                      max={selectedOrderForDiscount.totalPrice}
                      step="0.1"
                    />
                  </div>
                  
                  <div className="form-group">
                    <label htmlFor="discount-reason">سبب الخصم</label>
                    <textarea
                      id="discount-reason"
                      value={discountReason}
                      onChange={(e) => setDiscountReason(e.target.value)}
                      placeholder="أدخل سبب طلب الخصم"
                      rows={3}
                    />
                  </div>

                  {discountAmount && (
                    <div className="discount-preview">
                      <div className="preview-item">
                        <span>المبلغ الأصلي:</span>
                        <span>{selectedOrderForDiscount.totalPrice} ج.م</span>
                      </div>
                      <div className="preview-item">
                        <span>مبلغ الخصم:</span>
                        <span>-{discountAmount} ج.م</span>
                      </div>
                      <div className="preview-item total">
                        <span>المبلغ بعد الخصم:</span>
                        <span>{(selectedOrderForDiscount.totalPrice - parseFloat(discountAmount || '0')).toFixed(2)} ج.م</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
              
              <div className="modal-footer">
                <button 
                  className="btn-submit"
                  disabled={!discountAmount || !discountReason}
                  onClick={() => {
                    // Here you would call the discount request API
                    showSuccess('تم إرسال طلب الخصم للمراجعة');
                    setShowDiscountModal(false);
                    setDiscountAmount('');
                    setDiscountReason('');
                    setSelectedOrderForDiscount(null);
                  }}
                >
                  <i className="fas fa-paper-plane"></i>
                  إرسال طلب الخصم
                </button>
                <button 
                  className="btn-cancel"
                  onClick={() => {
                    setShowDiscountModal(false);
                    setDiscountAmount('');
                    setDiscountReason('');
                    setSelectedOrderForDiscount(null);
                  }}
                >
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Notes Modal */}
        {showNotesModal && selectedItemForNotes && (
          <div className="modal-overlay" onClick={() => setShowNotesModal(false)}>
            <div className="modal-content notes-modal" onClick={(e) => e.stopPropagation()}>
              <div className="modal-header">
                <h2>
                  <i className="fas fa-sticky-note"></i>
                  إضافة ملاحظات - {selectedItemForNotes.name}
                </h2>
                <button
                  className="modal-close"
                  onClick={() => setShowNotesModal(false)}
                >
                  <i className="fas fa-times"></i>
                </button>
              </div>

              <div className="modal-body">
                <div className="item-preview">
                  <div className="item-info">
                    <h3>{selectedItemForNotes.name}</h3>
                    <p className="item-price">{selectedItemForNotes.price} ج.م</p>
                    {selectedItemForNotes.description && (
                      <p className="item-description">{selectedItemForNotes.description}</p>
                    )}
                  </div>
                </div>

                <div className="notes-form">
                  <label htmlFor="item-notes">ملاحظات خاصة:</label>
                  <textarea
                    id="item-notes"
                    value={itemNotes}
                    onChange={(e) => setItemNotes(e.target.value)}
                    placeholder="أدخل ملاحظات خاصة للمشروب (مثل: بدون سكر، ساخن جداً، إضافة كريمة...)"
                    rows={4}
                    maxLength={200}
                  />
                  <div className="character-count">
                    {itemNotes.length}/200 حرف
                  </div>
                </div>
              </div>

              <div className="modal-footer">
                <button
                  className="btn-add-with-notes"
                  onClick={addItemWithNotes}
                >
                  <i className="fas fa-plus"></i>
                  إضافة للسلة مع الملاحظات
                </button>
                <button
                  className="btn-cancel"
                  onClick={() => setShowNotesModal(false)}
                >
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Toast Notifications */}
        <div className="toast-container">
          {toasts.map(toast => (
            <div 
              key={toast.id} 
              className={`toast toast-${toast.type}`}
              onClick={() => removeToast(toast.id)}
            >
              <i className={`fas ${toast.type === 'success' ? 'fa-check-circle' : 
                                   toast.type === 'error' ? 'fa-exclamation-circle' : 
                                   'fa-info-circle'}`}></i>
              <span>{toast.message}</span>
              <button className="toast-close" onClick={() => removeToast(toast.id)}>
                <i className="fas fa-times"></i>
              </button>
            </div>
          ))}
        </div>
      </main>
    </div>
  );
}
