// إعدادات محسنة لحل مشكلة Rate Limiting
export const RATE_LIMIT_CONFIG = {
  // التحديث التلقائي
  AUTO_REFRESH: {
    ENABLED: true,
    INTERVAL: 180000, // 3 دقائق بدلاً من 30 ثانية
    MAX_CONCURRENT_REQUESTS: 2, // أقصى عدد طلبات متزامنة
    RETRY_DELAY: 5000, // تأخير إعادة المحاولة
  },

  // Cache للبيانات
  CACHE: {
    DURATION: 45000, // 45 ثانية
    MAX_AGE: 300000, // 5 دقائق للـ cache القديم
  },

  // Rate Limiting
  RATE_LIMITS: {
    MAX_REQUESTS_PER_MINUTE: 10,
    MIN_REQUEST_INTERVAL: 3000, // 3 ثواني بين الطلبات
    BACKOFF_MULTIPLIER: 2,
    MAX_RETRY_ATTEMPTS: 3,
  },

  // Health Check
  HEALTH_CHECK: {
    ENABLED: false, // تعطيل Health Check المفرط
    INTERVAL: 300000, // 5 دقائق إذا تم تفعيله
  },

  // Error Handling
  ERROR_HANDLING: {
    RETRY_ON_429: true,
    RETRY_DELAY_429: 10000, // 10 ثواني للـ 429 errors
    FALLBACK_TO_CACHE: true,
  }
};

// دالة للتحقق من الحاجة للتحديث
export function shouldRefresh(lastFetch: number, forceRefresh: boolean = false): boolean {
  if (forceRefresh) return true;
  
  const now = Date.now();
  const timeSinceLastFetch = now - lastFetch;
  
  return timeSinceLastFetch >= RATE_LIMIT_CONFIG.CACHE.DURATION;
}

// دالة للحصول على تأخير آمن
export function getSafeDelay(requestType: string): number {
  const delays = {
    orders: 1000,
    tableAccounts: 1500,
    menuItems: 500,
    categories: 300,
    healthCheck: 2000,
  };
  
  return delays[requestType as keyof typeof delays] || 1000;
}

// دالة لإدارة الطلبات المتسلسلة
export class RequestQueue {
  private queue: (() => Promise<any>)[] = [];
  private processing = false;

  async add<T>(request: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.queue.push(async () => {
        try {
          const result = await request();
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });
      
      this.process();
    });
  }

  private async process() {
    if (this.processing || this.queue.length === 0) return;
    
    this.processing = true;
    
    while (this.queue.length > 0) {
      const request = this.queue.shift();
      if (request) {
        try {
          await request();
          // انتظار بين الطلبات
          await new Promise(resolve => 
            setTimeout(resolve, RATE_LIMIT_CONFIG.RATE_LIMITS.MIN_REQUEST_INTERVAL)
          );
        } catch (error) {
          console.error('Request queue error:', error);
        }
      }
    }
    
    this.processing = false;
  }
}

// Global request queue
export const globalRequestQueue = new RequestQueue();
