/* Sidebar Component Styles */
.sidebar {
  width: 250px;
  background: #2c3e50;
  color: white;
  height: 100vh;
  position: fixed;
  right: 0;
  top: 0;
  padding: 1rem;
  box-shadow: -2px 0 5px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.sidebar.collapsed {
  width: 70px;
}

.sidebar-header {
  text-align: center;
  padding: 1rem 0;
  border-bottom: 1px solid rgba(255,255,255,0.1);
  margin-bottom: 1rem;
}

.sidebar-logo {
  font-size: 1.5rem;
  font-weight: bold;
}

.sidebar-nav {
  list-style: none;
  padding: 0;
}

.sidebar-nav li {
  margin-bottom: 0.5rem;
}

.sidebar-nav button {
  width: 100%;
  padding: 1rem;
  background: none;
  border: none;
  color: white;
  text-align: right;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.sidebar-nav button:hover,
.sidebar-nav button.active {
  background: rgba(255,255,255,0.1);
}

.sidebar-footer {
  position: absolute;
  bottom: 1rem;
  left: 1rem;
  right: 1rem;
}

.logout-btn {
  width: 100%;
  padding: 1rem;
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.logout-btn:hover {
  background: #c0392b;
}
