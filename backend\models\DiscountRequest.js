const mongoose = require('mongoose');

const discountRequestSchema = new mongoose.Schema({
  orderId: {
    type: String,
    required: true
  },
  customerName: {
    type: String,
    required: true
  },
  originalAmount: {
    type: Number,
    required: true
  },
  requestedDiscount: {
    type: Number,
    required: true
  },
  reason: {
    type: String,
    required: true
  },
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected'],
    default: 'pending'
  },
  requestedBy: {
    type: String,
    required: true
  },
  approvedBy: {
    type: String
  }
}, {
  timestamps: true
});

module.exports = mongoose.model('DiscountRequest', discountRequestSchema);
