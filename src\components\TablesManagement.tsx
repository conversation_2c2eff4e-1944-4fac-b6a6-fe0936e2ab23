import { useState, useEffect } from 'react';
import socket from '../socket';
import { useToast } from '../hooks/useToast';
import { ToastContainer } from './Toast';
import type { Order } from '../types/Order';
import { getOrderTotal } from '../types/Order';
import { authenticatedGet, authenticatedPost, authenticatedPut, handleApiError } from '../utils/apiHelpers';

// تم نقل interface Order إلى src/types/Order.ts

interface TableAccount {
  _id: string;
  tableNumber: string;
  waiterName: string;
  orders: Order[];
  totalAmount: number;
  status: 'active' | 'closed';
  createdAt: string;
  closedAt?: string;
  customerName?: string;
  notes?: string;
}

export default function TablesManagement() {
  const [tables, setTables] = useState<TableAccount[]>([]);
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedTable, setSelectedTable] = useState<TableAccount | null>(null);
  const [showTableModal, setShowTableModal] = useState(false);
  const [showCloseConfirm, setShowCloseConfirm] = useState(false);
  const [tableToClose, setTableToClose] = useState<TableAccount | null>(null);
  const [longRunningTables, setLongRunningTables] = useState<TableAccount[]>([]);
  const { toasts, removeToast, showSuccess, showError } = useToast();

  // جلب البيانات
  const fetchTables = async () => {
    try {
      const data = await authenticatedGet('/api/table-accounts');
      setTables(data);
    } catch (error) {
      console.error('Error fetching tables:', error);
      const errorMessage = handleApiError(error);
      showError(errorMessage);
    }
  };

  const fetchOrders = async () => {
    try {
      const data = await authenticatedGet('/api/orders');
      setOrders(data);
    } catch (error) {
      console.error('Error fetching orders:', error);
      const errorMessage = handleApiError(error);
      showError(errorMessage);
    }
  };

  // جلب الطاولات طويلة المدى
  const fetchLongRunningTables = async () => {
    try {
      const data = await authenticatedGet('/api/table-accounts/long-running');
      setLongRunningTables(data);

      // إظهار إشعار إذا كان هناك طاولات تتجاوز الساعة
      if (data.length > 0) {
        showError(`تنبيه: يوجد ${data.length} طاولة مفتوحة لأكثر من ساعة!`);
      }
    } catch (error) {
      console.error('خطأ في جلب الطاولات طويلة المدى:', error);
      const errorMessage = handleApiError(error);
      showError(errorMessage);
    }
  };

  useEffect(() => {
    fetchTables();
    fetchOrders();
    fetchLongRunningTables();

    // إعداد Socket.IO للتحديثات الفورية
    socket.emit('register', { role: 'مدير', name: localStorage.getItem('username') || 'مدير' });

    // فحص الطاولات طويلة المدى كل 5 دقائق
    const longRunningInterval = setInterval(() => {
      fetchLongRunningTables();
    }, 5 * 60 * 1000);

    const handleUpdate = () => {
      fetchTables();
    fetchOrders();
  };

  socket.on('tableAccountUpdate', handleUpdate);
  socket.on('orderUpdate', handleUpdate);
  socket.on('newOrder', handleUpdate);
  // استقبال أحداث Socket.IO المحدثة
  socket.on('order-status-update', handleUpdate);
  socket.on('new-order-notification', handleUpdate);
  socket.on('table-status-updated', handleUpdate);

  // تحديث كل 4 دقائق بدلاً من 30 ثانية (تحسين الأداء)
  const interval = setInterval(() => {
    fetchTables();
    fetchOrders();
  }, 240000);

  return () => {
    socket.off('tableAccountUpdate', handleUpdate);
    socket.off('orderUpdate', handleUpdate);
    socket.off('newOrder', handleUpdate);
    socket.off('order-status-update', handleUpdate);
    socket.off('new-order-notification', handleUpdate);
    socket.off('table-status-updated', handleUpdate);
      clearInterval(interval);
      clearInterval(longRunningInterval);
    };
  }, []);

  // إغلاق حساب الطاولة
  const closeTableAccount = async (tableId: string) => {
    if (!tableToClose) {
      showError('لم يتم تحديد الطاولة للإغلاق');
      return;
    }

    setLoading(true);
    try {
      const response = await authenticatedPut(`/api/table-accounts/${tableId}/close`, {
        closedBy: localStorage.getItem('username') || 'مدير',
        closedAt: new Date().toISOString()
      });

      // Check if the response indicates success
      if (response && (response.success === true || response.data)) {
        showSuccess('تم إغلاق حساب الطاولة بنجاح');
        
        // إرسال إشعار للنادل
        socket.emit('tableAccountClosed', {
          tableNumber: tableToClose.tableNumber,
          waiterName: tableToClose.waiterName,
          totalAmount: tableToClose.totalAmount
        });

        // تحديث البيانات
        await fetchTables();
        
        // إغلاق المودال
        setShowCloseConfirm(false);
        setTableToClose(null);
      } else {
        throw new Error(response?.message || 'فشل في إغلاق الطاولة');
      }
    } catch (error) {
      console.error('Error closing table account:', error);
      const errorMessage = handleApiError(error);
      showError(errorMessage || 'حدث خطأ أثناء إغلاق حساب الطاولة');
    }
    setLoading(false);
  };

  // حساب إحصائيات الطاولات
  const tableStats = {
    total: tables.length,
    active: tables.filter(t => t.status === 'active').length,
    closed: tables.filter(t => t.status === 'closed').length,
    totalRevenue: tables.filter(t => t.status === 'closed').reduce((sum, table) => sum + table.totalAmount, 0),
    activeRevenue: tables.filter(t => t.status === 'active').reduce((sum, table) => sum + table.totalAmount, 0)
  };

  // الطاولات النشطة مرتبة حسب الوقت
  const activeTables = tables.filter(t => t.status === 'active').sort((a, b) =>
    new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
  );

  // الطاولات المغلقة حديثاً
  const recentClosedTables = tables.filter(t => t.status === 'closed').sort((a, b) =>
    new Date(b.closedAt || b.createdAt).getTime() - new Date(a.closedAt || a.createdAt).getTime()
  ).slice(0, 5);

  const getTableStatusColor = (status: string) => {
    switch (status) {
      case 'active': return '#4caf50';
      case 'closed': return '#9e9e9e';
      default: return '#666';
    }
  };

  const getTableStatusText = (status: string) => {
    switch (status) {
      case 'active': return 'نشط';
      case 'closed': return 'مغلق';
      default: return status;
    }
  };

  const formatDuration = (startTime: string) => {
    const start = new Date(startTime);
    const now = new Date();
    const diffMs = now.getTime() - start.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const hours = Math.floor(diffMins / 60);
    const minutes = diffMins % 60;

    if (hours > 0) {
      return `${hours}س ${minutes}د`;
    }
    return `${minutes}د`;
  };

  return (
    <div style={{ padding: '2rem', direction: 'rtl', minHeight: '100vh', background: '#f5f5f5' }}>
      {/* Header */}
      <div style={{
        background: 'linear-gradient(135deg, #8e24aa 0%, #ab47bc 100%)',
        color: 'white',
        padding: '2rem',
        borderRadius: '16px',
        marginBottom: '2rem',
        boxShadow: '0 8px 32px rgba(0,0,0,0.1)'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1.5rem' }}>
          <div>
            <h1 style={{ margin: 0, fontSize: '2rem', fontWeight: '700' }}>
              <i className="fas fa-table" style={{ marginLeft: '0.5rem' }}></i>
              إدارة الطاولات
            </h1>
            <p style={{ margin: '0.5rem 0 0 0', opacity: 0.9 }}>
              نظام إدارة حسابات الطاولات والعملاء
            </p>
          </div>
          <div style={{ display: 'flex', gap: '0.5rem' }}>
            <button
              onClick={() => { fetchTables(); fetchOrders(); fetchLongRunningTables(); }}
              disabled={loading}
              style={{
                background: '#4caf50',
                color: 'white',
                border: 'none',
                padding: '0.75rem 1.5rem',
                borderRadius: '8px',
                cursor: 'pointer',
                fontSize: '0.9rem',
                fontWeight: '600'
              }}
            >
              <i className="fas fa-sync-alt" style={{ marginLeft: '0.5rem' }}></i>
              تحديث
            </button>
            {longRunningTables.length > 0 && (
              <button
                onClick={() => fetchLongRunningTables()}
                style={{
                  background: '#ff5722',
                  color: 'white',
                  border: 'none',
                  padding: '0.75rem 1.5rem',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  fontSize: '0.9rem',
                  fontWeight: '600'
                }}
              >
                <i className="fas fa-exclamation-triangle" style={{ marginLeft: '0.5rem' }}></i>
                طاولات تتجاوز الساعة ({longRunningTables.length})
              </button>
            )}
          </div>
        </div>

        {/* إحصائيات الطاولات */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
          gap: '1rem'
        }}>
          <div style={{ background: 'rgba(255,255,255,0.15)', padding: '1rem', borderRadius: '12px', textAlign: 'center' }}>
            <i className="fas fa-table" style={{ fontSize: '1.5rem', color: '#ffab40', marginBottom: '0.5rem' }}></i>
            <div style={{ fontSize: '1.5rem', fontWeight: '700' }}>{tableStats.total}</div>
            <div style={{ fontSize: '0.8rem', opacity: 0.8 }}>إجمالي الطاولات</div>
          </div>
          <div style={{ background: 'rgba(255,255,255,0.15)', padding: '1rem', borderRadius: '12px', textAlign: 'center' }}>
            <i className="fas fa-play-circle" style={{ fontSize: '1.5rem', color: '#4caf50', marginBottom: '0.5rem' }}></i>
            <div style={{ fontSize: '1.5rem', fontWeight: '700' }}>{tableStats.active}</div>
            <div style={{ fontSize: '0.8rem', opacity: 0.8 }}>طاولات نشطة</div>
          </div>
          <div style={{ background: 'rgba(255,255,255,0.15)', padding: '1rem', borderRadius: '12px', textAlign: 'center' }}>
            <i className="fas fa-check-circle" style={{ fontSize: '1.5rem', color: '#9e9e9e', marginBottom: '0.5rem' }}></i>
            <div style={{ fontSize: '1.5rem', fontWeight: '700' }}>{tableStats.closed}</div>
            <div style={{ fontSize: '0.8rem', opacity: 0.8 }}>طاولات مغلقة</div>
          </div>
          <div style={{ background: 'rgba(255,255,255,0.15)', padding: '1rem', borderRadius: '12px', textAlign: 'center' }}>
            <i className="fas fa-money-bill-wave" style={{ fontSize: '1.5rem', color: '#4caf50', marginBottom: '0.5rem' }}></i>
            <div style={{ fontSize: '1.2rem', fontWeight: '700' }}>{tableStats.activeRevenue.toFixed(2)}</div>
            <div style={{ fontSize: '0.8rem', opacity: 0.8 }}>إيرادات نشطة</div>
          </div>
          <div style={{ background: 'rgba(255,255,255,0.15)', padding: '1rem', borderRadius: '12px', textAlign: 'center' }}>
            <i className="fas fa-chart-line" style={{ fontSize: '1.5rem', color: '#2196f3', marginBottom: '0.5rem' }}></i>
            <div style={{ fontSize: '1.2rem', fontWeight: '700' }}>{tableStats.totalRevenue.toFixed(2)}</div>
            <div style={{ fontSize: '0.8rem', opacity: 0.8 }}>إجمالي الإيرادات</div>
          </div>
        </div>
      </div>

      {/* الطاولات النشطة */}
      <div style={{
        background: 'white',
        padding: '2rem',
        borderRadius: '16px',
        marginBottom: '2rem',
        boxShadow: '0 4px 16px rgba(0,0,0,0.1)'
      }}>
        <h3 style={{ margin: '0 0 1.5rem 0', color: '#333', fontSize: '1.5rem' }}>
          <i className="fas fa-play-circle" style={{ marginLeft: '0.5rem', color: '#4caf50' }}></i>
          الطاولات النشطة ({activeTables.length})
        </h3>

        {activeTables.length === 0 ? (
          <div style={{
            textAlign: 'center',
            padding: '3rem',
            color: '#666',
            fontSize: '1.1rem'
          }}>
            <i className="fas fa-table" style={{ fontSize: '3rem', marginBottom: '1rem', color: '#ddd' }}></i>
            <div>لا توجد طاولات نشطة حالياً</div>
          </div>
        ) : (
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
            gap: '1.5rem'
          }}>
            {activeTables.map((table) => (
              <div key={table._id} style={{
                background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
                border: '2px solid #4caf50',
                borderRadius: '12px',
                padding: '1.5rem',
                transition: 'transform 0.2s ease'
              }}>
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginBottom: '1rem'
                }}>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center'
                  }}>
                    <div style={{
                      background: '#4caf50',
                      color: 'white',
                      width: '50px',
                      height: '50px',
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginLeft: '1rem',
                      fontSize: '1.2rem',
                      fontWeight: '700'
                    }}>
                      {table.tableNumber}
                    </div>
                    <div>
                      <h4 style={{ margin: 0, color: '#333', fontSize: '1.2rem' }}>
                        طاولة {table.tableNumber}
                      </h4>
                      <p style={{ margin: 0, color: '#666', fontSize: '0.9rem' }}>
                        النادل: {table.waiterName}
                      </p>
                    </div>
                  </div>
                  <div style={{ textAlign: 'left' }}>
                    <div style={{ color: '#4caf50', fontSize: '1.1rem', fontWeight: '600' }}>
                      {table.totalAmount.toFixed(2)} ج.م
                    </div>
                    <div style={{ color: '#666', fontSize: '0.8rem' }}>
                      {formatDuration(table.createdAt)}
                    </div>
                  </div>
                </div>

                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(2, 1fr)',
                  gap: '0.75rem',
                  fontSize: '0.9rem',
                  marginBottom: '1rem'
                }}>
                  <div style={{ background: 'white', padding: '0.75rem', borderRadius: '8px', textAlign: 'center' }}>
                    <div style={{ color: '#666' }}>عدد الطلبات</div>
                    <div style={{ fontSize: '1.2rem', fontWeight: '600', color: '#333' }}>{table.orders.length}</div>
                  </div>
                  <div style={{ background: 'white', padding: '0.75rem', borderRadius: '8px', textAlign: 'center' }}>
                    <div style={{ color: '#666' }}>متوسط الطلب</div>
                    <div style={{ fontSize: '1rem', fontWeight: '600', color: '#9c27b0' }}>
                      {table.orders.length > 0 ? (table.totalAmount / table.orders.length).toFixed(2) : '0.00'} ج.م
                    </div>
                  </div>
                </div>

                <div style={{ display: 'flex', gap: '0.5rem' }}>
                  <button
                    onClick={() => {
                      setSelectedTable(table);
                      setShowTableModal(true);
                    }}
                    style={{
                      flex: 1,
                      background: '#2196f3',
                      color: 'white',
                      border: 'none',
                      padding: '0.75rem',
                      borderRadius: '8px',
                      cursor: 'pointer',
                      fontSize: '0.9rem',
                      fontWeight: '600'
                    }}
                  >
                    <i className="fas fa-eye" style={{ marginLeft: '0.25rem' }}></i>
                    عرض التفاصيل
                  </button>
                  <button
                    onClick={() => {
                      setTableToClose(table);
                      setShowCloseConfirm(true);
                    }}
                    style={{
                      flex: 1,
                      background: '#f44336',
                      color: 'white',
                      border: 'none',
                      padding: '0.75rem',
                      borderRadius: '8px',
                      cursor: 'pointer',
                      fontSize: '0.9rem',
                      fontWeight: '600'
                    }}
                  >
                    <i className="fas fa-times" style={{ marginLeft: '0.25rem' }}></i>
                    إغلاق الحساب
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* الطاولات طويلة المدى */}
      {longRunningTables.length > 0 && (
        <div style={{
          background: 'linear-gradient(135deg, #ff5722 0%, #f44336 100%)',
          color: 'white',
          padding: '2rem',
          borderRadius: '16px',
          marginBottom: '2rem',
          boxShadow: '0 4px 16px rgba(255,87,34,0.3)'
        }}>
          <h3 style={{ margin: '0 0 1.5rem 0', fontSize: '1.5rem' }}>
            <i className="fas fa-exclamation-triangle" style={{ marginLeft: '0.5rem' }}></i>
            تنبيه: طاولات تتجاوز الساعة ({longRunningTables.length})
          </h3>

          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
            gap: '1rem'
          }}>
            {longRunningTables.map((table) => (
              <div key={table._id} style={{
                background: 'rgba(255,255,255,0.15)',
                borderRadius: '12px',
                padding: '1.5rem',
                border: '2px solid rgba(255,255,255,0.3)'
              }}>
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginBottom: '1rem'
                }}>
                  <div>
                    <h4 style={{ margin: 0, fontSize: '1.2rem' }}>
                      طاولة {table.tableNumber}
                    </h4>
                    <p style={{ margin: '0.25rem 0 0 0', opacity: 0.9 }}>
                      النادل: {table.waiterName}
                    </p>
                  </div>
                  <div style={{ textAlign: 'left' }}>
                    <div style={{ fontSize: '1.1rem', fontWeight: '600' }}>
                      {table.totalAmount.toFixed(2)} ج.م
                    </div>
                    <div style={{ fontSize: '0.9rem', opacity: 0.9 }}>
                      {formatDuration(table.createdAt)}
                    </div>
                  </div>
                </div>

                <div style={{
                  background: 'rgba(255,255,255,0.2)',
                  padding: '0.75rem',
                  borderRadius: '8px',
                  textAlign: 'center',
                  marginBottom: '1rem'
                }}>
                  <div style={{ fontSize: '0.9rem', opacity: 0.9 }}>عدد الطلبات</div>
                  <div style={{ fontSize: '1.2rem', fontWeight: '600' }}>{table.orders.length}</div>
                </div>

                <button
                  onClick={() => {
                    setTableToClose(table);
                    setShowCloseConfirm(true);
                  }}
                  style={{
                    width: '100%',
                    background: 'rgba(255,255,255,0.2)',
                    color: 'white',
                    border: '2px solid rgba(255,255,255,0.3)',
                    padding: '0.75rem',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    fontSize: '0.9rem',
                    fontWeight: '600'
                  }}
                >
                  <i className="fas fa-times" style={{ marginLeft: '0.25rem' }}></i>
                  إغلاق الحساب فوراً
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* الطاولات المغلقة حديثاً */}
      <div style={{
        background: 'white',
        padding: '2rem',
        borderRadius: '16px',
        marginBottom: '2rem',
        boxShadow: '0 4px 16px rgba(0,0,0,0.1)'
      }}>
        <h3 style={{ margin: '0 0 1.5rem 0', color: '#333', fontSize: '1.5rem' }}>
          <i className="fas fa-history" style={{ marginLeft: '0.5rem', color: '#9e9e9e' }}></i>
          الطاولات المغلقة حديثاً
        </h3>

        {recentClosedTables.length === 0 ? (
          <div style={{
            textAlign: 'center',
            padding: '2rem',
            color: '#666'
          }}>
            لا توجد طاولات مغلقة حديثاً
          </div>
        ) : (
          <div style={{ overflowX: 'auto' }}>
            <table style={{ width: '100%', borderCollapse: 'collapse' }}>
              <thead>
                <tr style={{ background: '#f8f9fa' }}>
                  <th style={{ padding: '1rem', textAlign: 'right', borderBottom: '2px solid #dee2e6' }}>رقم الطاولة</th>
                  <th style={{ padding: '1rem', textAlign: 'right', borderBottom: '2px solid #dee2e6' }}>النادل</th>
                  <th style={{ padding: '1rem', textAlign: 'center', borderBottom: '2px solid #dee2e6' }}>عدد الطلبات</th>
                  <th style={{ padding: '1rem', textAlign: 'center', borderBottom: '2px solid #dee2e6' }}>المبلغ الإجمالي</th>
                  <th style={{ padding: '1rem', textAlign: 'center', borderBottom: '2px solid #dee2e6' }}>وقت الإغلاق</th>
                  <th style={{ padding: '1rem', textAlign: 'center', borderBottom: '2px solid #dee2e6' }}>المدة</th>
                </tr>
              </thead>
              <tbody>
                {recentClosedTables.map((table) => (
                  <tr key={table._id} style={{ borderBottom: '1px solid #dee2e6' }}>
                    <td style={{ padding: '1rem', fontWeight: '600', color: '#8e24aa' }}>
                      طاولة {table.tableNumber}
                    </td>
                    <td style={{ padding: '1rem' }}>
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        <i className="fas fa-user" style={{ marginLeft: '0.5rem', color: '#8e24aa' }}></i>
                        {table.waiterName}
                      </div>
                    </td>
                    <td style={{ padding: '1rem', textAlign: 'center', fontWeight: '600' }}>
                      {table.orders.length}
                    </td>
                    <td style={{ padding: '1rem', textAlign: 'center', fontWeight: '600', color: '#4caf50' }}>
                      {table.totalAmount.toFixed(2)} ج.م
                    </td>
                    <td style={{ padding: '1rem', textAlign: 'center', fontSize: '0.85rem', color: '#666' }}>
                      {new Date(table.closedAt || table.createdAt).toLocaleString('ar-EG', {
                        month: 'short',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </td>
                    <td style={{ padding: '1rem', textAlign: 'center', fontSize: '0.85rem', color: '#666' }}>
                      {table.closedAt ? formatDuration(table.createdAt) : '-'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Modal تفاصيل الطاولة */}
      {showTableModal && selectedTable && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            borderRadius: '16px',
            padding: '2rem',
            maxWidth: '800px',
            width: '90%',
            maxHeight: '80vh',
            overflowY: 'auto'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1.5rem' }}>
              <h3 style={{ margin: 0, color: '#333' }}>
                تفاصيل طاولة {selectedTable.tableNumber}
              </h3>
              <button
                onClick={() => setShowTableModal(false)}
                style={{
                  background: 'none',
                  border: 'none',
                  fontSize: '1.5rem',
                  cursor: 'pointer',
                  color: '#666'
                }}
              >
                ×
              </button>
            </div>

            <div style={{ marginBottom: '2rem' }}>
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '1rem', marginBottom: '1rem' }}>
                <div>
                  <strong>النادل:</strong> {selectedTable.waiterName}
                </div>
                <div>
                  <strong>الحالة:</strong>
                  <span style={{
                    background: getTableStatusColor(selectedTable.status),
                    color: 'white',
                    padding: '0.25rem 0.75rem',
                    borderRadius: '12px',
                    fontSize: '0.85rem',
                    marginRight: '0.5rem'
                  }}>
                    {getTableStatusText(selectedTable.status)}
                  </span>
                </div>
                <div>
                  <strong>وقت البداية:</strong> {new Date(selectedTable.createdAt).toLocaleString('ar-EG')}
                </div>
                <div>
                  <strong>المدة:</strong> {formatDuration(selectedTable.createdAt)}
                </div>
              </div>

              <div style={{
                background: '#8e24aa',
                color: 'white',
                padding: '1rem',
                borderRadius: '8px',
                textAlign: 'center',
                fontSize: '1.2rem',
                fontWeight: '600'
              }}>
                إجمالي الحساب: {selectedTable.totalAmount.toFixed(2)} ج.م
              </div>
            </div>

            <h4>طلبات الطاولة ({selectedTable.orders.length}):</h4>
            <div style={{ marginBottom: '1rem' }}>
              {selectedTable.orders.map((order, index) => (
                <div key={order._id} style={{
                  background: '#f8f9fa',
                  border: '1px solid #dee2e6',
                  borderRadius: '8px',
                  padding: '1rem',
                  marginBottom: '0.5rem'
                }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '0.5rem' }}>
                    <div>
                      <strong>طلب #{order.orderNumber}</strong>
                      <span style={{
                        background: getTableStatusColor(order.status),
                        color: 'white',
                        padding: '0.25rem 0.5rem',
                        borderRadius: '8px',
                        fontSize: '0.75rem',
                        marginRight: '0.5rem'
                      }}>
                        {getTableStatusText(order.status)}
                      </span>
                    </div>
                    <div style={{ fontWeight: '600', color: '#4caf50' }}>
                      {getOrderTotal(order).toFixed(2)} ج.م
                    </div>
                  </div>
                  <div style={{ fontSize: '0.9rem', color: '#666' }}>
                    {order.items.map(item => `${item.name} (${item.quantity})`).join(', ')}
                  </div>
                  <div style={{ fontSize: '0.8rem', color: '#999', marginTop: '0.25rem' }}>
                    {order.createdAt ? new Date(order.createdAt).toLocaleString('ar-EG') : '-'}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Modal تأكيد إغلاق الحساب */}
      {showCloseConfirm && tableToClose && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            borderRadius: '16px',
            padding: '2rem',
            maxWidth: '500px',
            width: '90%'
          }}>
            <h3 style={{ margin: '0 0 1rem 0', color: '#333', textAlign: 'center' }}>
              تأكيد إغلاق حساب الطاولة
            </h3>

            <div style={{
              background: '#fff3cd',
              border: '1px solid #ffeaa7',
              borderRadius: '8px',
              padding: '1rem',
              marginBottom: '1.5rem',
              textAlign: 'center'
            }}>
              <i className="fas fa-exclamation-triangle" style={{ color: '#856404', fontSize: '2rem', marginBottom: '0.5rem' }}></i>
              <div style={{ color: '#856404', fontWeight: '600' }}>
                هل أنت متأكد من إغلاق حساب طاولة {tableToClose.tableNumber}؟
              </div>
              <div style={{ color: '#856404', fontSize: '0.9rem', marginTop: '0.5rem' }}>
                إجمالي الحساب: {tableToClose.totalAmount.toFixed(2)} ج.م
              </div>
              <div style={{ color: '#856404', fontSize: '0.9rem' }}>
                عدد الطلبات: {tableToClose.orders.length}
              </div>
            </div>

            <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center' }}>
              <button
                onClick={() => closeTableAccount(tableToClose._id)}
                disabled={loading}
                style={{
                  background: '#f44336',
                  color: 'white',
                  border: 'none',
                  padding: '0.75rem 2rem',
                  borderRadius: '8px',
                  cursor: loading ? 'not-allowed' : 'pointer',
                  fontSize: '1rem',
                  fontWeight: '600'
                }}
              >
                {loading ? 'جاري الإغلاق...' : 'نعم، إغلاق الحساب'}
              </button>
              <button
                onClick={() => {
                  setShowCloseConfirm(false);
                  setTableToClose(null);
                }}
                style={{
                  background: '#6c757d',
                  color: 'white',
                  border: 'none',
                  padding: '0.75rem 2rem',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  fontSize: '1rem',
                  fontWeight: '600'
                }}
              >
                إلغاء
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Toast Notifications */}
      <ToastContainer toasts={toasts} onRemove={removeToast} />
    </div>
  );
}
