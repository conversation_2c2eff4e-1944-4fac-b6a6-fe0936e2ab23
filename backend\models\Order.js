const mongoose = require('mongoose');

const orderItemSchema = new mongoose.Schema({
  product: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: true
  },
  productName: {
    type: String,
    required: true
  },
  quantity: {
    type: Number,
    required: true,
    min: [1, 'الكمية يجب أن تكون 1 على الأقل']
  },
  price: {
    type: Number,
    required: true,
    min: [0, 'السعر لا يمكن أن يكون سالب']
  },
  subtotal: {
    type: Number,
    required: true,
    min: [0, 'المجموع الفرعي لا يمكن أن يكون سالب']
  },
  notes: {
    type: String,
    trim: true,
    maxlength: [200, 'الملاحظات لا يمكن أن تزيد عن 200 حرف']
  }
});

const orderSchema = new mongoose.Schema({
  orderNumber: {
    type: String,
    unique: true
  },
  customer: {
    name: {
      type: String,
      required: [true, 'اسم العميل مطلوب'],
      trim: true,
      maxlength: [100, 'اسم العميل لا يمكن أن يزيد عن 100 حرف']
    },
    phone: {
      type: String,
      trim: true,
      match: [/^[0-9+\-\s()]+$/, 'رقم الهاتف غير صحيح']
    },
    email: {
      type: String,
      trim: true,
      lowercase: true,
      match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'البريد الإلكتروني غير صحيح']
    }
  },
  items: [orderItemSchema],
  totals: {
    subtotal: {
      type: Number,
      required: true,
      min: 0
    },
    tax: {
      type: Number,
      default: 0,
      min: 0
    },
    discount: {
      type: Number,
      default: 0,
      min: 0
    },
    total: {
      type: Number,
      required: true,
      min: 0
    }
  },
  status: {
    type: String,
    enum: {
      values: ['pending', 'confirmed', 'preparing', 'ready', 'served', 'completed', 'cancelled', 'delivered'],
      message: 'حالة الطلب غير صحيحة'
    },
    default: 'pending'
  },
  orderType: {
    type: String,
    enum: {
      values: ['dine-in', 'takeaway', 'delivery'],
      message: 'نوع الطلب غير صحيح'
    },
    required: true
  },
  table: {
    number: {
      type: Number,
      min: 1,
      max: 100
    },
    section: {
      type: String,
      trim: true
    }
  },
  delivery: {
    address: {
      street: String,
      city: String,
      area: String,
      building: String,
      floor: String,
      apartment: String,
      landmark: String
    },
    coordinates: {
      latitude: Number,
      longitude: Number
    },
    fee: {
      type: Number,
      default: 0,
      min: 0
    },
    estimatedTime: {
      type: Number, // in minutes
      min: 0
    }
  },
  payment: {
    method: {
      type: String,
      enum: ['cash', 'card', 'online', 'wallet'],
      default: 'cash'
    },
    status: {
      type: String,
      enum: ['pending', 'paid', 'failed', 'refunded'],
      default: 'pending'
    },
    transactionId: String,
    paidAt: Date
  },
  timing: {
    orderTime: {
      type: Date,
      default: Date.now
    },
    confirmedAt: Date,
    preparingAt: Date,
    readyAt: Date,
    servedAt: Date,
    completedAt: Date,
    deliveredAt: Date,
    estimatedPreparationTime: {
      type: Number, // in minutes
      min: 0
    }
  },
  staff: {
    waiter: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    chef: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    cashier: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  },
  notes: {
    customer: {
      type: String,
      trim: true,
      maxlength: [500, 'ملاحظات العميل لا يمكن أن تزيد عن 500 حرف']
    },
    kitchen: {
      type: String,
      trim: true,
      maxlength: [500, 'ملاحظات المطبخ لا يمكن أن تزيد عن 500 حرف']
    },
    internal: {
      type: String,
      trim: true,
      maxlength: [500, 'الملاحظات الداخلية لا يمكن أن تزيد عن 500 حرف']
    }
  },
  priority: {
    type: String,
    enum: ['low', 'normal', 'high', 'urgent'],
    default: 'normal'
  },
  rating: {
    score: {
      type: Number,
      min: 1,
      max: 5
    },
    comment: {
      type: String,
      trim: true,
      maxlength: [500, 'تعليق التقييم لا يمكن أن يزيد عن 500 حرف']
    },
    ratedAt: Date
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
orderSchema.index({ orderNumber: 1 });
orderSchema.index({ status: 1 });
orderSchema.index({ orderType: 1 });
orderSchema.index({ 'timing.orderTime': -1 });
orderSchema.index({ 'customer.phone': 1 });
orderSchema.index({ 'table.number': 1 });
orderSchema.index({ 'staff.waiter': 1 });

// Virtual for total preparation time
orderSchema.virtual('preparationTime').get(function() {
  if (this.timing.preparingAt && this.timing.readyAt) {
    return Math.round((this.timing.readyAt - this.timing.preparingAt) / (1000 * 60)); // in minutes
  }
  return null;
});

// Virtual for total order time
orderSchema.virtual('totalTime').get(function() {
  if (this.timing.orderTime && this.timing.completedAt) {
    return Math.round((this.timing.completedAt - this.timing.orderTime) / (1000 * 60)); // in minutes
  }
  return null;
});

// Pre-save middleware to generate order number
orderSchema.pre('save', async function(next) {
  if (this.isNew && !this.orderNumber) {
    const today = new Date();
    const dateStr = today.toISOString().slice(0, 10).replace(/-/g, '');
    const count = await this.constructor.countDocuments({
      createdAt: {
        $gte: new Date(today.getFullYear(), today.getMonth(), today.getDate()),
        $lt: new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1)
      }
    });
    this.orderNumber = `ORD-${dateStr}-${String(count + 1).padStart(4, '0')}`;
  }

  // Calculate totals consistently
  if (this.isModified('items') || this.isModified('totals.tax') || this.isModified('totals.discount') || (this.orderType === 'delivery' && this.isModified('delivery.fee'))) {
    // Ensure item subtotals are correct first
    this.items.forEach(item => {
      if (item.price && item.quantity) { // Ensure price and quantity exist
        item.subtotal = item.price * item.quantity;
      } else {
        item.subtotal = 0; // Or handle error appropriately
      }
    });
    
    this.totals.subtotal = this.items.reduce((sum, item) => sum + (item.subtotal || 0), 0);
    
    // Ensure tax and discount are numbers, default to 0 if not set
    const taxAmount = Number(this.totals.tax) || 0;
    const discountAmount = Number(this.totals.discount) || 0;
    
    this.totals.total = this.totals.subtotal + taxAmount - discountAmount;

    if (this.orderType === 'delivery' && this.delivery && this.delivery.fee) {
      this.totals.total += (Number(this.delivery.fee) || 0);
    }
    
    // Ensure total is not negative
    if (this.totals.total < 0) {
        this.totals.total = 0;
    }
  }

  next();
});

// Static method to get today's orders
orderSchema.statics.getTodayOrders = function() {
  const today = new Date();
  const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
  const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);

  return this.find({
    'timing.orderTime': {
      $gte: startOfDay,
      $lt: endOfDay
    }
  }).sort({ 'timing.orderTime': -1 });
};

// Static method to get orders by status
orderSchema.statics.getByStatus = function(status) {
  return this.find({ status }).sort({ 'timing.orderTime': -1 });
};

// Instance method to update status
orderSchema.methods.updateStatus = function(newStatus, userId) {
  this.status = newStatus;

  const now = new Date();
  switch (newStatus) {
    case 'confirmed':
      this.timing.confirmedAt = now;
      break;
    case 'preparing':
      this.timing.preparingAt = now;
      break;
    case 'ready':
      this.timing.readyAt = now;
      break;
    case 'served':
      this.timing.servedAt = now;
      break;
    case 'completed':
      this.timing.completedAt = now;
      break;
    case 'delivered':
      this.timing.deliveredAt = now;
      break;
  }

  return this.save();
};

module.exports = mongoose.model('Order', orderSchema);
