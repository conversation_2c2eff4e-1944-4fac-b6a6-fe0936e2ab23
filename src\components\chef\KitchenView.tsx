import React from 'react';
import type { ChefOrder } from '../../models/ChefModels';

interface KitchenViewProps {
  orders: ChefOrder[];
  onUpdateOrderStatus: (orderId: string, status: string) => void;
  onSelectOrder: (order: ChefOrder) => void;
  loading: boolean;
}

export const KitchenView: React.FC<KitchenViewProps> = ({
  orders,
  onUpdateOrderStatus,
  onSelectOrder,
  loading
}) => {
  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'في الانتظار';
      case 'preparing': return 'قيد التحضير';
      case 'ready': return 'جاهز';
      case 'completed': return 'مكتمل';
      case 'delivered': return 'تم التسليم';
      default: return status;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return '#ff9800';
      case 'preparing': return '#2196f3';
      case 'ready': return '#4caf50';
      case 'completed': return '#8bc34a';
      case 'delivered': return '#607d8b';
      default: return '#9e9e9e';
    }
  };

  const formatTime = (date: string) => {
    return new Date(date).toLocaleTimeString('ar-EG', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getElapsedTime = (createdAt: string) => {
    const now = new Date();
    const created = new Date(createdAt);
    const diffInMinutes = Math.floor((now.getTime() - created.getTime()) / (1000 * 60));
    return diffInMinutes;
  };

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>جاري تحميل الطلبات...</p>
      </div>
    );
  }

  if (orders.length === 0) {
    return (
      <div className="empty-state">
        <div className="empty-icon">🍽️</div>
        <h3>لا توجد طلبات</h3>
        <p>لا توجد طلبات تحتاج للتحضير حالياً</p>
      </div>
    );
  }

  return (
    <div className="kitchen-view">
      <div className="orders-grid">
        {orders.map((order) => {
          const elapsedTime = getElapsedTime(order.createdAt);
          const isUrgent = elapsedTime > 30;

          return (
            <div
              key={order._id}
              className={`order-card ${isUrgent ? 'urgent' : ''}`}
              onClick={() => onSelectOrder(order)}
            >
              <div className="order-header">
                <div className="order-info">
                  <h3>طلب #{order.orderNumber}</h3>
                  <p>طاولة {order.tableNumber}</p>
                </div>
                <div className="order-time">
                  <span className={`time ${isUrgent ? 'urgent' : ''}`}>
                    {elapsedTime}د
                  </span>
                  <span className="created-time">
                    {formatTime(order.createdAt)}
                  </span>
                </div>
              </div>

              <div className="order-items">
                {order.items.slice(0, 3).map((item, index) => (
                  <div key={index} className="order-item">
                    <span className="item-quantity">{item.quantity}×</span>
                    <span className="item-name">{item.name}</span>
                  </div>
                ))}
                {order.items.length > 3 && (
                  <div className="more-items">
                    +{order.items.length - 3} عنصر آخر
                  </div>
                )}
              </div>

              {order.notes && (
                <div className="order-notes">
                  <span>📝 {order.notes}</span>
                </div>
              )}

              <div className="order-footer">
                <div className="order-status">
                  <span 
                    className="status-badge"
                    style={{ backgroundColor: getStatusColor(order.status) }}
                  >
                    {getStatusText(order.status)}
                  </span>
                </div>
                <div className="order-actions">
                  {order.status === 'pending' && (
                    <button
                      className="btn btn-primary"
                      onClick={(e) => {
                        e.stopPropagation();
                        onUpdateOrderStatus(order._id, 'preparing');
                      }}
                    >
                      بدء التحضير
                    </button>
                  )}
                  {order.status === 'preparing' && (
                    <button
                      className="btn btn-success"
                      onClick={(e) => {
                        e.stopPropagation();
                        onUpdateOrderStatus(order._id, 'ready');
                      }}
                    >
                      جاهز
                    </button>
                  )}
                  {order.status === 'ready' && (
                    <button
                      className="btn btn-complete"
                      onClick={(e) => {
                        e.stopPropagation();
                        onUpdateOrderStatus(order._id, 'completed');
                      }}
                    >
                      مكتمل
                    </button>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};
