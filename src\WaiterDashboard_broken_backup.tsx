import { APP_CONFIG } from './config/app.config';
import { authenticatedGet, authenticatedPost, authenticatedPut, handleApiError } from './utils/apiHelpers';
import { useToast } from './hooks/useToast';
import { getOrderFinalPrice } from './utils/orderHelpers';
import React, { useState, useEffect, useCallback } from 'react'; // Added useCallback
import './WaiterDashboard.css'; // Assuming WaiterDashboard.css exists

// Define interfaces used in the component
interface MenuItem {
  _id: string;
  name: string;
  price: number;
  description?: string;
  categories?: string[];
  categoryDetails?: Category[];
  available: boolean;
  notes?: string; // Added for cart item notes
}

interface CartItem extends MenuItem {
  quantity: number;
}

interface Category {
  _id: string;
  name: string;
  color: string;
  icon?: string;
}

interface OrderItem {
  product: string;
  name: string;
  quantity: number;
  price: number;
  notes?: string;
  id?: string; // Add id property
}

interface Order {
  _id: string;
  orderNumber: string;
  waiterName: string;
  items: OrderItem[];
  totalPrice: number;
  tableNumber: string;
  customerName?: string;
  status: 'pending' | 'preparing' | 'ready' | 'delivered' | 'cancelled';
  createdAt: string; // Assuming createdAt is a string (ISO date)
  tableAccountId?: string;
  discountStatus?: 'pending' | 'approved' | 'rejected';
  discountAmount?: number;
}

interface TableAccount {
  _id: string;
  tableNumber: string;
  waiterId: string;
  waiterName: string;
  waiter?: {
    id: string;
    name?: string;
    username?: string;
  };
  orders: Order[]; // Array of Order objects or Order IDs
  totalAmount: number;
  isOpen: boolean;
  status: 'active' | 'closed';
  createdAt: string;
  updatedAt: string;
  lastActivityAt?: string;
  customerName?: string; 
  paymentMethod?: string;
  discountApplied?: number;
  notes?: string;
}

// Helper function to get API URL
const getApiUrl = () => {
  return APP_CONFIG.API.BASE_URL;
};

export default function WaiterDashboard() {
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [cart, setCart] = useState<CartItem[]>([]);
  const [tableNumber, setTableNumber] = useState('');
  const [customerName, setCustomerName] = useState('');
  const [loading, setLoading] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  // Toast notifications
  const {
    toasts,
    removeToast,
    showSuccess,
    showError,
    showInfo
  } = useToast();
  // Orders state
  const [orders, setOrders] = useState<Order[]>([]);
  // فلتر حالة الطلبات في شاشة الطلبات
  const [orderStatusFilter, setOrderStatusFilter] = useState<'all' | 'pending' | 'preparing' | 'ready' | 'delivered'>('all');
  
  // Screen state
  const [currentScreen, setCurrentScreen] = useState<'drinks' | 'orders' | 'tables' | 'cart'>('drinks');

  // Discount request states
  const [showDiscountModal, setShowDiscountModal] = useState(false);
  const [selectedOrderForDiscount, setSelectedOrderForDiscount] = useState<Order | null>(null);
  const [discountAmount, setDiscountAmount] = useState('');
  const [discountReason, setDiscountReason] = useState('');

  // Table account states
  const [showTableAccountModal, setShowTableAccountModal] = useState(false);
  const [tableAccounts, setTableAccounts] = useState<TableAccount[]>([]); // Use TableAccount type
  const [existingTableAccount, setExistingTableAccount] = useState<any>(null);

  // Order details modal states
  const [showOrderDetailsModal, setShowOrderDetailsModal] = useState(false);
  const [selectedOrderDetails, setSelectedOrderDetails] = useState<Order | null>(null);

  // Table details modal states - NEW
  const [showTableDetailsModal, setShowTableDetailsModal] = useState(false);
  const [selectedTableAccountDetails, setSelectedTableAccountDetails] = useState<TableAccount | null>(null);

  // متغيرات التحكم في القائمة الجانبية
  const [sidebarOpen, setSidebarOpen] = useState(window.innerWidth > 1024);
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 1024);
  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth <= 1024;
      setIsMobile(mobile);
      if (!mobile) setSidebarOpen(true);
    };
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);  const openSidebar = () => {
    setSidebarOpen(true);
    if (isMobile) {
      document.body.classList.add('sidebar-open');
    }
  };
  
  const closeSidebar = () => {
    setSidebarOpen(false);
    if (isMobile) {
      document.body.classList.remove('sidebar-open');
    }
  };
  
  const toggleSidebar = () => {
    const newState = !sidebarOpen;
    setSidebarOpen(newState);
    if (isMobile) {
      if (newState) {
        document.body.classList.add('sidebar-open');
      } else {
        document.body.classList.remove('sidebar-open');
      }
    }
  };

  // Load and save session functions
  const saveCartToSession = () => {
    const sessionData = {
      cart,
      tableNumber,
      customerName,
      timestamp: new Date().toISOString()
    };
    localStorage.setItem('waiterSession', JSON.stringify(sessionData));
    localStorage.setItem('waiterCart', JSON.stringify(cart));
  };

  const loadCartFromSession = () => {
    try {
      // Try to load from waiterCart first (most recent)
      const savedCart = localStorage.getItem('waiterCart');
      if (savedCart) {
        const cartData = JSON.parse(savedCart);
        if (cartData && cartData.length > 0) {
          setCart(cartData);
        }
      }

      // Then load session data for table number and customer name
      const sessionData = localStorage.getItem('waiterSession');
      if (sessionData) {
        const { cart: savedCart, tableNumber: savedTable, customerName: savedCustomer, timestamp } = JSON.parse(sessionData);

        // Check if session is less than 4 hours old
        const sessionAge = new Date().getTime() - new Date(timestamp).getTime();
        const maxAge = 4 * 60 * 60 * 1000; // 4 hours in milliseconds

        if (sessionAge < maxAge) {
          if (!savedCart || savedCart.length === 0) {
            // Only set cart from session if no cart was loaded from waiterCart
            if (savedCart && savedCart.length > 0 && (!cart || cart.length === 0)) {
              setCart(savedCart);
            }
          }
          setTableNumber(savedTable || '');
          setCustomerName(savedCustomer || '');
          return true;
        }
      }
    } catch (error) {
      console.error('Error loading session:', error);
    }
    return false;
  };

  // API functions
  const fetchMenuItems = async () => {
    try {
      const data = await authenticatedGet('/api/menu');
      
      // Enhance menu items with category details
      const enhancedItems = await Promise.all(data.map(async (item: MenuItem) => {
        if (item.categories && item.categories.length > 0 && categories && Array.isArray(categories) && categories.length > 0) {
          const categoryDetails = categories.filter(cat =>
            item.categories!.includes(cat._id)
          );
          return { ...item, categoryDetails };
        }
        return { ...item, categoryDetails: [] };
      }));

      setMenuItems(enhancedItems.filter((item: MenuItem) => item.available));
    } catch (error) {
      console.error('خطأ في جلب المنيو:', error);
      const errorMessage = handleApiError(error);
      showError(errorMessage);
    }
  };

  const fetchCategories = async () => {
    try {
      const data = await authenticatedGet('/api/categories');
      setCategories(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('خطأ في جلب الفئات:', error);
      const errorMessage = handleApiError(error);
      showError(errorMessage);
      setCategories([]); // Set to empty array on error
    }
  };

  const fetchOrders = useCallback(async () => {
    try {
      const data = await authenticatedGet('/api/orders');
      setOrders(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('خطأ في جلب الطلبات:', error);
      const errorMessage = handleApiError(error);
      showError(errorMessage);
    }
  }, [showError]); // Added showError to dependencies

  const fetchTableAccounts = useCallback(async () => {
    try {
      const data = await authenticatedGet('/api/table-accounts');
      setTableAccounts(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('خطأ في جلب حسابات الطاولات:', error);
      const errorMessage = handleApiError(error);
      showError(errorMessage);
      setTableAccounts([]); // Ensure it's an empty array on error
    }
  }, [showError]); // Added showError to dependencies

  const checkExistingTableAccount = async (tableNumber: string) => {
    try {
      // Use authenticatedGet for consistent auth header
      const data = await authenticatedGet(`/api/table-accounts/check?tableNumber=${tableNumber}`);
      // The backend now returns { exists, account, tableStatus, waiterName, waiterUsername }
      return data;
    } catch (error) {
      console.error('Error checking table account (using authenticatedGet):', error);
      // Attempt to parse error if it's a Response object from a failed fetch
      let errorMessage = 'Network error checking table account. Please try again.';
      if (error instanceof Error && error.message.startsWith('HTTP')) {
        // Assuming the error message is like "HTTP 401: Unauthorized"
        // You might need more sophisticated error parsing depending on what authenticatedGet throws
        errorMessage = `Error checking table: ${error.message}`;
      } else if (typeof error === 'object' && error !== null && 'message' in error) {
        errorMessage = (error as {message: string}).message;
      }
      
      // alert(errorMessage); // Consider using toast for better UX
      showError(errorMessage); // Using toast for consistency
      return { exists: false, account: null, tableStatus: 'unknown', waiterName: null, waiterUsername: null };
    }
  };

  // Cart functions
  const addToCart = (item: MenuItem) => {
    const existingItem = cart.find(cartItem => cartItem._id === item._id);
    let updatedCart;
    if (existingItem) {
      updatedCart = cart.map(cartItem =>
        cartItem._id === item._id
          ? { ...cartItem, quantity: cartItem.quantity + 1 }
          : cartItem
      );
    } else {
      updatedCart = [...cart, { ...item, quantity: 1 }];
    }
    setCart(updatedCart);
    localStorage.setItem('waiterCart', JSON.stringify(updatedCart));
  };

  const updateCartQuantity = (itemId: string, quantity: number) => {
    let updatedCart;
    if (quantity <= 0) {
      updatedCart = cart.filter(item => item._id !== itemId);
    } else {
      updatedCart = cart.map(item =>
        item._id === itemId ? { ...item, quantity } : item
      );
    }
    setCart(updatedCart);
    localStorage.setItem('waiterCart', JSON.stringify(updatedCart));
  };

  const clearCart = () => {
    setCart([]);
    setTableNumber('');
    setCustomerName('');
    localStorage.removeItem('waiterCart');
    localStorage.removeItem('waiterSession');
  };

  // Order functions
  const submitOrder = async () => {
    const waiterName = localStorage.getItem('waiterName');
    const waiterId = localStorage.getItem('waiterId');
    const waiterUsername = localStorage.getItem('username'); // Get waiter username

    if (!waiterName || !waiterId || !waiterUsername) {
      showError('تفاصيل النادل غير موجودة. يرجى تسجيل الخروج ثم الدخول مرة أخرى.');
      return;
    }

    if (cart.length === 0 || !tableNumber) {
      showError('يرجى إضافة عناصر للطلب وتحديد رقم الطاولة.');
      return;
    }

    setLoading(true);
    try {
      // Check if there is an existing open table account for the selected table
      const existingAccountResponse = await checkExistingTableAccount(tableNumber); // Use tableNumber here
      const existingAccount = existingAccountResponse.account;
      const tableIsAvailable = !existingAccountResponse.exists || !existingAccount?.isOpen;
      const currentTableWaiterUsername = existingAccountResponse.waiterUsername; // Get username from the response

      if (!tableIsAvailable && currentTableWaiterUsername !== waiterUsername) {
        showError(
          `الطاولة رقم ${tableNumber} مُدارة حاليًا بواسطة نادل آخر: ${existingAccountResponse.waiterName || 'غير معروف'}. لا يمكنك إضافة طلبات لهذه الطاولة.`
        );
        setLoading(false); // Stop loading if access is denied
        return;
      }

      const orderData = {
        waiterName: localStorage.getItem('username') || 'waiter',
        items: cart.map(item => ({
          product: item._id,
          name: item.name,
          quantity: item.quantity,
          price: item.price,
          notes: item.notes || ''
        })),
        totalPrice: cart.reduce((sum, item) => sum + (item.price * item.quantity), 0),
        tableNumber,
        customerName: customerName || '',
        status: 'pending'
        // Removed tableAccountId: existingAccount?._id || null
      };

      const result = await authenticatedPost('/api/orders', orderData);

      if (existingAccount) {
        showSuccess(`تم إضافة الطلب إلى حساب الطاولة ${tableNumber} بنجاح!`);
      } else {
        showSuccess(`تم إرسال الطلب وفتح حساب جديد للطاولة ${tableNumber} بنجاح!`);
      }      // Send notification via Socket.IO
      const socket = (await import('./socket')).default;
      socket.emit('order-created', {
        orderId: result._id || `ORD-${Date.now()}`,
        orderNumber: result.orderNumber || `ORD-${Date.now()}`,
        tableNumber: tableNumber,
        waiterName: localStorage.getItem('username') || 'waiter',
        items: cart.map(item => ({
          name: item.name,
          quantity: item.quantity,
          price: item.price
        })),
        status: 'pending',
        customer: customerName || 'عميل',
        total: orderData.totalPrice,
        timestamp: new Date().toISOString()
      });

      clearCart();
      setCurrentScreen('orders');
      fetchOrders();
      fetchTableAccounts();
    } catch (error) {
      console.error('خطأ في إرسال الطلب:', error);
      const errorMessage = handleApiError(error);
      showError(errorMessage);
    }
    setLoading(false);
  };

  const handleLogout = () => {
    const confirmLogout = window.confirm('هل أنت متأكد من تسجيل الخروج؟');

    if (confirmLogout) {
      // Clear localStorage
      localStorage.removeItem('username');
      localStorage.removeItem('jobTitle');
      localStorage.removeItem('isLoggedIn');
      localStorage.removeItem('user');
      localStorage.removeItem('userRole');
      localStorage.removeItem('waiterCart');
      localStorage.removeItem('waiterSession');

      // Show success message
      alert('تم تسجيل الخروج بنجاح!');

      // Redirect to login page
      window.location.href = '/';
    }
  };
  // Helper functions for orders
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return 'fa-clock';
      case 'preparing': return 'fa-utensils';
      case 'ready': return 'fa-check-circle';
      case 'delivered': return 'fa-truck';
      case 'cancelled': return 'fa-times-circle';
      default: return 'fa-question-circle';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return '#ffc107';
      case 'preparing': return '#17a2b8';
      case 'ready': return '#28a745';
      case 'delivered': return '#6c757d';
      case 'cancelled': return '#dc3545';
      default: return '#6c757d';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'في الانتظار';
      case 'preparing': return 'قيد التحضير';
      case 'ready': return 'جاهز';
      case 'delivered': return 'تم التسليم';
      case 'cancelled': return 'ملغي';
      default: return 'غير محدد';
    }
  };
  const markAsDelivered = async (orderId: string) => {
    try {
      // Update order status in database
      await authenticatedPut(`/api/orders/${orderId}`, { status: 'delivered' });
      
      // Find the order to get details for Socket notification
      const deliveredOrder = orders.find(order => order._id === orderId);
      
      // Send Socket notification about order delivery
      const socket = (await import('./socket')).default;
      socket.emit('order-status-update', {
        orderId: orderId,
        orderNumber: deliveredOrder?.orderNumber || `ORD-${Date.now()}`,
        newStatus: 'delivered',
        waiterName: localStorage.getItem('username') || 'waiter',
        tableNumber: deliveredOrder?.tableNumber || 'غير محدد',
        customer: deliveredOrder?.customerName || 'عميل',
        items: deliveredOrder?.items || [], // Ensure items is an array
        timestamp: new Date().toISOString()
      });      
      showSuccess(`تم تسليم الطلب من الطاولة رقم ${deliveredOrder?.tableNumber || 'غير محدد'} للعميل ${deliveredOrder?.customerName || 'غير محدد'} بنجاح`);
      fetchOrders();
      fetchTableAccounts(); // Refresh table accounts as well
    } catch (error) {
      console.error('خطأ في تحديث حالة الطلب:', error);
      showError('حدث خطأ أثناء تحديث حالة الطلب');
    }
  };

  // Get category icon based on name
  const getCategoryIcon = (categoryName: string) => {
    const name = categoryName.toLowerCase();
    if (name.includes('قهوة') || name.includes('coffee')) return 'fa-coffee';
    if (name.includes('شاي') || name.includes('tea')) return 'fa-leaf';
    if (name.includes('عصير') || name.includes('juice')) return 'fa-glass-whiskey';
    if (name.includes('بارد') || name.includes('cold')) return 'fa-snowflake';
    if (name.includes('حلو') || name.includes('sweet')) return 'fa-birthday-cake';
    if (name.includes('ساخن') || name.includes('hot')) return 'fa-fire';
    return 'fa-utensils';
  };

  // Effects
  useEffect(() => {
    const loadData = async () => {
      try {
        await fetchCategories();
        await fetchMenuItems();
      } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
      }
    };

    loadData();
    loadCartFromSession();
    // Setup Socket.IO listeners
    const setupSocketListeners = async () => {
      const socket = (await import('./socket')).default;

      const localWaiterName = localStorage.getItem('username') || 'waiter'; // Renamed variable
      const userId = localStorage.getItem('userId') || `waiter-${localWaiterName}`;
      
      socket.emit('register-user', {
        userId: userId,
        role: 'waiter',
        name: localWaiterName
      });

      socket.on('registration-confirmed', (data: any) => {
        console.log('✅ Waiter registered in Socket.IO:', data);
      });
      socket.on('order-created', (data: any) => {
        console.log('🔔 New order created notification:', data);
        if (data.waiterName === localWaiterName) {
          if (currentScreen === 'orders') {
            fetchOrders();
          }
          if (currentScreen === 'tables') {
            fetchTableAccounts();
          }
        }
      });

      socket.on('new-order-notification', (data: any) => {
        console.log('🔔 New order notification received:', data);
        if (currentScreen === 'orders') {
          fetchOrders();
        }
      });
      socket.on('order-status-update', (data: any) => {
        console.log('🔄 Order status updated:', data);
        if (currentScreen === 'orders') {
          fetchOrders();
        }
        if (currentScreen === 'tables') {
          fetchTableAccounts();
        }
      });

      socket.on('table-status-updated', (data: any) => {
        console.log('🪑 Table status updated:', data);
        showInfo(data.message || `تم تحديث حالة الطاولة ${data.tableNumber}`);
        if (currentScreen === 'tables') {
          fetchTableAccounts();
        }
      });      socket.on('table-access-denied', (data: any) => {
        console.log('⚠️ Table access denied:', data);
        showError(data.message || `الطاولة ${data.tableNumber} غير متاحة`);
      });

      socket.on('connect', () => {
        console.log('🔌 Waiter connected to Socket.IO');
        const connectedWaiterName = localStorage.getItem('username') || 'waiter'; // Renamed
        const connectedUserId = localStorage.getItem('userId') || `waiter-${connectedWaiterName}`;
        socket.emit('register-user', {
          userId: connectedUserId,
          role: 'waiter',
          name: connectedWaiterName
        });
      });

      socket.on('disconnect', () => {
        console.log('❌ Waiter disconnected from Socket.IO');
      });

      // مستمعين احتياطيين للأحداث القديمة
      socket.on('orderReady', (data: any) => {
        console.log('📥 [احتياطي] orderReady:', data);
        if (currentScreen === 'orders') fetchOrders();
      });
      socket.on('orderAccepted', (data: any) => {
        console.log('📥 [احتياطي] orderAccepted:', data);
        if (currentScreen === 'orders') fetchOrders();
      });
      socket.on('newOrder', (data: any) => {
        console.log('📥 [احتياطي] newOrder:', data);
        if (currentScreen === 'orders') fetchOrders();
      });
    };

    setupSocketListeners();    return () => {      const cleanupSocket = async () => {
        const socket = (await import('./socket')).default;
        socket.off('registration-confirmed');
        socket.off('order-created');
        socket.off('new-order-notification');
        socket.off('order-status-update');
        socket.off('table-status-updated');
        socket.off('table-access-denied');
        socket.off('orderAccepted'); 
        socket.off('orderReady');
        socket.off('newOrder');
        socket.off('connect');
        socket.off('disconnect');
      };
      cleanupSocket();
    };
  }, [showSuccess, showError, showInfo, currentScreen, fetchOrders, fetchTableAccounts]);

  useEffect(() => {
    if (currentScreen === 'orders' || currentScreen === 'tables') {
      fetchOrders();
      fetchTableAccounts();
    }
  }, [currentScreen]);

  useEffect(() => {
    if (cart.length > 0 || tableNumber || customerName) {
      saveCartToSession();
    }
  }, [cart, tableNumber, customerName]);

  useEffect(() => {
    if (categories && categories.length > 0) {
      fetchMenuItems();
    }
  }, [categories]);


  // NEW: cancelOrder function
  const cancelOrder = async (orderId: string) => {
    if (!orderId) {
      showError('معرف الطلب غير موجود.');
      return;
    }
    const confirmCancel = window.confirm('هل أنت متأكد من إلغاء هذا الطلب؟ لا يمكن التراجع عن هذا الإجراء.');
    if (!confirmCancel) {
      return;
    }

    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        showError('جلسة المستخدم غير صالحة. يرجى تسجيل الدخول مرة أخرى.');
        setLoading(false);
        // Optionally, redirect to login: window.location.href = '/';
        return;
      }

      const response = await fetch(`${getApiUrl()}/api/orders/${orderId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        // Try to parse the error message from the server response
        let errorData = { message: 'فشل في إلغاء الطلب. حاول مرة أخرى.' }; // Default error
        try {
          errorData = await response.json();
        } catch (e) {
          // If parsing JSON fails, use a generic error based on status text
          errorData.message = `خطأ ${response.status}: ${response.statusText || 'فشل في إلغاء الطلب'}`;
        }
        throw new Error(errorData.message);
      }
      
      showSuccess('تم إلغاء الطلب بنجاح.');
      fetchOrders(); // Refresh the orders list
      // If order cancellation affects table accounts (e.g. if a table closes when all its orders are cancelled)
      fetchTableAccounts(); 

    } catch (error) {
      console.error('خطأ في إلغاء الطلب:', error);
      // Use handleApiError if it can process the error object correctly
      // Otherwise, use error.message or a default message
      const errorMessage = (error instanceof Error) ? error.message : handleApiError(error);
      showError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Screen Renderers
  const renderDrinksScreen = () => {
    const filteredItems = menuItems.filter(item => {
      const matchesCategory = selectedCategory === 'all' ||
        (item.categories && Array.isArray(item.categories) && item.categories.includes(selectedCategory));
      const matchesSearch = searchTerm === '' ||
        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (item.description && item.description.toLowerCase().includes(searchTerm.toLowerCase()));
      return matchesCategory && matchesSearch;
    });

    return (
      <div className="content-container">
        {/* Screen Header */}
        <div className="screen-header">
          <h1 className="screen-title">
            <i className="fas fa-coffee"></i>
            قائمة المشروبات
          </h1>
          <p className="screen-subtitle">اختر المشروبات المطلوبة وأضفها إلى السلة</p>
        </div>

        {/* Search Section */}
        <div className="search-section">
          <div className="search-header">
            <i className="fas fa-search"></i>
            <h3 className="search-title">البحث في المشروبات</h3>
          </div>
          
          <div className="search-input-group">
            <i className="fas fa-search search-icon"></i>
            <input 
              type="text" 
              className="search-input" 
              placeholder="ابحث عن المشروبات بالاسم أو الوصف..." 
              value={searchTerm} 
              onChange={(e) => setSearchTerm(e.target.value)} 
            />
            {searchTerm && (
              <button className="clear-search" onClick={() => setSearchTerm('')} title="مسح البحث">
                <i className="fas fa-times"></i>
              </button>
            )}
          </div>

          {searchTerm && (
            <div className="search-results">
              <div className="results-info">
                <i className="fas fa-filter"></i>
                تم العثور على {filteredItems.length} مشروب
                {searchTerm && (
                  <span className="search-term"> يحتوي على "<strong>{searchTerm}</strong>"</span>
                )}
              </div>
              {filteredItems.length === 0 && (
                <div className="no-results">
                  <i className="fas fa-exclamation-circle"></i>
                  لا توجد مشروبات تطابق البحث
                </div>
              )}
            </div>
          )}
        </div>

        {/* Category Filter */}
        <div className="category-filter">
          <button 
            className={`category-btn ${selectedCategory === 'all' ? 'active' : ''}`}
            onClick={() => setSelectedCategory('all')}
          >
            <i className="fas fa-th"></i>
            جميع الفئات
          </button>
          {categories.map(category => (
            <button 
              key={category._id}
              className={`category-btn ${selectedCategory === category._id ? 'active' : ''}`}
              onClick={() => setSelectedCategory(category._id)}
              style={{ borderColor: category.color }}
            >
              <i className={`fas ${getCategoryIcon(category.name)}`}></i>
              {category.name}
            </button>
          ))}
        </div>

        {/* Menu Items Grid */}
        <div className="menu-grid">
          {filteredItems.map(item => (
            <div key={item._id} className="menu-item-card">
              <div className="menu-item-header">
                <h3 className="menu-item-name">{item.name}</h3>
                <span className="menu-item-price">{item.price} ج.م</span>
              </div>
              
              {item.description && (
                <p className="menu-item-description">{item.description}</p>
              )}
              
              <div className="menu-item-categories">
                {item.categoryDetails?.map(cat => (
                  <span 
                    key={cat._id} 
                    className="category-tag"
                    style={{ backgroundColor: cat.color }}
                  >
                    <i className={`fas ${getCategoryIcon(cat.name)}`}></i>
                    {cat.name}
                  </span>
                ))}
              </div>
              
              <button 
                className="add-to-cart-btn"
                onClick={() => addToCart(item)}
              >
                <i className="fas fa-plus"></i>
                إضافة للسلة
              </button>
            </div>
          ))}
        </div>

        {filteredItems.length === 0 && !searchTerm && (
          <div className="empty-state">
            <i className="fas fa-coffee"></i>
            <h3>لا توجد مشروبات متاحة</h3>
            <p>لا توجد مشروبات في هذه الفئة حالياً</p>
          </div>
        )}
      </div>
    );  const renderOrdersScreen = () => {
    const currentWaiterName = localStorage.getItem('username') || 'waiter'; // Renamed variable
    const filteredOrders = orders.filter(order => order.waiterName === currentWaiterName);
    const statusFilteredOrders = orderStatusFilter === 'all'
      ? filteredOrders
      : filteredOrders.filter(order => order.status === orderStatusFilter);

    return (
      <div className="content-container">
        <div className="screen-header">
          <h1 className="screen-title">
            <i className="fas fa-receipt"></i>
            طلباتي ({filteredOrders.length})
          </h1>
          <p className="screen-subtitle">إدارة ومتابعة الطلبات الخاصة بي</p>
        </div>

        <div className="orders-stats">
          {[
  { key: 'all', label: 'الكل', icon: 'fa-list', color: '#6c757d' },
  { key: 'pending', label: 'في الانتظار', icon: 'fa-clock', color: '#ffc107' },
  { key: 'preparing', label: 'قيد التحضير', icon: 'fa-utensils', color: '#17a2b8' },
  { key: 'ready', label: 'جاهز للتسليم', icon: 'fa-check-circle', color: '#28a745' },
  { key: 'delivered', label: 'تم التسليم', icon: 'fa-truck', color: '#6c757d' }
].map(stat => (
            <div
              key={stat.key}
              className={`stat-card${orderStatusFilter === stat.key ? ' active' : ''}`}
              style={{ cursor: 'pointer', border: orderStatusFilter === stat.key ? `2px solid ${stat.color}` : undefined }}
              onClick={() => setOrderStatusFilter(stat.key as 'all' | 'pending' | 'preparing' | 'ready' | 'delivered')}
            >
              <div className={`stat-icon ${stat.key}`}>
                <i className={`fas ${stat.icon}`}></i>
              </div>
              <div className="stat-info">
                <span className="stat-number">
                  {stat.key === 'all' ? filteredOrders.length : filteredOrders.filter(o => o.status === stat.key).length}
                </span>
                <span className="stat-label">{stat.label}</span>
              </div>
            </div>
          ))}
        </div>

        <div className="orders-list">
          {statusFilteredOrders.length === 0 ? (
            <div className="empty-state">
              <i className="fas fa-receipt"></i>
              <h3>لا توجد طلبات</h3>
              <p>لم تقم بإرسال أي طلبات بعد</p>
              <button 
                className="browse-menu-btn"
                onClick={() => setCurrentScreen('drinks')}
              >
                <i className="fas fa-coffee"></i>
                إنشاء طلب جديد
              </button>
            </div>
          ) : (
            statusFilteredOrders
              .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
              .map(currentOrder => ( // Renamed order to currentOrder to avoid conflict
                <div key={currentOrder._id} className="order-card">
                  <div className="order-summary">
                    <div className="order-info">
                      <h3 className="order-number">
                        <i className="fas fa-hashtag"></i>
                        {currentOrder.orderNumber}
                      </h3>
                      <div className="order-meta">
                        <span className="order-table">
                          <i className="fas fa-table"></i>
                          طاولة {currentOrder.tableNumber}
                        </span>
                        {currentOrder.customerName && (
                          <span className="order-customer">
                            <i className="fas fa-user"></i>
                            {currentOrder.customerName}
                          </span>
                        )}
                        <span className="order-time">
                          <i className="fas fa-clock"></i>
                          {new Date(currentOrder.createdAt).toLocaleString('ar-EG')}
                        </span>
                      </div>
                    </div>
                    
                    <div className="order-status-section">
                      {currentOrder.status && ( // Added null check for currentOrder.status
                        <span 
                          className="status-badge"
                          style={{ backgroundColor: getStatusColor(currentOrder.status) }}
                        >
                          <i className={`fas ${getStatusIcon(currentOrder.status)}`}></i>
                          {getStatusText(currentOrder.status)}
                        </span>
                      )}
                    </div>
                  </div>

                  <div className="order-items-preview">
                    {Array.isArray(currentOrder.items) && currentOrder.items.length > 0 && (
                       currentOrder.items.slice(0, 3).map((item, index) => (
                        <span key={index} className="order-item-chip">
                          {item.name} (x{item.quantity})
                        </span>
                      ))
                    )}
                    {currentOrder.items && currentOrder.items.length > 3 && (
                       <span className="order-item-chip more-items">
                         +{currentOrder.items.length - 3} أخرى
                       </span>
                    )}
                  </div>
                  
                  <div className="order-total">
                    <span className="total-label">الإجمالي:</span>
                    <span className="total-amount">{currentOrder.totalPrice.toFixed(2)} ج.م</span>
                  </div>

                  <div className="order-actions">
                    {currentOrder.status === 'ready' && (
                      <button 
                        className="deliver-btn"
                        onClick={() => markAsDelivered(currentOrder._id)}
                      >
                        <i className="fas fa-truck"></i>
                        تم التسليم
                      </button>
                    )}
                    {currentOrder.status === 'pending' && ( // Assuming cancelOrder is defined
                      <button 
                        className="cancel-btn"
                        onClick={() => cancelOrder(currentOrder._id)} 
                      >
                        <i className="fas fa-times-circle"></i>
                        إلغاء
                      </button>
                    )}
                    <button 
                      className="details-btn"
                      onClick={() => {
                        setSelectedOrderDetails(currentOrder);
                        setShowOrderDetailsModal(true);
                      }}
                    >
                      <i className="fas fa-info-circle"></i>
                      التفاصيل
                    </button>
                  </div>
                </div>
              ))
          )}
        </div>
      </div>
    );
  };  const renderTablesScreen = () => {
    const waiterName = localStorage.getItem('waiterName');
    const waiterUsername = localStorage.getItem('username');
    const waiterId = localStorage.getItem('waiterId'); 

    if (!waiterId) { 
      console.error("[RenderTablesScreen] Waiter ID not found in localStorage. Please log in again.");
      return <p className="error-message">خطأ: تفاصيل النادل غير موجودة (المعرف مفقود). يرجى تسجيل الدخول مرة أخرى.</p>;
    }

    const waiterTableAccounts = tableAccounts.filter(
      (account): account is TableAccount => { // Type guard
        if (!account || typeof account !== 'object') {
          // console.warn("Skipping invalid account object:", account);
          return false; 
        }
        // Ensure crucial properties exist before accessing them
        if (typeof account.status !== 'string' || 
            !account.waiter || typeof account.waiter !== 'object' || 
            typeof account.waiter.id === 'undefined') {
          // console.warn("Skipping malformed table account (missing status, waiter, or waiter.id):", account);
          return false;
        }
        const accountWaiterIdString = String(account.waiter.id); 
        const isMatch = accountWaiterIdString === waiterId && account.status === 'active';
        return isMatch;
      }
    );
    
    const openTablesCount = waiterTableAccounts.length;
    // Calculate totalAmountForWaiter safely, ensuring account.totalAmount is a number
    const totalAmountForWaiter = waiterTableAccounts.reduce((sum, account) => {
        const amount = account.totalAmount;
        return sum + (typeof amount === 'number' ? amount : 0);
    }, 0);

    const totalOrdersForWaiter = waiterTableAccounts.reduce((sum, account) => {
        const ordersArray = account.orders; // Ensure account.orders is accessed safely
        return sum + (Array.isArray(ordersArray) ? ordersArray.length : 0);
    }, 0);


    return (
      <div className="content-container">
        <div className="screen-header">
          <h1 className="screen-title">
            <i className="fas fa-table"></i>
            طاولاتي ({openTablesCount})
          </h1>
          <p className="screen-subtitle">إدارة ومتابعة حسابات الطاولات الخاصة بي</p>
        </div>

        <div className="tables-stats">
          <div className="stat-card">
            <div className="stat-icon active">
              <i className="fas fa-play-circle"></i>
            </div>
            <div className="stat-info">
              <span className="stat-number">{openTablesCount}</span>
              <span className="stat-label">طاولات مفتوحة</span>
            </div>
          </div>
          
          <div className="stat-card">
            <div className="stat-icon total">
              <i className="fas fa-money-bill-wave"></i>
            </div>
            <div className="stat-info">
              <span className="stat-value">{totalAmountForWaiter.toFixed(2)}</span>
              <span className="stat-label">ج.م - إجمالي المبيعات</span>
            </div>
          </div>
          
          <div className="stat-card">
            <div className="stat-icon orders">
              <i className="fas fa-receipt"></i>
            </div>
            <div className="stat-info">
              <span className="stat-number">{totalOrdersForWaiter}</span>
              <span className="stat-label">إجمالي الطلبات</span>
            </div>
          </div>
        </div>

        <div className="tables-list">
          {waiterTableAccounts.length === 0 ? (
            <div className="empty-state">
              <i className="fas fa-table"></i>
              <h3>لا توجد طاولات مفتوحة</h3>
              <p>لم تقم بفتح أي حسابات طاولات بعد</p>
              <button 
                className="browse-menu-btn"
                onClick={() => setCurrentScreen('drinks')}
              >
                <i className="fas fa-plus"></i>
                إنشاء طلب جديد
              </button>
            </div>
          ) : (
            waiterTableAccounts
              .sort((a, b) => new Date(b.createdAt || 0).getTime() - new Date(a.createdAt || 0).getTime())
              .map(account => (
                <div key={account._id} className="table-account-card modern">
                  <div className="table-account-main-info">
                    <div className="table-identifier">
                      <i className="fas fa-table"></i>
                      <h3>طاولة {account.tableNumber || 'N/A'}</h3>
                    </div>
                    <span className={`table-status-badge ${account.status === 'active' ? 'active' : 'closed'}`}>
                      {account.status === 'active' ? 'نشطة' : (account.status === 'closed' ? 'مغلقة' : 'غير معروف')}
                    </span>
                  </div>
                  <div className="table-account-details-grid">
                    <div className="detail-item">
                      <span className="detail-label">الناديل:</span>
                      <span className="detail-value">{account.waiter?.name || account.waiter?.username || 'غير محدد'}</span>
                    </div>
                    <div className="detail-item">
                      <span className="detail-label">إجمالي الحساب:</span>
                      <span className="detail-value amount">{(typeof account.totalAmount === 'number' ? account.totalAmount.toFixed(2) : '0.00')} ج.م</span>
                    </div>
                    <div className="detail-item">
                      <span className="detail-label">عدد الطلبات:</span>
                      <span className="detail-value">{Array.isArray(account.orders) ? account.orders.length : 0}</span>
                    </div>
                    <div className="detail-item">
                      <span className="detail-label">وقت الفتح:</span>
                      <span className="detail-value time">{account.createdAt ? new Date(account.createdAt).toLocaleString('ar-EG') : '-'}</span>
                    </div>
                    {account.lastActivityAt && (
                      <div className="detail-item">
                        <span className="detail-label">آخر نشاط:</span>
                        <span className="detail-value time">{new Date(account.lastActivityAt).toLocaleString('ar-EG')}</span>
                      </div>
                    )}
                  </div>
                  <div className="table-account-actions">
                    <button 
                      className="details-btn-table"
                      onClick={() => {
                        setSelectedTableAccountDetails(account); 
                        setShowTableDetailsModal(true);
                      }}
                    >
                      <i className="fas fa-eye"></i>
                      عرض التفاصيل
                    </button>
                    {/* Add other actions like close table if needed */}
                  </div>
                </div>
              ))
          )}
        </div>
      </div>
    );  const renderCartScreen = () => {
    return (
      <div className="content-container">
        <div className="screen-header">
          <h1 className="screen-title">
            <i className="fas fa-shopping-cart"></i>
            سلة المشتريات ({cart.length})
          </h1>
          <p className="screen-subtitle">مراجعة وإدارة عناصر السلة</p>
        </div>

        <div className="cart-screen-content">
          {cart.length === 0 ? (
            <div className="empty-cart-state">
              <div className="empty-cart-icon">
                <i className="fas fa-shopping-cart"></i>
              </div>
              <h3>سلة المشتريات فارغة</h3>
              <p>لم تقم بإضافة أي عناصر إلى السلة بعد</p>
              <button 
                className="browse-menu-btn"
                onClick={() => setCurrentScreen('drinks')}
              >
                <i className="fas fa-coffee"></i>
                تصفح المشروبات
              </button>
            </div>
          ) : (
            <div className="cart-screen-grid">
              {/* Cart Items Section */}
              <div className="cart-items-section">
                <div className="section-header">
                  <h3>
                    <i className="fas fa-list"></i>
                    العناصر المحددة
                  </h3>
                  <span className="items-count">{cart.length} عنصر</span>
                </div>

                <div className="cart-items-list">
                  {cart.map(item => (
                    <div key={item._id} className="cart-item-card">                        <div className="cart-item-info">
                        <h4 className="cart-item-name">{item.name}</h4>
                        {item.description && (
                          <p className="cart-item-description">{item.description}</p>
                        )}
                        <div className="cart-item-categories">
                          {item.categoryDetails?.map(cat => (
                            <span 
                              key={cat._id} 
                              className="category-tag small"
                              style={{ backgroundColor: cat.color }}
                            >
                              <i className={`fas ${getCategoryIcon(cat.name)}`}></i>
                              {cat.name}
                            </span>
                          ))}
                        </div>
                        
                        {/* ملاحظات للطباخ */}
                        <div className="cart-item-notes">
                          <label htmlFor={`notes-${item._id}`} className="notes-label">
                            <i className="fas fa-sticky-note"></i>
                            ملاحظات للطباخ:
                          </label>
                          <textarea
                            id={`notes-${item._id}`}
                            className="notes-input"
                            placeholder="أدخل ملاحظات خاصة للطباخ (اختياري)..."
                            value={item.notes || ''}
                            onChange={(e) => {
                              const updatedCart = cart.map(cartItem =>
                                cartItem._id === item._id
                                  ? { ...cartItem, notes: e.target.value }
                                  : cartItem
                              );
                              setCart(updatedCart);
                              localStorage.setItem('waiterCart', JSON.stringify(updatedCart));
                            }}
                            rows={2}
                          />
                        </div>
                      </div>

                      <div className="cart-item-controls">
                        <div className="price-info">
                          <span className="unit-price">{item.price} ج.م</span>
                          <span className="total-price">
                            {(item.price * item.quantity).toFixed(2)} ج.م
                          </span>
                        </div>

                        <div className="quantity-controls">
                          <button 
                            className="quantity-btn minus"
                            onClick={() => updateCartQuantity(item._id, item.quantity - 1)}
                            disabled={item.quantity <= 1}
                          >
                            <i className="fas fa-minus"></i>
                          </button>
                          <span className="quantity-display">{item.quantity}</span>
                          <button 
                            className="quantity-btn plus"
                            onClick={() => updateCartQuantity(item._id, item.quantity + 1)}
                          >
                            <i className="fas fa-plus"></i>
                          </button>
                        </div>

                        <button 
                          className="remove-item-btn"
                          onClick={() => updateCartQuantity(item._id, 0)}
                          title="حذف العنصر"
                        >
                          <i className="fas fa-trash"></i>
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Order Summary Section */}
              <div className="order-summary-section">
                <div className="section-header">
                  <h3>
                    <i className="fas fa-calculator"></i>
                    ملخص الطلب
                  </h3>
                </div>

                <div className="summary-content">
                  <div className="summary-items">
                    {cart.map(item => (
                      <div key={item._id} className="summary-item">
                        <span className="item-name">{item.name}</span>
                        <span className="item-details">
                          {item.quantity} × {item.price} ج.م
                        </span>
                        <span className="item-total">
                          {(item.price * item.quantity).toFixed(2)} ج.م
                        </span>
                      </div>
                    ))}
                  </div>

                  <div className="summary-divider"></div>

                  <div className="summary-total">
                    <span className="total-label">المجموع الإجمالي:</span>
                    <span className="total-amount">
                      {cart.reduce((sum, item) => sum + (item.price * item.quantity), 0).toFixed(2)} ج.م
                    </span>
                  </div>

                  {/* Order Form */}
                  <div className="order-form">
                    <div className="form-group">
                      <label htmlFor="table-number">
                        <i className="fas fa-table"></i>
                        رقم الطاولة *
                      </label>
                      <input 
                        id="table-number"
                        type="text" 
                        value={tableNumber}
                        onChange={(e) => setTableNumber(e.target.value)}
                        placeholder="أدخل رقم الطاولة"
                        className="form-input"
                        required
                      />
                    </div>
                    
                    <div className="form-group">
                      <label htmlFor="customer-name">
                        <i className="fas fa-user"></i>
                        اسم العميل (اختياري)
                      </label>
                      <input 
                        id="customer-name"
                        type="text" 
                        value={customerName}
                        onChange={(e) => setCustomerName(e.target.value)}
                        placeholder="أدخل اسم العميل"
                        className="form-input"
                      />
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="cart-actions">
                    <button 
                      className="clear-cart-btn"
                      onClick={clearCart}
                    >
                      <i className="fas fa-trash-alt"></i>
                      مسح السلة
                    </button>
                    
                    <button 
                      className="submit-order-btn"
                      onClick={submitOrder}
                      disabled={loading || !tableNumber || cart.length === 0}
                    >
                      <i className="fas fa-paper-plane"></i>
                      {loading ? 'جاري الإرسال...' : 'إرسال الطلب'}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  // Sidebar Component
  const Sidebar = () => (
    <>
      {/* Overlay for mobile */}
      {isMobile && sidebarOpen && (
        <div className="sidebar-overlay active" onClick={closeSidebar}></div>
      )}
      <div className={`dashboard-sidebar${sidebarOpen ? ' visible' : ''}${isMobile && !sidebarOpen ? ' hidden' : ''}`}
        style={isMobile ? { right: sidebarOpen ? 0 : '-100vw', transition: 'right 0.3s' } : {}}>
        {/* Sidebar Header */}
        <div className="sidebar-header">
          <div className="sidebar-logo">
            <i className="fas fa-coffee"></i>
            <span>DeshaCoffee</span>
          </div>
          {/* Close button for mobile */}
          {isMobile && (
            <button className="sidebar-close-btn" onClick={closeSidebar} aria-label="إغلاق القائمة الجانبية">
              <i className="fas fa-times"></i>
            </button>
          )}
        </div>

        {/* Navigation */}
        <nav className="sidebar-nav">
          <ul className="nav-menu">
            <li className="nav-item">
              <button 
                className={`nav-link ${currentScreen === 'drinks' ? 'active' : ''}`}
                onClick={() => { setCurrentScreen('drinks'); if(isMobile) closeSidebar(); }}
              >
                <i className="fas fa-coffee nav-icon"></i>
                <span className="nav-text">المشروبات</span>
              </button>
            </li>
            <li className="nav-item">
              <button 
                className={`nav-link ${currentScreen === 'orders' ? 'active' : ''}`}
                onClick={() => { setCurrentScreen('orders'); if(isMobile) closeSidebar(); }}
              >
                <i className="fas fa-receipt nav-icon"></i>
                <span className="nav-text">الطلبات</span>
              </button>
            </li>
            <li className="nav-item">
              <button 
                className={`nav-link ${currentScreen === 'cart' ? 'active' : ''}`}
                onClick={() => { setCurrentScreen('cart'); if(isMobile) closeSidebar(); }}
              >
                <i className="fas fa-shopping-cart nav-icon"></i>
                <span className="nav-text">سلة المشتريات ({cart.length})</span>
              </button>
            </li>
            <li className="nav-item">
              <button 
                className={`nav-link ${currentScreen === 'tables' ? 'active' : ''}`}
                onClick={() => { setCurrentScreen('tables'); if(isMobile) closeSidebar(); }}
              >
                <i className="fas fa-table nav-icon"></i>
                <span className="nav-text">الطاولات</span>
              </button>
            </li>
          </ul>
        </nav>      {/* Stats Section */}
        <div className="sidebar-stats">
          <div className="stats-title">
            <i className="fas fa-chart-line"></i>
            إحصائيات سريعة
          </div>
          <div className="stat-item">
            <div className="stat-icon">
              <i className="fas fa-shopping-cart"></i>
            </div>
            <div className="stat-details">
              <span className="stat-value">{cart.length}</span>
              <span className="stat-label">عناصر في السلة</span>
            </div>
          </div>
          <div className="stat-item">
            <div className="stat-icon">
              <i className="fas fa-money-bill-wave"></i>
            </div>
            <div className="stat-details">
              <span className="stat-value">{cart.reduce((sum, item) => sum + (item.price * item.quantity), 0)}</span>
              <span className="stat-label">ج.م - المجموع</span>
            </div>
          </div>
          <div className="stat-item">
            <div className="stat-icon">
              <i className="fas fa-clock"></i>
            </div>
            <div className="stat-details">
              <span className="stat-value">{orders.length}</span>
              <span className="stat-label">طلباتي اليوم</span>
            </div>
          </div>
        </div>

        {/* Sidebar Footer */}
        <div className="sidebar-footer">
          <button className="logout-btn" onClick={handleLogout}>
            <i className="fas fa-sign-out-alt"></i>
            <span>تسجيل الخروج</span>
          </button>
        </div>
      </div>
    </>
  );

  // Cart Component  
  const CartSection = () => {
    if (cart.length === 0) return null;

    return (
      <div className="cart-section">
        <div className="cart-header">
          <h3>
            <i className="fas fa-shopping-cart"></i>
            السلة ({cart.length})
          </h3>
          <span className="cart-total">
            {cart.reduce((sum, item) => sum + (item.price * item.quantity), 0)} ج.م
          </span>
        </div>

        <div className="cart-items">
          {cart.map(item => (
            <div key={item._id} className="cart-item">
              <div className="cart-item-info">
                <h4>{item.name}</h4>
                <span className="cart-item-price">{item.price} ج.م</span>
              </div>
              
              <div className="cart-item-controls">
                <button 
                  className="quantity-btn"
                  onClick={() => updateCartQuantity(item._id, item.quantity - 1)}
                >
                  <i className="fas fa-minus"></i>
                </button>
                <span className="quantity">{item.quantity}</span>
                <button 
                  className="quantity-btn"
                  onClick={() => updateCartQuantity(item._id, item.quantity + 1)}
                >
                  <i className="fas fa-plus"></i>
                </button>
              </div>
            </div>
          ))}
        </div>

        <div className="cart-form">
          <div className="form-group">
            <label>رقم الطاولة</label>
            <input 
              type="text" 
              value={tableNumber}
              onChange={(e) => setTableNumber(e.target.value)}
              placeholder="أدخل رقم الطاولة"
              required
            />
          </div>
          
          <div className="form-group">
            <label>اسم العميل (اختياري)</label>
            <input 
              type="text" 
              value={customerName}
              onChange={(e) => setCustomerName(e.target.value)}
              placeholder="أدخل اسم العميل"
            />
          </div>
        </div>

        <div className="cart-actions">
          <button className="clear-cart-btn" onClick={clearCart}>
            <i className="fas fa-trash"></i>
            مسح السلة
          </button>
          <button 
            className="submit-order-btn"
            onClick={submitOrder}
            disabled={loading || !tableNumber}
          >
            <i className="fas fa-paper-plane"></i>
            {loading ? 'جاري الإرسال...' : 'إرسال الطلب'}
          </button>
        </div>
      </div>
    );
  };  // Main Render
  return (
    <div className="waiter-dashboard">
      {/* Sidebar toggle button for mobile */}
      {isMobile && !sidebarOpen && (
        <button className="mobile-menu-toggle" onClick={openSidebar} aria-label="فتح القائمة الجانبية">
          <i className="fas fa-bars"></i>
        </button>
      )}
      <Sidebar />
      <main className="dashboard-main">
        <div className="content-card">
          {currentScreen === 'drinks' && renderDrinksScreen()}
          {currentScreen === 'orders' && renderOrdersScreen()}
          {currentScreen === 'tables' && renderTablesScreen()}
          {currentScreen === 'cart' && renderCartScreen()}
        </div>
      </main>

      {/* Order Details Modal */}
      {showOrderDetailsModal && selectedOrderDetails && (
        <div className="modal-overlay" onClick={() => setShowOrderDetailsModal(false)}>
          <div className="modal-content order-details-modal" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>
                <i className="fas fa-receipt"></i>
                تفاصيل الطلب {selectedOrderDetails.orderNumber}
              </h2>
              <button 
                className="close-modal-btn"
                onClick={() => setShowOrderDetailsModal(false)}
              >
                <i className="fas fa-times"></i>
              </button>
            </div>

            <div className="modal-body">
              {/* Order Info */}
              <div className="order-details-section">
                <h3>
                  <i className="fas fa-info-circle"></i>
                  معلومات الطلب
                </h3>
                <div className="order-info-grid">
                  <div className="info-item">
                    <span className="info-label">رقم الطلب:</span>
                    <span className="info-value">{selectedOrderDetails.orderNumber}</span>
                  </div>
                  <div className="info-item">
                    <span className="info-label">الطاولة:</span>
                    <span className="info-value">طاولة {selectedOrderDetails.tableNumber}</span>
                  </div>
                  {selectedOrderDetails.customerName && (
                    <div className="info-item">
                      <span className="info-label">العميل:</span>
                      <span className="info-value">{selectedOrderDetails.customerName}</span>
                    </div>
                  )}
                  <div className="info-item">
                    <span className="info-label">الحالة:</span>
                    <span 
                      className="info-value status-badge"
                      style={{ backgroundColor: getStatusColor(selectedOrderDetails.status) }}
                    >
                      <i className={`fas ${getStatusIcon(selectedOrderDetails.status)}`}></i>
                      {getStatusText(selectedOrderDetails.status)}
                    </span>
                  </div>
                  <div className="info-item">
                    <span className="info-label">وقت الإنشاء:</span>
                    <span className="info-value">
                      {new Date(selectedOrderDetails.createdAt).toLocaleString('ar-EG')}
                    </span>
                  </div>
                </div>
              </div>

              {/* Order Items */}
              <div className="order-details-section">
                <h3>
                  <i className="fas fa-list"></i>
                  العناصر المطلوبة ({selectedOrderDetails.items.length})
                </h3>
                <div className="detailed-items-list">
                  {selectedOrderDetails.items.map((item, index) => (
                    <div key={index} className="detailed-order-item">
                      <div className="item-main-info">
                        <div className="item-name-section">
                          <h4 className="item-name">{item.name}</h4>
                          <div className="item-quantity-price">
                            <span className="item-quantity">الكمية: {item.quantity}</span>
                            <span className="item-unit-price">السعر: {item.price} ج.م</span>
                            <span className="item-total-price">الإجمالي: {(item.price * item.quantity).toFixed(2)} ج.م</span>
                          </div>
                        </div>
                      </div>
                      {item.notes && (
                        <div className="item-notes-section">
                          <div className="notes-header">
                            <i className="fas fa-sticky-note"></i>
                            ملاحظات للطباخ:
                          </div>
                          <div className="notes-content">{item.notes}</div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {/* Discount Section */}
              <div className="order-details-section">
                <h3>
                  <i className="fas fa-percent"></i>
                  طلب خصم
                </h3>
                {selectedOrderDetails.discountStatus === 'pending' ? (
                  <div className="discount-status pending">
                    <i className="fas fa-clock"></i>
                    يوجد طلب خصم في الانتظار
                  </div>
                ) : selectedOrderDetails.discountStatus === 'approved' ? (
                  <div className="discount-status approved">
                    <i className="fas fa-check-circle"></i>
                    تم الموافقة على الخصم: {selectedOrderDetails.discountAmount} ج.م
                  </div>
                ) : selectedOrderDetails.discountStatus === 'rejected' ? (
                  <div className="discount-status rejected">
                    <i className="fas fa-times-circle"></i>
                    تم رفض طلب الخصم
                  </div>
                ) : (
                  <button 
                    className="request-discount-btn"
                    onClick={() => {
                      setSelectedOrderForDiscount(selectedOrderDetails);
                      setShowDiscountModal(true);
                      setShowOrderDetailsModal(false);
                    }}
                    disabled={selectedOrderDetails.status !== 'pending' && selectedOrderDetails.status !== 'preparing'}
                  >
                    <i className="fas fa-percent"></i>
                    طلب خصم
                  </button>
                )}
              </div>

              {/* Order Total */}
              <div className="order-details-section order-total-section">
                <div className="total-breakdown">
                  <div className="total-row">
                    <span className="total-label">المجموع الفرعي:</span>
                    <span className="total-value">{
                      selectedOrderDetails.items.reduce((sum, item) => sum + (item.price * item.quantity), 0).toFixed(2)
                    } ج.م</span>
                  </div>
                  {selectedOrderDetails.discountAmount && selectedOrderDetails.discountStatus === 'approved' && (
                    <div className="total-row discount-row">
                      <span className="total-label">الخصم:</span>
                      <span className="total-value discount">-{selectedOrderDetails.discountAmount.toFixed(2)} ج.م</span>
                    </div>
                  )}
                  <div className="total-row final-total">
                    <span className="total-label">المجموع النهائي:</span>
                    <span className="total-value">
                      {getOrderFinalPrice(selectedOrderDetails)}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div className="modal-footer">
              {selectedOrderDetails.status === 'ready' && (
                <button 
                  className="deliver-btn"
                  onClick={() => {
                    if (selectedOrderDetails && selectedOrderDetails._id) {
                       markAsDelivered(selectedOrderDetails._id);
                    }
                    setShowOrderDetailsModal(false);
                  }}
                >
                  <i className="fas fa-truck"></i>
                  تم التسليم
                </button>
              )}
              <button 
                className="close-btn"
                onClick={() => setShowOrderDetailsModal(false)}
              >
                إغلاق
              </button>
            </div>
          </div>
        </div>      )}

      {/* Discount Request Modal */}
      {showDiscountModal && selectedOrderForDiscount && (
        <div className="modal-overlay" onClick={() => setShowDiscountModal(false)}>
          <div className="modal-content discount-modal" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>
                <i className="fas fa-percent"></i>
                طلب خصم للطلب {selectedOrderForDiscount.orderNumber}
              </h2>
              <button 
                className="close-modal-btn"
                onClick={() => setShowDiscountModal(false)}
              >
                <i className="fas fa-times"></i>
              </button>
            </div>

            <div className="modal-body">
              <div className="discount-form">
                <div className="form-group">
                  <label htmlFor="discount-amount">
                    <i className="fas fa-money-bill-wave"></i>
                    مبلغ الخصم (ج.م)
                  </label>
                  <input
                    id="discount-amount"
                    type="number"
                    min="0"
                    step="0.01"
                    max={selectedOrderForDiscount.totalPrice}
                    value={discountAmount}
                    onChange={(e) => setDiscountAmount(e.target.value)}
                    placeholder="أدخل مبلغ الخصم"
                    className="form-input"
                  />
                </div>
                
                <div className="form-group">
                  <label htmlFor="discount-reason">
                    <i className="fas fa-comment"></i>
                    سبب الخصم
                  </label>
                  <textarea
                    id="discount-reason"
                    value={discountReason}
                    onChange={(e) => setDiscountReason(e.target.value)}
                    placeholder="أدخل سبب طلب الخصم..."
                    className="form-textarea"
                    rows={3}
                    required
                  />
                </div>

                <div className="discount-summary">
                  <div className="summary-row">
                    <span>المجموع الأصلي:</span>
                    <span>{selectedOrderForDiscount.totalPrice.toFixed(2)} ج.م</span>
                  </div>
                  {discountAmount && (
                    <>
                      <div className="summary-row discount">
                        <span>الخصم:</span>
                        <span>-{parseFloat(discountAmount || '0').toFixed(2)} ج.م</span>
                      </div>
                      <div className="summary-row total">
                        <span>المجموع بعد الخصم:</span>
                        <span>
                          {(selectedOrderForDiscount.totalPrice - parseFloat(discountAmount || '0')).toFixed(2)} ج.م
                        </span>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button 
                className="submit-discount-btn"
                onClick={async () => {
                  if (!discountAmount || !discountReason) {
                    showError('يرجى إدخال مبلغ الخصم وسبب الطلب');
                    return;
                  }
                  if (!selectedOrderForDiscount || !selectedOrderForDiscount._id) {
                    showError('لم يتم تحديد طلب صالح للخصم.');
                    return;
                  }
                  try {
                    await authenticatedPost('/api/discount-requests', {
                      orderId: selectedOrderForDiscount._id,
                      discountAmount: parseFloat(discountAmount),
                      reason: discountReason
                    });
                    showSuccess('تم إرسال طلب الخصم بنجاح');
                    setShowDiscountModal(false);
                    setDiscountAmount('');
                    setDiscountReason('');
                    setSelectedOrderForDiscount(null);
                    fetchOrders();
                  } catch (error) {
                    console.error('خطأ في إرسال طلب الخصم:', error);
                    const errorMessage = handleApiError(error);
                    showError(errorMessage || 'فشل في إرسال طلب الخصم');
                  }                }}
                disabled={loading || !discountAmount || !discountReason || parseFloat(discountAmount) <= 0 || (selectedOrderForDiscount && parseFloat(discountAmount) > getOrderFinalPrice(selectedOrderForDiscount))}
              >
                <i className="fas fa-paper-plane"></i>
                إرسال طلب الخصم
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Table Details Modal - NEW */}
      {showTableDetailsModal && selectedTableAccountDetails && (
        <div className="modal-overlay" onClick={() => setShowTableDetailsModal(false)}>
          <div className="modal-content table-details-modal modern-modal" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>
                <i className="fas fa-table"></i>
                تفاصيل حساب الطاولة {selectedTableAccountDetails.tableNumber}
              </h2>
              <button className="close-modal-btn" onClick={() => setShowTableDetailsModal(false)}>
                <i className="fas fa-times"></i>
              </button>
            </div>
            <div className="modal-body">
              <div className="table-details-section info-section">
                <h3><i className="fas fa-info-circle"></i> معلومات الحساب</h3>
                <div className="info-grid compact">
                  <div className="info-item"><span className="info-label">رقم الطاولة:</span> <span className="info-value">{selectedTableAccountDetails.tableNumber || 'N/A'}</span></div>
                  <div className="info-item"><span className="info-label">الناديل:</span> <span className="info-value">{selectedTableAccountDetails.waiter?.name || selectedTableAccountDetails.waiter?.username || 'N/A'}</span></div>
                  <div className="info-item"><span className="info-label">وقت الفتح:</span> <span className="info-value time">{selectedTableAccountDetails.createdAt ? new Date(selectedTableAccountDetails.createdAt).toLocaleString('ar-EG', { dateStyle: 'short', timeStyle: 'short' }) : '-'}</span></div>
                  <div className="info-item"><span className="info-label">آخر نشاط:</span> <span className="info-value time">{selectedTableAccountDetails.lastActivityAt ? new Date(selectedTableAccountDetails.lastActivityAt).toLocaleString('ar-EG', { dateStyle: 'short', timeStyle: 'short' }) : '-'}</span></div>
                  <div className="info-item">
                    <span className="info-label">الحالة:</span> 
                    <span className={`info-value status-badge-modal ${selectedTableAccountDetails.status === 'active' ? 'active' : 'closed'}`}>
                      {selectedTableAccountDetails.status === 'active' ? 'نشط' : (selectedTableAccountDetails.status === 'closed' ? 'مغلق' : 'غير معروف')}
                    </span>
                  </div>
                </div>
              </div>

              <div className="table-details-section orders-section">
                <h3><i className="fas fa-receipt"></i> الطلبات المرتبطة</h3>
                {selectedTableAccountDetails.orders && selectedTableAccountDetails.orders.length > 0 ? (
                  <div className="orders-list-in-table-details">
                    {selectedTableAccountDetails.orders.map(order => (
                      <div key={order._id} className="order-item-in-table-details">
                        <div className="order-item-info">
                          <span className="order-item-id">#{order.orderNumber}</span>
                          <div className="order-item-meta">
                            <span className="order-item-time">{order.createdAt ? new Date(order.createdAt).toLocaleTimeString('ar-EG', { hour: '2-digit', minute:'2-digit' }) : '-'}</span>
                            <span className="order-item-total">{(typeof order.finalPrice === 'number' ? order.finalPrice.toFixed(2) : (typeof order.totalPrice === 'number' ? order.totalPrice.toFixed(2) : '0.00'))} ج.م</span>
                          </div>
                          {Array.isArray(order.items) && order.items.length > 0 && (
                            <details className="order-items-details-toggle">
                              <summary>عرض عناصر الطلب ({order.items.length})</summary>
                              <ul className="order-items-in-table-details compact-sub-list">
                                {order.items.map((item, idx) => (
                                  <li key={item.id || idx}> 
                                    {item.name || 'Unknown Item'} (x{item.quantity || 1}) - {(typeof item.price === 'number' ? item.price.toFixed(2) : '0.00')} ج.م
                                    {item.notes && <p className="item-notes-in-modal"><em>ملاحظات: {item.notes}</em></p>}
                                  </li>
                                ))}
                              </ul>
                            </details>
                          )}
                        </div>
                      ))}
                  </div>
                ) : (
                  <p className="no-orders-message">لا توجد طلبات مرتبطة بهذا الحساب حاليًا.</p>
                )}
              </div>
              
              <div className="table-details-section total-section summary-box">
                <h3><i className="fas fa-calculator"></i> إجمالي الحساب</h3>
                <p className="table-total-amount final-amount">{(typeof selectedTableAccountDetails.totalAmount === 'number' ? selectedTableAccountDetails.totalAmount.toFixed(2) : '0.00')} ج.م</p>
              </div>
            </div>
            <div className="modal-footer">
              <button className="close-btn modern-button" onClick={() => setShowTableDetailsModal(false)}>إغلاق</button>
            </div>
          </div>
        </div>
      )}

    </div>
  );
  
  // Move helper function definitions inside the component
  const getAccountStatus = (account: any): string => {
    if (!account) return 'غير معروف';
    if (account.isClosed === true) return 'مغلق';
    if (Array.isArray(account.orders) && account.orders.length > 0) return 'مفتوح'; // Or any other logic for "active"
    return 'فارغ'; // Or 'جاهزة للإغلاق' based on your logic
  };

  const getAccountTotal = (account: any): number => {
    if (!account) return 0;
    if (typeof account.totalAmount === 'number') {
      return account.totalAmount;
    }
    if (Array.isArray(account.orders)) {
      return account.orders.reduce((sum: number, order: any) => {
        const price = (order && typeof order.totalPrice === 'number') ? order.totalPrice : 0;
        return sum + price;
      }, 0);
    }
    return 0;
  };

  const formatTime = (timestamp: string | Date | undefined): string => {
    if (!timestamp) return '-';
    try {
      const date = new Date(timestamp);
      if (isNaN(date.getTime())) {
        console.warn('Invalid date/timestamp provided to formatTime:', timestamp);
        return '-';
      }
      return date.toLocaleTimeString('ar-EG', { hour: '2-digit', minute: '2-digit' });
    } catch (e) {
      console.error("Error formatting time for timestamp:", timestamp, e);
      return '-';
    }
  };

  const closeTableAccount = async (accountId: string) => {
    if (!accountId) {
      // showError, showSuccess, fetchTableAccounts are now in scope
      if (typeof showError === 'function') {
        showError('معرف الحساب غير صالح.');
      } else {
        console.error('showError function not available. Account ID missing for closeTableAccount.');
      }
      return;
    }
    try {
      const url = `${APP_CONFIG.API.BASE_URL}/table-accounts/${accountId}/close`;
      const response = await authenticatedPut(url, {});

      if (response && response.data) {
        if (typeof showSuccess === 'function') {
          showSuccess('تم إغلاق الحساب بنجاح.');
        } else {
          console.log('Account closed successfully. showSuccess function not available.');
        }
        if (typeof fetchTableAccounts === 'function') {
          fetchTableAccounts();
        } else {
          console.warn('fetchTableAccounts function not available to refresh data after closing account.');
        }
      } else {
        const errorMessage = response?.error || 'فشل في إغلاق الحساب. لم يتم تلقي بيانات تأكيد.';
        if (typeof showError === 'function') {
          showError(errorMessage);
        } else {
          console.error('showError function not available. Error closing account:', errorMessage);
        }
        console.error('Failed to close table account, response:', response);
      }
    } catch (error: any) {
      console.error('Exception during closeTableAccount for account ID:', accountId, error);
      const message = error.response?.data?.message || error.message || 'حدث خطأ أثناء إغلاق الحساب.';
      if (typeof showError === 'function') {
        showError(message);
      } else {
        console.error('showError function not available. Exception message for closeTableAccount:', message);
      }
    }
  };
}
