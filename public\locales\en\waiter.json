{"dashboardTitle": "Waiter Dash<PERSON>", "menu": "<PERSON><PERSON>", "tables": "Tables", "orders": "Orders", "cart": "<PERSON><PERSON>", "placeOrderButton": "Place Order", "openTableButton": "Open Table", "viewBillButton": "View Bill", "processPaymentButton": "Process Payment", "addNotesModalTitle": "Add/Edit Order Notes", "itemAddedToCart": "{{itemName}} added to cart.", "cartEmptyError": "Cart is empty. Please add items before placing an order.", "orderPlacedSuccessfully": "Order {{orderNumber}} placed successfully!", "orderPlacementError": "Failed to place order: {{error}}", "tableOpenedSuccessfully": "Table {{tableNumber}} opened successfully.", "tableOpenError": "Failed to open table: {{error}}", "orderNotFoundError": "Order not found.", "noBillToPayError": "No bill selected to pay.", "paymentSuccessful": "Payment processed successfully!", "paymentFailedError": "Payment failed: {{error}}", "errorLoadingTables": "Error loading tables: {{error}}", "errorLoadingActiveTables": "Error loading active table accounts: {{error}}", "newOrderReceived": "New order received: {{orderNumber}}", "orderUpdated": "Order {{orderNumber}} updated to {{status}}", "selectCategory": "Select Category", "allCategories": "All Categories", "items": "Items", "quantity": "Quantity", "price": "Price", "notes": "Notes", "total": "Total", "customerName": "Customer Name", "tableNumber": "Table Number", "orderType": "Order Type", "dineIn": "<PERSON><PERSON>-<PERSON>", "takeAway": "Take Away", "delivery": "Delivery", "filterByStatus": "Filter by Status", "all": "All", "pending": "Pending", "preparing": "Preparing", "ready": "Ready", "completed": "Completed", "delivered": "Delivered", "cancelled": "Cancelled", "activeTables": "Active Tables", "noActiveTables": "No active tables.", "selectItems": "Select Items", "orderNotes": "Order Notes", "saveNotes": "Save Notes", "close": "Close", "paymentDetails": "Payment Details", "paymentMethod": "Payment Method", "cash": "Cash", "card": "Card", "online": "Online", "splitBill": "Split Bill", "splitByWays": "Split by ways", "amountPerWay": "Amount per way", "confirmPayment": "Confirm Payment", "billDetails": "<PERSON>", "orderId": "Order ID", "noOrdersToDisplay": "No orders to display for this filter."}