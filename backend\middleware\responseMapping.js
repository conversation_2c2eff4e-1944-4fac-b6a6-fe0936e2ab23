/**
 * Response Mapping Middleware - تحويل استجابات Backend للتوافق مع Frontend
 * للاستخدام مع Railway Backend + Vercel Frontend
 */

const responseMapping = {
  /**
   * تحويل Order من Backend format إلى Frontend format
   * @param {Object} order - Order object من قاعدة البيانات
   * @returns {Object} - Order object متوافق مع Frontend
   */
  mapOrderForFrontend: (order) => {
    if (!order) return null;
    
    const orderObj = order.toObject ? order.toObject() : order;
    
    return {
      _id: orderObj._id,
      orderNumber: orderObj.orderNumber,
      
      // تحويل staff إلى أسماء مباشرة
      waiterName: orderObj.staff?.waiter?.name || orderObj.staff?.waiter?.username || 'غير محدد',
      chefName: orderObj.staff?.chef?.name || orderObj.staff?.chef?.username || null,
      
      // تحويل البيانات المتداخلة إلى قيم مباشرة
      totalPrice: orderObj.totals?.total || orderObj.totalPrice || 0,
      tableNumber: orderObj.table?.number?.toString() || orderObj.tableNumber || null,
      customerName: orderObj.customer?.name || orderObj.customerName || null,
      
      // تحويل items للتوافق مع ChefDashboard
      items: (orderObj.items || []).map(item => ({
        id: item.product?._id || item.product || item.id,
        name: item.productName || item.product?.name || item.name,
        quantity: item.quantity || 1,
        price: item.price || 0,
        notes: item.notes || ''
      })),
      
      // باقي الحقول
      status: orderObj.status,
      preparationTime: orderObj.preparationTime,
      notes: orderObj.notes,
      
      // التواريخ
      createdAt: orderObj.timing?.orderTime || orderObj.createdAt,
      updatedAt: orderObj.timing?.updateTime || orderObj.updatedAt
    };
  },

  /**
   * تحويل مجموعة من Orders
   * @param {Array} orders - مصفوفة Orders
   * @returns {Array} - مصفوفة Orders محولة
   */
  mapOrdersArrayForFrontend: (orders) => {
    if (!Array.isArray(orders)) return [];
    return orders.map(order => responseMapping.mapOrderForFrontend(order));
  },

  /**
   * Middleware function للاستخدام في routes
   */
  orderResponseMiddleware: (req, res, next) => {
    // حفظ الدالة الأصلية
    const originalJson = res.json;
    
    res.json = function(data) {
      // إذا كانت البيانات تحتوي على orders، قم بتحويلها
      if (data && typeof data === 'object') {
        // إذا كانت البيانات مصفوفة من Orders
        if (Array.isArray(data)) {
          data = responseMapping.mapOrdersArrayForFrontend(data);
        }
        // إذا كانت البيانات object يحتوي على data array
        else if (data.data && Array.isArray(data.data)) {
          data.data = responseMapping.mapOrdersArrayForFrontend(data.data);
        }
        // إذا كانت البيانات order واحد
        else if (data._id && data.orderNumber) {
          data = responseMapping.mapOrderForFrontend(data);
        }
        // إذا كانت البيانات تحتوي على order واحد في data
        else if (data.data && data.data._id && data.data.orderNumber) {
          data.data = responseMapping.mapOrderForFrontend(data.data);
        }
      }
      
      // استدعاء الدالة الأصلية مع البيانات المحولة
      return originalJson.call(this, data);
    };
    
    next();
  },

  /**
   * تحويل Table Account للتوافق مع Frontend
   */
  mapTableAccountForFrontend: (tableAccount) => {
    if (!tableAccount) return null;
    
    const accountObj = tableAccount.toObject ? tableAccount.toObject() : tableAccount;
    
    return {
      _id: accountObj._id,
      tableNumber: accountObj.table?.number?.toString() || accountObj.tableNumber,
      waiterName: accountObj.staff?.waiter?.name || accountObj.waiterName,
      orders: responseMapping.mapOrdersArrayForFrontend(accountObj.orders || []),
      totalAmount: accountObj.totalAmount || 0,
      status: accountObj.status,
      createdAt: accountObj.createdAt,
      updatedAt: accountObj.updatedAt
    };
  },

  /**
   * تحويل بيانات الطاولة الواحدة للتوافق مع Frontend
   */
  mapTableForFrontend: (table) => {
    if (!table) return null;
    
    const mapped = {
      // Frontend fields mapping
      tableNumber: table.number?.toString() || table.tableNumber,
      waiterName: table.assignedWaiter?.name || table.waiterName,
      
      // Additional compatibility fields
      section: table.section,
      status: table.status,
      capacity: table.capacity,
      
      // Handle orders array vs currentOrder
      orders: table.orders || (table.currentOrder ? [table.currentOrder] : []),
      totalAmount: table.totalAmount || table.stats?.totalRevenue || 0,
      
      // Customer info mapping
      customerName: table.customer?.name || table.currentOrder?.customer?.name,
      
      // Time-based fields
      createdAt: table.createdAt || table.stats?.lastUsed,
      openTime: table.openTime || table.stats?.lastUsed,
      duration: table.duration || table.occupationTime,
      
      // VIP and features
      isVIP: table.features?.isVIP || false,
      features: table.features || {}
    };
    
    return mapped;
  },

  /**
   * تحويل مصفوفة الطاولات للتوافق مع Frontend
   */
  mapTablesArrayForFrontend: (tables) => {
    if (!Array.isArray(tables)) return [];
    
    return tables.map(table => ({
      ...table.toObject ? table.toObject() : table,
      ...responseMapping.mapTableForFrontend(table)
    }));
  }
};

module.exports = responseMapping;
