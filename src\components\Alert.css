.alert {
  display: flex;
  align-items: flex-start;
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--border-radius-md);
  margin-bottom: var(--spacing-lg);
  position: relative;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.alert-icon {
  margin-left: var(--spacing-md);
  font-size: var(--font-size-lg);
  display: flex;
  align-items: center;
}

.alert-content {
  flex: 1;
}

.alert-close {
  background: transparent;
  border: none;
  color: inherit;
  opacity: 0.7;
  cursor: pointer;
  font-size: var(--font-size-md);
  padding: var(--spacing-xs);
  margin-right: var(--spacing-xs);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-speed) ease;
}

.alert-close:hover {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.1);
}

/* أنواع التنبيهات */
.alert-info {
  background-color: rgba(33, 150, 243, 0.1);
  color: var(--info);
  border-right: 4px solid var(--info);
}

.alert-success {
  background-color: rgba(76, 175, 80, 0.1);
  color: var(--success);
  border-right: 4px solid var(--success);
}

.alert-warning {
  background-color: rgba(255, 152, 0, 0.1);
  color: var(--warning);
  border-right: 4px solid var(--warning);
}

.alert-error {
  background-color: rgba(244, 67, 54, 0.1);
  color: var(--error);
  border-right: 4px solid var(--error);
}

/* تنسيق خاص للوضع المظلم */
[data-theme="dark"] .alert-info {
  background-color: rgba(33, 150, 243, 0.15);
}

[data-theme="dark"] .alert-success {
  background-color: rgba(76, 175, 80, 0.15);
}

[data-theme="dark"] .alert-warning {
  background-color: rgba(255, 152, 0, 0.15);
}

[data-theme="dark"] .alert-error {
  background-color: rgba(244, 67, 54, 0.15);
}

[data-theme="dark"] .alert-close:hover {
  background-color: rgba(255, 255, 255, 0.1);
}
