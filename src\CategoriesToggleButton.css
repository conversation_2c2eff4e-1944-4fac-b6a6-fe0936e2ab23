/* ========================================= */
/* Enhanced Categories Toggle Button Styles */
/* ========================================= */

/* CSS Variables for consistent theming */
:root {
  --primary-color: #6d4c41;
  --primary-dark: #5d4037;
  --accent-color: #ffab40;
  --white: #ffffff;
  --transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Enhanced Floating Categories Toggle Button */
.categories-quick-toggle {
  position: fixed;
  top: 50%;
  left: 1rem;
  transform: translateY(-50%);
  z-index: 1000;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: var(--white);
  border: none;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition);
  box-shadow: 0 8px 25px rgba(109, 76, 65, 0.4);
  font-size: 1.3rem;
  backdrop-filter: blur(15px);
  border: 3px solid rgba(255, 255, 255, 0.2);
  animation: floatingPulse 3s ease-in-out infinite;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

/* Animated border effect */
.categories-quick-toggle::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  background: linear-gradient(45deg, var(--accent-color), var(--primary-color), var(--accent-color));
  border-radius: 50%;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.categories-quick-toggle:hover::before {
  opacity: 1;
  animation: rotate 2s linear infinite;
}

/* Hover effects */
.categories-quick-toggle:hover {
  background: linear-gradient(135deg, var(--primary-dark), #5d4037);
  transform: translateY(-50%) scale(1.1);
  box-shadow: 0 12px 35px rgba(109, 76, 65, 0.5);
}

.categories-quick-toggle:active {
  transform: translateY(-50%) scale(0.95);
  transition: transform 0.1s ease;
}

/* Icon animations */
.categories-quick-toggle i {
  transition: all 0.3s ease;
}

.categories-quick-toggle:hover i {
  transform: rotate(15deg);
  color: var(--accent-color);
}

/* Badge indicator when categories are hidden */
.categories-quick-toggle.has-badge::after {
  content: '';
  position: absolute;
  top: 8px;
  right: 8px;
  width: 12px;
  height: 12px;
  background: var(--accent-color);
  border-radius: 50%;
  border: 2px solid var(--white);
  animation: badgePulse 2s ease-in-out infinite;
}

/* Keyframe animations */
@keyframes floatingPulse {
  0%, 100% { 
    transform: translateY(-50%) scale(1);
    box-shadow: 0 8px 25px rgba(109, 76, 65, 0.4);
  }
  50% { 
    transform: translateY(-50%) scale(1.05);
    box-shadow: 0 12px 35px rgba(109, 76, 65, 0.5);
  }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes badgePulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.2); }
}

/* ========================================= */
/* Responsive Design */
/* ========================================= */

/* Desktop - Hide the button */
@media (min-width: 769px) {
  .categories-quick-toggle {
    display: none !important;
  }
}

/* Tablet and Mobile */
@media (max-width: 768px) {
  .categories-quick-toggle {
    display: flex;
    width: 60px;
    height: 60px;
    font-size: 1.3rem;
    left: 1rem;
  }
}

/* Landscape orientation optimizations */
@media (max-width: 768px) and (orientation: landscape) {
  .categories-quick-toggle {
    top: 40%;
    width: 56px;
    height: 56px;
    font-size: 1.2rem;
  }
}

/* Small mobile devices (phones in portrait) */
@media (max-width: 480px) {
  .categories-quick-toggle {
    width: 52px;
    height: 52px;
    font-size: 1.1rem;
    left: 0.75rem;
    border: 2px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 6px 20px rgba(109, 76, 65, 0.4);
  }

  .categories-quick-toggle:hover {
    transform: translateY(-50%) scale(1.08);
    box-shadow: 0 8px 25px rgba(109, 76, 65, 0.5);
  }

  .categories-quick-toggle.has-badge::after {
    top: 6px;
    right: 6px;
    width: 10px;
    height: 10px;
  }
}

/* Extra small devices */
@media (max-width: 360px) {
  .categories-quick-toggle {
    width: 48px;
    height: 48px;
    font-size: 1rem;
    left: 0.5rem;
  }

  .categories-quick-toggle:hover {
    transform: translateY(-50%) scale(1.05);
  }

  .categories-quick-toggle.has-badge::after {
    top: 4px;
    right: 4px;
    width: 8px;
    height: 8px;
  }
}

/* ========================================= */
/* Touch Optimization */
/* ========================================= */

@media (hover: none) and (pointer: coarse) {
  .categories-quick-toggle {
    min-height: 44px; /* iOS recommended touch target size */
    min-width: 44px;
  }

  .categories-quick-toggle:active {
    transform: translateY(-50%) scale(0.95);
    transition: transform 0.1s ease;
  }

  /* Remove hover effects on touch devices */
  .categories-quick-toggle:hover {
    transform: translateY(-50%) scale(1);
    box-shadow: 0 8px 25px rgba(109, 76, 65, 0.4);
  }

  .categories-quick-toggle:hover i {
    transform: none;
    color: var(--white);
  }

  .categories-quick-toggle:hover::before {
    opacity: 0;
    animation: none;
  }
}

/* ========================================= */
/* Accessibility */
/* ========================================= */

.categories-quick-toggle:focus {
  outline: 3px solid var(--accent-color);
  outline-offset: 2px;
}

.categories-quick-toggle:focus-visible {
  outline: 3px solid var(--accent-color);
  outline-offset: 2px;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  .categories-quick-toggle {
    animation: none;
  }

  .categories-quick-toggle::before {
    animation: none;
  }

  .categories-quick-toggle.has-badge::after {
    animation: none;
  }

  .categories-quick-toggle i {
    transition: none;
  }

  .categories-quick-toggle:hover i {
    transform: none;
  }
}

/* ========================================= */
/* High Contrast Mode */
/* ========================================= */

@media (prefers-contrast: high) {
  .categories-quick-toggle {
    border: 3px solid var(--white);
    background: var(--primary-color);
  }

  .categories-quick-toggle:hover {
    background: var(--primary-dark);
    border-color: var(--accent-color);
  }

  .categories-quick-toggle.has-badge::after {
    border: 3px solid var(--white);
  }
}

/* ========================================= */
/* Dark Mode Support */
/* ========================================= */

@media (prefers-color-scheme: dark) {
  .categories-quick-toggle {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.6);
  }

  .categories-quick-toggle:hover {
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.7);
  }
}
