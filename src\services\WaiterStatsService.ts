// خدمة الإحصائيات للوحة النادل
import type { WaiterOrder, WaiterTableAccount, WaiterStats } from '../models/WaiterModels';

export class WaiterStatsService {
  
  // حساب إحصائيات النادل اليومية
  static calculateDailyStats(orders: WaiterOrder[], tables: WaiterTableAccount[]): WaiterStats {
    const today = new Date();
    
    // طلبات اليوم فقط
    const todayOrders = orders.filter(order => {
      const orderDate = new Date(order.createdAt);
      return orderDate.toDateString() === today.toDateString();
    });

    // حساب المبيعات
    const todaySales = todayOrders.reduce((sum, order) => {
      return sum + (order.totalAmount || 0);
    }, 0);

    // الطلبات النشطة (غير المكتملة)
    const activeOrders = orders.filter(order => 
      order.status !== 'completed' && order.status !== 'delivered'
    ).length;

    // الطلبات المكتملة اليوم
    const completedOrders = todayOrders.filter(order => 
      order.status === 'completed' || order.status === 'delivered'
    ).length;

    // متوسط قيمة الطلب
    const averageOrderValue = todayOrders.length > 0 ? todaySales / todayOrders.length : 0;

    // عدد الطاولات النشطة
    const myTables = tables.filter(table => table.isOpen).length;    return {
      todayOrders: todayOrders.length,
      todaySales,
      activeOrders,
      completedOrders,
      readyOrders: orders.filter(order => order.status === 'ready').length,
      openTables: tables.filter(table => table.isOpen).length,
      averageOrderValue,
      myTables
    };
  }

  // حساب إحصائيات الطلبات حسب الحالة
  static getOrderStatusStats(orders: WaiterOrder[]) {
    const statusCounts = {
      pending: 0,
      preparing: 0,
      ready: 0,
      completed: 0,
      delivered: 0
    };

    orders.forEach(order => {
      if (statusCounts.hasOwnProperty(order.status)) {
        statusCounts[order.status as keyof typeof statusCounts]++;
      }
    });

    return statusCounts;
  }

  // حساب إحصائيات الطاولات
  static getTableStats(tables: WaiterTableAccount[]) {
    const openTables = tables.filter(table => table.isOpen);
    const closedTables = tables.filter(table => !table.isOpen);
    
    const totalRevenue = openTables.reduce((sum, table) => sum + table.totalAmount, 0);
    const averageTableValue = openTables.length > 0 ? totalRevenue / openTables.length : 0;

    return {
      totalTables: tables.length,
      openTables: openTables.length,
      closedTables: closedTables.length,
      totalRevenue,
      averageTableValue
    };
  }

  // حساب الأداء الأسبوعي
  static getWeeklyPerformance(orders: WaiterOrder[]) {
    const now = new Date();
    const weekStart = new Date(now);
    weekStart.setDate(now.getDate() - now.getDay());
    weekStart.setHours(0, 0, 0, 0);

    const weeklyOrders = orders.filter(order => {
      const orderDate = new Date(order.createdAt);
      return orderDate >= weekStart;
    });

    const dailyStats: { [key: string]: { orders: number; sales: number } } = {};
    
    for (let i = 0; i < 7; i++) {
      const day = new Date(weekStart);
      day.setDate(weekStart.getDate() + i);
      const dayKey = day.toLocaleDateString('ar-SA');
      
      const dayOrders = weeklyOrders.filter(order => {
        const orderDate = new Date(order.createdAt);
        return orderDate.toDateString() === day.toDateString();
      });

      const daySales = dayOrders.reduce((sum, order) => sum + (order.totalAmount || 0), 0);

      dailyStats[dayKey] = {
        orders: dayOrders.length,
        sales: daySales
      };
    }

    return dailyStats;
  }

  // حساب المنتجات الأكثر طلباً للنادل
  static getTopOrderedItems(orders: WaiterOrder[], limit: number = 5) {
    const itemCounts: { [key: string]: { name: string; count: number; revenue: number } } = {};

    orders.forEach(order => {
      order.items.forEach(item => {
        const key = item.name || item._id;
        if (!itemCounts[key]) {
          itemCounts[key] = {
            name: item.name || 'منتج غير معروف',
            count: 0,
            revenue: 0
          };
        }
        itemCounts[key].count += item.quantity;
        itemCounts[key].revenue += (item.price * item.quantity);
      });
    });

    return Object.values(itemCounts)
      .sort((a, b) => b.count - a.count)
      .slice(0, limit);
  }

  // حساب متوسط وقت معالجة الطلبات
  static getAverageProcessingTime(orders: WaiterOrder[]) {
    const completedOrders = orders.filter(order => 
      (order.status === 'completed' || order.status === 'delivered') &&
      order.createdAt && order.updatedAt
    );

    if (completedOrders.length === 0) {
      return { averageMinutes: 0, totalOrders: 0 };
    }

    const totalProcessingTime = completedOrders.reduce((sum, order) => {
      const startTime = new Date(order.createdAt).getTime();
      const endTime = new Date(order.updatedAt).getTime();
      return sum + (endTime - startTime);
    }, 0);

    const averageMs = totalProcessingTime / completedOrders.length;
    const averageMinutes = Math.round(averageMs / (1000 * 60));

    return {
      averageMinutes,
      totalOrders: completedOrders.length
    };
  }

  // تحليل أداء الطاولات
  static analyzeTablePerformance(tables: WaiterTableAccount[]) {
    const tableAnalysis = tables.map(table => {
      const totalOrders = table.orders.length;
      const completedOrders = table.orders.filter(order => 
        order.status === 'completed' || order.status === 'delivered'
      ).length;
      
      const completionRate = totalOrders > 0 ? (completedOrders / totalOrders) * 100 : 0;
      
      const avgOrderValue = totalOrders > 0 ? table.totalAmount / totalOrders : 0;

      return {
        tableNumber: table.tableNumber,
        totalOrders,
        completedOrders,
        completionRate,
        totalRevenue: table.totalAmount,
        avgOrderValue,
        isOpen: table.isOpen,
        createdAt: table.createdAt
      };
    });

    return tableAnalysis.sort((a, b) => b.totalRevenue - a.totalRevenue);
  }

  // إحصائيات الذروة (أوقات أكثر الطلبات)
  static getPeakHoursAnalysis(orders: WaiterOrder[]) {
    const hourlyStats: { [key: number]: number } = {};
    
    // تهيئة جميع الساعات
    for (let hour = 0; hour < 24; hour++) {
      hourlyStats[hour] = 0;
    }

    orders.forEach(order => {
      const orderHour = new Date(order.createdAt).getHours();
      hourlyStats[orderHour]++;
    });

    // العثور على ساعات الذروة
    const sortedHours = Object.entries(hourlyStats)
      .map(([hour, count]) => ({ hour: parseInt(hour), count }))
      .sort((a, b) => b.count - a.count);

    const peakHours = sortedHours.slice(0, 3);

    return {
      hourlyStats,
      peakHours,
      busiestHour: sortedHours[0]
    };
  }

  // دالة alias للتوافق
  static calculateWaiterStats(tables: WaiterTableAccount[], orders: WaiterOrder[]): WaiterStats {
    const stats = this.calculateDailyStats(orders, tables);
    
    // إضافة الإحصائيات المفقودة
    const readyOrders = orders.filter(order => order.status === 'ready').length;
    const openTables = tables.filter(table => table.isOpen).length;
    
    return {
      ...stats,
      readyOrders,
      openTables
    };
  }
}
