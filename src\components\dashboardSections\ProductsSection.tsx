import React, { useState, useMemo } from 'react';
import { FaEdit, FaTrash, FaPlus } from 'react-icons/fa';
import type { Product, Category, ApiResponse } from '../../utils/api'; // Adjusted import path
import * as apiUtil from '../../utils/api'; // Renamed to avoid conflict with api prop
import { APP_CONFIG as GlobalAppConfig } from '../../config/app.config'; // Adjusted import path and alias
import Button from '../Button'; // Adjusted import path
import Modal from '../Modal'; // Adjusted import path
import Alert from '../Alert'; // Adjusted import path
import Card from '../Card'; // Adjusted import path

interface ProductsSectionProps {
  products: Product[];
  setProducts: React.Dispatch<React.SetStateAction<Product[]>>;
  categories: Category[];
  api: typeof apiUtil.default;
  addToast: (message: string, type: 'success' | 'error' | 'info') => void;
  handleOpenConfirmDeleteModal: (item: Product, type: 'Product') => void;
  APP_CONFIG: typeof GlobalAppConfig;
  initialProductFormData: Partial<Product>;
  searchTerm: string;
  loading: boolean;
  CURRENCY_SYMBOL: string;
}

const ProductsSection: React.FC<ProductsSectionProps> = ({
  products,
  setProducts,
  categories,
  api,
  addToast,
  handleOpenConfirmDeleteModal,
  APP_CONFIG,
  initialProductFormData,
  searchTerm,
  loading,
  CURRENCY_SYMBOL,
}) => {
  const [isInventoryModalOpen, setIsInventoryModalOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [productFormData, setProductFormData] = useState<Partial<Product>>(initialProductFormData);
  const [productModalError, setProductModalError] = useState<string | null>(null);

  const filteredProducts = useMemo(() => 
    products.filter(product => 
      product.name.toLowerCase().includes(searchTerm.toLowerCase())
    ), 
    [products, searchTerm]
  );

  const handleOpenInventoryModal = (product?: Product) => {
    setIsInventoryModalOpen(true);
    setSelectedProduct(product || null);
    setProductFormData(product ? { ...product } : initialProductFormData);
    setProductModalError(null);
  };

  const closeInventoryModal = () => {
    setIsInventoryModalOpen(false);
    setSelectedProduct(null);
    setProductFormData(initialProductFormData);
    setProductModalError(null);
  };

  const handleProductFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    let processedValue: string | number | boolean = value;
    if (type === 'checkbox') {
      processedValue = (e.target as HTMLInputElement).checked;
    } else if (type === 'number') {
      processedValue = value === '' ? '' : parseFloat(value);
    }
    setProductFormData(prev => ({ ...prev, [name]: processedValue }));
  };

  const handleProductFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setProductModalError(null);
    const { name, description, price, category: categoryId, stock, isAvailable, imageUrl } = productFormData;

    if (!name || (price === undefined || String(price).trim() === '') || !categoryId || (stock === undefined || String(stock).trim() === '')) {
      setProductModalError("يرجى ملء الحقول: الاسم، السعر، الفئة، والكمية.");
      addToast("يرجى ملء الحقول: الاسم، السعر، الفئة، والكمية.", 'error');
      return;
    }

    const numericPrice = Number(price);
    const numericStock = Number(stock);

    if (isNaN(numericPrice) || isNaN(numericStock)) {
      setProductModalError("السعر والكمية يجب أن تكون أرقامًا صالحة.");
      addToast("السعر والكمية يجب أن تكون أرقامًا صالحة.", 'error');
      return;
    }

    if (numericPrice < 0 || numericStock < 0) {
      setProductModalError("السعر والكمية يجب أن تكون أرقامًا موجبة.");
      addToast("السعر والكمية يجب أن تكون أرقامًا موجبة.", 'error');
      return;
    }

    const payload: any = {
      name,
      description: description || '',
      price: numericPrice,
      category: categoryId,
      stock: numericStock,
      isAvailable: isAvailable === undefined ? true : isAvailable,
      imageUrl: imageUrl || undefined,
    };

    try {
      let response: ApiResponse<Product>;
      if (selectedProduct && selectedProduct.id) {
        response = await api.updateProduct(selectedProduct.id, payload);
      } else {
        response = await api.createProduct(payload);
      }

      if (response.success) {
        const productsResponse = await api.getProducts();
        if (productsResponse.success && productsResponse.data) setProducts(productsResponse.data);
        closeInventoryModal();
        addToast(selectedProduct ? 'تم تحديث المنتج بنجاح!' : 'تم إنشاء المنتج بنجاح!', 'success');
      } else {
        setProductModalError(response.error || 'فشل في معالجة الطلب.');
        addToast(response.error || 'فشل في معالجة الطلب.', 'error');
      }
    } catch (err) {
      setProductModalError('حدث خطأ غير متوقع.');
      addToast('حدث خطأ غير متوقع.', 'error');
      console.error(err);
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-semibold">إدارة المنتجات</h2>
        <Button onClick={() => handleOpenInventoryModal(undefined)} icon="fa-plus" variant="primary">إضافة منتج</Button>
      </div>
      {filteredProducts.length === 0 && !loading && (
        <p className="text-center text-gray-500 my-4">لا توجد منتجات لعرضها حاليًا.</p>
      )}
      {filteredProducts.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredProducts.map(product => (
            <Card key={product.id} title={product.name} className="flex flex-col">
              <img src={product.imageUrl || 'https://via.placeholder.com/150'} alt={product.name} className="w-full h-48 object-cover rounded-t-lg" />
              <div className="p-4 flex-grow">
                <p className="text-gray-700 mb-2">{product.description}</p>
                <p className="text-lg font-semibold mb-2">{CURRENCY_SYMBOL}{product.price.toFixed(2)}</p>
                <p className={`text-sm font-medium ${product.isAvailable ? 'text-green-600' : 'text-red-600'}`}>
                  {product.isAvailable ? 'متوفر' : 'غير متوفر'} ({product.stock} متوفر)
                </p>
              </div>
              <div className="p-4 border-t border-gray-200">
                <Button onClick={() => handleOpenInventoryModal(product)} icon="fa-edit" size="sm" variant="info" className="mr-2">تعديل</Button>
                <Button onClick={() => handleOpenConfirmDeleteModal(product, 'Product')} icon="fa-trash" size="sm" variant="error">حذف</Button>
              </div>
            </Card>
          ))}
        </div>
      )}
      <Modal isOpen={isInventoryModalOpen} onClose={closeInventoryModal} title={selectedProduct ? "تعديل المنتج" : "إضافة منتج"}>
        <form onSubmit={handleProductFormSubmit}>
          {productModalError && <Alert type="error" onClose={() => setProductModalError(null)}>{productModalError}</Alert>}
          <div className="mb-4">
            <label htmlFor="productName" className="block text-sm font-medium text-gray-700">اسم المنتج</label>
            <input
              type="text"
              name="name"
              id="productName"
              value={productFormData.name || ''}
              onChange={handleProductFormChange}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              required
            />
          </div>
          <div className="mb-4">
            <label htmlFor="productDescription" className="block text-sm font-medium text-gray-700">الوصف</label>
            <textarea
              name="description"
              id="productDescription"
              value={productFormData.description || ''}
              onChange={handleProductFormChange}
              rows={3}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            />
          </div>
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div>
              <label htmlFor="productPrice" className="block text-sm font-medium text-gray-700">السعر ({CURRENCY_SYMBOL})</label>
              <input
                type="number"
                name="price"
                id="productPrice"
                value={productFormData.price === undefined ? '' : productFormData.price}
                onChange={handleProductFormChange}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                required
                step="0.01"
                min="0"
              />
            </div>
            <div>
              <label htmlFor="productStock" className="block text-sm font-medium text-gray-700">الكمية المتوفرة</label>
              <input
                type="number"
                name="stock"
                id="productStock"
                value={productFormData.stock === undefined ? '' : productFormData.stock}
                onChange={handleProductFormChange}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                required
                min="0"
              />
            </div>
          </div>
          <div className="mb-4">
            <label htmlFor="productCategory" className="block text-sm font-medium text-gray-700">الفئة</label>
            <select
              name="category"
              id="productCategory"
              value={productFormData.category || ''}
              onChange={handleProductFormChange}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              required
            >
              <option value="" disabled>اختر فئة</option>
              {categories.map(cat => (
                <option key={cat.id} value={cat.id}>{cat.name}</option>
              ))}
            </select>
          </div>
          <div className="mb-4">
            <label htmlFor="productImageUrl" className="block text-sm font-medium text-gray-700">رابط الصورة (اختياري)</label>
            <input
              type="url"
              name="imageUrl"
              id="productImageUrl"
              value={productFormData.imageUrl || ''}
              onChange={handleProductFormChange}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            />
          </div>
          <div className="mb-4">
            <label htmlFor="productIsAvailable" className="flex items-center">
              <input
                type="checkbox"
                name="isAvailable"
                id="productIsAvailable"
                checked={productFormData.isAvailable === undefined ? true : productFormData.isAvailable}
                onChange={handleProductFormChange}
                className="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
              />
              <span className="ml-2 text-sm text-gray-700">متوفر بالمخزون</span>
            </label>
          </div>
          <div className="flex justify-end space-x-2 space-x-reverse">
            <Button type="button" variant="secondary" onClick={closeInventoryModal}>إلغاء</Button>
            <Button type="submit" variant="primary">{selectedProduct ? 'حفظ التغييرات' : 'إضافة منتج'}</Button>
          </div>
        </form>
      </Modal>
    </div>
  );
};

export default ProductsSection;
