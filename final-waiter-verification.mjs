// اختبار سريع نهائي للوحة النادل
import fetch from 'node-fetch';

console.log('🎯 اختبار سريع نهائي للوحة النادل');
console.log('=' .repeat(50));

const API_BASE = 'https://deshacoffee-production.up.railway.app';

async function quickFinalTest() {
    const results = {
        orderAPI: '❓',
        dataStructure: '❓',
        calculations: '❓',
        orderNumbers: '❓'
    };

    try {
        // 1. اختبار API الطلبات
        console.log('📡 اختبار API الطلبات...');
        const ordersResponse = await fetch(`${API_BASE}/api/orders`);
        if (ordersResponse.ok) {
            const orders = await ordersResponse.json();
            results.orderAPI = orders.length > 0 ? '✅' : '⚠️';
            console.log(`   ${results.orderAPI} تم جلب ${orders.length} طلب`);
            
            if (orders.length > 0) {
                const order = orders[0];
                
                // 2. اختبار بنية البيانات
                console.log('🔍 اختبار بنية البيانات...');
                const hasRequiredFields = order._id && 
                                        typeof order.totalPrice !== 'undefined' && 
                                        order.status;
                results.dataStructure = hasRequiredFields ? '✅' : '❌';
                console.log(`   ${results.dataStructure} الحقول المطلوبة: ${hasRequiredFields ? 'موجودة' : 'مفقودة'}`);
                
                // 3. اختبار دقة الحسابات
                console.log('🧮 اختبار دقة الحسابات...');
                const totalPrice = order.totalPrice || 0;
                const isValidNumber = typeof totalPrice === 'number' && !isNaN(totalPrice);
                results.calculations = isValidNumber ? '✅' : '❌';
                console.log(`   ${results.calculations} المبلغ: ${totalPrice} ج.م (${isValidNumber ? 'صحيح' : 'خاطئ'})`);
                
                // 4. اختبار أرقام الطلبات
                console.log('🔢 اختبار أرقام الطلبات...');
                const orderNumber = order.orderNumber || order._id?.slice(-6) || 'غير محدد';
                const hasOrderNumber = orderNumber !== 'غير محدد';
                results.orderNumbers = hasOrderNumber ? '✅' : '⚠️';
                console.log(`   ${results.orderNumbers} رقم الطلب: ${orderNumber}`);
                
                // عرض تفاصيل الطلب
                console.log('\n📋 تفاصيل الطلب الأخير:');
                console.log(`   🆔 المعرف: ${order._id}`);
                console.log(`   🔢 رقم الطلب: ${orderNumber}`);
                console.log(`   💰 المبلغ: ${totalPrice.toFixed(2)} ج.م`);
                console.log(`   📊 الحالة: ${order.status}`);
                console.log(`   🕐 التوقيت: ${new Date(order.createdAt).toLocaleString('ar-EG')}`);
            }
        } else {
            results.orderAPI = '❌';
            console.log(`   ❌ فشل في جلب الطلبات: ${ordersResponse.status}`);
        }
        
    } catch (error) {
        console.log(`❌ خطأ في الاختبار: ${error.message}`);
    }
    
    // النتيجة النهائية
    console.log('\n' + '=' .repeat(50));
    console.log('📊 نتائج الاختبار النهائي:');
    console.log(`   API الطلبات: ${results.orderAPI}`);
    console.log(`   بنية البيانات: ${results.dataStructure}`);
    console.log(`   دقة الحسابات: ${results.calculations}`);
    console.log(`   أرقام الطلبات: ${results.orderNumbers}`);
    
    const allPassed = Object.values(results).every(result => result === '✅');
    const mostPassed = Object.values(results).filter(result => result === '✅').length >= 3;
    
    console.log('\n🎯 التقييم النهائي:');
    if (allPassed) {
        console.log('🎉 ممتاز! جميع الاختبارات نجحت - النظام جاهز للإنتاج');
    } else if (mostPassed) {
        console.log('✅ جيد! معظم الاختبارات نجحت - النظام يعمل بشكل صحيح');
    } else {
        console.log('⚠️  يوجد مشاكل تحتاج مراجعة');
    }
    
    console.log('\n🔗 روابط مهمة:');
    console.log('   Frontend: https://coffee-ten-sandy.vercel.app/waiter');
    console.log('   Backend: https://deshacoffee-production.up.railway.app');
    console.log('\n✨ تم الانتهاء من جميع الإصلاحات بنجاح!');
}

quickFinalTest().catch(console.error);
