// مدير الحالة للوحة النادل
import { useState, useCallback } from 'react';
import type { 
  WaiterState,
  WaiterModalState,
  WaiterFilters,
  WaiterSettings,
  WaiterOrder,
  WaiterTableAccount,
  WaiterProduct,
  WaiterCategory,
  WaiterStats,
  WaiterNotification,
  NewOrderData,
  CartItem
} from '../../models/WaiterModels';

// الحالة الأولية للفلاتر
const initialFilters: WaiterFilters = {
  tableStatus: 'all',
  orderStatus: 'all',
  categoryFilter: null,
  searchTerm: ''
};

// الحالة الأولية للنوافذ المنبثقة
const initialModalState: WaiterModalState = {
  showOrderModal: false,
  showDiscountModal: false,
  showCloseTableModal: false,
  showProductModal: false,
  showNotificationsModal: false,
  selectedOrderForDiscount: null,
  selectedTableForClose: null,
  selectedProductForOrder: null
};

// الحالة الأولية للإحصائيات
const initialStats: WaiterStats = {
  todayOrders: 0,
  todaySales: 0,
  activeOrders: 0,
  completedOrders: 0,
  readyOrders: 0,
  openTables: 0,
  averageOrderValue: 0,
  myTables: 0
};

// الحالة الأولية للإعدادات
const initialSettings: WaiterSettings = {
  soundEnabled: true,
  autoRefresh: true,
  refreshInterval: 30000, // 30 ثانية
  showNotifications: true,
  language: 'ar'
};

// الحالة الأولية للطلب الجديد
const initialNewOrder: NewOrderData = {
  tableNumber: '',
  customerName: '',
  items: [],
  totalPrice: 0,
  notes: ''
};

export function useWaiterState() {
  // الحالات الأساسية
  const [currentView, setCurrentView] = useState<WaiterState['currentView']>('tables');
  const [loading, setLoading] = useState<boolean>(false);
  const [sidebarOpen, setSidebarOpen] = useState<boolean>(false);
  
  // حالات البيانات
  const [tableAccounts, setTableAccounts] = useState<WaiterTableAccount[]>([]);
  const [orders, setOrders] = useState<WaiterOrder[]>([]);
  const [products, setProducts] = useState<WaiterProduct[]>([]);
  const [categories, setCategories] = useState<WaiterCategory[]>([]);
  const [stats, setStats] = useState<WaiterStats>(initialStats);
  const [notifications, setNotifications] = useState<WaiterNotification[]>([]);
  
  // حالات التحديد
  const [selectedTable, setSelectedTable] = useState<WaiterTableAccount | null>(null);
  const [selectedOrder, setSelectedOrder] = useState<WaiterOrder | null>(null);

  // حالات الفلاتر والإعدادات
  const [filters, setFilters] = useState<WaiterFilters>(initialFilters);
  const [settings, setSettings] = useState<WaiterSettings>(initialSettings);
  
  // حالات النوافذ المنبثقة
  const [modalState, setModalState] = useState<WaiterModalState>(initialModalState);

  // حالات النماذج
  const [newOrder, setNewOrder] = useState<NewOrderData>(initialNewOrder);

  // دوال تحديث البيانات
  const updateTableAccounts = useCallback((newTables: WaiterTableAccount[]) => {
    setTableAccounts(newTables);
  }, []);

  const updateOrders = useCallback((newOrders: WaiterOrder[]) => {
    setOrders(newOrders);
  }, []);

  const updateProducts = useCallback((newProducts: WaiterProduct[]) => {
    setProducts(newProducts);
  }, []);

  const updateCategories = useCallback((newCategories: WaiterCategory[]) => {
    setCategories(newCategories);
  }, []);

  const updateStats = useCallback((newStats: WaiterStats) => {
    setStats(newStats);
  }, []);

  const updateNotifications = useCallback((newNotifications: WaiterNotification[]) => {
    setNotifications(newNotifications);
  }, []);

  // دوال الإشعارات
  const addNotification = useCallback((notification: Omit<WaiterNotification, '_id' | 'isRead' | 'createdAt'>) => {
    const newNotification: WaiterNotification = {
      ...notification,
      _id: Date.now().toString(),
      isRead: false,
      createdAt: new Date().toISOString()
    };
    setNotifications(prev => [newNotification, ...prev]);
  }, []);

  const markNotificationAsRead = useCallback((notificationId: string) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification._id === notificationId 
          ? { ...notification, isRead: true }
          : notification
      )
    );
  }, []);

  const clearAllNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  // دوال تحديث الفلاتر
  const updateFilter = useCallback(<K extends keyof WaiterFilters>(
    key: K, 
    value: WaiterFilters[K]
  ) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  }, []);

  const resetFilters = useCallback(() => {
    setFilters(initialFilters);
  }, []);

  // دوال تحديث الإعدادات
  const updateSetting = useCallback(<K extends keyof WaiterSettings>(
    key: K, 
    value: WaiterSettings[K]
  ) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  }, []);

  // دوال تحديث النوافذ المنبثقة
  const updateModal = useCallback(<K extends keyof WaiterModalState>(
    key: K, 
    value: WaiterModalState[K]
  ) => {
    setModalState(prev => ({
      ...prev,
      [key]: value
    }));
  }, []);

  const closeAllModals = useCallback(() => {
    setModalState(initialModalState);
  }, []);

  // دوال تحديث الطلب الجديد
  const updateNewOrder = useCallback(<K extends keyof NewOrderData>(
    key: K, 
    value: NewOrderData[K]
  ) => {
    setNewOrder(prev => ({
      ...prev,
      [key]: value
    }));
  }, []);

  const resetNewOrder = useCallback(() => {
    setNewOrder(initialNewOrder);
  }, []);
  // إضافة منتج للطلب الجديد
  const addItemToNewOrder = useCallback((product: WaiterProduct, quantity: number = 1, notes?: string) => {
    setNewOrder(prev => {
      const existingItemIndex = prev.items.findIndex(item => item.productId === product._id);
      
      if (existingItemIndex >= 0) {
        // إذا كان المنتج موجود، زيادة الكمية
        const updatedItems = [...prev.items];
        updatedItems[existingItemIndex] = {
          ...updatedItems[existingItemIndex],
          quantity: updatedItems[existingItemIndex].quantity + quantity,
          notes: notes || updatedItems[existingItemIndex].notes
        };
        return { ...prev, items: updatedItems };
      } else {
        // إضافة منتج جديد
        const newItem: CartItem = {
          _id: `temp-${Date.now()}`,
          productId: product._id,
          name: product.name,
          price: product.price,
          quantity,
          notes,
          category: product.categories[0] || ''
        };
        return { ...prev, items: [...prev.items, newItem] };
      }
    });
  }, []);

  // إزالة منتج من الطلب الجديد
  const removeItemFromNewOrder = useCallback((itemId: string) => {
    setNewOrder(prev => ({
      ...prev,
      items: prev.items.filter(item => item._id !== itemId)
    }));
  }, []);

  // تحديث كمية منتج في الطلب الجديد
  const updateItemQuantityInNewOrder = useCallback((itemId: string, quantity: number) => {
    if (quantity <= 0) {
      removeItemFromNewOrder(itemId);
      return;
    }

    setNewOrder(prev => ({
      ...prev,
      items: prev.items.map(item => 
        item._id === itemId ? { ...item, quantity } : item
      )
    }));
  }, [removeItemFromNewOrder]);

  // دوال مساعدة للفلترة
  const getFilteredTables = useCallback(() => {
    return tableAccounts.filter(table => {
      // فلتر حالة الطاولة
      if (filters.tableStatus !== 'all') {
        if (filters.tableStatus === 'open' && !table.isOpen) return false;
        if (filters.tableStatus === 'closed' && table.isOpen) return false;
      }

      // فلتر البحث
      if (filters.searchTerm) {
        const searchTerm = filters.searchTerm.toLowerCase();
        const tableMatch = table.tableNumber.toLowerCase().includes(searchTerm);
        const customerMatch = table.orders.some(order => 
          order.customerName?.toLowerCase().includes(searchTerm)
        );
        if (!tableMatch && !customerMatch) return false;
      }

      return true;
    });
  }, [tableAccounts, filters]);

  const getFilteredOrders = useCallback(() => {
    return orders.filter(order => {
      // فلتر حالة الطلب
      if (filters.orderStatus !== 'all' && order.status !== filters.orderStatus) {
        return false;
      }

      // فلتر البحث
      if (filters.searchTerm) {
        const searchTerm = filters.searchTerm.toLowerCase();
        const orderMatch = order.orderNumber.toLowerCase().includes(searchTerm);
        const tableMatch = order.tableNumber.toString().includes(searchTerm);
        const customerMatch = order.customerName?.toLowerCase().includes(searchTerm);
        if (!orderMatch && !tableMatch && !customerMatch) return false;
      }

      return true;
    });
  }, [orders, filters]);

  const getFilteredProducts = useCallback(() => {
    return products.filter(product => {
      // فلتر الفئة
      if (filters.categoryFilter && !product.categories.includes(filters.categoryFilter)) {
        return false;
      }

      // فلتر البحث
      if (filters.searchTerm) {
        const searchTerm = filters.searchTerm.toLowerCase();
        const nameMatch = product.name.toLowerCase().includes(searchTerm);
        const descriptionMatch = product.description?.toLowerCase().includes(searchTerm);
        if (!nameMatch && !descriptionMatch) return false;
      }

      return true;
    });
  }, [products, filters]);

  // حساب إجمالي الطلب الجديد
  const getNewOrderTotal = useCallback(() => {
    return newOrder.items.reduce((total, item) => total + (item.price * item.quantity), 0);
  }, [newOrder.items]);

  // الحصول على الإشعارات غير المقروءة
  const getUnreadNotifications = useCallback(() => {
    return notifications.filter(notification => !notification.isRead);
  }, [notifications]);

  return {
    // الحالات
    currentView,
    loading,
    sidebarOpen,
    tableAccounts,
    orders,
    products,
    categories,
    stats,
    notifications,
    selectedTable,
    selectedOrder,
    filters,
    settings,
    modalState,
    newOrder,

    // دوال التحديث الأساسية
    setCurrentView,
    setLoading,
    setSidebarOpen,
    setSelectedTable,
    setSelectedOrder,
    updateTableAccounts,
    updateOrders,
    updateProducts,
    updateCategories,
    updateStats,
    updateNotifications,

    // دوال الفلاتر
    updateFilter,
    resetFilters,
    getFilteredTables,
    getFilteredOrders,
    getFilteredProducts,

    // دوال الإعدادات
    updateSetting,

    // دوال النوافذ المنبثقة
    updateModal,
    closeAllModals,    // دوال الطلب الجديد
    updateNewOrder,
    resetNewOrder,
    addItemToNewOrder,
    removeItemFromNewOrder,
    updateItemQuantityInNewOrder,
    getNewOrderTotal,

    // دوال الإشعارات
    addNotification,
    markNotificationAsRead,
    clearAllNotifications,

    // دوال مساعدة
    getUnreadNotifications,

    // aliases for compatibility
    menuItems: products,
    updateMenuItems: updateProducts,
    newOrderData: newOrder,
    updateNewOrderData: updateNewOrder,
    resetNewOrderData: resetNewOrder
  };
}
