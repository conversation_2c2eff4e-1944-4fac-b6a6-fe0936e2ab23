# 🧪 خطة اختبار سير العمل العملي

## 📋 تعليمات الاختبار الشامل

### متطلبات قبل البدء
- ✅ الخوادم تعمل:
  - Backend: http://localhost:4003
  - Frontend: http://localhost:5176
- ✅ قاعدة البيانات متصلة
- ✅ Socket.IO نشط

### 🔧 إعداد الاختبار

#### الخطوة 1: تحضير حسابات المستخدمين
1. تأكد من وجود حسابات:
   - نادل 1 (waiter1)
   - نادل 2 (waiter2) 
   - طب<PERSON><PERSON> (chef)
   - مدير (manager)

#### الخطوة 2: فتح المتصفحات
1. متصفح 1: http://localhost:5176 (نادل 1)
2. متصفح 2: http://localhost:5176 (نادل 2)
3. متصفح 3: http://localhost:5176/chef-dashboard (طباخ)
4. متصفح 4: http://localhost:5176/manager-dashboard (مدير - اختياري)

---

## 🧪 الاختبارات

### اختبار 1: إنشاء طلب أساسي
**الهدف:** التحقق من إنشاء طلب مع رقم الطاولة واسم العميل

**الخطوات:**
1. **في متصفح النادل 1:**
   - تسجيل الدخول كنادل
   - الانتقال إلى شاشة القائمة (المشروبات)
   - إضافة عنصر أو أكثر للسلة
   - في السلة: إدخال رقم الطاولة (مثال: 5)
   - إدخال اسم العميل (مثال: "أحمد محمد")
   - الضغط على "إرسال الطلب"

**النتائج المتوقعة:**
- ✅ يجب ظهور رسالة نجاح
- ✅ يجب فتح حساب جديد للطاولة 5
- ✅ يجب الانتقال لشاشة الطلبات تلقائياً

2. **في متصفح الطباخ:**
   - ✅ يجب ظهور إشعار فوري بالطلب الجديد
   - ✅ يجب ظهور الطلب في قسم "انتظار"
   - ✅ يجب عرض رقم الطاولة واسم العميل

---

### اختبار 2: منع تعارض الطاولات
**الهدف:** التحقق من منع النوادل من فتح طاولة مفتوحة

**الخطوات:**
1. **في متصفح النادل 2:**
   - تسجيل الدخول كنادل مختلف
   - إضافة عناصر للسلة
   - محاولة إدخال نفس رقم الطاولة (5)
   - الضغط على "إرسال الطلب"

**النتائج المتوقعة:**
- ❌ يجب ظهور رسالة خطأ: "الطاولة رقم 5 مُدارة حاليًا بواسطة نادل آخر"
- ❌ يجب منع إرسال الطلب

---

### اختبار 3: سير عمل الطباخ
**الهدف:** التحقق من تقسيم الطلبات وتحديث الحالات

**الخطوات:**
1. **في متصفح الطباخ:**
   - التحقق من وجود الطلب في قسم "انتظار"
   - الضغط على "بدء التحضير" للطلب
   
**النتائج المتوقعة:**
- ✅ يجب انتقال الطلب إلى قسم "قيد التحضير"
- ✅ يجب إرسال إشعار للنادل: "بدأ تحضير الطلب من الطاولة رقم 5 للعميل أحمد محمد"

2. **استكمال التحضير:**
   - في قسم "قيد التحضير": الضغط على "الطلب جاهز"
   
**النتائج المتوقعة:**
- ✅ يجب انتقال الطلب إلى قسم "مكتملة"
- ✅ يجب إرسال إشعار للنادل: "الطلب جاهز للتقديم من الطاولة رقم 5 للعميل أحمد محمد"

---

### اختبار 4: تسليم الطلب
**الهدف:** التحقق من إتمام دورة الطلب

**الخطوات:**
1. **في متصفح النادل 1:**
   - الانتقال إلى شاشة "الطلبات"
   - البحث عن الطلب (حالة: ready)
   - الضغط على "تم التسليم"

**النتائج المتوقعة:**
- ✅ يجب تحديث حالة الطلب إلى "delivered"
- ✅ يجب إرسال إشعار للطباخ: "تم تسليم الطلب من الطاولة رقم 5 للعميل أحمد محمد"

---

### اختبار 5: اختبار طلب بدون رقم طاولة
**الهدف:** التحقق من رفض الطلبات بدون رقم طاولة

**الخطوات:**
1. **في متصفح النادل:**
   - إضافة عناصر للسلة
   - ترك حقل رقم الطاولة فارغاً
   - الضغط على "إرسال الطلب"

**النتائج المتوقعة:**
- ❌ يجب ظهور رسالة خطأ: "يرجى إضافة عناصر للطلب وتحديد رقم الطاولة"

---

### اختبار 6: اختبار الإشعارات المباشرة
**الهدف:** التحقق من Socket.IO والإشعارات الفورية

**الخطوات:**
1. **ترتيب النوافذ:** رتب المتصفحات بحيث ترى النادل والطباخ معاً
2. **إنشاء طلب جديد** من النادل برقم طاولة مختلف (مثال: 7)
3. **مراقبة الطباخ:** يجب ظهور الإشعار فوراً
4. **تحديث الحالة** من الطباخ
5. **مراقبة النادل:** يجب ظهور الإشعار فوراً

**النتائج المتوقعة:**
- ✅ جميع الإشعارات تظهر خلال ثوانٍ
- ✅ المعلومات صحيحة (رقم الطاولة، اسم العميل)

---

## 📊 تقرير النتائج

### قائمة التحقق السريع
- [ ] الطلب برقم الطاولة واسم العميل
- [ ] منع تعارض الطاولات  
- [ ] تقسيم لوحة الطباخ (انتظار، تحضير، مكتملة)
- [ ] إشعارات Socket.IO فورية
- [ ] رسائل الإشعارات تحتوي معلومات الطاولة والعميل
- [ ] رفض الطلبات بدون رقم طاولة

### في حالة فشل أي اختبار
1. تحقق من وحدة التحكم (Console) للأخطاء
2. تحقق من حالة الاتصال بـ Socket.IO
3. تحقق من استجابة API في علامة التبويب Network
4. راجع سجلات الخادم الخلفي

---

## ✅ الخلاصة
إذا نجحت جميع الاختبارات، فإن سير العمل محقق بالكامل وفقاً للمتطلبات المحددة.

**النظام المختبر يشمل:**
- ✅ إنشاء طلبات مع معلومات الطاولة والعميل
- ✅ حماية الطاولات من التعارض
- ✅ لوحة طباخ مقسمة وفعالة
- ✅ نظام إشعارات شامل ومباشر
- ✅ دورة حياة طلب كاملة ومتكاملة
