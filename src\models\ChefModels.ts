// نماذج البيانات للوحة الطباخ

export interface ChefOrder {
  _id: string;
  orderNumber: string;
  items: ChefOrderItem[];
  totalAmount: number;
  status: 'pending' | 'preparing' | 'ready' | 'completed' | 'delivered';
  tableNumber: number;
  customerName?: string;
  waiterName: string;
  waiterId?: string;
  chefName?: string;
  chefId?: string;
  notes?: string;
  preparationTime?: number;
  createdAt: string;
  updatedAt: string;
  priority?: 'low' | 'normal' | 'high' | 'urgent';
}

export interface ChefOrderItem {
  _id: string;
  productId: string;
  name: string;
  price: number;
  quantity: number;
  notes?: string;
  category?: string;
  preparationTime?: number;
  status?: 'pending' | 'preparing' | 'ready';
}

export interface ChefStats {
  todayOrders: number;
  completedOrders: number;
  pendingOrders: number;
  preparingOrders: number;
  readyOrders: number;
  averagePreparationTime: number;
  totalPreparationTime: number;
  ordersPerHour: number;
}

export interface ChefNotification {
  _id: string;
  type: 'new_order' | 'order_updated' | 'priority_order' | 'general';
  title: string;
  message: string;
  orderId?: string;
  orderNumber?: string;
  tableNumber?: number;
  waiterName?: string;
  priority?: 'low' | 'normal' | 'high' | 'urgent';
  isRead: boolean;
  createdAt: string;
}

// واجهة الحالة العامة للطباخ
export interface ChefState {
  currentView: 'orders' | 'kitchen' | 'stats' | 'profile';
  loading: boolean;
  sidebarOpen: boolean;
  orders: ChefOrder[];
  stats: ChefStats;
  notifications: ChefNotification[];
  selectedOrder: ChefOrder | null;
  currentFilter: ChefOrderFilter;
}

// واجهة حالة النوافذ المنبثقة للطباخ
export interface ChefModalState {
  showOrderModal: boolean;
  showStatsModal: boolean;
  showNotificationsModal: boolean;
  showPreparationTimer: boolean;
  selectedOrderForModal: ChefOrder | null;
}

// واجهة فلاتر الطباخ
export interface ChefOrderFilter {
  status: 'all' | 'pending' | 'preparing' | 'ready';
  priority: 'all' | 'low' | 'normal' | 'high' | 'urgent';
  timeRange: 'all' | 'today' | 'last_hour' | 'last_3_hours';
  searchTerm: string;
}

// واجهة إعدادات الطباخ
export interface ChefSettings {
  soundEnabled: boolean;
  autoRefresh: boolean;
  refreshInterval: number;
  showNotifications: boolean;
  defaultPreparationTime: number;
  priorityAlerts: boolean;
  language: 'ar' | 'en';
}

// واجهة بيانات تحديث الطلب
export interface OrderUpdateData {
  orderId: string;
  status: ChefOrder['status'];
  chefName?: string;
  chefId?: string;
  preparationTime?: number;
  notes?: string;
}

// واجهة إحصائيات مفصلة
export interface DetailedChefStats {
  basic: ChefStats;
  hourlyStats: {
    hour: number;
    ordersCount: number;
    averageTime: number;
  }[];
  categoryStats: {
    category: string;
    ordersCount: number;
    averageTime: number;
  }[];
  performanceMetrics: {
    efficiency: number; // نسبة الطلبات المكتملة في الوقت المحدد
    speed: number; // متوسط سرعة التحضير مقارنة بالمعيار
    quality: number; // تقييم الجودة (إذا توفر)
  };
}

// واجهة مؤقت التحضير
export interface PreparationTimer {
  orderId: string;
  orderNumber: string;
  startTime: Date;
  estimatedTime: number; // بالدقائق
  actualTime?: number; // بالدقائق
  status: 'running' | 'paused' | 'completed' | 'cancelled';
}

// واجهة إعدادات المطبخ
export interface KitchenSettings {
  workstations: {
    id: string;
    name: string;
    type: 'grill' | 'fryer' | 'oven' | 'cold' | 'drinks';
    capacity: number;
    currentLoad: number;
    orders: string[]; // order IDs
  }[];
  preparationTimes: {
    [category: string]: number; // minutes
  };
  priorityRules: {
    urgentOrderThreshold: number; // minutes
    vipCustomers: string[];
    rushHours: {
      start: string;
      end: string;
    }[];
  };
}
