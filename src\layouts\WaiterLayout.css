.sidebar-link {
  padding: 0.75rem 1rem;
  border-radius: 8px;
  text-decoration: none;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.sidebar-link:hover {
  background-color: var(--primary-light);
  transform: translateX(-5px);
}

.sidebar-link.active {
  background-color: var(--primary-light);
}

.logout-button {
  margin-top: auto;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  border: none;
  background-color: var(--error);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.logout-button:hover {
  background-color: var(--error-dark);
  transform: translateX(-5px);
}

.logo-container img {
  width: 100px;
  margin-bottom: 1rem;
  transition: transform 0.3s ease;
}

.logo-container img:hover {
  transform: scale(1.05);
}

.logo-container h3 {
  color: var(--primary);
  margin-bottom: 1rem;
}

.sidebar {
  width: 250px;
  background: var(--surface);
  box-shadow: var(--shadow-md);
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
