const express = require('express');
const jwt = require('jsonwebtoken');
const { body, validationResult } = require('express-validator');
const User = require('../models/User');
const config = require('../config/environment');
const router = express.Router();

// Login endpoint
router.post('/login', [
  body('username').notEmpty().withMessage('اسم المستخدم مطلوب'),
  body('password').isLength({ min: 6 }).withMessage('كلمة المرور يجب أن تكون 6 أحرف على الأقل')
], async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array()
      });
    }

    const { username, password } = req.body;

    // Authenticate user using MongoDB Atlas only
    const user = await User.getAuthenticated(username, password);
    console.log(`✅ Authentication successful via MongoDB Atlas for user: ${username}`);

    // Generate JWT token
    const token = jwt.sign(
      {
        userId: user._id,
        username: user.username,
        role: user.role
      },
      config.jwt.secret,
      { expiresIn: config.jwt.expiresIn }
    );

    // Send Socket notifications for successful login
    if (global.socketHandlers) {
      try {
        // Notify managers about user login
        global.socketHandlers.sendRoleNotification('manager', 
          `قام ${user.name || user.username} بتسجيل الدخول`, {
          type: 'user-login',
          userId: user._id,
          userName: user.name || user.username,
          userRole: user.role,
          timestamp: new Date().toISOString()
        });

        console.log(`👤 تم إرسال إشعار تسجيل الدخول للمستخدم: ${user.name || user.username}`);
      } catch (socketError) {
        console.error('خطأ في إرسال إشعار Socket:', socketError);
      }
    }

    res.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      user: {
        id: user._id,
        username: user.username,
        name: user.name,
        email: user.email,
        role: user.role,
        status: user.status
      },
      token
    });

  } catch (error) {
    console.error('Login error:', error);

    // تحديد نوع الخطأ وإرسال رسالة مناسبة
    let errorMessage = 'خطأ في تسجيل الدخول';
    let statusCode = 401;

    if (error.message.includes('المستخدم غير موجود')) {
      errorMessage = 'اسم المستخدم أو كلمة المرور غير صحيحة';
    } else if (error.message.includes('كلمة المرور غير صحيحة')) {
      errorMessage = 'اسم المستخدم أو كلمة المرور غير صحيحة';
    } else if (error.message.includes('الحساب مقفل')) {
      errorMessage = 'الحساب مقفل مؤقتاً بسبب محاولات دخول خاطئة متكررة';
    } else if (error.message.includes('غير نشط')) {
      errorMessage = 'الحساب غير نشط، يرجى التواصل مع الإدارة';
    } else if (error.name === 'ValidationError') {
      errorMessage = 'بيانات غير صحيحة';
      statusCode = 400;
    } else {
      errorMessage = error.message || 'خطأ في الخادم';
      statusCode = 500;
    }

    res.status(statusCode).json({
      success: false,
      message: errorMessage,
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Get current user
router.get('/me', require('../middleware/auth').authenticateToken, (req, res) => {
  res.json({
    success: true,
    user: req.user
  });
});

// Logout endpoint
router.post('/logout', require('../middleware/auth').authenticateToken, (req, res) => {
  try {
    // Send Socket notifications for logout
    if (global.socketHandlers && req.user) {
      try {
        // Notify managers about user logout
        global.socketHandlers.sendRoleNotification('manager', 
          `قام ${req.user.name || req.user.username} بتسجيل الخروج`, {
          type: 'user-logout',
          userId: req.user.userId,
          userName: req.user.name || req.user.username,
          userRole: req.user.role,
          timestamp: new Date().toISOString()
        });

        console.log(`👋 تم إرسال إشعار تسجيل الخروج للمستخدم: ${req.user.name || req.user.username}`);
      } catch (socketError) {
        console.error('خطأ في إرسال إشعار Socket:', socketError);
      }
    }

    res.json({
      success: true,
      message: 'تم تسجيل الخروج بنجاح'
    });
  } catch (error) {
    console.error('Logout error:', error);
    res.json({
      success: true,
      message: 'تم تسجيل الخروج بنجاح'
    });
  }
});

module.exports = router;
