// خدمة تشغيل الصوت للإشعارات
export class NotificationSoundService {
  private audio: HTMLAudioElement | null = null;
  private soundEnabled: boolean = true;

  constructor() {
    this.initializeSound();
  }

  private initializeSound() {
    try {
      // إنشاء عنصر الصوت
      this.audio = new Audio('/notification.wav');
      this.audio.volume = 0.7; // مستوى الصوت 70%
      this.audio.preload = 'auto';

      // إعداد الأحداث
      this.audio.addEventListener('canplaythrough', () => {
        console.log('✅ ملف الصوت جاهز للتشغيل');
      });

      this.audio.addEventListener('error', (e) => {
        console.error('❌ خطأ في تحميل ملف الصوت:', e);
        this.soundEnabled = false;
      });
    } catch (error) {
      console.error('❌ خطأ في إنشاء عنصر الصوت:', error);
      this.soundEnabled = false;
    }
  }

  // تشغيل الصوت
  public playNotification(): void {
    if (!this.soundEnabled || !this.audio) {
      console.log('⚠️ الصوت غير متاح');
      return;
    }

    try {
      // إعادة تعيين الوقت إلى البداية
      this.audio.currentTime = 0;
      
      // تشغيل الصوت
      const playPromise = this.audio.play();
      
      if (playPromise !== undefined) {
        playPromise
          .then(() => {
            console.log('🔊 تم تشغيل صوت الإشعار');
          })
          .catch((error) => {
            console.error('❌ خطأ في تشغيل الصوت:', error);
            // في حالة فشل التشغيل التلقائي، يمكن إظهار رسالة للمستخدم
            this.requestUserInteraction();
          });
      }
    } catch (error) {
      console.error('❌ خطأ في تشغيل الصوت:', error);
    }
  }

  // طلب تفاعل المستخدم لتمكين الصوت
  private requestUserInteraction(): void {
    console.log('⚠️ يتطلب تفاعل المستخدم لتشغيل الصوت');
    // يمكن إضافة منطق هنا لإظهار رسالة للمستخدم
  }

  // تمكين/تعطيل الصوت
  public setSoundEnabled(enabled: boolean): void {
    this.soundEnabled = enabled;
    console.log(`🔊 الصوت ${enabled ? 'مُفعل' : 'مُعطل'}`);
  }

  // التحقق من حالة الصوت
  public isSoundEnabled(): boolean {
    return this.soundEnabled;
  }

  // تعديل مستوى الصوت
  public setVolume(volume: number): void {
    if (this.audio && volume >= 0 && volume <= 1) {
      this.audio.volume = volume;
      console.log(`🔊 مستوى الصوت: ${Math.round(volume * 100)}%`);
    }
  }

  // تنظيف الموارد
  public cleanup(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.removeEventListener('canplaythrough', () => {});
      this.audio.removeEventListener('error', () => {});
    }
  }
}

// إنشاء مثيل واحد للخدمة
export const notificationSound = new NotificationSoundService();
