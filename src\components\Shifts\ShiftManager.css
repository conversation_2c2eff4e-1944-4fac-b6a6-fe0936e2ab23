/* Shift Manager Styles */
.shift-manager {
  padding: 1.5rem;
  max-width: 1400px;
  margin: 0 auto;
  background: var(--light-bg, #f8f9fa);
  min-height: 100vh;
}

/* Loading State */
.shift-manager.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  color: var(--primary-color, #2c3e50);
}

.loading-spinner i {
  font-size: 2rem;
  color: var(--primary-color, #2c3e50);
}

/* Header */
.shift-header {
  text-align: center;
  margin-bottom: 2rem;
}

.shift-header h1 {
  margin: 0 0 0.5rem 0;
  color: var(--primary-color, #2c3e50);
  font-size: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}

.shift-header p {
  color: var(--text-muted, #95a5a6);
  font-size: 1.1rem;
  margin: 0;
}

/* Section Headers */
.active-shifts h2,
.available-employees h2 {
  margin: 0 0 1.5rem 0;
  color: var(--primary-color, #2c3e50);
  font-size: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--border-color, #ecf0f1);
}

/* Active Shifts */
.active-shifts {
  margin-bottom: 3rem;
}

.shifts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

.shift-card {
  background: var(--white, #ffffff);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border-left: 4px solid var(--success-color, #27ae60);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.shift-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.employee-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.employee-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: var(--primary-color, #2c3e50);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white, #ffffff);
  font-size: 1.5rem;
}

.employee-details h3 {
  margin: 0 0 0.25rem 0;
  color: var(--primary-color, #2c3e50);
  font-size: 1.25rem;
}

.employee-details .role {
  background: var(--secondary-color, #f39c12);
  color: var(--white, #ffffff);
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.shift-info {
  margin-bottom: 1.5rem;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
  padding: 0.5rem;
  background: var(--light-bg, #f8f9fa);
  border-radius: 0.5rem;
}

.info-item i {
  color: var(--secondary-color, #f39c12);
  width: 1.25rem;
  text-align: center;
}

.info-item span {
  color: var(--text-color, #2c3e50);
  font-size: 0.9rem;
}

.end-shift-btn {
  width: 100%;
  background: var(--error-color, #e74c3c);
  color: var(--white, #ffffff);
  border: none;
  border-radius: 0.5rem;
  padding: 0.75rem 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.end-shift-btn:hover {
  background: #c0392b;
  transform: translateY(-1px);
}

/* Available Employees */
.available-employees {
  margin-bottom: 2rem;
}

.employees-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.employee-card {
  background: var(--white, #ffffff);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border-left: 4px solid var(--info-color, #3498db);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.employee-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.start-shift-btn {
  width: 100%;
  background: var(--success-color, #27ae60);
  color: var(--white, #ffffff);
  border: none;
  border-radius: 0.5rem;
  padding: 0.75rem 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 1rem;
}

.start-shift-btn:hover {
  background: #229954;
  transform: translateY(-1px);
}

/* No Active Shifts */
.no-active-shifts {
  text-align: center;
  padding: 3rem;
  color: var(--text-muted, #95a5a6);
}

.no-active-shifts i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.no-active-shifts p {
  font-size: 1.1rem;
  margin: 0;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-content {
  background: var(--white, #ffffff);
  border-radius: 1rem;
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color, #ecf0f1);
}

.modal-header h2 {
  margin: 0;
  color: var(--primary-color, #2c3e50);
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: var(--text-muted, #95a5a6);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: var(--light-bg, #f8f9fa);
  color: var(--text-color, #2c3e50);
}

.modal-body {
  padding: 1.5rem;
}

.modal-body p {
  margin: 0 0 1rem 0;
  color: var(--text-color, #2c3e50);
  font-size: 1rem;
}

.employee-preview {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: var(--light-bg, #f8f9fa);
  border-radius: 0.5rem;
  margin: 1rem 0;
}

.employee-preview i {
  font-size: 1.5rem;
  color: var(--primary-color, #2c3e50);
}

.employee-preview span {
  font-weight: 500;
  color: var(--text-color, #2c3e50);
}

.employee-preview .role {
  color: var(--text-muted, #95a5a6);
  font-weight: normal;
}

.shift-effects,
.shift-summary {
  margin: 1.5rem 0;
}

.shift-effects h3,
.shift-summary h3 {
  margin: 0 0 1rem 0;
  color: var(--primary-color, #2c3e50);
  font-size: 1rem;
}

.shift-effects ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.shift-effects li {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0;
  color: var(--text-color, #2c3e50);
}

.shift-effects li i {
  color: var(--success-color, #27ae60);
  font-size: 0.875rem;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--border-color, #ecf0f1);
}

.summary-item:last-child {
  border-bottom: none;
}

.summary-item span:first-child {
  color: var(--text-muted, #95a5a6);
}

.summary-item span:last-child {
  font-weight: 500;
  color: var(--text-color, #2c3e50);
}

.modal-footer {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid var(--border-color, #ecf0f1);
}

.btn-confirm {
  flex: 1;
  background: var(--success-color, #27ae60);
  color: var(--white, #ffffff);
  border: none;
  border-radius: 0.5rem;
  padding: 0.75rem 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-confirm:hover {
  background: #229954;
}

.btn-confirm.end {
  background: var(--error-color, #e74c3c);
}

.btn-confirm.end:hover {
  background: #c0392b;
}

.btn-cancel {
  flex: 1;
  background: var(--border-color, #ecf0f1);
  color: var(--text-color, #2c3e50);
  border: none;
  border-radius: 0.5rem;
  padding: 0.75rem 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-cancel:hover {
  background: #bdc3c7;
}
