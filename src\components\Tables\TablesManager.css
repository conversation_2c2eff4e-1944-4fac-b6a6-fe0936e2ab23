/* Tables Manager Styles */
.tables-manager {
  padding: 1.5rem;
  max-width: 1400px;
  margin: 0 auto;
  background: var(--light-bg, #f8f9fa);
  min-height: 100vh;
}

/* Loading State */
.tables-manager.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  color: var(--primary-color, #2c3e50);
}

.loading-spinner i {
  font-size: 2rem;
  color: var(--primary-color, #2c3e50);
}

/* Header */
.tables-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  gap: 2rem;
}

.header-left h1 {
  margin: 0 0 0.5rem 0;
  color: var(--primary-color, #2c3e50);
  font-size: 2rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.header-left p {
  color: var(--text-muted, #95a5a6);
  font-size: 1.1rem;
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.btn-new-table {
  background: var(--success-color, #27ae60);
  color: var(--white, #ffffff);
  border: none;
  border-radius: 0.5rem;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-new-table:hover {
  background: #229954;
  transform: translateY(-1px);
}

.view-controls {
  display: flex;
  border: 1px solid var(--border-color, #ecf0f1);
  border-radius: 0.5rem;
  overflow: hidden;
}

.view-btn {
  background: var(--white, #ffffff);
  border: none;
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--text-muted, #95a5a6);
}

.view-btn:hover {
  background: var(--light-bg, #f8f9fa);
  color: var(--primary-color, #2c3e50);
}

.view-btn.active {
  background: var(--primary-color, #2c3e50);
  color: var(--white, #ffffff);
}

/* Filters */
.tables-filters {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
  padding: 1rem;
  background: var(--white, #ffffff);
  border-radius: 0.75rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-group label {
  font-weight: 500;
  color: var(--text-color, #2c3e50);
  white-space: nowrap;
}

.filter-group select {
  padding: 0.5rem 1rem;
  border: 1px solid var(--border-color, #ecf0f1);
  border-radius: 0.375rem;
  background: var(--white, #ffffff);
  color: var(--text-color, #2c3e50);
  font-size: 0.9rem;
  cursor: pointer;
}

.refresh-btn {
  background: var(--info-color, #3498db);
  color: var(--white, #ffffff);
  border: none;
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-right: auto;
}

.refresh-btn:hover {
  background: #2980b9;
}

/* Tables Display */
.tables-display {
  margin-bottom: 2rem;
}

.tables-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1.5rem;
}

.tables-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.table-card {
  background: var(--white, #ffffff);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  cursor: pointer;
  position: relative;
  border-left: 4px solid transparent;
}

.table-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.table-card.available {
  border-left-color: #27ae60;
}

.table-card.occupied {
  border-left-color: #e74c3c;
}

.table-card.reserved {
  border-left-color: #f39c12;
}

.table-card.maintenance {
  border-left-color: #95a5a6;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.table-number {
  font-size: 1.25rem;
  font-weight: bold;
  color: var(--primary-color, #2c3e50);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.table-status {
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  color: var(--white, #ffffff);
  font-size: 0.75rem;
  font-weight: 500;
}

.table-info {
  margin-bottom: 1rem;
}

.info-row {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  color: var(--text-color, #2c3e50);
  font-size: 0.9rem;
}

.info-row i {
  color: var(--secondary-color, #f39c12);
  width: 1rem;
  text-align: center;
}

.table-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: var(--light-bg, #f8f9fa);
  border-radius: 0.5rem;
  margin-bottom: 1rem;
}

.orders-count,
.table-total {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: var(--text-color, #2c3e50);
}

.orders-count i,
.table-total i {
  color: var(--secondary-color, #f39c12);
}

.close-request-alert {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: rgba(231, 76, 60, 0.1);
  color: var(--error-color, #e74c3c);
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 1rem;
}

.table-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.action-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.375rem;
}

.action-btn.close-request {
  background: var(--warning-color, #f39c12);
  color: var(--white, #ffffff);
}

.action-btn.close-request:hover {
  background: #e67e22;
}

.action-btn.approve {
  background: var(--success-color, #27ae60);
  color: var(--white, #ffffff);
}

.action-btn.approve:hover {
  background: #229954;
}

.action-btn.reject {
  background: var(--error-color, #e74c3c);
  color: var(--white, #ffffff);
}

.action-btn.reject:hover {
  background: #c0392b;
}

.manager-actions {
  display: flex;
  gap: 0.5rem;
}

/* No Tables */
.no-tables {
  text-align: center;
  padding: 4rem 2rem;
  color: var(--text-muted, #95a5a6);
}

.no-tables i {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.no-tables p {
  font-size: 1.1rem;
  margin: 0;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-content {
  background: var(--white, #ffffff);
  border-radius: 1rem;
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.table-details-modal {
  max-width: 700px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color, #ecf0f1);
}

.modal-header h2 {
  margin: 0;
  color: var(--primary-color, #2c3e50);
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: var(--text-muted, #95a5a6);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: var(--light-bg, #f8f9fa);
  color: var(--text-color, #2c3e50);
}

.modal-body {
  padding: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-color, #2c3e50);
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color, #ecf0f1);
  border-radius: 0.5rem;
  font-family: inherit;
  font-size: 0.9rem;
  transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color, #2c3e50);
}

.table-summary-modal {
  background: var(--light-bg, #f8f9fa);
  padding: 1rem;
  border-radius: 0.5rem;
  margin-bottom: 1.5rem;
}

.table-summary-modal h3 {
  margin: 0 0 1rem 0;
  color: var(--primary-color, #2c3e50);
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--border-color, #ecf0f1);
}

.summary-item:last-child {
  border-bottom: none;
}

.summary-item span:first-child {
  color: var(--text-muted, #95a5a6);
}

.summary-item span:last-child {
  font-weight: 500;
  color: var(--text-color, #2c3e50);
}

.modal-footer {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid var(--border-color, #ecf0f1);
}

.btn-confirm {
  flex: 1;
  background: var(--success-color, #27ae60);
  color: var(--white, #ffffff);
  border: none;
  border-radius: 0.5rem;
  padding: 0.75rem 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-confirm:hover {
  background: #229954;
}

.btn-confirm.close {
  background: var(--warning-color, #f39c12);
}

.btn-confirm.close:hover {
  background: #e67e22;
}

.btn-confirm:disabled {
  background: var(--text-muted, #95a5a6);
  cursor: not-allowed;
}

.btn-cancel {
  flex: 1;
  background: var(--border-color, #ecf0f1);
  color: var(--text-color, #2c3e50);
  border: none;
  border-radius: 0.5rem;
  padding: 0.75rem 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-cancel:hover {
  background: #bdc3c7;
}

.btn-close {
  background: var(--border-color, #ecf0f1);
  color: var(--text-color, #2c3e50);
  border: none;
  border-radius: 0.5rem;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-close:hover {
  background: #bdc3c7;
}
