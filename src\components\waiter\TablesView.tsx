// مكون شاشة الطاولات للوحة النادل
import React from 'react';
import type { WaiterTableAccount, WaiterFilters } from '../../models/WaiterModels';

interface TablesViewProps {
  tables: WaiterTableAccount[];
  filters: WaiterFilters;
  onFilterChange: (key: keyof WaiterFilters, value: any) => void;
  onTableSelect: (table: WaiterTableAccount) => void;
  onNewOrder: (tableNumber: string) => void;
  onCloseTable: (table: WaiterTableAccount) => void;
  onClearClosedTables: () => void;
  onRefresh: () => void;
}

export function TablesView({
  tables,
  filters,
  onFilterChange,
  onTableSelect,
  onNewOrder,
  onCloseTable,
  onClearClosedTables,
  onRefresh
}: TablesViewProps) {

  // إحصائيات الطاولات
  const openTables = tables.filter(table => table.isOpen);
  const closedTables = tables.filter(table => !table.isOpen);
  const totalRevenue = openTables.reduce((sum, table) => sum + table.totalAmount, 0);

  // الطاولات المفلترة
  const filteredTables = tables.filter(table => {
    // فلتر حالة الطاولة
    if (filters.tableStatus !== 'all') {
      if (filters.tableStatus === 'open' && !table.isOpen) return false;
      if (filters.tableStatus === 'closed' && table.isOpen) return false;
    }

    // فلتر البحث
    if (filters.searchTerm) {
      const searchTerm = filters.searchTerm.toLowerCase();
      const tableMatch = table.tableNumber.toLowerCase().includes(searchTerm);
      const customerMatch = table.orders.some(order => 
        order.customerName?.toLowerCase().includes(searchTerm)
      );
      if (!tableMatch && !customerMatch) return false;
    }

    return true;
  });

  // ترتيب الطاولات: المفتوحة أولاً ثم حسب آخر نشاط
  const sortedTables = [...filteredTables].sort((a, b) => {
    if (a.isOpen !== b.isOpen) {
      return a.isOpen ? -1 : 1; // المفتوحة أولاً
    }
    
    // ترتيب حسب آخر طلب
    const aLastOrder = a.orders[a.orders.length - 1];
    const bLastOrder = b.orders[b.orders.length - 1];
    
    if (!aLastOrder && !bLastOrder) return 0;
    if (!aLastOrder) return 1;
    if (!bLastOrder) return -1;
    
    return new Date(bLastOrder.createdAt).getTime() - new Date(aLastOrder.createdAt).getTime();
  });

  return (
    <div className="tables-view">
      <div className="tables-header">
        <h1>
          <i className="fas fa-table"></i>
          طاولاتي
        </h1>
        <div className="header-actions">
          <button
            className="refresh-btn"
            onClick={onRefresh}
            title="تحديث قائمة الطاولات"
          >
            <i className="fas fa-sync-alt"></i>
            تحديث
          </button>
          {closedTables.length > 0 && (
            <button
              className="clear-closed-btn"
              onClick={onClearClosedTables}
              title="مسح الطاولات المغلقة"
            >
              <i className="fas fa-trash"></i>
              مسح المغلقة ({closedTables.length})
            </button>
          )}
        </div>
      </div>

      {/* إحصائيات سريعة */}
      <div className="tables-stats">
        <div className="stat-card open-tables">
          <div className="stat-icon">
            <i className="fas fa-table"></i>
          </div>
          <div className="stat-content">
            <div className="stat-number">{openTables.length}</div>
            <div className="stat-label">طاولات مفتوحة</div>
          </div>
        </div>

        <div className="stat-card total-revenue">
          <div className="stat-icon">
            <i className="fas fa-money-bill-wave"></i>
          </div>
          <div className="stat-content">
            <div className="stat-number">{totalRevenue.toFixed(2)}</div>
            <div className="stat-label">إجمالي الإيرادات (ج.م)</div>
          </div>
        </div>

        <div className="stat-card closed-tables">
          <div className="stat-icon">
            <i className="fas fa-check-circle"></i>
          </div>
          <div className="stat-content">
            <div className="stat-number">{closedTables.length}</div>
            <div className="stat-label">طاولات مغلقة</div>
          </div>
        </div>

        <div className="stat-card total-tables">
          <div className="stat-icon">
            <i className="fas fa-list"></i>
          </div>
          <div className="stat-content">
            <div className="stat-number">{tables.length}</div>
            <div className="stat-label">إجمالي الطاولات</div>
          </div>
        </div>
      </div>

      {/* فلاتر الطاولات */}
      <div className="tables-filters">
        <div className="filter-group">
          <label>حالة الطاولة:</label>
          <select 
            value={filters.tableStatus} 
            onChange={(e) => onFilterChange('tableStatus', e.target.value)}
            className="status-filter"
          >
            <option value="all">جميع الطاولات</option>
            <option value="open">مفتوحة فقط</option>
            <option value="closed">مغلقة فقط</option>
          </select>
        </div>

        <div className="filter-group">
          <label>البحث:</label>
          <div className="search-input-group">
            <i className="fas fa-search"></i>
            <input
              type="text"
              placeholder="البحث في الطاولات أو العملاء..."
              value={filters.searchTerm}
              onChange={(e) => onFilterChange('searchTerm', e.target.value)}
              className="search-input"
            />
            {filters.searchTerm && (
              <button 
                className="clear-search"
                onClick={() => onFilterChange('searchTerm', '')}
                title="مسح البحث"
              >
                <i className="fas fa-times"></i>
              </button>
            )}
          </div>
        </div>
      </div>

      {/* نتائج الفلترة */}
      {filteredTables.length !== tables.length && (
        <div className="filter-results">
          <span>عرض {filteredTables.length} من {tables.length} طاولة</span>
          <button 
            className="clear-filters"
            onClick={() => {
              onFilterChange('tableStatus', 'all');
              onFilterChange('searchTerm', '');
            }}
          >
            <i className="fas fa-times"></i>
            إلغاء الفلاتر
          </button>
        </div>
      )}

      {/* قائمة الطاولات */}
      <div className="tables-grid">
        {sortedTables.length === 0 ? (
          <div className="empty-state">
            <i className="fas fa-table"></i>
            <h3>لا توجد طاولات</h3>
            <p>
              {tables.length === 0 
                ? 'لم يتم تعيين أي طاولة لك بعد'
                : 'لا توجد طاولات تطابق معايير البحث المحددة'
              }
            </p>
            {tables.length === 0 && (
              <button 
                className="new-order-btn"
                onClick={() => onNewOrder('')}
              >
                <i className="fas fa-plus"></i>
                إنشاء طلب جديد
              </button>
            )}
          </div>
        ) : (
          sortedTables.map(table => (
            <div 
              key={table._id} 
              className={`table-card ${table.isOpen ? 'open' : 'closed'}`}
              onClick={() => onTableSelect(table)}
            >
              {/* رأس الطاولة */}
              <div className="table-header">
                <div className="table-number">
                  <i className="fas fa-table"></i>
                  طاولة {table.tableNumber}
                </div>
                <div className={`table-status ${table.status}`}>
                  <i className={`fas ${table.isOpen ? 'fa-unlock' : 'fa-lock'}`}></i>
                  {table.isOpen ? 'مفتوحة' : 'مغلقة'}
                </div>
              </div>

              {/* إجمالي المبلغ */}
              <div className="table-total">
                <div className="total-amount">
                  <i className="fas fa-money-bill-wave"></i>
                  {table.totalAmount.toFixed(2)} ج.م
                </div>
              </div>

              {/* معلومات الطلبات */}
              <div className="table-orders-info">
                <div className="orders-count">
                  <i className="fas fa-shopping-cart"></i>
                  {table.orders.length} طلب
                </div>
                <div className="table-time">
                  <i className="fas fa-clock"></i>
                  {new Date(table.createdAt).toLocaleString('ar-SA', {
                    day: '2-digit',
                    month: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </div>
              </div>

              {/* ملخص حالات الطلبات */}
              {table.orders.length > 0 && (
                <div className="orders-status-summary">
                  {['pending', 'preparing', 'ready', 'completed'].map(status => {
                    const count = table.orders.filter(order => order.status === status).length;
                    if (count === 0) return null;
                    
                    return (
                      <div key={status} className={`status-badge ${status}`}>
                        <span className="count">{count}</span>
                        <span className="status-text">
                          {status === 'pending' && 'انتظار'}
                          {status === 'preparing' && 'تحضير'}
                          {status === 'ready' && 'جاهز'}
                          {status === 'completed' && 'مكتمل'}
                        </span>
                      </div>
                    );
                  })}
                </div>
              )}

              {/* آخر طلب */}
              {table.orders.length > 0 && (
                <div className="last-order-info">
                  <div className="last-order-label">آخر طلب:</div>
                  <div className="last-order-details">
                    <span className="order-number">#{table.orders[table.orders.length - 1].orderNumber}</span>
                    <span className="order-time">
                      {new Date(table.orders[table.orders.length - 1].createdAt).toLocaleTimeString('ar-SA', {
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </span>
                  </div>
                </div>
              )}

              {/* أزرار العمليات */}
              <div className="table-actions">
                {table.isOpen ? (
                  <>
                    <button
                      className="new-order-btn"
                      onClick={(e) => {
                        e.stopPropagation();
                        onNewOrder(table.tableNumber);
                      }}
                      title="طلب جديد"
                    >
                      <i className="fas fa-plus"></i>
                      طلب جديد
                    </button>
                    
                    {table.orders.every(order => 
                      order.status === 'completed' || order.status === 'delivered'
                    ) && (
                      <button
                        className="close-table-btn"
                        onClick={(e) => {
                          e.stopPropagation();
                          onCloseTable(table);
                        }}
                        title="إغلاق الطاولة"
                      >
                        <i className="fas fa-lock"></i>
                        إغلاق
                      </button>
                    )}
                  </>
                ) : (
                  <div className="closed-table-info">
                    <i className="fas fa-check-double"></i>
                    تم الإغلاق
                  </div>
                )}
              </div>
            </div>
          ))
        )}
      </div>

      {/* زر الطلب الجديد العائم */}
      <button 
        className="floating-new-order-btn"
        onClick={() => onNewOrder('')}
        title="إنشاء طلب جديد"
      >
        <i className="fas fa-plus"></i>
      </button>
    </div>
  );
}
