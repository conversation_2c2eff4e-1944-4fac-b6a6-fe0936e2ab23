const io = require('socket.io-client');
const axios = require('axios');

// Test configuration
const BACKEND_URL = 'http://localhost:4003';
const socket = io(BACKEND_URL);

console.log('🚀 Starting Notification System Test...');
console.log(`Backend URL: ${BACKEND_URL}`);

// Test data
const testOrder = {
  items: [
    {
      name: 'قهوة عربية',
      quantity: 2,
      price: 15,
      category: 'المشروبات الساخنة'
    }
  ],
  tableNumber: 5,
  customerName: 'أحمد محمد',
  totalAmount: 30,
  notes: 'بدون سكر'
};

// Socket event listeners
socket.on('connect', () => {
  console.log('✅ Socket connected successfully');
  runTests();
});

socket.on('disconnect', () => {
  console.log('❌ Socket disconnected');
});

// Listen for notification events
socket.on('orderCreated', (data) => {
  console.log('📢 Order Created Notification:', data);
  checkNotificationFormat(data);
});

socket.on('orderStatusChanged', (data) => {
  console.log('📢 Order Status Changed Notification:', data);
  checkNotificationFormat(data);
});

socket.on('orderUpdate', (data) => {
  console.log('📢 Order Update Notification:', data);
  checkNotificationFormat(data);
});

function checkNotificationFormat(data) {
  console.log('🔍 Checking notification format...');
  
  // Check if the notification contains table number and customer name
  const hasTableNumber = data.message && data.message.includes('الطاولة');
  const hasCustomerName = data.message && data.message.includes('للعميل');
  const hasOldFormat = data.message && data.message.includes('رقم الطلب');
  
  if (hasTableNumber && hasCustomerName && !hasOldFormat) {
    console.log('✅ Notification format is correct!');
  } else {
    console.log('❌ Notification format needs attention:');
    console.log(`   - Has table number: ${hasTableNumber}`);
    console.log(`   - Has customer name: ${hasCustomerName}`);
    console.log(`   - Has old format: ${hasOldFormat}`);
  }
  
  console.log(`   - Message: ${data.message}`);
  console.log('---');
}

async function runTests() {
  console.log('\n🧪 Running Notification Tests...\n');
  
  try {
    // Test 1: Create an order
    console.log('Test 1: Creating a new order...');
    const response = await axios.post(`${BACKEND_URL}/api/orders`, testOrder);
    console.log('✅ Order created successfully:', response.data._id);
    
    // Wait a bit for notifications
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Test 2: Update order status
    console.log('\nTest 2: Updating order status...');
    const orderId = response.data._id;
    
    // Update to preparing
    await axios.patch(`${BACKEND_URL}/api/orders/${orderId}/status`, {
      status: 'preparing'
    });
    console.log('✅ Order status updated to preparing');
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Update to ready
    await axios.patch(`${BACKEND_URL}/api/orders/${orderId}/status`, {
      status: 'ready'
    });
    console.log('✅ Order status updated to ready');
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Update to served
    await axios.patch(`${BACKEND_URL}/api/orders/${orderId}/status`, {
      status: 'served'
    });
    console.log('✅ Order status updated to served');
    
    console.log('\n🎉 All tests completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
  }
  
  // Disconnect after tests
  setTimeout(() => {
    socket.disconnect();
    process.exit(0);
  }, 3000);
}

socket.on('connect_error', (error) => {
  console.error('❌ Connection error:', error.message);
  process.exit(1);
});
