// نماذج البيانات للوحة المدير

export interface Order {
  _id: string;
  orderNumber: string;
  items: OrderItem[];
  totalAmount?: number;  // Legacy field
  totalPrice?: number;   // Common field
  totals?: {             // New structure
    subtotal: number;
    tax: number;
    discount: number;
    total: number;
  };
  status: 'pending' | 'preparing' | 'ready' | 'completed' | 'delivered';
  tableNumber?: number;
  customerName?: string;
  waiterName?: string;
  waiterId?: string;
  chefName?: string;
  chefId?: string;
  createdAt: string;
  updatedAt: string;
}

export interface OrderItem {
  _id: string;
  name: string;
  price: number;
  quantity: number;
  notes?: string;
  category?: string;
}

export interface Employee {
  _id: string;
  username: string;
  name: string;
  email?: string;
  phone?: string;
  role: 'waiter' | 'chef' | 'manager';
  status: 'active' | 'inactive';
  isActive: boolean;
  currentShift?: any;
}

export interface TableAccount {
  _id: string;
  tableNumber: string;
  waiterName: string;
  totalAmount: number;
  isOpen: boolean;
  status: 'active' | 'closed';
  orders: Order[];
  createdAt: string;
}

export interface DashboardStats {
  totalOrders: number;
  totalSales: number;
  activeEmployees: number;
  activeTables: number;
  pendingOrders: number;
  preparingOrders: number;
  readyOrders: number;
  completedOrders: number;
  averageOrderValue: number;
  statusCounts: {
    pending: number;
    preparing: number;
    ready: number;
    completed: number;
  };
}

export interface DiscountRequest {
  _id: string;
  orderId: string;
  orderNumber: string;
  waiterName: string;
  amount: number;
  reason: string;
  status: 'pending' | 'approved' | 'rejected';
  createdAt: string;
  order?: Order;
}

export interface MenuItem {
  _id: string;
  name: string;
  price: number;
  description?: string;
  categories?: string[];
  available: boolean;
  stock?: number | { quantity: number };
}

export interface Category {
  _id: string;
  name: string;
  color: string;
  icon?: string;
  description?: string;
}

export interface Shift {
  _id: string;
  employeeId: string;
  employeeName: string;
  role: 'waiter' | 'chef' | 'manager';
  startTime: string;
  endTime?: string;
  duration?: string;
  status: 'active' | 'completed';
  ordersCount: number;
  salesAmount: number;
  createdAt: string;
}

// واجهة للفلاتر
export interface FilterState {
  orderStatus: 'all' | 'pending' | 'preparing' | 'ready' | 'completed';
  waiter: string;
  date: string;
  categoryFilter: string | null;
  availability: string;
  searchTerm: string;
}

// واجهة للحالة العامة
export interface ManagerState {
  currentScreen: 'home' | 'orders' | 'employees' | 'tables' | 'reports' | 'inventory' | 'menu' | 'categories';
  loading: boolean;
  sidebarOpen: boolean;
  orders: Order[];
  employees: Employee[];
  tableAccounts: TableAccount[];
  discountRequests: DiscountRequest[];
  shifts: Shift[];
  menuItems: MenuItem[];
  categories: Category[];
  stats: DashboardStats;
  filters: FilterState;
}

// واجهة حالة النوافذ المنبثقة
export interface ModalState {
  showDiscountModal: boolean;
  selectedDiscountRequest: DiscountRequest | null;
  showShiftModal: boolean;
  selectedEmployee: Employee | null;
  showOrderDetailsModal: boolean;
  selectedOrder: Order | null;
  showMenuModal: boolean;
  selectedMenuItem: MenuItem | null;
  showCategoryModal: boolean;
  selectedCategory: Category | null;
  showAddEmployeeModal: boolean;
  showEditEmployeeModal: boolean;
  selectedEmployeeForEdit: Employee | null;
  showAddProductModal: boolean;
  showEditProductModal: boolean;
  selectedProductForEdit: MenuItem | null;
}

// واجهة بيانات النماذج
export interface NewEmployee {
  username: string;
  name: string;
  email: string;
  role: 'waiter' | 'chef';
  password: string;
  phone: string;
}

export interface NewProduct {
  name: string;
  description: string;
  price: number;
  categories: string[];
  available: boolean;
  stock: number;
}
