const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const DiscountRequest = require('../models/DiscountRequest');

// GET /api/discount-requests - جلب طلبات الخصم
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { status } = req.query;

    let filter = {};
    if (status) {
      filter.status = status;
    }

    const discountRequests = await DiscountRequest.find(filter).sort({ createdAt: -1 });

    res.json(discountRequests);
  } catch (error) {
    console.error('Error fetching discount requests:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب طلبات الخصم',
      error: error.message
    });
  }
});

// POST /api/discount-requests - إنشاء طلب خصم جديد
router.post('/', authenticateToken, async (req, res) => {
  try {
    const { orderId, customerName, originalAmount, requestedDiscount, reason } = req.body;

    if (!orderId || !customerName || !originalAmount || !requestedDiscount) {
      return res.status(400).json({
        success: false,
        message: 'جميع الحقول مطلوبة'
      });
    }

    const newRequest = new DiscountRequest({
      orderId,
      customerName,
      originalAmount,
      requestedDiscount,
      reason: reason || '',
      status: 'pending',
      requestedBy: req.user.username
    });

    await newRequest.save();

    // Send Socket notifications for new discount request
    if (global.socketHandlers) {
      try {
        // Notify managers about new discount request
        global.socketHandlers.sendRoleNotification('manager', 
          `طلب خصم جديد من ${newRequest.customerName}`, {
          type: 'discount-request-created',
          requestId: newRequest._id,
          orderId: newRequest.orderId,
          customerName: newRequest.customerName,
          originalAmount: newRequest.originalAmount,
          requestedDiscount: newRequest.requestedDiscount,
          requestedBy: newRequest.requestedBy,
          timestamp: new Date().toISOString()
        });

        console.log(`💰 تم إرسال إشعار طلب خصم جديد للعميل: ${newRequest.customerName}`);
      } catch (socketError) {
        console.error('خطأ في إرسال إشعار Socket:', socketError);
      }
    }

    res.status(201).json({
      success: true,
      message: 'تم إنشاء طلب الخصم بنجاح',
      data: newRequest
    });
  } catch (error) {
    console.error('Error creating discount request:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في إنشاء طلب الخصم',
      error: error.message
    });
  }
});

// PUT /api/discount-requests/:id - تحديث حالة طلب الخصم
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { status, approvedBy } = req.body;

    const discountRequest = await DiscountRequest.findById(id);

    if (!discountRequest) {
      return res.status(404).json({
        success: false,
        message: 'طلب الخصم غير موجود'
      });
    }

    discountRequest.status = status;
    discountRequest.approvedBy = approvedBy || req.user.username;
    
    await discountRequest.save();

    // Send Socket notifications for discount request status update
    if (global.socketHandlers) {
      try {
        const statusMessages = {
          'approved': 'تم قبول طلب الخصم',
          'rejected': 'تم رفض طلب الخصم',
          'pending': 'طلب الخصم قيد المراجعة'
        };

        // Notify waiters about discount request status update
        global.socketHandlers.sendRoleNotification('waiter', 
          statusMessages[status] || `تم تحديث حالة طلب الخصم`, {
          type: 'discount-request-updated',
          requestId: discountRequest._id,
          orderId: discountRequest.orderId,
          customerName: discountRequest.customerName,
          status: status,
          approvedBy: discountRequest.approvedBy,
          timestamp: new Date().toISOString()
        });

        // Also notify managers
        global.socketHandlers.sendRoleNotification('manager', 
          `${statusMessages[status]} للعميل ${discountRequest.customerName}`, {
          type: 'discount-request-processed',
          requestId: discountRequest._id,
          orderId: discountRequest.orderId,
          customerName: discountRequest.customerName,
          status: status,
          approvedBy: discountRequest.approvedBy,
          timestamp: new Date().toISOString()
        });

        console.log(`💰 تم إرسال إشعار تحديث طلب الخصم: ${status} للعميل ${discountRequest.customerName}`);
      } catch (socketError) {
        console.error('خطأ في إرسال إشعار Socket:', socketError);
      }
    }

    res.json({
      success: true,
      message: 'تم تحديث طلب الخصم بنجاح',
      data: discountRequest
    });
  } catch (error) {
    console.error('Error updating discount request:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في تحديث طلب الخصم',
      error: error.message
    });
  }
});

// DELETE /api/discount-requests/:id - حذف طلب خصم
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    const discountRequest = await DiscountRequest.findByIdAndDelete(id);

    if (!discountRequest) {
      return res.status(404).json({
        success: false,
        message: 'طلب الخصم غير موجود'
      });
    }

    res.json({
      success: true,
      message: 'تم حذف طلب الخصم بنجاح'
    });
  } catch (error) {
    console.error('Error deleting discount request:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في حذف طلب الخصم',
      error: error.message
    });
  }
});

module.exports = router;
