import React, { useState, useEffect } from 'react';
import type { Setting, ApiResponse } from '../../utils/api'; // Changed to type-only import
import * as apiUtil from '../../utils/api';
import Button from '../Button';
import Alert from '../Alert';
import Loading from '../Loading'; // For internal loading state if needed, or use parent's

interface SettingsSectionProps {
  settings: Setting[];
  setSettings: React.Dispatch<React.SetStateAction<Setting[]>>;
  api: typeof apiUtil;
  addToast: (message: string, type: 'success' | 'error' | 'info') => void;
  initialLoading: boolean; // Loading state from parent for initial fetch
}

const SettingsSection: React.FC<SettingsSectionProps> = ({
  settings,
  setSettings,
  api,
  addToast,
  initialLoading,
}) => {
  const [formData, setFormData] = useState<{ [key: string]: any }>({});
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (settings) {
      const initialForm: { [key: string]: any } = {};
      settings.forEach(setting => {
        initialForm[setting.key] = setting.value;
      });
      setFormData(initialForm);
    }
  }, [settings]);

  const handleSettingChange = (key: string, value: any) => {
    setFormData(prev => ({ ...prev, [key]: value }));
  };

  const handleSettingsSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setIsSubmitting(true);
    try {
      const settingsToUpdate = Object.keys(formData).map(key => ({
        key,
        value: formData[key],
      }));

      const response = await api.updateSettings(settingsToUpdate);
      if (response.success && response.data) {
        setSettings(response.data); // Update parent state
        // FormData will be updated by useEffect due to settings prop change
        addToast('تم تحديث الإعدادات بنجاح!', 'success');
      } else {
        const errorMessage = response.error || 'فشل في تحديث الإعدادات.';
        setError(errorMessage);
        addToast(errorMessage, 'error');
      }
    } catch (err) {
      const catchMessage = 'حدث خطأ غير متوقع أثناء تحديث الإعدادات.';
      setError(catchMessage);
      addToast(catchMessage, 'error');
      console.error(err);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (initialLoading && settings.length === 0) {
    return <Loading />;
  }

  if (!settings || settings.length === 0) {
    return <p className="text-center text-gray-500 my-4">لا توجد إعدادات لعرضها حاليًا.</p>;
  }

  return (
    <div>
      <h2 className="text-2xl font-semibold mb-6">إدارة الإعدادات</h2>
      {error && <Alert type="error" onClose={() => setError(null)}>{error}</Alert>}
      <form onSubmit={handleSettingsSubmit} className="space-y-6 bg-white p-6 rounded-lg shadow">
        {settings.map(setting => (
          <div key={setting.key} className="border-b pb-4 last:border-b-0 last:pb-0">
            <label htmlFor={setting.key} className="block text-sm font-medium text-gray-700 mb-1">
              {setting.label}
            </label>
            {setting.description && <p className="text-xs text-gray-500 mb-2">{setting.description}</p>}
            
            {setting.type === 'text' && (
              <input
                type="text"
                id={setting.key}
                name={setting.key}
                value={formData[setting.key] || ''}
                onChange={(e) => handleSettingChange(setting.key, e.target.value)}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              />
            )}
            {setting.type === 'number' && (
              <input
                type="number"
                id={setting.key}
                name={setting.key}
                value={formData[setting.key] || 0}
                onChange={(e) => handleSettingChange(setting.key, parseFloat(e.target.value))}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              />
            )}
            {setting.type === 'boolean' && (
              <div className="flex items-center mt-2">
                <input
                  type="checkbox"
                  id={setting.key}
                  name={setting.key}
                  checked={!!formData[setting.key]}
                  onChange={(e) => handleSettingChange(setting.key, e.target.checked)}
                  className="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
                />
                 <span className="ml-2 text-sm text-gray-700">تفعيل</span>
              </div>
            )}
            {setting.type === 'select' && setting.options && (
              <select
                id={setting.key}
                name={setting.key}
                value={formData[setting.key] || ''}
                onChange={(e) => handleSettingChange(setting.key, e.target.value)}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              >
                <option value="" disabled={!setting.allowEmpty}>
                  {setting.allowEmpty ? 'اختر قيمة (أو اترك فارغًا)' : 'اختر قيمة'}
                </option>
                {setting.options.map(option => (
                  <option key={option} value={option}>{option}</option>
                ))}
              </select>
            )}
          </div>
        ))}
        <div className="pt-4">
          <Button type="submit" variant="primary" disabled={isSubmitting}>
            {isSubmitting ? 'جارٍ الحفظ...' : 'حفظ الإعدادات'}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default SettingsSection;
