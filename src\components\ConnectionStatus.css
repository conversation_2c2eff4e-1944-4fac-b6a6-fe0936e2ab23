.connection-error {
  position: fixed;
  top: 1rem;
  right: 1rem;
  background-color: #ffffff;
  border: 1px solid #ff4444;
  color: #333333;
  padding: 1rem;
  border-radius: 0.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 300px;
  direction: rtl;
}

.connection-status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #eee;
}

.status-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 0.5rem;
}

.status-indicator.error {
  background-color: #ff4444;
  box-shadow: 0 0 8px rgba(255, 68, 68, 0.5);
}

.connection-details {
  margin-bottom: 1rem;
}

.connection-details p {
  margin: 0.25rem 0;
  font-size: 0.9rem;
}

.error-message {
  color: #ff4444;
  font-size: 0.85rem;
  margin-top: 0.5rem;
}

.connection-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.connection-actions button {
  background-color: #fff;
  color: #ff4444;
  border: 1px solid #ff4444;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  cursor: pointer;
  font-weight: bold;
  width: 100%;
  transition: all 0.2s ease;
}

.connection-actions button:hover:not(:disabled) {
  background-color: #ff4444;
  color: #fff;
}

.connection-actions button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.connection-actions small {
  color: #666;
  font-size: 0.8rem;
}
