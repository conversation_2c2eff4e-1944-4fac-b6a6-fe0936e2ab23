// لوحة المدير الرئيسية - معاد تنظيمها باستخدام مبادئ البرمجة الكائنية
import React, { useEffect, useCallback } from 'react';
import { useToast } from './hooks/useToast';
import { notificationSound } from './utils/notificationSound';
import socket from './socket';
import { ManagerDataService } from './services/ManagerDataService';
import { StatsService } from './services/StatsService';
import { useManagerState } from './components/manager/useManagerState';
import { HomeScreen } from './components/manager/HomeScreen';
import { ReportsScreen } from './components/manager/ReportsScreen';
import { SimpleTablesScreen } from './components/manager/SimpleTablesScreen';
import { MenuScreen } from './components/manager/MenuScreen';
import type { Order, MenuItem } from './models/ManagerModels';
import './ManagerDashboard.css';

interface ManagerDashboardProps {
  user?: any;
  onLogout?: () => void;
}

export default function ManagerDashboard({ user: propUser, onLogout }: ManagerDashboardProps) {
  const { showSuccess, showError, showInfo } = useToast();
  
  // استخدام مدير الحالة المخصص
  const {
    // الحالات
    currentScreen,
    loading,
    sidebarOpen,
    orders,
    employees,
    tableAccounts,
    discountRequests,
    menuItems,
    categories,
    stats,
    filters,
    modalState,
    newEmployee,
    newProduct,

    // دوال التحديث
    setCurrentScreen,
    setLoading,
    setSidebarOpen,
    updateOrders,
    updateEmployees,
    updateTableAccounts,
    updateDiscountRequests,
    updateMenuItems,
    updateCategories,
    updateStats,
    updateFilter,
    resetFilters,
    getFilteredOrders,
    getFilteredMenuItems,
    updateModal,
    closeAllModals,
    updateNewEmployee,
    resetNewEmployee,
    updateNewProduct,
    resetNewProduct
  } = useManagerState();

  // الحصول على بيانات المستخدم
  const user = propUser || JSON.parse(localStorage.getItem('user') || 'null');
  const managerName = user?.username || user?.name || localStorage.getItem('username') || 'المدير';
  const managerId = user?._id || user?.id || 'manager-user';

  // دوال جلب البيانات
  const fetchOrders = useCallback(async () => {
    try {
      const ordersData = await ManagerDataService.fetchOrders();
      updateOrders(ordersData);
    } catch (error) {
      console.error('خطأ في جلب الطلبات:', error);
      showError('فشل في جلب الطلبات');
    }
  }, [updateOrders, showError]);

  const fetchEmployees = useCallback(async () => {
    try {
      const employeesData = await ManagerDataService.fetchEmployees();
      updateEmployees(employeesData);
    } catch (error) {
      console.error('خطأ في جلب الموظفين:', error);
      showError('فشل في جلب الموظفين');
    }
  }, [updateEmployees, showError]);

  const fetchTableAccounts = useCallback(async () => {
    try {
      const tablesData = await ManagerDataService.fetchTableAccounts();
      updateTableAccounts(tablesData);
    } catch (error) {
      console.error('خطأ في جلب الطاولات:', error);
      showError('فشل في جلب الطاولات');
    }
  }, [updateTableAccounts, showError]);

  const fetchDiscountRequests = useCallback(async () => {
    try {
      const discountData = await ManagerDataService.fetchDiscountRequests();
      updateDiscountRequests(discountData);
    } catch (error) {
      console.error('خطأ في جلب طلبات الخصم:', error);
      // لا نعرض خطأ للمستخدم هنا لأنه ليس حرجاً
    }
  }, [updateDiscountRequests]);

  const fetchMenuItems = useCallback(async () => {
    try {
      const menuData = await ManagerDataService.fetchMenuItems();
      updateMenuItems(menuData);
    } catch (error) {
      console.error('خطأ في جلب المنتجات:', error);
      showError('فشل في جلب المنتجات');
    }
  }, [updateMenuItems, showError]);

  const fetchCategories = useCallback(async () => {
    try {
      const categoriesData = await ManagerDataService.fetchCategories();
      updateCategories(categoriesData);
    } catch (error) {
      console.error('خطأ في جلب الفئات:', error);
      // لا نعرض خطأ للمستخدم هنا
    }
  }, [updateCategories]);

  // دالة جلب جميع البيانات
  const fetchAllData = useCallback(async () => {
    setLoading(true);
    try {
      await Promise.all([
        fetchOrders(),
        fetchEmployees(),
        fetchTableAccounts(),
        fetchDiscountRequests(),
        fetchMenuItems(),
        fetchCategories()
      ]);
    } catch (error) {
      console.error('خطأ في جلب البيانات:', error);
    } finally {
      setLoading(false);
    }
  }, [
    fetchOrders,
    fetchEmployees,
    fetchTableAccounts,
    fetchDiscountRequests,
    fetchMenuItems,
    fetchCategories,
    setLoading
  ]);

  // تحديث الإحصائيات عند تغيير البيانات
  useEffect(() => {
    const newStats = StatsService.calculateDashboardStats(orders, employees);
    // إضافة عدد الطاولات النشطة
    newStats.activeTables = tableAccounts.filter(table => table.isOpen).length;
    updateStats(newStats);
  }, [orders, employees, tableAccounts, updateStats]);

  // جلب البيانات عند تحميل المكون
  useEffect(() => {
    fetchAllData();
  }, [fetchAllData]);

  // إعداد Socket.IO للإشعارات
  useEffect(() => {    // الاستماع للطلبات الجديدة
    socket.on('new-order', (orderData) => {
      console.log('🔔 طلب جديد:', orderData);
      fetchOrders();
      notificationSound.playNotification();
      showInfo(`طلب جديد #${orderData.orderNumber} من طاولة ${orderData.tableNumber}`);
    });

    // الاستماع لتحديثات الطلبات
    socket.on('order-updated', (orderData) => {
      console.log('📝 تحديث طلب:', orderData);
      fetchOrders();
    });

    // الاستماع لطلبات الخصم
    socket.on('discount-request', (requestData) => {
      console.log('💸 طلب خصم جديد:', requestData);
      fetchDiscountRequests();
      notificationSound.playNotification();
      showInfo(`طلب خصم جديد من ${requestData.waiterName}`);
    });

    return () => {
      socket.off('new-order');
      socket.off('order-updated');
      socket.off('discount-request');
    };
  }, [fetchOrders, fetchDiscountRequests, showInfo]);

  // دوال إدارة المنتجات
  const handleAddProduct = async () => {
    try {
      if (!newProduct.name || newProduct.price <= 0) {
        showError('اسم المنتج والسعر مطلوبان');
        return;
      }

      if (!newProduct.categories || newProduct.categories.length === 0) {
        showError('يجب اختيار فئة واحدة على الأقل');
        return;
      }

      const result = await ManagerDataService.addProduct(newProduct);
      
      if (result.success) {
        showSuccess('تم إضافة المنتج بنجاح');
        updateModal('showAddProductModal', false);
        resetNewProduct();
        await fetchMenuItems();
      } else {
        showError(result.message || 'فشل في إضافة المنتج');
      }
    } catch (error) {
      console.error('خطأ في إضافة المنتج:', error);
      showError('فشل في إضافة المنتج');
    }
  };

  const handleEditProduct = async (product: MenuItem) => {
    try {
      const result = await ManagerDataService.updateProduct(product._id, product);
      
      if (result.success) {
        showSuccess('تم تحديث المنتج بنجاح');
        updateModal('showEditProductModal', false);
        updateModal('selectedProductForEdit', null);
        await fetchMenuItems();
      } else {
        showError(result.message || 'فشل في تحديث المنتج');
      }
    } catch (error) {
      console.error('خطأ في تحديث المنتج:', error);
      showError('فشل في تحديث المنتج');
    }
  };

  const handleDeleteProduct = async (product: MenuItem) => {
    const confirmed = window.confirm(
      `هل أنت متأكد من حذف المنتج "${product.name}"؟\n\n` +
      `السعر: ${product.price} ج.م\n` +
      `الحالة: ${product.available ? 'متوفر' : 'غير متوفر'}\n\n` +
      `⚠️ هذا الإجراء لا يمكن التراجع عنه!`
    );
    
    if (!confirmed) return;

    try {
      const result = await ManagerDataService.deleteProduct(product._id);

      if (result.success) {
        showSuccess(`تم حذف منتج "${product.name}" بنجاح`);
        await fetchMenuItems();
      } else {
        showError(result.message || 'فشل في حذف المنتج');
      }
    } catch (error) {
      console.error('خطأ في حذف المنتج:', error);
      showError('فشل في حذف المنتج');
    }
  };

  const handleToggleProductAvailability = async (productId: string, currentAvailability: boolean) => {
    try {
      const result = await ManagerDataService.toggleProductAvailability(productId, currentAvailability);

      if (result.success) {
        showSuccess(`تم ${!currentAvailability ? 'تفعيل' : 'إلغاء تفعيل'} المنتج بنجاح`);
        await fetchMenuItems();
      } else {
        showError(result.message || 'فشل في تغيير حالة المنتج');
      }
    } catch (error) {
      console.error('خطأ في تغيير حالة المنتج:', error);
      showError('فشل في تغيير حالة المنتج');
    }
  };

  // دالة تسجيل الخروج
  const handleLogout = () => {
    if (onLogout) {
      onLogout();
    } else {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      localStorage.removeItem('username');
      window.location.reload();
    }
  };

  // دالة عرض تفاصيل الطلب
  const handleOrderClick = (order: Order) => {
    updateModal('selectedOrder', order);
    updateModal('showOrderDetailsModal', true);
  };

  // رندر المكون الحالي
  const renderCurrentScreen = () => {
    switch (currentScreen) {
      case 'home':
        return (
          <HomeScreen
            stats={stats}
            orders={orders}
            employees={employees}
            onOrderClick={handleOrderClick}
          />
        );
      
      case 'reports':
        return (
          <ReportsScreen
            orders={orders}
            employees={employees}
          />
        );
        case 'tables':
        return (
          <SimpleTablesScreen
            tableAccounts={tableAccounts}
            onRefresh={fetchTableAccounts}
          />
        );
      
      case 'menu':
        return (
          <MenuScreen
            menuItems={menuItems}
            categories={categories}
            filters={filters}
            onFilterChange={updateFilter}
            onAddProduct={() => updateModal('showAddProductModal', true)}
            onEditProduct={(product) => {
              updateModal('selectedProductForEdit', product);
              updateModal('showEditProductModal', true);
            }}
            onDeleteProduct={handleDeleteProduct}
            onToggleAvailability={handleToggleProductAvailability}
            onRefresh={() => {
              fetchMenuItems();
              fetchCategories();            }}
          />
        );
      
      case 'orders':
        return (
          <div className="screen-placeholder">
            <div className="orders-screen">
              <div className="orders-header">
                <h1>
                  <i className="fas fa-list"></i>
                  إدارة الطلبات
                </h1>
              </div>
              <div className="orders-content">
                <p>شاشة إدارة الطلبات - قيد التطوير</p>
                <div className="orders-stats">
                  <div className="stat-card">
                    <div className="stat-number">{orders.filter(order => order.status === 'pending').length}</div>
                    <div className="stat-label">طلبات معلقة</div>
                  </div>
                  <div className="stat-card">
                    <div className="stat-number">{orders.filter(order => order.status === 'preparing').length}</div>
                    <div className="stat-label">قيد التحضير</div>
                  </div>
                  <div className="stat-card">
                    <div className="stat-number">{orders.filter(order => order.status === 'ready').length}</div>
                    <div className="stat-label">جاهز</div>
                  </div>
                  <div className="stat-card">
                    <div className="stat-number">{orders.filter(order => order.status === 'completed').length}</div>
                    <div className="stat-label">مكتمل</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      case 'employees':
        return (
          <div className="screen-placeholder">
            <div className="employees-screen">
              <div className="employees-header">
                <h1>
                  <i className="fas fa-users"></i>
                  إدارة الموظفين
                </h1>
              </div>
              <div className="employees-content">
                <p>شاشة إدارة الموظفين - قيد التطوير</p>
                <div className="employees-stats">
                  <div className="stat-card">
                    <div className="stat-number">{employees.length}</div>
                    <div className="stat-label">إجمالي الموظفين</div>
                  </div>
                  <div className="stat-card">
                    <div className="stat-number">{employees.filter(emp => emp.isActive).length}</div>
                    <div className="stat-label">موظفون نشطون</div>
                  </div>
                  <div className="stat-card">
                    <div className="stat-number">{employees.filter(emp => emp.role === 'waiter').length}</div>
                    <div className="stat-label">نُدل</div>
                  </div>
                  <div className="stat-card">
                    <div className="stat-number">{employees.filter(emp => emp.role === 'chef').length}</div>
                    <div className="stat-label">طباخين</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      case 'inventory':
        return (
          <div className="screen-placeholder">
            <div className="inventory-screen">
              <div className="inventory-header">
                <h1>
                  <i className="fas fa-boxes"></i>
                  إدارة المخزون
                </h1>
              </div>
              <div className="inventory-content">
                <p>شاشة إدارة المخزون - قيد التطوير</p>
                <div className="inventory-stats">
                  <div className="stat-card">
                    <div className="stat-number">{menuItems.length}</div>
                    <div className="stat-label">إجمالي المنتجات</div>
                  </div>
                  <div className="stat-card">
                    <div className="stat-number">{menuItems.filter(item => item.available).length}</div>
                    <div className="stat-label">منتجات متوفرة</div>
                  </div>
                  <div className="stat-card">
                    <div className="stat-number">{menuItems.filter(item => !item.available).length}</div>
                    <div className="stat-label">منتجات غير متوفرة</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      case 'categories':
        return (
          <div className="screen-placeholder">
            <div className="categories-screen">
              <div className="categories-header">
                <h1>
                  <i className="fas fa-tags"></i>
                  إدارة الفئات
                </h1>
              </div>
              <div className="categories-content">
                <p>شاشة إدارة الفئات - قيد التطوير</p>
                <div className="categories-stats">
                  <div className="stat-card">
                    <div className="stat-number">{categories.length}</div>
                    <div className="stat-label">إجمالي الفئات</div>
                  </div>
                  <div className="stat-card">
                    <div className="stat-number">{categories.filter(cat => cat.name).length}</div>
                    <div className="stat-label">فئات نشطة</div>
                  </div>
                </div>
                <div className="categories-list">
                  {categories.map(category => (
                    <div key={category._id} className="category-item" style={{ backgroundColor: category.color }}>
                      {category.icon && <i className={category.icon}></i>}
                      <span>{category.name}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        );
      
      default:
        return <div className="screen-placeholder">هذه الشاشة قيد التطوير...</div>;
    }
  };

  return (
    <div className="manager-dashboard">
      {/* Header */}
      <header className="manager-header">
        <div className="header-content">
          <div className="header-left">
            <button
              className="sidebar-toggle"
              onClick={() => setSidebarOpen(!sidebarOpen)}
            >
              ☰
            </button>
            <h1>لوحة المدير</h1>
          </div>
          <div className="header-right">
            <span className="manager-name">مرحباً، {managerName}</span>
            <button className="logout-btn" onClick={handleLogout}>
              تسجيل الخروج
            </button>
          </div>
        </div>
      </header>

      <div className="dashboard-content">
        {/* Sidebar */}
        <aside className={`manager-sidebar ${sidebarOpen ? 'open' : ''}`}>
          <div className="sidebar-content">
            <div className="manager-profile">
              <div className="manager-avatar">👨‍💼</div>
              <h3>{managerName}</h3>
              <p>مدير</p>
            </div>

            <nav className="manager-nav">
              <button
                className={`nav-btn ${currentScreen === 'home' ? 'active' : ''}`}
                onClick={() => setCurrentScreen('home')}
              >
                <i className="fas fa-home"></i>
                <span>الرئيسية</span>
              </button>

              <button
                className={`nav-btn ${currentScreen === 'orders' ? 'active' : ''}`}
                onClick={() => setCurrentScreen('orders')}
              >
                <i className="fas fa-shopping-cart"></i>
                <span>الطلبات</span>
              </button>

              <button
                className={`nav-btn ${currentScreen === 'employees' ? 'active' : ''}`}
                onClick={() => setCurrentScreen('employees')}
              >
                <i className="fas fa-users"></i>
                <span>الموظفون</span>
              </button>

              <button
                className={`nav-btn ${currentScreen === 'tables' ? 'active' : ''}`}
                onClick={() => setCurrentScreen('tables')}
              >
                <i className="fas fa-table"></i>
                <span>الطاولات</span>
              </button>

              <button
                className={`nav-btn ${currentScreen === 'reports' ? 'active' : ''}`}
                onClick={() => setCurrentScreen('reports')}
              >
                <i className="fas fa-chart-bar"></i>
                <span>التقارير</span>
              </button>

              <button
                className={`nav-btn ${currentScreen === 'inventory' ? 'active' : ''}`}
                onClick={() => setCurrentScreen('inventory')}
              >
                <i className="fas fa-boxes"></i>
                <span>المخزون</span>
              </button>

              <button
                className={`nav-btn ${currentScreen === 'menu' ? 'active' : ''}`}
                onClick={() => setCurrentScreen('menu')}
              >
                <i className="fas fa-coffee"></i>
                <span>القائمة</span>
              </button>

              <button
                className={`nav-btn ${currentScreen === 'categories' ? 'active' : ''}`}
                onClick={() => setCurrentScreen('categories')}
              >
                <i className="fas fa-tags"></i>
                <span>الفئات</span>
              </button>
            </nav>
          </div>
        </aside>

        {/* Main Content */}
        <main className="manager-main">
          {loading ? (
            <div className="loading-screen">
              <div className="loading-spinner"></div>
              <p>جاري تحميل البيانات...</p>
            </div>
          ) : (
            renderCurrentScreen()
          )}
        </main>
      </div>
    </div>
  );
}
