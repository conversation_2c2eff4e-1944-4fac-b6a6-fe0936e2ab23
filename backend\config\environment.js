// Config for Environment Variables
// ملف التكوين المركزي لمتغيرات البيئة

require('dotenv').config();

// تصدير جميع المتغيرات المطلوبة مع قيم افتراضية
module.exports = {  // Server Configuration
  server: {
    port: process.env.PORT || process.env.BACKEND_PORT || 4003,
    nodeEnv: process.env.NODE_ENV || 'production',
    host: process.env.HOST || '0.0.0.0'
  },
  // Database Configuration
  database: {
    mongoUri: process.env.MONGODB_URI || 'mongodb+srv://besomustafa:<EMAIL>/deshacoffee?retryWrites=true&w=majority&appName=MyCoffeChop',
    maxPoolSize: parseInt(process.env.DB_MAX_POOL_SIZE) || 10,
    minPoolSize: parseInt(process.env.DB_MIN_POOL_SIZE) || 1,
    maxIdleTimeMS: parseInt(process.env.DB_MAX_IDLE_TIME_MS) || 30000
  },
  // JWT Configuration
  jwt: {
    secret: process.env.JWT_SECRET || 'desha-coffee-super-secret-jwt-key-production-2024',
    expiresIn: process.env.JWT_EXPIRES_IN || '7d'
  },
  // Security Configuration
  security: {
    bcryptSaltRounds: parseInt(process.env.BCRYPT_SALT_ROUNDS) || 12,
    sessionSecret: process.env.SESSION_SECRET || 'default-session-secret'
  },
  // CORS Configuration
  cors: {
    origin: process.env.CORS_ORIGIN || process.env.FRONTEND_URL || 'https://desha-coffee.vercel.app',
    credentials: process.env.CORS_CREDENTIALS === 'true' || true
  },

  // Rate Limiting
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 900000, // 15 minutes
    maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100
  },

  // Email Configuration
  email: {
    host: process.env.EMAIL_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.EMAIL_PORT) || 587,
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS,
    from: process.env.EMAIL_FROM || '<EMAIL>'
  },

  // File Upload Configuration
  upload: {
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE) || 10485760, // 10MB
    uploadPath: process.env.UPLOAD_PATH || 'uploads/',
    allowedTypes: process.env.ALLOWED_FILE_TYPES?.split(',') || ['jpg', 'jpeg', 'png', 'pdf']
  },

  // Logging Configuration
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    debug: process.env.DEBUG === 'true' || false
  },  // Frontend URLs (for backend reference)
  frontend: {
    url: process.env.FRONTEND_URL || 'https://desha-coffee.vercel.app',
    apiUrl: process.env.BACKEND_URL || 'https://deshacoffee-production.up.railway.app',
    allowedOrigins: [
      'https://desha-coffee.vercel.app',
      'https://deshacoffee-production.up.railway.app',
      process.env.FRONTEND_URL || 'https://desha-coffee.vercel.app',
      process.env.BACKEND_URL || 'https://deshacoffee-production.up.railway.app',
      /\.vercel\.app$/,
      /\.railway\.app$/
    ]
  },

  // Development flags
  development: {
    enableSeedData: process.env.ENABLE_SEED_DATA === 'true' || false,
    enableTestRoutes: process.env.ENABLE_TEST_ROUTES === 'true' || false
  }
};

// دالة للتحقق من وجود المتغيرات المطلوبة
function validateRequiredEnvVars() {
  const requiredVars = [
    'MONGODB_URI',
    'JWT_SECRET',
    'NODE_ENV'
  ];

  const criticalVars = [
    'MONGODB_URI',
    'JWT_SECRET'
  ];

  const missingVars = requiredVars.filter(varName => !process.env[varName]);
  const missingCritical = criticalVars.filter(varName => !process.env[varName]);
  
  if (missingCritical.length > 0) {
    console.error('❌ خطأ: المتغيرات التالية مطلوبة ولا يمكن تشغيل النظام بدونها:');
    missingCritical.forEach(varName => {
      console.error(`   - ${varName}`);
    });
    console.error('💥 لا يمكن تشغيل الخادم - يرجى إضافة المتغيرات المطلوبة في ملف .env');
    process.exit(1);
  }
  
  if (missingVars.length > 0) {
    console.warn('⚠️  تحذير: المتغيرات التالية غير مُعرَّفة في ملف .env:');
    missingVars.forEach(varName => {
      console.warn(`   - ${varName}`);
    });
    console.warn('سيتم استخدام القيم الافتراضية للتطوير.');
  } else {
    console.log('✅ جميع متغيرات البيئة المطلوبة متوفرة');
  }
}

// تشغيل التحقق عند تحميل الملف
if (process.env.NODE_ENV !== 'test') {
  validateRequiredEnvVars();
}
