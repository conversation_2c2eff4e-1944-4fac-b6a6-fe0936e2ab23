// نماذج البيانات للوحة النادل

export interface WaiterOrder {
  _id: string;
  orderNumber: string;
  items: WaiterOrderItem[];
  totalAmount: number;
  status: 'pending' | 'preparing' | 'ready' | 'completed' | 'delivered';
  tableNumber: number;
  customerName?: string;
  waiterName: string;
  waiterId?: string;
  chefName?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface WaiterOrderItem {
  _id: string;
  productId: string;
  name: string;
  price: number;
  quantity: number;
  notes?: string;
  category?: string;
}

export interface WaiterTableAccount {
  _id: string;
  tableNumber: string;
  waiterName: string;
  waiterId?: string;
  customerName?: string;
  totalAmount: number;
  discountPercent?: number;
  isOpen: boolean;
  status: 'active' | 'closed';
  orders: WaiterOrder[];
  createdAt: string;
  lastOrderTime?: string;
}

export interface WaiterProduct {
  _id: string;
  name: string;
  price: number;
  description?: string;
  categories: string[];
  categoryDetails?: WaiterCategory[];
  available: boolean;
  stock?: number | { quantity: number };
  image?: string;
  notes?: string;
}

// واجهة عنصر السلة
export interface CartItem {
  _id: string;
  productId: string;
  name: string;
  price: number;
  quantity: number;
  notes?: string;
  category?: string;
}

export interface WaiterCategory {
  _id: string;
  name: string;
  color: string;
  icon?: string;
  description?: string;
  order?: number;
}

export interface WaiterStats {
  todayOrders: number;
  todaySales: number;
  activeOrders: number;
  completedOrders: number;
  readyOrders: number;
  openTables: number;
  averageOrderValue: number;
  myTables: number;
}

export interface DiscountRequestData {
  orderId: string;
  orderNumber: string;
  amount: number;
  reason: string;
  tableNumber: number;
}

export interface WaiterNotification {
  _id: string;
  type: 'order_ready' | 'order_completed' | 'discount_approved' | 'discount_rejected' | 'general';
  title: string;
  message: string;
  orderId?: string;
  tableNumber?: number;
  isRead: boolean;
  createdAt: string;
}

// واجهة الحالة العامة للنادل
export interface WaiterState {
  currentView: 'tables' | 'orders' | 'menu' | 'new-order' | 'notifications' | 'profile';
  loading: boolean;
  sidebarOpen: boolean;
  tableAccounts: WaiterTableAccount[];
  orders: WaiterOrder[];
  products: WaiterProduct[];
  categories: WaiterCategory[];
  stats: WaiterStats;
  notifications: WaiterNotification[];
  selectedTable: WaiterTableAccount | null;
  selectedOrder: WaiterOrder | null;
}

// واجهة حالة النوافذ المنبثقة للنادل
export interface WaiterModalState {
  showOrderModal: boolean;
  showDiscountModal: boolean;
  showCloseTableModal: boolean;
  showProductModal: boolean;
  showNotificationsModal: boolean;
  selectedOrderForDiscount: WaiterOrder | null;
  selectedTableForClose: WaiterTableAccount | null;
  selectedProductForOrder: WaiterProduct | null;
}

// واجهة بيانات الطلب الجديد
export interface NewOrderData {
  tableId?: string;
  tableNumber: string;
  customerName: string;
  items: CartItem[];
  totalPrice: number;
  notes?: string;
  discountPercent?: number;
}

// واجهة فلاتر النادل
export interface WaiterFilters {
  tableStatus: 'all' | 'open' | 'closed';
  orderStatus: 'all' | 'pending' | 'preparing' | 'ready' | 'completed';
  categoryFilter: string | null;
  searchTerm: string;
}

// واجهة إعدادات النادل
export interface WaiterSettings {
  soundEnabled: boolean;
  autoRefresh: boolean;
  refreshInterval: number;
  showNotifications: boolean;
  language: 'ar' | 'en';
}
