.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--spacing-md);
  backdrop-filter: blur(4px);
  animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.modal-content {
  background: var(--surface);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  max-height: 90vh;
  overflow-y: auto;
  animation: modalSlideIn 0.3s ease;
  direction: rtl;
}

@keyframes modalSlideIn {
  from {
    transform: translateY(-50px) scale(0.95);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

/* أحجام مختلفة للنافذة */
.modal-sm {
  width: 100%;
  max-width: 400px;
}

.modal-md {
  width: 100%;
  max-width: 600px;
}

.modal-lg {
  width: 100%;
  max-width: 800px;
}

.modal-xl {
  width: 100%;
  max-width: 1000px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border);
}

.modal-title {
  margin: 0;
  color: var(--text-primary);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
}

.modal-close-btn {
  background: transparent;
  border: none;
  color: var(--text-secondary);
  font-size: var(--font-size-xl);
  cursor: pointer;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.modal-close-btn:hover {
  background-color: rgba(0, 0, 0, 0.1);
  color: var(--text-primary);
}

.modal-body {
  padding: var(--spacing-lg);
}

/* تنسيق خاص للوضع المظلم */
[data-theme="dark"] .modal-overlay {
  background-color: rgba(0, 0, 0, 0.8);
}

[data-theme="dark"] .modal-content {
  background: var(--surface);
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.5);
}

[data-theme="dark"] .modal-close-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* تصميم متجاوب */
@media (max-width: 768px) {
  .modal-overlay {
    padding: var(--spacing-sm);
  }
  
  .modal-content {
    max-height: 95vh;
  }
  
  .modal-sm,
  .modal-md,
  .modal-lg,
  .modal-xl {
    width: 100%;
    max-width: none;
  }
  
  .modal-header {
    padding: var(--spacing-md);
  }
  
  .modal-body {
    padding: var(--spacing-md);
  }
  
  .modal-title {
    font-size: var(--font-size-lg);
  }
}
