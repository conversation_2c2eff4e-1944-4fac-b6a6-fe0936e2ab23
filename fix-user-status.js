import mongoose from 'mongoose';
import dotenv from 'dotenv';

dotenv.config();

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb+srv://besomustafa:<EMAIL>/deshacoffee?retryWrites=true&w=majority&appName=MyCoffeChop';

async function fixUserStatus() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('✅ متصل بقاعدة البيانات');

    const db = mongoose.connection.db;
    const usersCollection = db.collection('users');
    
    console.log('🔍 فحص المستخدمين الحاليين...');
    
    // عرض المستخدمين الحاليين
    const currentUsers = await usersCollection.find({ 
      username: { $in: ['<PERSON><PERSON>', 'azz', '<PERSON><PERSON>', 'khaled'] } 
    }).toArray();
    
    console.log('\n📋 حالة المستخدمين الحالية:');
    currentUsers.forEach(user => {
      console.log(`- ${user.username}:`);
      console.log(`  * active: ${user.active}`);
      console.log(`  * status: ${user.status}`);
      console.log(`  * role: ${user.role}`);
      console.log(`  * email: ${user.email || 'غير محدد'}`);
    });
    
    console.log('\n🔧 إصلاح حقول المستخدمين...');
    
    // تحديث المستخدمين لإضافة الحقول المطلوبة حسب نموذج User.js
    const usersToUpdate = [
      {
        username: 'Beso',
        email: '<EMAIL>',
        name: 'بيسو',
        role: 'manager',
        status: 'active',
        phone: '01234567890'
      },
      {
        username: 'azz',
        email: '<EMAIL>',
        name: 'عز',
        role: 'waiter',
        status: 'active',
        phone: '01234567891'
      },
      {
        username: 'Bosy',
        email: '<EMAIL>',
        name: 'بوسي',
        role: 'waiter',
        status: 'active',
        phone: '01234567892'
      },
      {
        username: 'khaled',
        email: '<EMAIL>',
        name: 'خالد',
        role: 'chef',
        status: 'active',
        phone: '01234567893'
      }
    ];

    for (const userData of usersToUpdate) {
      const result = await usersCollection.updateOne(
        { username: userData.username },
        { 
          $set: {
            email: userData.email,
            name: userData.name,
            role: userData.role,
            status: userData.status,
            phone: userData.phone
          },
          $unset: {
            active: "",  // إزالة الحقل القديم
            isActive: "" // إزالة الحقل القديم إذا كان موجوداً
          }
        }
      );
      
      if (result.modifiedCount > 0) {
        console.log(`✅ تم تحديث المستخدم: ${userData.username}`);
      } else {
        console.log(`⚠️  لم يتم العثور على المستخدم: ${userData.username}`);
      }
    }
    
    console.log('\n📋 حالة المستخدمين بعد التحديث:');
    const updatedUsers = await usersCollection.find({ 
      username: { $in: ['Beso', 'azz', 'Bosy', 'khaled'] } 
    }).toArray();
    
    updatedUsers.forEach(user => {
      console.log(`- ${user.username}:`);
      console.log(`  * email: ${user.email}`);
      console.log(`  * name: ${user.name}`);
      console.log(`  * role: ${user.role}`);
      console.log(`  * status: ${user.status}`);
      console.log(`  * phone: ${user.phone}`);
      console.log(`  * password موجود: ${user.password ? 'نعم' : 'لا'}`);
      console.log('');
    });
    
    console.log('🎉 تم إصلاح جميع المستخدمين بنجاح!');
    console.log('\n📝 يمكنك الآن تسجيل الدخول باستخدام:');
    console.log('- اسم المستخدم: Beso');
    console.log('- كلمة المرور: MOHAMEDmostafa123');

  } catch (error) {
    console.error('❌ خطأ:', error.message);
    console.error(error.stack);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

console.log('🚀 بدء عملية إصلاح بيانات المستخدمين...');
console.log('⏰ الوقت:', new Date().toLocaleString('ar-EG'));
console.log('');

fixUserStatus()
  .then(() => {
    console.log('✅ انتهت العملية بنجاح');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ فشلت العملية:', error);
    process.exit(1);
  });
