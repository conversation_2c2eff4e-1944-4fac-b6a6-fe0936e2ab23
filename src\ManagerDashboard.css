/* Manager Dashboard Styles */
.manager-dashboard {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: var(--light-bg, #f8f9fa);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  direction: rtl;
}

/* Header */
.manager-header {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: white;
  padding: 1rem 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.sidebar-toggle {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

.sidebar-toggle:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.header-left h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.manager-name {
  font-size: 1rem;
  opacity: 0.9;
}

.logout-btn {
  background: rgba(231, 76, 60, 0.9);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.logout-btn:hover {
  background: #e74c3c;
  transform: translateY(-1px);
}

/* Dashboard Content */
.dashboard-content {
  display: flex;
  flex: 1;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

/* Sidebar */
.manager-sidebar {
  width: 280px;
  background: white;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  position: fixed;
  top: 80px;
  right: 0;
  height: calc(100vh - 80px);
  z-index: 50;
  overflow-y: auto;
}

.manager-sidebar.open {
  transform: translateX(0);
}

.sidebar-content {
  padding: 2rem 1rem;
}

.manager-profile {
  text-align: center;
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid #eee;
}

.manager-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3498db, #2980b9);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  margin: 0 auto 1rem;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.manager-profile h3 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 1.2rem;
}

.manager-profile p {
  margin: 0;
  color: #7f8c8d;
  font-size: 0.9rem;
}

/* Navigation */
.manager-nav {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.nav-btn {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: none;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: right;
  font-size: 1rem;
  color: #2c3e50;
}

.nav-btn:hover {
  background: #f8f9fa;
  transform: translateX(-5px);
}

.nav-btn.active {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.nav-btn i {
  font-size: 1.2rem;
  width: 20px;
  text-align: center;
}

/* Main Content */
.manager-main {
  flex: 1;
  padding: 2rem;
  margin-right: 0;
  transition: margin-right 0.3s ease;
}

.manager-sidebar.open ~ .manager-main {
  margin-right: 280px;
}

/* Loading */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  gap: 1rem;
  color: #2c3e50;
}

.loading i {
  font-size: 2rem;
  color: #3498db;
}

/* Manager Home */
.manager-home {
  max-width: 1200px;
  margin: 0 auto;
}

.manager-header .welcome-section {
  text-align: center;
  margin-bottom: 2rem;
}

.manager-header .welcome-section h1 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.role-badge {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.stat-card {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1.5rem;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.stat-card.orders .stat-icon {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.stat-card.sales .stat-icon {
  background: linear-gradient(135deg, #27ae60, #229954);
}

.stat-card.employees .stat-icon {
  background: linear-gradient(135deg, #f39c12, #e67e22);
}

.stat-card.tables .stat-icon {
  background: linear-gradient(135deg, #9b59b6, #8e44ad);
}

.stat-content h3 {
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 700;
  color: #2c3e50;
}

.stat-content p {
  margin: 0;
  color: #7f8c8d;
  font-size: 0.9rem;
}

/* Orders Stats */
.orders-stats {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.orders-stats h2 {
  margin: 0 0 2rem 0;
  color: #2c3e50;
  text-align: center;
  font-size: 1.5rem;
}

.orders-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.order-stat {
  text-align: center;
  padding: 1.5rem;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.order-stat:hover {
  transform: translateY(-3px);
}

.order-stat.pending {
  background: linear-gradient(135deg, #f39c12, #e67e22);
  color: white;
}

.order-stat.preparing {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
}

.order-stat.ready {
  background: linear-gradient(135deg, #27ae60, #229954);
  color: white;
}

.order-stat.completed {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
}

.order-stat i {
  font-size: 2rem;
  margin-bottom: 1rem;
  display: block;
}

.order-stat .count {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.order-stat .label {
  font-size: 0.9rem;
  opacity: 0.9;
}

/* Coming Soon */
.coming-soon {
  text-align: center;
  padding: 4rem 2rem;
  color: #7f8c8d;
  font-size: 1.2rem;
}

/* Orders Screen */
.orders-screen {
  padding: 2rem;
}

.orders-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.orders-header h1 {
  margin: 0;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.orders-filters {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.filter-select {
  padding: 0.5rem 1rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: white;
  font-size: 0.9rem;
  min-width: 150px;
}

.waiter-stats {
  margin-bottom: 2rem;
}

.waiter-stats h2 {
  margin-bottom: 1rem;
  color: #2c3e50;
}

.waiter-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.waiter-stat-card {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.waiter-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.waiter-info i {
  font-size: 2rem;
  color: #3498db;
}

.waiter-info h3 {
  margin: 0;
  color: #2c3e50;
}

.waiter-numbers {
  display: flex;
  gap: 1rem;
}

.waiter-numbers .stat {
  text-align: center;
}

.waiter-numbers .number {
  display: block;
  font-size: 1.5rem;
  font-weight: bold;
  color: #2c3e50;
}

.waiter-numbers .label {
  font-size: 0.8rem;
  color: #7f8c8d;
}

.discount-requests {
  margin-bottom: 2rem;
}

.discount-requests h2 {
  margin-bottom: 1rem;
  color: #e74c3c;
}

.discount-requests-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.discount-request-card {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #e74c3c;
}

.request-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.order-number {
  font-weight: bold;
  color: #2c3e50;
}

.waiter-name {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.request-details {
  margin-bottom: 1rem;
}

.amount {
  font-size: 1.1rem;
  font-weight: bold;
  color: #e74c3c;
  margin-bottom: 0.5rem;
}

.reason {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.request-actions {
  display: flex;
  gap: 0.5rem;
}

.approve-btn, .reject-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.approve-btn {
  background: #27ae60;
  color: white;
}

.approve-btn:hover {
  background: #229954;
}

.reject-btn {
  background: #e74c3c;
  color: white;
}

.reject-btn:hover {
  background: #c0392b;
}

.orders-list h2 {
  margin-bottom: 1rem;
  color: #2c3e50;
}

.orders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.order-card {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.order-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.order-card.pending {
  border-left: 4px solid #f39c12;
}

.order-card.preparing {
  border-left: 4px solid #e74c3c;
}

.order-card.ready {
  border-left: 4px solid #27ae60;
}

.order-card.completed {
  border-left: 4px solid #3498db;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.order-number {
  font-weight: bold;
  color: #2c3e50;
}

.order-status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.order-status.pending {
  background: #f39c12;
  color: white;
}

.order-status.preparing {
  background: #e74c3c;
  color: white;
}

.order-status.ready {
  background: #27ae60;
  color: white;
}

.order-status.completed {
  background: #3498db;
  color: white;
}

.order-info {
  margin-bottom: 1rem;
}

.order-detail {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  color: #7f8c8d;
  font-size: 0.9rem;
}

.order-detail i {
  width: 16px;
  text-align: center;
}

.order-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.items-count {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.total-amount {
  font-weight: bold;
  color: #2c3e50;
  font-size: 1.1rem;
}

.order-time {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #7f8c8d;
  font-size: 0.8rem;
  margin-bottom: 1rem;
}

.details-btn {
  width: 100%;
  padding: 0.75rem;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.details-btn:hover {
  background: #2980b9;
}

/* Employees Screen */
.employees-screen {
  padding: 2rem;
}

.employees-header h1 {
  margin: 0 0 2rem 0;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.active-shifts-section, .inactive-employees-section, .performance-stats {
  margin-bottom: 3rem;
}

.active-shifts-section h2, .inactive-employees-section h2, .performance-stats h2 {
  margin-bottom: 1.5rem;
  color: #2c3e50;
}

.shifts-grid, .employees-grid, .performance-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

.shift-card, .employee-card, .performance-card {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.shift-card:hover, .employee-card:hover, .performance-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.shift-card.active {
  border-left: 4px solid #27ae60;
}

.employee-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.employee-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3498db, #2980b9);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.employee-details h3 {
  margin: 0 0 0.25rem 0;
  color: #2c3e50;
}

.employee-details .role {
  color: #7f8c8d;
  font-size: 0.9rem;
  display: block;
  margin-bottom: 0.25rem;
}

.employee-details .status {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status.active {
  background: #d5f4e6;
  color: #27ae60;
}

.status.inactive {
  background: #fadbd8;
  color: #e74c3c;
}

.shift-stats {
  margin-bottom: 1.5rem;
}

.shift-stats .stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.shift-stats .label {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.shift-stats .value {
  font-weight: bold;
  color: #2c3e50;
}

.shift-actions, .employee-actions {
  display: flex;
  gap: 0.5rem;
}

.start-shift-btn, .end-shift-btn {
  flex: 1;
  padding: 0.75rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.start-shift-btn {
  background: #27ae60;
  color: white;
}

.start-shift-btn:hover:not(:disabled) {
  background: #229954;
}

.start-shift-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
}

.end-shift-btn {
  background: #e74c3c;
  color: white;
}

.end-shift-btn:hover {
  background: #c0392b;
}

.performance-card .employee-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.performance-card .employee-header i {
  font-size: 2rem;
  color: #3498db;
}

.performance-card .employee-header h3 {
  margin: 0;
  color: #2c3e50;
}

.performance-card .employee-header .role {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.performance-stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.perf-stat {
  text-align: center;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.perf-stat .number {
  display: block;
  font-size: 1.5rem;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.perf-stat .label {
  color: #7f8c8d;
  font-size: 0.8rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .manager-sidebar {
    width: 100%;
    top: 70px;
    height: calc(100vh - 70px);
  }

  .manager-sidebar.open ~ .manager-main {
    margin-right: 0;
  }

  .manager-main {
    padding: 1rem;
  }

  .header-content {
    padding: 0 1rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .orders-stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .orders-header {
    flex-direction: column;
    align-items: stretch;
  }

  .orders-filters {
    justify-content: center;
  }

  .filter-select {
    min-width: auto;
    flex: 1;
  }

  .waiter-stats-grid, .discount-requests-grid, .orders-grid {
    grid-template-columns: 1fr;
  }

  .shifts-grid, .employees-grid, .performance-grid {
    grid-template-columns: 1fr;
  }

  .performance-stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Tables Screen */
.tables-screen {
  padding: 2rem;
}

.tables-header h1 {
  margin: 0 0 2rem 0;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.tables-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.table-card {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.table-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.table-card.open {
  border-left: 4px solid #27ae60;
}

.table-card.closed {
  border-left: 4px solid #95a5a6;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.table-number {
  font-size: 1.2rem;
  font-weight: bold;
  color: #2c3e50;
}

.table-status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.table-status.active {
  background: #27ae60;
  color: white;
}

.table-status.closed {
  background: #95a5a6;
  color: white;
}

.table-info {
  margin-bottom: 1rem;
}

.table-info > div {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  color: #7f8c8d;
  font-size: 0.9rem;
}

.table-info i {
  width: 16px;
  text-align: center;
}

.table-orders h4 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 0.9rem;
}

.table-order {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 6px;
  margin-bottom: 0.25rem;
  font-size: 0.8rem;
}

.table-order .status {
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  font-size: 0.7rem;
}

.more-orders {
  text-align: center;
  color: #7f8c8d;
  font-size: 0.8rem;
  font-style: italic;
  margin-top: 0.5rem;
}

/* Reports Screen */
.reports-screen {
  padding: 2rem;
}

.reports-header h1 {
  margin: 0 0 2rem 0;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.sales-reports, .waiters-reports, .chefs-reports {
  margin-bottom: 3rem;
}

.sales-reports h2, .waiters-reports h2, .chefs-reports h2 {
  margin-bottom: 1.5rem;
  color: #2c3e50;
}

.sales-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.sales-stat-card {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1.5rem;
  transition: all 0.3s ease;
}

.sales-stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.sales-stat-card.today .stat-icon {
  background: linear-gradient(135deg, #f39c12, #e67e22);
}

.sales-stat-card.week .stat-icon {
  background: linear-gradient(135deg, #3498db, #2980b9);
}

.sales-stat-card.total .stat-icon {
  background: linear-gradient(135deg, #27ae60, #229954);
}

.sales-stat-card .stat-content h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.8rem;
  font-weight: 700;
  color: #2c3e50;
}

.sales-stat-card .stat-content p {
  margin: 0 0 0.5rem 0;
  color: #7f8c8d;
  font-size: 0.9rem;
}

.sales-stat-card .orders-count {
  color: #3498db;
  font-size: 0.8rem;
  font-weight: 500;
}

.waiters-stats-table, .chefs-stats-table {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.waiters-stats-table table, .chefs-stats-table table {
  width: 100%;
  border-collapse: collapse;
}

.waiters-stats-table th, .chefs-stats-table th {
  background: #2c3e50;
  color: white;
  padding: 1rem;
  text-align: right;
  font-weight: 600;
}

.waiters-stats-table td, .chefs-stats-table td {
  padding: 1rem;
  border-bottom: 1px solid #ecf0f1;
  color: #2c3e50;
}

.waiters-stats-table tr:hover, .chefs-stats-table tr:hover {
  background: #f8f9fa;
}

/* Inventory Screen */
.inventory-screen {
  padding: 2rem;
}

.inventory-header h1 {
  margin: 0 0 2rem 0;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.inventory-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.inventory-item {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.inventory-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.inventory-item.unavailable {
  opacity: 0.6;
  border-left: 4px solid #e74c3c;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.item-header h3 {
  margin: 0;
  color: #2c3e50;
}

.availability {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.availability.available {
  background: #27ae60;
  color: white;
}

.availability.unavailable {
  background: #e74c3c;
  color: white;
}

.item-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.price {
  font-size: 1.2rem;
  font-weight: bold;
  color: #27ae60;
}

.stock {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #7f8c8d;
  font-size: 0.9rem;
}

.stock.low {
  color: #e74c3c;
  font-weight: 500;
}

.stock.low i {
  color: #e74c3c;
}

.item-description {
  color: #7f8c8d;
  font-size: 0.9rem;
  line-height: 1.4;
}

/* Menu Screen */
.menu-screen {
  padding: 2rem;
}

.menu-header h1 {
  margin: 0 0 2rem 0;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.category-section {
  margin-bottom: 2rem;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.category-header {
  padding: 1.5rem;
  color: white;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.category-header h2 {
  margin: 0;
  flex: 1;
}

.items-count {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
}

.category-items {
  padding: 1.5rem;
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #ecf0f1;
  transition: all 0.3s ease;
}

.menu-item:hover {
  background: #f8f9fa;
}

.menu-item:last-child {
  border-bottom: none;
}

.item-info h3 {
  margin: 0 0 0.25rem 0;
  color: #2c3e50;
}

.item-info p {
  margin: 0;
  color: #7f8c8d;
  font-size: 0.9rem;
}

.item-price {
  font-size: 1.1rem;
  font-weight: bold;
  color: #27ae60;
  margin-left: 1rem;
}

.item-status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.item-status.available {
  background: #27ae60;
  color: white;
}

.item-status.unavailable {
  background: #e74c3c;
  color: white;
}

/* Categories Screen */
.categories-screen {
  padding: 2rem;
}

.categories-header h1 {
  margin: 0 0 2rem 0;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.category-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.category-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.category-card .category-header {
  padding: 1.5rem;
  color: white;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.category-card .category-header h3 {
  margin: 0;
}

.category-info {
  padding: 1.5rem;
}

.category-description {
  color: #7f8c8d;
  font-size: 0.9rem;
  line-height: 1.4;
  margin-bottom: 1rem;
}

.category-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.items-count {
  color: #3498db;
  font-weight: 500;
  font-size: 0.9rem;
}

.color-code {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-content {
  background: white;
  border-radius: 12px;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #ecf0f1;
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #7f8c8d;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: #f8f9fa;
  color: #2c3e50;
}

.modal-body {
  padding: 1.5rem;
}

.order-details {
  margin-bottom: 2rem;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #ecf0f1;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-row .label {
  color: #7f8c8d;
  font-weight: 500;
}

.detail-row .value {
  color: #2c3e50;
  font-weight: 600;
}

.detail-row .value.total {
  color: #27ae60;
  font-size: 1.2rem;
}

.order-items-details h4 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
}

.items-list {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
}

.item-detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #ecf0f1;
}

.item-detail:last-child {
  border-bottom: none;
}

.item-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.item-name {
  color: #2c3e50;
  font-weight: 500;
}

.item-quantity {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.item-price {
  color: #27ae60;
  font-weight: 600;
}

/* Enhanced Tables */
.employees-table-section, .products-table-section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.employees-table-section h2, .products-table-section h2 {
  margin: 0 0 1.5rem 0;
  color: #2c3e50;
}

.employees-table-container, .products-table-container {
  overflow-x: auto;
  border-radius: 8px;
  border: 1px solid #ecf0f1;
}

.employees-table, .products-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

.employees-table th, .products-table th {
  background: #2c3e50;
  color: white;
  padding: 1rem;
  text-align: right;
  font-weight: 600;
  border-bottom: 2px solid #34495e;
}

.employees-table td, .products-table td {
  padding: 1rem;
  border-bottom: 1px solid #ecf0f1;
  vertical-align: middle;
}

.employees-table tr:hover, .products-table tr:hover {
  background: #f8f9fa;
}

.employee-name, .product-name {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: #2c3e50;
}

.employee-name i, .product-name i {
  color: #3498db;
}

.role-badge, .availability-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.role-badge.waiter {
  background: #3498db;
  color: white;
}

.role-badge.chef {
  background: #e74c3c;
  color: white;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status-badge.active {
  background: #27ae60;
  color: white;
}

.status-badge.inactive {
  background: #95a5a6;
  color: white;
}

.availability-badge.available {
  background: #27ae60;
  color: white;
}

.availability-badge.unavailable {
  background: #e74c3c;
  color: white;
}

.shift-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.shift-active {
  color: #27ae60;
  font-weight: 500;
}

.shift-inactive {
  color: #95a5a6;
  font-style: italic;
}

.employee-actions, .product-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
}

.action-btn {
  padding: 0.5rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  min-width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn.edit {
  background: #3498db;
  color: white;
}

.action-btn.edit:hover {
  background: #2980b9;
}

.action-btn.toggle.activate {
  background: #27ae60;
  color: white;
}

.action-btn.toggle.activate:hover {
  background: #229954;
}

.action-btn.toggle.deactivate {
  background: #f39c12;
  color: white;
}

.action-btn.toggle.deactivate:hover {
  background: #e67e22;
}

.action-btn.start-shift {
  background: #27ae60;
  color: white;
}

.action-btn.start-shift:hover:not(:disabled) {
  background: #229954;
}

.action-btn.end-shift {
  background: #e74c3c;
  color: white;
}

.action-btn.end-shift:hover {
  background: #c0392b;
}

.action-btn.delete {
  background: #e74c3c;
  color: white;
}

.action-btn.delete:hover {
  background: #c0392b;
}

.action-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
  opacity: 0.6;
}

.product-categories {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.category-tag {
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  font-size: 0.7rem;
  color: white;
  font-weight: 500;
}

.price {
  font-weight: bold;
  color: #27ae60;
  font-size: 1.1rem;
}

.stock {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.stock.low {
  color: #e74c3c;
  font-weight: 500;
}

.stock.low i {
  color: #e74c3c;
}

/* Enhanced Buttons */
.add-employee-btn, .add-product-btn {
  background: linear-gradient(135deg, #27ae60, #229954);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(39, 174, 96, 0.3);
}

.add-employee-btn:hover, .add-product-btn:hover {
  background: linear-gradient(135deg, #229954, #1e8449);
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(39, 174, 96, 0.4);
}

/* Enhanced Modals */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
  backdrop-filter: blur(5px);
}

.modal-content {
  background: white;
  border-radius: 16px;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  border-bottom: 1px solid #ecf0f1;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.3rem;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.8rem;
  cursor: pointer;
  color: #7f8c8d;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: #e74c3c;
  color: white;
  transform: rotate(90deg);
}

.modal-body {
  padding: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #2c3e50;
  font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #ecf0f1;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.checkbox-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.checkbox-group input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.modal-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #ecf0f1;
}

.save-btn {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.save-btn:hover {
  background: linear-gradient(135deg, #2980b9, #21618c);
  transform: translateY(-1px);
}

.cancel-btn {
  background: #95a5a6;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.cancel-btn:hover {
  background: #7f8c8d;
}

/* Additional Responsive Design */
@media (max-width: 768px) {
  .tables-grid, .inventory-grid, .categories-grid {
    grid-template-columns: 1fr;
  }

  .sales-stats-grid {
    grid-template-columns: 1fr;
  }

  .employees-table-container, .products-table-container,
  .waiters-stats-table, .chefs-stats-table {
    overflow-x: auto;
  }

  .employees-table, .products-table {
    min-width: 800px;
  }

  .modal-content {
    margin: 1rem;
    max-width: calc(100% - 2rem);
  }

  .modal-header {
    padding: 1.5rem;
  }

  .modal-body {
    padding: 1.5rem;
  }

  .detail-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .item-detail {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .employee-actions, .product-actions {
    flex-wrap: wrap;
  }

  .action-btn {
    min-width: 30px;
    height: 30px;
    font-size: 0.8rem;
  }

  .employees-header, .menu-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .add-employee-btn, .add-product-btn {
    justify-content: center;
  }
}
