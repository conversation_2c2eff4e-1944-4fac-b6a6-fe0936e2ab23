import React, { useState, useEffect } from 'react';
import { authenticatedGet } from '../../utils/apiHelpers';
import { useToast } from '../../hooks/useToast';
import './ReportsScreen.css';

interface ReportData {
  dailySales: {
    date: string;
    totalSales: number;
    ordersCount: number;
    averageOrderValue: number;
  }[];
  weeklySales: {
    week: string;
    totalSales: number;
    ordersCount: number;
    averageOrderValue: number;
  }[];
  waiterStats: {
    waiterName: string;
    ordersCount: number;
    totalSales: number;
    averageOrderValue: number;
    workingHours: number;
  }[];
  chefStats: {
    chefName: string;
    ordersProcessed: number;
    averagePreparationTime: number;
    workingHours: number;
  }[];
  topItems: {
    itemName: string;
    quantitySold: number;
    totalRevenue: number;
  }[];
}

const ReportsScreen: React.FC = () => {
  const [reportData, setReportData] = useState<ReportData>({
    dailySales: [],
    weeklySales: [],
    waiterStats: [],
    chefStats: [],
    topItems: []
  });
  const [loading, setLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState('today');
  const [selectedReport, setSelectedReport] = useState('sales');

  const { showError, showSuccess, showInfo } = useToast();

  // جلب بيانات التقارير
  const fetchReportData = async () => {
    try {
      setLoading(true);
      
      // جلب بيانات المبيعات اليومية
      const dailySalesResponse = await authenticatedGet(`/api/reports/daily-sales?period=${selectedPeriod}`);
      
      // جلب بيانات المبيعات الأسبوعية
      const weeklySalesResponse = await authenticatedGet(`/api/reports/weekly-sales`);
      
      // جلب إحصائيات النادلين
      const waiterStatsResponse = await authenticatedGet(`/api/reports/waiter-stats?period=${selectedPeriod}`);
      
      // جلب إحصائيات الطباخين
      const chefStatsResponse = await authenticatedGet(`/api/reports/chef-stats?period=${selectedPeriod}`);
      
      // جلب أفضل الأصناف مبيعاً
      const topItemsResponse = await authenticatedGet(`/api/reports/top-items?period=${selectedPeriod}`);

      setReportData({
        dailySales: dailySalesResponse.data || [],
        weeklySales: weeklySalesResponse.data || [],
        waiterStats: waiterStatsResponse.data || [],
        chefStats: chefStatsResponse.data || [],
        topItems: topItemsResponse.data || []
      });

    } catch (error) {
      console.error('خطأ في جلب بيانات التقارير:', error);
      showError('فشل في تحميل التقارير');
      
      // بيانات تجريبية في حالة الخطأ
      setReportData({
        dailySales: [
          { date: '2024-01-15', totalSales: 1250.50, ordersCount: 45, averageOrderValue: 27.79 },
          { date: '2024-01-14', totalSales: 980.25, ordersCount: 38, averageOrderValue: 25.80 },
          { date: '2024-01-13', totalSales: 1450.75, ordersCount: 52, averageOrderValue: 27.90 }
        ],
        weeklySales: [
          { week: 'الأسبوع الحالي', totalSales: 8750.25, ordersCount: 315, averageOrderValue: 27.78 },
          { week: 'الأسبوع الماضي', totalSales: 7890.50, ordersCount: 289, averageOrderValue: 27.30 }
        ],
        waiterStats: [
          { waiterName: 'أحمد محمد', ordersCount: 125, totalSales: 3450.75, averageOrderValue: 27.61, workingHours: 40 },
          { waiterName: 'فاطمة علي', ordersCount: 98, totalSales: 2780.50, averageOrderValue: 28.37, workingHours: 35 },
          { waiterName: 'محمد حسن', ordersCount: 87, totalSales: 2398.25, averageOrderValue: 27.56, workingHours: 32 }
        ],
        chefStats: [
          { chefName: 'خالد أحمد', ordersProcessed: 245, averagePreparationTime: 8.5, workingHours: 42 },
          { chefName: 'سارة محمد', ordersProcessed: 198, averagePreparationTime: 7.8, workingHours: 38 }
        ],
        topItems: [
          { itemName: 'قهوة تركية', quantitySold: 145, totalRevenue: 2175.00 },
          { itemName: 'كابتشينو', quantitySold: 132, totalRevenue: 2640.00 },
          { itemName: 'شاي أحمر', quantitySold: 98, totalRevenue: 980.00 },
          { itemName: 'لاتيه', quantitySold: 87, totalRevenue: 1740.00 }
        ]
      });
    } finally {
      setLoading(false);
    }
  };

  // تصدير البيانات
  const exportData = (format: 'csv' | 'pdf') => {
    if (format === 'csv') {
      exportToCSV();
    } else if (format === 'pdf') {
      exportToPDF();
    }
  };

  // تصدير CSV
  const exportToCSV = () => {
    try {
      let csvContent = '';
      const currentDate = new Date().toLocaleDateString('ar-EG');

      switch (selectedReport) {
        case 'daily':
          csvContent = 'التاريخ,إجمالي المبيعات,عدد الطلبات,متوسط قيمة الطلب\n';
          reportData.dailySales.forEach(day => {
            csvContent += `${day.date},${day.totalSales},${day.ordersCount},${day.averageOrderValue}\n`;
          });
          downloadCSV(csvContent, `تقرير_المبيعات_اليومية_${currentDate}`);
          break;

        case 'weekly':
          csvContent = 'الأسبوع,إجمالي المبيعات,عدد الطلبات,متوسط قيمة الطلب\n';
          reportData.weeklySales.forEach(week => {
            csvContent += `${week.week},${week.totalSales},${week.ordersCount},${week.averageOrderValue}\n`;
          });
          downloadCSV(csvContent, `تقرير_المبيعات_الأسبوعية_${currentDate}`);
          break;

        case 'waiters':
          csvContent = 'اسم النادل,عدد الطلبات,إجمالي المبيعات,متوسط قيمة الطلب,ساعات العمل\n';
          reportData.waiterStats.forEach(waiter => {
            csvContent += `${waiter.waiterName},${waiter.ordersCount},${waiter.totalSales},${waiter.averageOrderValue},${waiter.workingHours}\n`;
          });
          downloadCSV(csvContent, `إحصائيات_النادلين_${currentDate}`);
          break;

        case 'chefs':
          csvContent = 'اسم الطباخ,الطلبات المُعالجة,متوسط وقت التحضير,ساعات العمل\n';
          reportData.chefStats.forEach(chef => {
            csvContent += `${chef.chefName},${chef.ordersProcessed},${chef.averagePreparationTime},${chef.workingHours}\n`;
          });
          downloadCSV(csvContent, `إحصائيات_الطباخين_${currentDate}`);
          break;

        case 'top-items':
          csvContent = 'اسم الصنف,الكمية المباعة,إجمالي الإيرادات,متوسط السعر\n';
          reportData.topItems.forEach(item => {
            csvContent += `${item.itemName},${item.quantitySold},${item.totalRevenue},${(item.totalRevenue / item.quantitySold).toFixed(2)}\n`;
          });
          downloadCSV(csvContent, `أفضل_الأصناف_مبيعاً_${currentDate}`);
          break;

        default:
          showError('نوع التقرير غير مدعوم للتصدير');
          return;
      }

      showSuccess('تم تصدير البيانات بنجاح');
    } catch (error) {
      console.error('خطأ في تصدير CSV:', error);
      showError('فشل في تصدير البيانات');
    }
  };

  // تنزيل ملف CSV
  const downloadCSV = (content: string, filename: string) => {
    const BOM = '\uFEFF'; // UTF-8 BOM for Arabic support
    const csvContent = BOM + content;
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `${filename}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  // تصدير PDF (مبسط)
  const exportToPDF = () => {
    showInfo('ميزة تصدير PDF ستكون متاحة قريباً');
  };

  // تحميل البيانات عند تغيير الفترة
  useEffect(() => {
    fetchReportData();
  }, [selectedPeriod]);

  // تحميل البيانات الأولية
  useEffect(() => {
    fetchReportData();
  }, []);

  if (loading) {
    return (
      <div className="reports-screen loading">
        <div className="loading-spinner">
          <i className="fas fa-spinner fa-spin"></i>
          <span>جاري تحميل التقارير...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="reports-screen">
      {/* Header */}
      <div className="reports-header">
        <div className="header-left">
          <h1>
            <i className="fas fa-chart-bar"></i>
            التقارير والإحصائيات
          </h1>
          <p>تقارير شاملة للمبيعات والأداء مع إمكانية التصدير</p>
        </div>
        
        <div className="header-actions">
          <button 
            className="export-btn csv"
            onClick={() => exportData('csv')}
          >
            <i className="fas fa-file-csv"></i>
            تصدير CSV
          </button>
          <button 
            className="export-btn pdf"
            onClick={() => exportData('pdf')}
          >
            <i className="fas fa-file-pdf"></i>
            تصدير PDF
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="reports-filters">
        <div className="filter-group">
          <label>الفترة الزمنية:</label>
          <select 
            value={selectedPeriod} 
            onChange={(e) => setSelectedPeriod(e.target.value)}
          >
            <option value="today">اليوم</option>
            <option value="week">هذا الأسبوع</option>
            <option value="month">هذا الشهر</option>
            <option value="quarter">هذا الربع</option>
            <option value="year">هذا العام</option>
          </select>
        </div>

        <div className="filter-group">
          <label>نوع التقرير:</label>
          <select 
            value={selectedReport} 
            onChange={(e) => setSelectedReport(e.target.value)}
          >
            <option value="sales">تقارير المبيعات</option>
            <option value="waiters">إحصائيات النادلين</option>
            <option value="chefs">إحصائيات الطباخين</option>
            <option value="items">أفضل الأصناف</option>
          </select>
        </div>

        <button 
          className="refresh-btn"
          onClick={fetchReportData}
          title="تحديث البيانات"
        >
          <i className="fas fa-sync-alt"></i>
          تحديث
        </button>
      </div>

      {/* Report Content */}
      <div className="reports-content">
        {selectedReport === 'sales' && (
          <div className="sales-reports">
            <div className="report-section">
              <h2>
                <i className="fas fa-chart-line"></i>
                المبيعات اليومية
              </h2>
              <div className="sales-table">
                <table>
                  <thead>
                    <tr>
                      <th>التاريخ</th>
                      <th>إجمالي المبيعات</th>
                      <th>عدد الطلبات</th>
                      <th>متوسط قيمة الطلب</th>
                    </tr>
                  </thead>
                  <tbody>
                    {reportData.dailySales.map((day, index) => (
                      <tr key={index}>
                        <td>{new Date(day.date).toLocaleDateString('ar-EG')}</td>
                        <td>{day.totalSales.toFixed(2)} ج.م</td>
                        <td>{day.ordersCount}</td>
                        <td>{day.averageOrderValue.toFixed(2)} ج.م</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            <div className="report-section">
              <h2>
                <i className="fas fa-calendar-week"></i>
                المبيعات الأسبوعية
              </h2>
              <div className="sales-table">
                <table>
                  <thead>
                    <tr>
                      <th>الأسبوع</th>
                      <th>إجمالي المبيعات</th>
                      <th>عدد الطلبات</th>
                      <th>متوسط قيمة الطلب</th>
                    </tr>
                  </thead>
                  <tbody>
                    {reportData.weeklySales.map((week, index) => (
                      <tr key={index}>
                        <td>{week.week}</td>
                        <td>{week.totalSales.toFixed(2)} ج.م</td>
                        <td>{week.ordersCount}</td>
                        <td>{week.averageOrderValue.toFixed(2)} ج.م</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {selectedReport === 'waiters' && (
          <div className="waiters-reports">
            <div className="report-section">
              <h2>
                <i className="fas fa-user-tie"></i>
                إحصائيات النادلين
              </h2>
              <div className="stats-table">
                <table>
                  <thead>
                    <tr>
                      <th>اسم النادل</th>
                      <th>عدد الطلبات</th>
                      <th>إجمالي المبيعات</th>
                      <th>متوسط قيمة الطلب</th>
                      <th>ساعات العمل</th>
                      <th>المبيعات/الساعة</th>
                    </tr>
                  </thead>
                  <tbody>
                    {reportData.waiterStats.map((waiter, index) => (
                      <tr key={index}>
                        <td>{waiter.waiterName}</td>
                        <td>{waiter.ordersCount}</td>
                        <td>{waiter.totalSales.toFixed(2)} ج.م</td>
                        <td>{waiter.averageOrderValue.toFixed(2)} ج.م</td>
                        <td>{waiter.workingHours} ساعة</td>
                        <td>{(waiter.totalSales / waiter.workingHours).toFixed(2)} ج.م</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {selectedReport === 'chefs' && (
          <div className="chefs-reports">
            <div className="report-section">
              <h2>
                <i className="fas fa-chef-hat"></i>
                إحصائيات الطباخين
              </h2>
              <div className="stats-table">
                <table>
                  <thead>
                    <tr>
                      <th>اسم الطباخ</th>
                      <th>الطلبات المعالجة</th>
                      <th>متوسط وقت التحضير</th>
                      <th>ساعات العمل</th>
                      <th>الطلبات/الساعة</th>
                    </tr>
                  </thead>
                  <tbody>
                    {reportData.chefStats.map((chef, index) => (
                      <tr key={index}>
                        <td>{chef.chefName}</td>
                        <td>{chef.ordersProcessed}</td>
                        <td>{chef.averagePreparationTime.toFixed(1)} دقيقة</td>
                        <td>{chef.workingHours} ساعة</td>
                        <td>{(chef.ordersProcessed / chef.workingHours).toFixed(1)}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {selectedReport === 'items' && (
          <div className="items-reports">
            <div className="report-section">
              <h2>
                <i className="fas fa-star"></i>
                أفضل الأصناف مبيعاً
              </h2>
              <div className="items-table">
                <table>
                  <thead>
                    <tr>
                      <th>اسم الصنف</th>
                      <th>الكمية المباعة</th>
                      <th>إجمالي الإيرادات</th>
                      <th>متوسط السعر</th>
                    </tr>
                  </thead>
                  <tbody>
                    {reportData.topItems.map((item, index) => (
                      <tr key={index}>
                        <td>{item.itemName}</td>
                        <td>{item.quantitySold}</td>
                        <td>{item.totalRevenue.toFixed(2)} ج.م</td>
                        <td>{(item.totalRevenue / item.quantitySold).toFixed(2)} ج.م</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ReportsScreen;
