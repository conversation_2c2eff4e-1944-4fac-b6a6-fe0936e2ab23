// لوحة النادل الجديدة - معاد تنظيمها باستخدام مبادئ البرمجة الكائنية
import React, { useEffect, useCallback } from 'react';
import { useToast } from './hooks/useToast';
import { notificationSound } from './utils/notificationSound';
import socket from './socket';
import { WaiterDataService } from './services/WaiterDataService';
import { WaiterStatsService } from './services/WaiterStatsService';
import { useWaiterState } from './components/waiter/useWaiterState';
import { TablesView } from './components/waiter/TablesView';
import { OrdersView } from './components/waiter/OrdersView';
import { MenuView } from './components/waiter/MenuView';
import { NewOrderView } from './components/waiter/NewOrderView';
import { NotificationsView } from './components/waiter/NotificationsView';
import type { NewOrderData } from './models/WaiterModels';
import './WaiterDashboard.css';
import './styles/waiter-dashboard-enhanced.css';

interface WaiterDashboardProps {
  user?: any;
  onLogout?: () => void;
}

export default function WaiterDashboard({ user: propUser, onLogout }: WaiterDashboardProps) {
  const { showSuccess, showError, showInfo } = useToast();
  
  // استخدام مدير الحالة المخصص
  const {    // الحالات
    currentView,
    loading,
    sidebarOpen,
    tableAccounts,
    orders,
    products: menuItems,
    categories,
    stats,
    notifications,
    filters,
    modalState,
    newOrder: newOrderData,
    selectedTable,
    setSelectedTable,

    // دوال التحديث
    setCurrentView,
    setLoading,
    setSidebarOpen,
    updateTableAccounts,
    updateOrders,
    updateProducts: updateMenuItems,
    updateCategories,
    updateStats,
    updateNotifications,
    updateModal,
    updateNewOrder: updateNewOrderData,
    resetNewOrder: resetNewOrderData,

    // دوال مساعدة
    addNotification,
    markNotificationAsRead,
    clearAllNotifications,
    updateFilter
  } = useWaiterState();

  // معلومات المستخدم
  const user = propUser || JSON.parse(localStorage.getItem('user') || '{}');
  const waiterName = user?.name || user?.username || 'النادل';
  const waiterId = user?._id || user?.id;

  // دوال جلب البيانات
  const fetchTableAccounts = useCallback(async () => {
    try {
      setLoading(true);
      const data = await WaiterDataService.fetchTableAccounts(waiterId);
      updateTableAccounts(data);
    } catch (error) {
      console.error('خطأ في جلب الطاولات:', error);
      showError('فشل في جلب بيانات الطاولات');
    } finally {
      setLoading(false);
    }
  }, [waiterId, updateTableAccounts, setLoading, showError]);

  const fetchOrders = useCallback(async () => {
    try {
      const data = await WaiterDataService.fetchOrders(waiterId);
      updateOrders(data);
    } catch (error) {
      console.error('خطأ في جلب الطلبات:', error);
      showError('فشل في جلب بيانات الطلبات');
    }
  }, [waiterId, updateOrders, showError]);

  const fetchMenuItems = useCallback(async () => {
    try {
      const data = await WaiterDataService.fetchMenuItems();
      updateMenuItems(data);
    } catch (error) {
      console.error('خطأ في جلب القائمة:', error);
      showError('فشل في جلب بيانات القائمة');
    }
  }, [updateMenuItems, showError]);

  const fetchCategories = useCallback(async () => {
    try {
      const data = await WaiterDataService.fetchCategories();
      updateCategories(data);
    } catch (error) {
      console.error('خطأ في جلب الفئات:', error);
      showError('فشل في جلب بيانات الفئات');
    }
  }, [updateCategories, showError]);

  // دالة جلب جميع البيانات
  const fetchAllData = useCallback(async () => {
    await Promise.all([
      fetchTableAccounts(),
      fetchOrders(),
      fetchMenuItems(),
      fetchCategories()
    ]);
  }, [fetchTableAccounts, fetchOrders, fetchMenuItems, fetchCategories]);

  // تحديث الإحصائيات عند تغيير البيانات
  useEffect(() => {
    const newStats = WaiterStatsService.calculateWaiterStats(tableAccounts, orders);
    updateStats(newStats);
  }, [tableAccounts, orders, updateStats]);

  // جلب البيانات عند تحميل المكون
  useEffect(() => {
    fetchAllData();
  }, [fetchAllData]);

  // إعداد Socket.IO للإشعارات
  useEffect(() => {
    // الاستماع لتحديثات الطلبات
    socket.on('order-updated', (orderData) => {
      console.log('📝 تحديث طلب:', orderData);
      fetchOrders();      addNotification({
        type: 'general',
        title: 'تحديث طلب',
        message: `تم تحديث الطلب #${orderData.orderNumber}`,
        orderId: orderData._id
      });
      notificationSound.playNotification();
    });

    // الاستماع لقبول الطلبات من الطباخ
    socket.on('order-ready', (orderData) => {
      console.log('🔔 طلب جاهز:', orderData);
      fetchOrders();      addNotification({
        type: 'order_ready',
        title: 'طلب جاهز',
        message: `الطلب #${orderData.orderNumber} جاهز للتقديم`,
        orderId: orderData._id,
        tableNumber: orderData.tableNumber
      });
      notificationSound.playNotification();
      showInfo(`الطلب #${orderData.orderNumber} جاهز للتقديم`);
    });

    return () => {
      socket.off('order-updated');
      socket.off('order-ready');
    };
  }, [fetchOrders, addNotification, showInfo]);

  // دالة تسجيل الخروج
  const handleLogout = () => {
    localStorage.removeItem('user');
    localStorage.removeItem('token');
    if (onLogout) {
      onLogout();
    } else {
      window.location.href = '/login';
    }
  };

  // دوال معالجة الأحداث
  const handleCreateOrder = async (orderData: any) => {
    try {
      setLoading(true);
      const result = await WaiterDataService.createOrder(orderData);
      
      if (result.success) {
        showSuccess('تم إنشاء الطلب بنجاح');
        resetNewOrderData();
        await fetchTableAccounts();
        await fetchOrders();
        setCurrentView('orders');
      } else {
        showError(result.message || 'فشل في إنشاء الطلب');
      }
    } catch (error) {
      console.error('خطأ في إنشاء الطلب:', error);
      showError('فشل في إنشاء الطلب');
    } finally {
      setLoading(false);
    }
  };

  const handleCompleteOrder = async (orderId: string) => {
    try {
      setLoading(true);
      const result = await WaiterDataService.completeOrder(orderId);
      
      if (result.success) {
        showSuccess('تم تسليم الطلب بنجاح');
        await fetchOrders();
        await fetchTableAccounts();
      } else {
        showError(result.message || 'فشل في تسليم الطلب');
      }
    } catch (error) {
      console.error('خطأ في تسليم الطلب:', error);
      showError('فشل في تسليم الطلب');
    } finally {
      setLoading(false);
    }
  };

  const handleRequestDiscount = async (orderId: string, discountData: any) => {
    try {
      setLoading(true);
      const result = await WaiterDataService.requestDiscount(orderId, discountData);
      
      if (result.success) {
        showSuccess('تم إرسال طلب الخصم');
      } else {
        showError(result.message || 'فشل في إرسال طلب الخصم');
      }
    } catch (error) {
      console.error('خطأ في طلب الخصم:', error);
      showError('فشل في إرسال طلب الخصم');
    } finally {
      setLoading(false);
    }
  };

  // رندر المكون الحالي
  const renderCurrentView = () => {
    if (loading && !tableAccounts.length) {
      return (
        <div className="loading-container">
          <div className="spinner"></div>
          <p>جاري تحميل البيانات...</p>
        </div>
      );
    }

    switch (currentView) {      case 'tables':
        return (
          <TablesView
            tables={tableAccounts}
            filters={filters}
            onFilterChange={updateFilter}
            onTableSelect={(table) => {
              setSelectedTable(table);
              updateNewOrderData('tableId', table._id);
              updateNewOrderData('tableNumber', table.tableNumber);
              setCurrentView('new-order');
            }}
            onNewOrder={(tableNumber) => {
              resetNewOrderData();
              updateNewOrderData('tableNumber', tableNumber);
              setCurrentView('new-order');
            }}
            onCloseTable={() => {/* سيتم تنفيذ هذا لاحقاً */}}
            onClearClosedTables={() => {/* سيتم تنفيذ هذا لاحقاً */}}
            onRefresh={fetchTableAccounts}
          />
        );        case 'orders':
        return (
          <OrdersView
            orders={orders}
            filters={filters}
            onFilterChange={updateFilter}
            onOrderSelect={() => {/* سيتم تنفيذ هذا لاحقاً */}}
            onOrderStatusUpdate={() => {/* سيتم تنفيذ هذا لاحقاً */}}
            onDiscountRequest={() => {/* سيتم تنفيذ هذا لاحقاً */}}
            onRefresh={fetchOrders}
          />
        );
      
      case 'menu':
        return (
          <MenuView
            onAddToCart={() => {/* سيتم تنفيذ هذا لاحقاً */}}
            cart={newOrderData.items}
            onUpdateCartQuantity={() => {/* سيتم تنفيذ هذا لاحقاً */}}
            onRemoveFromCart={() => {/* سيتم تنفيذ هذا لاحقاً */}}
            categoryFilter={filters.categoryFilter}
            onCategoryFilterChange={(categoryId) => updateFilter('categoryFilter', categoryId)}
            searchTerm={filters.searchTerm}
            onSearchChange={(search) => updateFilter('searchTerm', search)}
          />
        );
        case 'new-order':
        return (
          <NewOrderView
            cart={newOrderData.items}
            onUpdateCartQuantity={() => {/* سيتم تنفيذ هذا لاحقاً */}}
            onRemoveFromCart={() => {/* سيتم تنفيذ هذا لاحقاً */}}
            onClearCart={() => resetNewOrderData()}
            onSubmitOrder={async (orderData: NewOrderData) => {
              try {
                // سيتم تنفيذ هذا لاحقاً
                return true;
              } catch (error) {
                return false;
              }
            }}
            selectedTable={selectedTable}
            onTableSelect={setSelectedTable}
          />
        );
      
      case 'notifications':
        return (
          <NotificationsView
            notifications={notifications}
            onNotificationRead={() => {/* سيتم تنفيذ هذا لاحقاً */}}
            onCloseModal={() => setCurrentView('tables')}
          />
        );
        default:
        return (
          <TablesView
            tables={tableAccounts}
            filters={filters}
            onFilterChange={updateFilter}
            onTableSelect={(table) => {
              setSelectedTable(table);
              updateNewOrderData('tableId', table._id);
              updateNewOrderData('tableNumber', table.tableNumber);
              setCurrentView('new-order');
            }}
            onNewOrder={(tableNumber) => {
              resetNewOrderData();
              updateNewOrderData('tableNumber', tableNumber);
              setCurrentView('new-order');
            }}
            onCloseTable={() => {/* سيتم تنفيذ هذا لاحقاً */}}
            onClearClosedTables={() => {/* سيتم تنفيذ هذا لاحقاً */}}
            onRefresh={fetchTableAccounts}
          />
        );
    }
  };

  return (
    <div className="waiter-dashboard">
      {/* Header */}
      <header className="waiter-header">
        <div className="header-content">
          <div className="header-left">
            <button
              className="sidebar-toggle"
              onClick={() => setSidebarOpen(!sidebarOpen)}
            >
              ☰
            </button>
            <h1>لوحة النادل</h1>
          </div>
          <div className="header-right">
            <div className="notifications-badge" onClick={() => setCurrentView('notifications')}>
              <i className="fas fa-bell"></i>
              {notifications.filter(n => !n.isRead).length > 0 && (
                <span className="badge">{notifications.filter(n => !n.isRead).length}</span>
              )}
            </div>
            <span className="waiter-name">مرحباً، {waiterName}</span>
            <button className="logout-btn" onClick={handleLogout}>
              تسجيل الخروج
            </button>
          </div>
        </div>
      </header>

      <div className="dashboard-content">
        {/* Sidebar */}
        <aside className={`waiter-sidebar ${sidebarOpen ? 'open' : ''}`}>
          <div className="sidebar-content">
            <div className="waiter-profile">
              <div className="waiter-avatar">👨‍🍳</div>
              <h3>{waiterName}</h3>
              <p>نادل</p>
            </div>

            {/* إحصائيات سريعة */}
            <div className="quick-stats">
              <div className="stat-item">
                <i className="fas fa-table"></i>
                <span>{stats.openTables} طاولة مفتوحة</span>
              </div>
              <div className="stat-item">
                <i className="fas fa-shopping-cart"></i>
                <span>{stats.activeOrders} طلب نشط</span>
              </div>
              <div className="stat-item">
                <i className="fas fa-money-bill-wave"></i>
                <span>{stats.todaySales.toFixed(2)} ج.م</span>
              </div>
            </div>

            <nav className="waiter-nav">
              <button
                className={`nav-btn ${currentView === 'tables' ? 'active' : ''}`}
                onClick={() => setCurrentView('tables')}
              >
                <i className="fas fa-table"></i>
                الطاولات
              </button>
              
              <button
                className={`nav-btn ${currentView === 'orders' ? 'active' : ''}`}
                onClick={() => setCurrentView('orders')}
              >
                <i className="fas fa-list"></i>
                الطلبات
                {stats.readyOrders > 0 && (
                  <span className="nav-badge">{stats.readyOrders}</span>
                )}
              </button>
              
              <button
                className={`nav-btn ${currentView === 'menu' ? 'active' : ''}`}
                onClick={() => setCurrentView('menu')}
              >
                <i className="fas fa-coffee"></i>
                القائمة
              </button>
              
              <button
                className={`nav-btn ${currentView === 'new-order' ? 'active' : ''}`}
                onClick={() => setCurrentView('new-order')}
              >
                <i className="fas fa-plus"></i>
                طلب جديد
              </button>
              
              <button
                className={`nav-btn ${currentView === 'notifications' ? 'active' : ''}`}
                onClick={() => setCurrentView('notifications')}
              >
                <i className="fas fa-bell"></i>
                الإشعارات
                {notifications.filter(n => !n.isRead).length > 0 && (
                  <span className="nav-badge">{notifications.filter(n => !n.isRead).length}</span>
                )}
              </button>
            </nav>
          </div>
        </aside>

        {/* Main Content */}
        <main className="waiter-main">
          {renderCurrentView()}
        </main>
      </div>
    </div>
  );
}
