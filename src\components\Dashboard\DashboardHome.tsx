import React, { useState, useEffect } from 'react';
import { authenticatedGet } from '../../utils/apiHelpers';
import { useToast } from '../../hooks/useToast';
import socket from '../../socket';
import './DashboardHome.css';

interface DashboardStats {
  totalOrders: number;
  pendingOrders: number;
  preparingOrders: number;
  readyOrders: number;
  completedOrders: number;
  totalSales: number;
  activeTables: number;
  totalTables: number;
  currentShift: {
    isActive: boolean;
    startTime: string;
    duration: string;
    ordersCount: number;
    salesAmount: number;
  };
}

interface DashboardHomeProps {
  userRole: 'waiter' | 'chef';
  userName: string;
}

const DashboardHome: React.FC<DashboardHomeProps> = ({ userRole, userName }) => {
  const [stats, setStats] = useState<DashboardStats>({
    totalOrders: 0,
    pendingOrders: 0,
    preparingOrders: 0,
    readyOrders: 0,
    completedOrders: 0,
    totalSales: 0,
    activeTables: 0,
    totalTables: 0,
    currentShift: {
      isActive: false,
      startTime: '',
      duration: '00:00:00',
      ordersCount: 0,
      salesAmount: 0
    }
  });

  const [isConnected, setIsConnected] = useState(false);
  const [loading, setLoading] = useState(true);
  const { showSuccess, showError, showInfo } = useToast();

  // جلب الإحصائيات
  const fetchStats = async () => {
    try {
      setLoading(true);
      
      // جلب الطلبات
      const ordersResponse = await authenticatedGet('/api/orders');
      const orders = ordersResponse.data || [];
      
      // جلب الطاولات
      const tablesResponse = await authenticatedGet('/api/table-accounts');
      const tables = tablesResponse.data || [];
      
      // حساب الإحصائيات حسب الدور
      let userOrders = orders;
      let userTables = tables;
      
      if (userRole === 'waiter') {
        userOrders = orders.filter(order => order.waiterName === userName);
        userTables = tables.filter(table => table.waiterName === userName);
      } else if (userRole === 'chef') {
        // للطباخ: عرض جميع الطلبات
        userOrders = orders;
      }

      // حساب الإحصائيات
      const totalOrders = userOrders.length;
      const pendingOrders = userOrders.filter(o => o.status === 'pending').length;
      const preparingOrders = userOrders.filter(o => o.status === 'preparing').length;
      const readyOrders = userOrders.filter(o => o.status === 'ready').length;
      const completedOrders = userOrders.filter(o => o.status === 'completed').length;
      
      const totalSales = userOrders.reduce((sum, order) => sum + (order.totalPrice || 0), 0);
      const activeTables = userTables.filter(t => t.status === 'active').length;
      
      // حساب إحصائيات المناوبة الحالية
      const currentShift = {
        isActive: true, // سيتم تحديثها لاحقاً من API المناوبات
        startTime: new Date().toLocaleTimeString('ar-EG'),
        duration: '02:30:15', // سيتم حسابها لاحقاً
        ordersCount: totalOrders,
        salesAmount: totalSales
      };

      setStats({
        totalOrders,
        pendingOrders,
        preparingOrders,
        readyOrders,
        completedOrders,
        totalSales,
        activeTables,
        totalTables: tables.length,
        currentShift
      });

    } catch (error) {
      console.error('خطأ في جلب الإحصائيات:', error);
      showError('فشل في تحميل الإحصائيات');
    } finally {
      setLoading(false);
    }
  };

  // إعداد Socket.IO للتحديث الفوري
  useEffect(() => {
    if (socket) {
      socket.on('connect', () => {
        setIsConnected(true);
        console.log('🔗 متصل للتحديث الفوري - الشاشة الرئيسية');
      });

      socket.on('disconnect', () => {
        setIsConnected(false);
      });

      // تحديث الإحصائيات عند تحديث الطلبات
      socket.on('order-updated', () => {
        fetchStats();
      });

      socket.on('new-order', () => {
        fetchStats();
      });

      socket.on('table-updated', () => {
        fetchStats();
      });
    }

    return () => {
      if (socket) {
        socket.off('order-updated');
        socket.off('new-order');
        socket.off('table-updated');
      }
    };
  }, []);

  // تحميل البيانات الأولية
  useEffect(() => {
    fetchStats();
    
    // تحديث دوري كل دقيقة
    const interval = setInterval(fetchStats, 60000);
    return () => clearInterval(interval);
  }, [userRole, userName]);

  if (loading) {
    return (
      <div className="dashboard-home loading">
        <div className="loading-spinner">
          <i className="fas fa-spinner fa-spin"></i>
          <span>جاري تحميل الإحصائيات...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="dashboard-home">
      {/* Header */}
      <div className="dashboard-header">
        <div className="welcome-section">
          <h1>
            <i className={`fas ${userRole === 'waiter' ? 'fa-user-tie' : 'fa-chef-hat'}`}></i>
            مرحباً، {userName}
          </h1>
          <p className="role-badge">
            {userRole === 'waiter' ? 'نادل' : 'طباخ'}
          </p>
        </div>
        
        <div className="connection-indicator">
          <div className={`status ${isConnected ? 'connected' : 'disconnected'}`}>
            <i className={`fas ${isConnected ? 'fa-wifi' : 'fa-wifi-slash'}`}></i>
            <span>{isConnected ? 'متصل' : 'غير متصل'}</span>
          </div>
        </div>
      </div>

      {/* Current Shift Info */}
      <div className="current-shift">
        <div className="shift-header">
          <h2>
            <i className="fas fa-clock"></i>
            المناوبة الحالية
          </h2>
          <div className={`shift-status ${stats.currentShift.isActive ? 'active' : 'inactive'}`}>
            {stats.currentShift.isActive ? 'نشطة' : 'غير نشطة'}
          </div>
        </div>
        
        <div className="shift-details">
          <div className="shift-item">
            <i className="fas fa-play"></i>
            <span>بداية المناوبة: {stats.currentShift.startTime}</span>
          </div>
          <div className="shift-item">
            <i className="fas fa-stopwatch"></i>
            <span>المدة: {stats.currentShift.duration}</span>
          </div>
          <div className="shift-item">
            <i className="fas fa-shopping-cart"></i>
            <span>الطلبات: {stats.currentShift.ordersCount}</span>
          </div>
          <div className="shift-item">
            <i className="fas fa-money-bill-wave"></i>
            <span>المبيعات: {stats.currentShift.salesAmount.toFixed(2)} ج.م</span>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="quick-stats">
        <div className="stats-grid">
          <div className="stat-card orders">
            <div className="stat-icon">
              <i className="fas fa-shopping-cart"></i>
            </div>
            <div className="stat-content">
              <h3>{stats.totalOrders}</h3>
              <p>إجمالي الطلبات</p>
            </div>
          </div>

          <div className="stat-card pending">
            <div className="stat-icon">
              <i className="fas fa-clock"></i>
            </div>
            <div className="stat-content">
              <h3>{stats.pendingOrders}</h3>
              <p>قيد الانتظار</p>
            </div>
          </div>

          <div className="stat-card preparing">
            <div className="stat-icon">
              <i className="fas fa-fire"></i>
            </div>
            <div className="stat-content">
              <h3>{stats.preparingOrders}</h3>
              <p>قيد التحضير</p>
            </div>
          </div>

          <div className="stat-card ready">
            <div className="stat-icon">
              <i className="fas fa-check-circle"></i>
            </div>
            <div className="stat-content">
              <h3>{stats.readyOrders}</h3>
              <p>جاهزة</p>
            </div>
          </div>

          <div className="stat-card sales">
            <div className="stat-icon">
              <i className="fas fa-money-bill-wave"></i>
            </div>
            <div className="stat-content">
              <h3>{stats.totalSales.toFixed(2)}</h3>
              <p>إجمالي المبيعات (ج.م)</p>
            </div>
          </div>

          {userRole === 'waiter' && (
            <div className="stat-card tables">
              <div className="stat-icon">
                <i className="fas fa-table"></i>
              </div>
              <div className="stat-content">
                <h3>{stats.activeTables}/{stats.totalTables}</h3>
                <p>الطاولات النشطة</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="quick-actions">
        <h2>
          <i className="fas fa-bolt"></i>
          إجراءات سريعة
        </h2>
        
        <div className="actions-grid">
          {userRole === 'waiter' ? (
            <>
              <button className="action-btn new-order">
                <i className="fas fa-plus"></i>
                <span>طلب جديد</span>
              </button>
              <button className="action-btn tables">
                <i className="fas fa-table"></i>
                <span>إدارة الطاولات</span>
              </button>
              <button className="action-btn menu">
                <i className="fas fa-coffee"></i>
                <span>قائمة المشروبات</span>
              </button>
            </>
          ) : (
            <>
              <button className="action-btn pending-orders">
                <i className="fas fa-clock"></i>
                <span>الطلبات المعلقة</span>
              </button>
              <button className="action-btn preparing-orders">
                <i className="fas fa-fire"></i>
                <span>قيد التحضير</span>
              </button>
              <button className="action-btn ready-orders">
                <i className="fas fa-check"></i>
                <span>الطلبات الجاهزة</span>
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default DashboardHome;
