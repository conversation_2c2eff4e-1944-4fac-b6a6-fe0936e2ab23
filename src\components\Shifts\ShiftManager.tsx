import React, { useState, useEffect } from 'react';
import { authenticatedGet, authenticatedPost, authenticatedPut } from '../../utils/apiHelpers';
import { useToast } from '../../hooks/useToast';
import socket from '../../socket';
import './ShiftManager.css';

interface Shift {
  _id: string;
  employeeId: string;
  employeeName: string;
  role: 'waiter' | 'chef';
  startTime: string;
  endTime?: string;
  duration?: string;
  status: 'active' | 'completed';
  ordersCount: number;
  salesAmount: number;
  createdAt: string;
}

interface Employee {
  _id: string;
  username: string;
  role: 'waiter' | 'chef';
  isActive: boolean;
  currentShift?: Shift;
}

const ShiftManager: React.FC = () => {
  const [shifts, setShifts] = useState<Shift[]>([]);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null);
  const [showStartShiftModal, setShowStartShiftModal] = useState(false);
  const [showEndShiftModal, setShowEndShiftModal] = useState(false);
  const { showSuccess, showError, showInfo } = useToast();

  // جلب المناوبات
  const fetchShifts = async () => {
    try {
      const response = await authenticatedGet('/api/shifts');
      setShifts(response.data || []);
    } catch (error) {
      console.error('خطأ في جلب المناوبات:', error);
      showError('فشل في تحميل المناوبات');
    }
  };

  // جلب الموظفين
  const fetchEmployees = async () => {
    try {
      const response = await authenticatedGet('/api/employees');
      const employeesData = response.data || [];
      
      // ربط المناوبات النشطة بالموظفين
      const employeesWithShifts = employeesData.map((employee: Employee) => {
        const activeShift = shifts.find(shift => 
          shift.employeeId === employee._id && shift.status === 'active'
        );
        return {
          ...employee,
          currentShift: activeShift
        };
      });
      
      setEmployees(employeesWithShifts);
    } catch (error) {
      console.error('خطأ في جلب الموظفين:', error);
      showError('فشل في تحميل الموظفين');
    }
  };

  // بدء مناوبة
  const startShift = async (employee: Employee) => {
    try {
      const shiftData = {
        employeeId: employee._id,
        employeeName: employee.username,
        role: employee.role,
        startTime: new Date().toISOString(),
        status: 'active',
        ordersCount: 0,
        salesAmount: 0
      };

      const response = await authenticatedPost('/api/shifts', shiftData);
      
      if (response.success) {
        showSuccess(`تم بدء مناوبة ${employee.username} بنجاح`);
        
        // تحديث حالة الموظف
        await authenticatedPut(`/api/employees/${employee._id}`, {
          isActive: true,
          currentShiftId: response.data._id
        });
        
        // إرسال إشعار Socket.IO
        if (socket) {
          socket.emit('shift-started', {
            employeeId: employee._id,
            employeeName: employee.username,
            role: employee.role,
            shiftId: response.data._id
          });
        }
        
        fetchShifts();
        fetchEmployees();
        setShowStartShiftModal(false);
      }
    } catch (error) {
      console.error('خطأ في بدء المناوبة:', error);
      showError('فشل في بدء المناوبة');
    }
  };

  // إنهاء مناوبة
  const endShift = async (employee: Employee) => {
    try {
      if (!employee.currentShift) {
        showError('لا توجد مناوبة نشطة لهذا الموظف');
        return;
      }

      const endTime = new Date().toISOString();
      const startTime = new Date(employee.currentShift.startTime);
      const duration = calculateDuration(startTime, new Date(endTime));

      // جلب إحصائيات المناوبة
      const ordersResponse = await authenticatedGet(`/api/orders?employeeId=${employee._id}&shiftId=${employee.currentShift._id}`);
      const orders = ordersResponse.data || [];
      
      const ordersCount = orders.length;
      const salesAmount = orders.reduce((sum: number, order: any) => sum + (order.totalPrice || 0), 0);

      const updateData = {
        endTime,
        duration,
        status: 'completed',
        ordersCount,
        salesAmount
      };

      const response = await authenticatedPut(`/api/shifts/${employee.currentShift._id}`, updateData);
      
      if (response.success) {
        showSuccess(`تم إنهاء مناوبة ${employee.username} بنجاح`);
        
        // تحديث حالة الموظف
        await authenticatedPut(`/api/employees/${employee._id}`, {
          isActive: false,
          currentShiftId: null
        });
        
        // إرسال إشعار Socket.IO
        if (socket) {
          socket.emit('shift-ended', {
            employeeId: employee._id,
            employeeName: employee.username,
            role: employee.role,
            shiftId: employee.currentShift._id,
            duration,
            ordersCount,
            salesAmount
          });
        }
        
        fetchShifts();
        fetchEmployees();
        setShowEndShiftModal(false);
      }
    } catch (error) {
      console.error('خطأ في إنهاء المناوبة:', error);
      showError('فشل في إنهاء المناوبة');
    }
  };

  // حساب مدة المناوبة
  const calculateDuration = (startTime: Date, endTime: Date): string => {
    const diffMs = endTime.getTime() - startTime.getTime();
    const hours = Math.floor(diffMs / (1000 * 60 * 60));
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((diffMs % (1000 * 60)) / 1000);
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  // حساب مدة المناوبة النشطة
  const getActiveDuration = (startTime: string): string => {
    const start = new Date(startTime);
    const now = new Date();
    return calculateDuration(start, now);
  };

  // تحميل البيانات الأولية
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await fetchShifts();
      await fetchEmployees();
      setLoading(false);
    };

    loadData();
  }, []);

  // تحديث المدة كل 30 ثانية بدلاً من كل ثانية (تحسين الأداء)
  useEffect(() => {
    const interval = setInterval(() => {
      setEmployees(prev => [...prev]); // إعادة رندر لتحديث المدة
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <div className="shift-manager loading">
        <div className="loading-spinner">
          <i className="fas fa-spinner fa-spin"></i>
          <span>جاري تحميل المناوبات...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="shift-manager">
      <div className="shift-header">
        <h1>
          <i className="fas fa-clock"></i>
          إدارة المناوبات
        </h1>
        <p>إدارة مناوبات الموظفين وتتبع الأداء</p>
      </div>

      {/* Active Shifts */}
      <div className="active-shifts">
        <h2>
          <i className="fas fa-play-circle"></i>
          المناوبات النشطة
        </h2>
        
        <div className="shifts-grid">
          {employees.filter(emp => emp.currentShift).map(employee => (
            <div key={employee._id} className="shift-card active">
              <div className="employee-info">
                <div className="employee-avatar">
                  <i className={`fas ${employee.role === 'waiter' ? 'fa-user-tie' : 'fa-chef-hat'}`}></i>
                </div>
                <div className="employee-details">
                  <h3>{employee.username}</h3>
                  <span className="role">{employee.role === 'waiter' ? 'نادل' : 'طباخ'}</span>
                </div>
              </div>
              
              <div className="shift-info">
                <div className="info-item">
                  <i className="fas fa-play"></i>
                  <span>بداية: {new Date(employee.currentShift!.startTime).toLocaleTimeString('ar-EG')}</span>
                </div>
                <div className="info-item">
                  <i className="fas fa-stopwatch"></i>
                  <span>المدة: {getActiveDuration(employee.currentShift!.startTime)}</span>
                </div>
                <div className="info-item">
                  <i className="fas fa-shopping-cart"></i>
                  <span>الطلبات: {employee.currentShift!.ordersCount}</span>
                </div>
                <div className="info-item">
                  <i className="fas fa-money-bill-wave"></i>
                  <span>المبيعات: {employee.currentShift!.salesAmount.toFixed(2)} ج.م</span>
                </div>
              </div>
              
              <button 
                className="end-shift-btn"
                onClick={() => {
                  setSelectedEmployee(employee);
                  setShowEndShiftModal(true);
                }}
              >
                <i className="fas fa-stop"></i>
                إنهاء المناوبة
              </button>
            </div>
          ))}
        </div>
        
        {employees.filter(emp => emp.currentShift).length === 0 && (
          <div className="no-active-shifts">
            <i className="fas fa-clock"></i>
            <p>لا توجد مناوبات نشطة حالياً</p>
          </div>
        )}
      </div>

      {/* Available Employees */}
      <div className="available-employees">
        <h2>
          <i className="fas fa-users"></i>
          الموظفون المتاحون
        </h2>
        
        <div className="employees-grid">
          {employees.filter(emp => !emp.currentShift).map(employee => (
            <div key={employee._id} className="employee-card">
              <div className="employee-info">
                <div className="employee-avatar">
                  <i className={`fas ${employee.role === 'waiter' ? 'fa-user-tie' : 'fa-chef-hat'}`}></i>
                </div>
                <div className="employee-details">
                  <h3>{employee.username}</h3>
                  <span className="role">{employee.role === 'waiter' ? 'نادل' : 'طباخ'}</span>
                </div>
              </div>
              
              <button 
                className="start-shift-btn"
                onClick={() => {
                  setSelectedEmployee(employee);
                  setShowStartShiftModal(true);
                }}
              >
                <i className="fas fa-play"></i>
                بدء المناوبة
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* Start Shift Modal */}
      {showStartShiftModal && selectedEmployee && (
        <div className="modal-overlay" onClick={() => setShowStartShiftModal(false)}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>
                <i className="fas fa-play"></i>
                بدء مناوبة جديدة
              </h2>
              <button 
                className="modal-close"
                onClick={() => setShowStartShiftModal(false)}
              >
                <i className="fas fa-times"></i>
              </button>
            </div>
            
            <div className="modal-body">
              <p>هل تريد بدء مناوبة جديدة للموظف:</p>
              <div className="employee-preview">
                <i className={`fas ${selectedEmployee.role === 'waiter' ? 'fa-user-tie' : 'fa-chef-hat'}`}></i>
                <span>{selectedEmployee.username}</span>
                <span className="role">({selectedEmployee.role === 'waiter' ? 'نادل' : 'طباخ'})</span>
              </div>
              
              <div className="shift-effects">
                <h3>سيتم تفعيل:</h3>
                <ul>
                  <li><i className="fas fa-check"></i> تسجيل الدخول للموظف</li>
                  <li><i className="fas fa-check"></i> بدء حساب الطلبات والمبيعات</li>
                  <li><i className="fas fa-check"></i> بدء حساب ساعات العمل</li>
                  <li><i className="fas fa-check"></i> تفعيل الصلاحيات</li>
                </ul>
              </div>
            </div>
            
            <div className="modal-footer">
              <button 
                className="btn-confirm"
                onClick={() => startShift(selectedEmployee)}
              >
                <i className="fas fa-play"></i>
                بدء المناوبة
              </button>
              <button 
                className="btn-cancel"
                onClick={() => setShowStartShiftModal(false)}
              >
                إلغاء
              </button>
            </div>
          </div>
        </div>
      )}

      {/* End Shift Modal */}
      {showEndShiftModal && selectedEmployee && (
        <div className="modal-overlay" onClick={() => setShowEndShiftModal(false)}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>
                <i className="fas fa-stop"></i>
                إنهاء المناوبة
              </h2>
              <button 
                className="modal-close"
                onClick={() => setShowEndShiftModal(false)}
              >
                <i className="fas fa-times"></i>
              </button>
            </div>
            
            <div className="modal-body">
              <p>هل تريد إنهاء مناوبة الموظف:</p>
              <div className="employee-preview">
                <i className={`fas ${selectedEmployee.role === 'waiter' ? 'fa-user-tie' : 'fa-chef-hat'}`}></i>
                <span>{selectedEmployee.username}</span>
                <span className="role">({selectedEmployee.role === 'waiter' ? 'نادل' : 'طباخ'})</span>
              </div>
              
              {selectedEmployee.currentShift && (
                <div className="shift-summary">
                  <h3>ملخص المناوبة:</h3>
                  <div className="summary-item">
                    <span>مدة المناوبة:</span>
                    <span>{getActiveDuration(selectedEmployee.currentShift.startTime)}</span>
                  </div>
                  <div className="summary-item">
                    <span>عدد الطلبات:</span>
                    <span>{selectedEmployee.currentShift.ordersCount}</span>
                  </div>
                  <div className="summary-item">
                    <span>إجمالي المبيعات:</span>
                    <span>{selectedEmployee.currentShift.salesAmount.toFixed(2)} ج.م</span>
                  </div>
                </div>
              )}
              
              <div className="shift-effects">
                <h3>سيتم:</h3>
                <ul>
                  <li><i className="fas fa-check"></i> إنهاء تسجيل الدخول</li>
                  <li><i className="fas fa-check"></i> حفظ إحصائيات المناوبة</li>
                  <li><i className="fas fa-check"></i> تصفير العدادات</li>
                  <li><i className="fas fa-check"></i> إلغاء الصلاحيات</li>
                </ul>
              </div>
            </div>
            
            <div className="modal-footer">
              <button 
                className="btn-confirm end"
                onClick={() => endShift(selectedEmployee)}
              >
                <i className="fas fa-stop"></i>
                إنهاء المناوبة
              </button>
              <button 
                className="btn-cancel"
                onClick={() => setShowEndShiftModal(false)}
              >
                إلغاء
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ShiftManager;
