import { useNavigate } from 'react-router-dom';
import './Sidebar.css';

interface SidebarProps {
  userRole: 'waiter' | 'chef';
  userName: string;
  currentScreen?: string;
  onScreenChange?: (screen: string) => void;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
}

export default function Sidebar({ 
  userRole, 
  userName, 
  currentScreen, 
  onScreenChange, 
  isCollapsed = false, 
  onToggleCollapse 
}: SidebarProps) {
  const navigate = useNavigate();
  const handleLogout = () => {
    if (confirm('هل تريد تسجيل الخروج؟')) {
      localStorage.removeItem('user');
      localStorage.removeItem('username');
      localStorage.removeItem('userRole');
      localStorage.removeItem('waiterSession');
      localStorage.removeItem('token');
      navigate('/', { replace: true });
    }
  };

  const waiterMenuItems = [
    { id: 'drinks', label: 'إضافة طلب', icon: 'fas fa-plus-circle' },
    { id: 'orders', label: 'الطلبات الحالية', icon: 'fas fa-list-ul' },
  ];

  const chefMenuItems = [
    { id: 'pending', label: 'الطلبات المعلقة', icon: 'fas fa-clock' },
    { id: 'completed', label: 'الطلبات المكتملة', icon: 'fas fa-check-circle' },
  ];

  const menuItems = userRole === 'waiter' ? waiterMenuItems : chefMenuItems;

  return (
    <div className={`sidebar ${isCollapsed ? 'collapsed' : ''}`}>
      {/* Header */}
      <div className="sidebar-header">
        <div className="sidebar-logo">
          <i className="fas fa-coffee"></i>
          {!isCollapsed && <span>مقهى النجمة</span>}
        </div>
        {onToggleCollapse && (
          <button className="sidebar-toggle" onClick={onToggleCollapse}>
            <i className={`fas fa-${isCollapsed ? 'expand' : 'compress'}-arrows-alt`}></i>
          </button>
        )}
      </div>

      {/* User Info */}
      <div className="sidebar-user">
        <div className="user-avatar">
          <i className={`fas fa-${userRole === 'waiter' ? 'user-tie' : 'chef-hat'}`}></i>
        </div>        {!isCollapsed && (
          <div className="user-info">
            <div className="user-name">{userName}</div>
            <div className="user-role">{userRole === 'waiter' ? 'نادل' : 'طباخ'}</div>
          </div>
        )}
      </div>

      {/* Navigation Menu */}
      <nav className="sidebar-nav">
        <ul>
          {menuItems.map(item => (
            <li key={item.id}>
              <button
                className={`nav-button ${currentScreen === item.id ? 'active' : ''}`}
                onClick={() => onScreenChange?.(item.id)}
                title={isCollapsed ? item.label : ''}
              >
                <i className={item.icon}></i>
                {!isCollapsed && <span>{item.label}</span>}
              </button>
            </li>
          ))}
        </ul>
      </nav>

      {/* Stats Section */}
      {!isCollapsed && (
        <div className="sidebar-stats">
          <div className="stats-title">الإحصائيات اليومية</div>
          <div className="stats-item">
            <i className="fas fa-chart-line"></i>
            <span>المبيعات: تحديث...</span>
          </div>
          <div className="stats-item">
            <i className="fas fa-tasks"></i>
            <span>الطلبات: تحديث...</span>
          </div>
        </div>
      )}

      {/* Logout Button */}
      <div className="sidebar-footer">
        <button className="logout-button" onClick={handleLogout} title="تسجيل الخروج">
          <i className="fas fa-sign-out-alt"></i>
          {!isCollapsed && <span>تسجيل الخروج</span>}
        </button>
      </div>
    </div>
  );
}
