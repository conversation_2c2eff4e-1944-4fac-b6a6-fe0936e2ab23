import React, { useState, useEffect } from 'react';
import { useToast } from './hooks/useToast';
import { ToastContainer } from './components/Toast';
import { authenticatedGet, authenticatedPost, authenticatedPut, authenticatedDelete, handleApiError } from './utils/apiHelpers';

// Add CSS animations
const styles = `
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(30px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.textContent = styles;
  document.head.appendChild(styleSheet);
}

interface Employee {
  _id?: string;
  name?: string;
  displayName?: string;
  username?: string;
  email?: string;
  role: 'waiter' | 'chef' | 'manager';
  roleArabic?: 'نادل' | 'طباخ' | 'مدير';
  password?: string;
  active: boolean;
  status?: 'active' | 'inactive' | 'suspended';
  createdAt?: string;
  lastLogin?: string;
}

export default function Employees() {
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [username, setUsername] = useState('');
  const [displayName, setDisplayName] = useState('');
  const [role, setRole] = useState<'waiter' | 'chef' | 'manager'>('waiter');
  const [roleArabic, setRoleArabic] = useState<'نادل' | 'طباخ' | 'مدير'>('نادل');
  const [password, setPassword] = useState('');
  const [editId, setEditId] = useState<string|null>(null);
  const [editData, setEditData] = useState<Partial<Employee>>({});
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterRole, setFilterRole] = useState<'all' | 'waiter' | 'chef' | 'manager'>('all');
  const [showAddForm, setShowAddForm] = useState(false);
  const { toasts, removeToast, showSuccess, showError } = useToast();

  // Role conversion functions
  const roleToEnglish = (arabicRole: string): string => {
    switch (arabicRole) {
      case 'نادل': return 'waiter';
      case 'طباخ': return 'chef';
      case 'مدير': return 'manager';
      default: return 'waiter';
    }
  };

  const roleToArabic = (englishRole: string): string => {
    switch (englishRole) {
      case 'waiter': return 'نادل';
      case 'chef': return 'طباخ';
      case 'manager': return 'مدير';
      default: return 'نادل';
    }
  };

  // جلب الموظفين من قاعدة البيانات عند التحميل
  useEffect(() => {
    fetchEmployees();
  }, []);  const fetchEmployees = async () => {
    try {
      const response = await authenticatedGet('/api/users');
      // Handle both direct array response and wrapped response
      let employeesData: any[] = [];
      if (Array.isArray(response)) {
        employeesData = response;
      } else if (response && response.data && Array.isArray(response.data)) {
        employeesData = response.data;
      } else if (response && response.success && Array.isArray(response.data)) {
        employeesData = response.data;
      }

      // Convert roles and status for frontend display
      const processedEmployees: Employee[] = employeesData.map((emp: any) => ({
        ...emp,
        role: emp.role, // Keep English role for API calls
        roleArabic: roleToArabic(emp.role), // Add Arabic role for display
        active: emp.status === 'active', // Convert status to active boolean
        displayName: emp.name || emp.username
      }));

      setEmployees(processedEmployees);
    } catch (error) {
      console.error('خطأ في جلب الموظفين:', error);
      const errorMessage = handleApiError(error);
      showError(errorMessage);
      setEmployees([]);
    }
  };
  // Get role icon
  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'نادل':
      case 'waiter': return 'fa-user-tie';
      case 'طباخ':
      case 'chef': return 'fa-utensils';
      case 'مدير':
      case 'manager': return 'fa-user-shield';
      default: return 'fa-user';
    }
  };

  // Get role color
  const getRoleColor = (role: string) => {
    switch (role) {
      case 'نادل':
      case 'waiter': return '#2196f3';
      case 'طباخ':
      case 'chef': return '#ff9800';
      case 'مدير':
      case 'manager': return '#4caf50';
      default: return '#666';
    }
  };
  // Filter employees
  const filteredEmployees = employees.filter(emp => {
    const matchesSearch = searchTerm === '' ||
      (emp.name && emp.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (emp.displayName && emp.displayName.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (emp.username && emp.username.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesRole = filterRole === 'all' || emp.role === filterRole;

    return matchesSearch && matchesRole;
  });  // إضافة موظف جديد
  const handleAdd = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!username || !password || !displayName) {
      showError('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    setLoading(true);
    try {
      const employeeData = { 
        username, 
        name: displayName, // Use displayName as name
        email: `${username}@deshacoffee.com`, 
        role: role, // Send English role to backend
        password, 
        status: 'active' // Use status instead of active
      };
      const response = await authenticatedPost('/api/users', employeeData);
      // Handle both direct response and wrapped response
      const savedEmployee = response.data || response;
      
      // Add display properties for frontend
      const processedEmployee = {
        ...savedEmployee,
        roleArabic: roleToArabic(savedEmployee.role),
        active: savedEmployee.status === 'active',
        displayName: savedEmployee.name
      };
      
      setEmployees([...employees, processedEmployee]);
      setUsername('');
      setDisplayName('');
      setRole('waiter');
      setRoleArabic('نادل');
      setPassword('');
      setShowAddForm(false);
      showSuccess('تم إضافة الموظف بنجاح!');
    } catch (error) {
      const errorMessage = handleApiError(error);
      showError(errorMessage);
    }
    setLoading(false);
  };
  // حذف موظف
  const handleDelete = async (id: string) => {
    const confirmDelete = window.confirm('هل أنت متأكد من حذف هذا الموظف؟');
    if (!confirmDelete) return;    setLoading(true);
    try {
      await authenticatedDelete(`/api/users/${id}`);
      setEmployees(employees => employees.filter(emp => emp._id !== id));
      showSuccess('تم حذف الموظف بنجاح!');
    } catch (error) {
      const errorMessage = handleApiError(error);
      showError(errorMessage);
    }
    setLoading(false);
  };  // تفعيل/تعطيل موظف
  const handleToggle = async (id: string) => {
    const emp = employees.find(e => e._id === id);
    if (!emp) return;

    setLoading(true);
    try {
      const newStatus = emp.active ? 'inactive' : 'active';
      const updatedData = { status: newStatus };
      await authenticatedPut(`/api/users/${id}`, updatedData);
      
      setEmployees(employees => employees.map(e => 
        e._id === id ? { ...e, active: !e.active, status: newStatus } : e
      ));
      showSuccess(`تم ${!emp.active ? 'تفعيل' : 'تعطيل'} الموظف بنجاح!`);
    } catch (error) {
      const errorMessage = handleApiError(error);
      showError(errorMessage);
    }
    setLoading(false);
  };
  // فتح نافذة التعديل
  const handleEdit = (emp: Employee) => {
    setEditId(emp._id!);
    setEditData({ 
      ...emp, 
      username: emp.username || emp.name || '',
      name: emp.displayName || emp.name || '',
      role: emp.role // Keep English role for API
    });
  };

  // حفظ التعديلات
  const handleSaveEdit = async () => {
    if (!editId) return;

    setLoading(true);
    try {
      // Prepare data for backend (convert to backend format)
      const backendData = {
        ...editData,
        status: editData.active ? 'active' : 'inactive'
      };
      delete backendData.active; // Remove frontend-specific field
      delete backendData.roleArabic; // Remove frontend-specific field
      delete backendData.displayName; // Remove frontend-specific field

      await authenticatedPut(`/api/users/${editId}`, backendData);
      
      // Update frontend state with display properties
      const updatedEmployee = {
        ...editData,
        roleArabic: roleToArabic(editData.role || 'waiter'),
        displayName: editData.name,
        status: editData.active ? 'active' : 'inactive'
      };
      
      setEmployees(employees => employees.map(emp => 
        emp._id === editId ? { ...emp, ...updatedEmployee } as Employee : emp
      ));
      setEditId(null);
      setEditData({});
      showSuccess('تم تحديث بيانات الموظف بنجاح!');
    } catch (error) {
      const errorMessage = handleApiError(error);
      showError(errorMessage);
    }
    setLoading(false);
  };

  return (
    <div style={{
      direction: 'rtl',
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
      padding: '2rem',
      position: 'relative'
    }}>
      {/* Background Pattern */}
      <div style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: `radial-gradient(circle at 20% 80%, rgba(109, 76, 65, 0.1) 0%, transparent 50%),
                     radial-gradient(circle at 80% 20%, rgba(255, 171, 64, 0.1) 0%, transparent 50%)`,
        pointerEvents: 'none',
        zIndex: -1
      }}></div>

      {/* Enhanced Header */}
      <div style={{
        background: 'linear-gradient(135deg, #6d4c41 0%, #8d6e63 100%)',
        color: 'white',
        padding: '2rem',
        borderRadius: '24px',
        boxShadow: '0 8px 32px rgba(109, 76, 65, 0.2)',
        marginBottom: '2rem',
        position: 'relative',
        overflow: 'hidden'
      }}>
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.05"%3E%3Ccircle cx="30" cy="30" r="4"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
          opacity: 0.3
        }}></div>

        <div style={{ position: 'relative', zIndex: 1 }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '1.5rem', marginBottom: '1rem' }}>
            <div style={{
              background: 'rgba(255, 255, 255, 0.2)',
              padding: '1rem',
              borderRadius: '16px',
              backdropFilter: 'blur(10px)'
            }}>
              <i className="fas fa-users" style={{ fontSize: '2rem', color: '#ffab40' }}></i>
            </div>
            <div>
              <h1 style={{ margin: 0, fontSize: '2.5rem', fontWeight: '700' }}>
                إدارة الموظفين المحسنة
              </h1>
              <p style={{ margin: '0.5rem 0 0 0', opacity: 0.9, fontSize: '1.1rem' }}>
                إدارة شاملة للموظفين مع إحصائيات متقدمة وتحكم كامل
              </p>
            </div>
          </div>

          {/* Quick Stats */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '1rem',
            marginTop: '1.5rem'
          }}>
            <div style={{
              background: 'rgba(255, 255, 255, 0.15)',
              backdropFilter: 'blur(15px)',
              borderRadius: '16px',
              padding: '1.5rem',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              textAlign: 'center'
            }}>
              <i className="fas fa-users" style={{ fontSize: '1.5rem', color: '#ffab40', marginBottom: '0.5rem' }}></i>
              <div style={{ fontSize: '1.8rem', fontWeight: '700' }}>{employees.length}</div>
              <div style={{ fontSize: '0.9rem', opacity: 0.8 }}>إجمالي الموظفين</div>
            </div>

            <div style={{
              background: 'rgba(255, 255, 255, 0.15)',
              backdropFilter: 'blur(15px)',
              borderRadius: '16px',
              padding: '1.5rem',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              textAlign: 'center'
            }}>
              <i className="fas fa-user-check" style={{ fontSize: '1.5rem', color: '#ffab40', marginBottom: '0.5rem' }}></i>
              <div style={{ fontSize: '1.8rem', fontWeight: '700' }}>
                {employees.filter(emp => emp.active).length}
              </div>
              <div style={{ fontSize: '0.9rem', opacity: 0.8 }}>موظفين نشطين</div>
            </div>

            <div style={{
              background: 'rgba(255, 255, 255, 0.15)',
              backdropFilter: 'blur(15px)',
              borderRadius: '16px',
              padding: '1.5rem',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              textAlign: 'center'
            }}>              <i className="fas fa-user-tie" style={{ fontSize: '1.5rem', color: '#ffab40', marginBottom: '0.5rem' }}></i>
              <div style={{ fontSize: '1.8rem', fontWeight: '700' }}>
                {employees.filter(emp => emp.role === 'waiter').length}
              </div>
              <div style={{ fontSize: '0.9rem', opacity: 0.8 }}>نُدُل</div>
            </div>

            <div style={{
              background: 'rgba(255, 255, 255, 0.15)',
              backdropFilter: 'blur(15px)',
              borderRadius: '16px',
              padding: '1.5rem',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              textAlign: 'center'
            }}>
              <i className="fas fa-utensils" style={{ fontSize: '1.5rem', color: '#ffab40', marginBottom: '0.5rem' }}></i>
              <div style={{ fontSize: '1.8rem', fontWeight: '700' }}>
                {employees.filter(emp => emp.role === 'chef').length}
              </div>
              <div style={{ fontSize: '0.9rem', opacity: 0.8 }}>طباخين</div>
            </div>
          </div>
        </div>
      </div>

      {/* Controls Section */}
      <div style={{
        background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
        borderRadius: '20px',
        padding: '2rem',
        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
        border: '1px solid rgba(109, 76, 65, 0.1)',
        marginBottom: '2rem'
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '1.5rem',
          flexWrap: 'wrap',
          gap: '1rem'
        }}>
          <h3 style={{
            margin: 0,
            color: '#6d4c41',
            fontSize: '1.5rem',
            fontWeight: '700',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem'
          }}>
            <i className="fas fa-search" style={{ color: '#ffab40' }}></i>
            البحث والفلترة
          </h3>

          <button
            onClick={() => setShowAddForm(!showAddForm)}
            style={{
              background: showAddForm
                ? 'linear-gradient(135deg, #f44336, #e57373)'
                : 'linear-gradient(135deg, #4caf50, #66bb6a)',
              color: 'white',
              border: 'none',
              padding: '0.75rem 1.5rem',
              borderRadius: '12px',
              cursor: 'pointer',
              fontWeight: '600',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              transition: 'all 0.3s ease'
            }}
          >
            <i className={`fas ${showAddForm ? 'fa-times' : 'fa-plus'}`}></i>
            {showAddForm ? 'إلغاء' : 'إضافة موظف جديد'}
          </button>
        </div>

        {/* Search and Filter */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '1rem',
          marginBottom: showAddForm ? '2rem' : '0'
        }}>
          <div style={{ position: 'relative' }}>
            <i className="fas fa-search" style={{
              position: 'absolute',
              right: '1rem',
              top: '50%',
              transform: 'translateY(-50%)',
              color: '#666',
              zIndex: 1
            }}></i>
            <input
              type="text"
              placeholder="ابحث عن موظف..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              style={{
                width: '100%',
                padding: '0.75rem 3rem 0.75rem 1rem',
                border: '2px solid #e0e0e0',
                borderRadius: '12px',
                fontSize: '1rem',
                background: 'white',
                transition: 'border-color 0.3s ease',
                boxSizing: 'border-box'
              }}
            />
          </div>

          <div style={{ position: 'relative' }}>
            <i className="fas fa-filter" style={{
              position: 'absolute',
              right: '1rem',
              top: '50%',
              transform: 'translateY(-50%)',
              color: '#666',
              zIndex: 1
            }}></i>            <select
              value={filterRole}
              onChange={(e) => setFilterRole(e.target.value as any)}
              style={{
                width: '100%',
                padding: '0.75rem 3rem 0.75rem 1rem',
                border: '2px solid #e0e0e0',
                borderRadius: '12px',
                fontSize: '1rem',
                background: 'white',
                transition: 'border-color 0.3s ease',
                boxSizing: 'border-box'
              }}
            >
              <option value="all">جميع الأدوار</option>
              <option value="waiter">نادل</option>
              <option value="chef">طباخ</option>
              <option value="manager">مدير</option>
            </select>
          </div>
        </div>

        {/* Add Employee Form */}
        {showAddForm && (
          <div style={{
            background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
            borderRadius: '16px',
            padding: '2rem',
            border: '2px solid rgba(109, 76, 65, 0.1)',
            marginTop: '1rem'
          }}>
            <h4 style={{
              margin: '0 0 1.5rem 0',
              color: '#6d4c41',
              fontSize: '1.3rem',
              fontWeight: '700',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}>
              <i className="fas fa-user-plus" style={{ color: '#ffab40' }}></i>
              إضافة موظف جديد
            </h4>

            <form onSubmit={handleAdd}>
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
                gap: '1.5rem',
                marginBottom: '2rem'
              }}>                <div>
                  <label style={{
                    display: 'block',
                    marginBottom: '0.5rem',
                    color: '#6d4c41',
                    fontWeight: '600'
                  }}>
                    <i className="fas fa-user" style={{ marginLeft: '0.5rem', color: '#ffab40' }}></i>
                    اسم المستخدم
                  </label>
                  <input
                    type="text"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    required
                    placeholder="مثال: mohamed_ahmed"
                    style={{
                      width: '100%',
                      padding: '0.75rem',
                      border: '2px solid #e0e0e0',
                      borderRadius: '12px',
                      fontSize: '1rem',
                      background: 'white',
                      transition: 'border-color 0.3s ease',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>

                <div>
                  <label style={{
                    display: 'block',
                    marginBottom: '0.5rem',
                    color: '#6d4c41',
                    fontWeight: '600'
                  }}>
                    <i className="fas fa-id-badge" style={{ marginLeft: '0.5rem', color: '#ffab40' }}></i>
                    الاسم للعرض
                  </label>
                  <input
                    type="text"
                    value={displayName}
                    onChange={(e) => setDisplayName(e.target.value)}
                    required
                    placeholder="مثال: محمد أحمد"
                    style={{
                      width: '100%',
                      padding: '0.75rem',
                      border: '2px solid #e0e0e0',
                      borderRadius: '12px',
                      fontSize: '1rem',
                      background: 'white',
                      transition: 'border-color 0.3s ease',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>                <div>
                  <label style={{
                    display: 'block',
                    marginBottom: '0.5rem',
                    color: '#6d4c41',
                    fontWeight: '600'
                  }}>
                    <i className="fas fa-briefcase" style={{ marginLeft: '0.5rem', color: '#ffab40' }}></i>
                    الدور الوظيفي
                  </label>
                  <select
                    value={roleArabic}
                    onChange={(e) => {
                      const arabicRole = e.target.value as 'نادل' | 'طباخ' | 'مدير';
                      setRoleArabic(arabicRole);
                      setRole(roleToEnglish(arabicRole) as 'waiter' | 'chef' | 'manager');
                    }}
                    style={{
                      width: '100%',
                      padding: '0.75rem',
                      border: '2px solid #e0e0e0',
                      borderRadius: '12px',
                      fontSize: '1rem',
                      background: 'white',
                      transition: 'border-color 0.3s ease',
                      boxSizing: 'border-box'
                    }}
                  >
                    <option value="نادل">نادل</option>
                    <option value="طباخ">طباخ</option>
                    <option value="مدير">مدير</option>
                  </select>
                </div>

                <div>
                  <label style={{
                    display: 'block',
                    marginBottom: '0.5rem',
                    color: '#6d4c41',
                    fontWeight: '600'
                  }}>
                    <i className="fas fa-lock" style={{ marginLeft: '0.5rem', color: '#ffab40' }}></i>
                    كلمة المرور
                  </label>
                  <input
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    required
                    placeholder="••••••••"
                    style={{
                      width: '100%',
                      padding: '0.75rem',
                      border: '2px solid #e0e0e0',
                      borderRadius: '12px',
                      fontSize: '1rem',
                      background: 'white',
                      transition: 'border-color 0.3s ease',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
              </div>

              <div style={{ display: 'flex', gap: '1rem', justifyContent: 'flex-end' }}>
                <button
                  type="button"
                  onClick={() => setShowAddForm(false)}
                  style={{
                    background: 'linear-gradient(135deg, #9e9e9e, #bdbdbd)',
                    color: 'white',
                    border: 'none',
                    padding: '0.75rem 1.5rem',
                    borderRadius: '12px',
                    cursor: 'pointer',
                    fontWeight: '600',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem'
                  }}
                >
                  <i className="fas fa-times"></i>
                  إلغاء
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  style={{
                    background: loading
                      ? 'linear-gradient(135deg, #bdbdbd, #e0e0e0)'
                      : 'linear-gradient(135deg, #4caf50, #66bb6a)',
                    color: 'white',
                    border: 'none',
                    padding: '0.75rem 1.5rem',
                    borderRadius: '12px',
                    cursor: loading ? 'not-allowed' : 'pointer',
                    fontWeight: '600',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem'
                  }}
                >
                  <i className={`fas ${loading ? 'fa-spinner fa-spin' : 'fa-plus'}`}></i>
                  {loading ? 'جاري الإضافة...' : 'إضافة الموظف'}
                </button>
              </div>
            </form>
          </div>
        )}
      </div>
      {/* Employees Table */}
      <div style={{
        background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
        borderRadius: '20px',
        padding: '2rem',
        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
        border: '1px solid rgba(109, 76, 65, 0.1)'
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '1.5rem'
        }}>
          <h3 style={{
            margin: 0,
            color: '#6d4c41',
            fontSize: '1.5rem',
            fontWeight: '700',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem'
          }}>
            <i className="fas fa-table" style={{ color: '#ffab40' }}></i>
            قائمة الموظفين
          </h3>

          <div style={{
            background: 'rgba(109, 76, 65, 0.1)',
            padding: '0.5rem 1rem',
            borderRadius: '12px',
            color: '#6d4c41',
            fontWeight: '600'
          }}>
            {filteredEmployees.length} من {employees.length} موظف
          </div>
        </div>

        {filteredEmployees.length === 0 ? (
          <div style={{
            textAlign: 'center',
            padding: '4rem 2rem',
            background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
            borderRadius: '16px',
            border: '2px dashed #dee2e6'
          }}>
            <i className="fas fa-users" style={{
              fontSize: '4rem',
              color: '#dee2e6',
              marginBottom: '1rem'
            }}></i>
            <h4 style={{
              color: '#6d4c41',
              marginBottom: '0.5rem',
              fontSize: '1.5rem'
            }}>
              {searchTerm || filterRole !== 'all' ? 'لا توجد نتائج' : 'لا يوجد موظفين'}
            </h4>
            <p style={{
              color: '#666',
              margin: 0,
              fontSize: '1.1rem'
            }}>
              {searchTerm || filterRole !== 'all'
                ? 'جرب تغيير معايير البحث أو الفلترة'
                : 'ابدأ بإضافة موظفين جدد للنظام'
              }
            </p>
          </div>
        ) : (
          <div style={{ overflowX: 'auto' }}>
            <table style={{
              width: '100%',
              borderCollapse: 'collapse',
              background: 'white',
              borderRadius: '12px',
              overflow: 'hidden',
              boxShadow: '0 4px 15px rgba(0, 0, 0, 0.1)'
            }}>
              <thead>
                <tr style={{
                  background: 'linear-gradient(135deg, #6d4c41, #8d6e63)',
                  color: 'white'
                }}>
                  <th style={{ padding: '1rem', textAlign: 'center', fontWeight: '700' }}>
                    <i className="fas fa-hashtag" style={{ marginLeft: '0.5rem' }}></i>
                    #
                  </th>
                  <th style={{ padding: '1rem', textAlign: 'center', fontWeight: '700' }}>
                    <i className="fas fa-user" style={{ marginLeft: '0.5rem' }}></i>
                    الاسم
                  </th>
                  <th style={{ padding: '1rem', textAlign: 'center', fontWeight: '700' }}>
                    <i className="fas fa-briefcase" style={{ marginLeft: '0.5rem' }}></i>
                    الدور
                  </th>
                  <th style={{ padding: '1rem', textAlign: 'center', fontWeight: '700' }}>
                    <i className="fas fa-toggle-on" style={{ marginLeft: '0.5rem' }}></i>
                    الحالة
                  </th>
                  <th style={{ padding: '1rem', textAlign: 'center', fontWeight: '700' }}>
                    <i className="fas fa-cogs" style={{ marginLeft: '0.5rem' }}></i>
                    إجراءات
                  </th>
                </tr>
              </thead>
              <tbody>
                {filteredEmployees.map((emp, idx) => (
                  <tr key={emp._id} style={{
                    borderBottom: '1px solid #f0f0f0',
                    background: idx % 2 === 0 ? '#fafafa' : 'white',
                    transition: 'all 0.3s ease'
                  }}>
                    <td style={{
                      padding: '1rem',
                      textAlign: 'center',
                      fontWeight: '600',
                      color: '#6d4c41'
                    }}>
                      {idx + 1}
                    </td>
                    <td style={{
                      padding: '1rem',
                      textAlign: 'center',
                      fontWeight: '600'
                    }}>
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        gap: '0.5rem'
                      }}>
                        <div style={{
                          width: '40px',
                          height: '40px',
                          borderRadius: '50%',
                          background: `linear-gradient(135deg, ${getRoleColor(emp.role)}, ${getRoleColor(emp.role)}80)`,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          color: 'white',
                          fontWeight: '700',
                          fontSize: '1.2rem'
                        }}>
                          <i className={`fas ${getRoleIcon(emp.role)}`}></i>
                        </div>                        <div>
                          <div style={{ color: '#6d4c41', fontWeight: '700' }}>
                            {emp.displayName || emp.name || emp.username}
                          </div>
                          <div style={{ fontSize: '0.8rem', color: '#666' }}>
                            {emp.username && emp.name ? `@${emp.username}` : ''}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td style={{
                      padding: '1rem',
                      textAlign: 'center',
                      fontWeight: '600'
                    }}>                      <span style={{
                        background: `linear-gradient(135deg, ${getRoleColor(emp.role)}, ${getRoleColor(emp.role)}80)`,
                        color: 'white',
                        padding: '0.5rem 1rem',
                        borderRadius: '12px',
                        fontSize: '0.9rem',
                        fontWeight: '600',
                        display: 'inline-flex',
                        alignItems: 'center',
                        gap: '0.5rem'
                      }}>
                        <i className={`fas ${getRoleIcon(emp.role)}`}></i>
                        {emp.roleArabic || roleToArabic(emp.role)}
                      </span>
                    </td>
                    <td style={{
                      padding: '1rem',
                      textAlign: 'center'
                    }}>
                      <button
                        onClick={() => handleToggle(emp._id!)}
                        disabled={loading}
                        style={{
                          background: emp.active
                            ? 'linear-gradient(135deg, #4caf50, #66bb6a)'
                            : 'linear-gradient(135deg, #9e9e9e, #bdbdbd)',
                          color: 'white',
                          border: 'none',
                          borderRadius: '12px',
                          padding: '0.5rem 1rem',
                          cursor: loading ? 'not-allowed' : 'pointer',
                          fontWeight: '600',
                          fontSize: '0.9rem',
                          display: 'flex',
                          alignItems: 'center',
                          gap: '0.5rem',
                          margin: '0 auto',
                          transition: 'all 0.3s ease'
                        }}
                      >
                        <i className={`fas ${emp.active ? 'fa-check-circle' : 'fa-times-circle'}`}></i>
                        {emp.active ? 'نشط' : 'معطل'}
                      </button>
                    </td>
                    <td style={{
                      padding: '1rem',
                      textAlign: 'center'
                    }}>
                      <div style={{
                        display: 'flex',
                        gap: '0.5rem',
                        justifyContent: 'center',
                        flexWrap: 'wrap'
                      }}>
                        <button
                          onClick={() => handleEdit(emp)}
                          disabled={loading}
                          style={{
                            background: 'linear-gradient(135deg, #2196f3, #64b5f6)',
                            color: 'white',
                            border: 'none',
                            borderRadius: '8px',
                            padding: '0.5rem 0.75rem',
                            cursor: loading ? 'not-allowed' : 'pointer',
                            fontWeight: '600',
                            fontSize: '0.8rem',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '0.25rem',
                            transition: 'all 0.3s ease'
                          }}
                          title="تعديل الموظف"
                        >
                          <i className="fas fa-edit"></i>
                          تعديل
                        </button>
                        <button
                          onClick={() => handleDelete(emp._id!)}
                          disabled={loading}
                          style={{
                            background: 'linear-gradient(135deg, #f44336, #e57373)',
                            color: 'white',
                            border: 'none',
                            borderRadius: '8px',
                            padding: '0.5rem 0.75rem',
                            cursor: loading ? 'not-allowed' : 'pointer',
                            fontWeight: '600',
                            fontSize: '0.8rem',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '0.25rem',
                            transition: 'all 0.3s ease'
                          }}
                          title="حذف الموظف"
                        >
                          <i className="fas fa-trash"></i>
                          حذف
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
      {/* Enhanced Edit Modal */}
      {editId !== null && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000,
          backdropFilter: 'blur(5px)',
          animation: 'fadeIn 0.3s ease-out'
        }}>
          <div style={{
            background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
            padding: '2rem',
            borderRadius: '20px',
            minWidth: '400px',
            maxWidth: '90vw',
            boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)',
            border: '1px solid rgba(109, 76, 65, 0.1)',
            position: 'relative',
            animation: 'slideUp 0.3s ease-out'
          }}>
            {/* Modal Header */}
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '1rem',
              marginBottom: '2rem',
              paddingBottom: '1rem',
              borderBottom: '2px solid #e0e0e0'
            }}>
              <div style={{
                background: 'linear-gradient(135deg, #6d4c41, #8d6e63)',
                padding: '0.75rem',
                borderRadius: '12px',
                color: 'white'
              }}>
                <i className="fas fa-user-edit" style={{ fontSize: '1.5rem' }}></i>
              </div>
              <div>
                <h3 style={{
                  margin: 0,
                  color: '#6d4c41',
                  fontSize: '1.5rem',
                  fontWeight: '700'
                }}>
                  تعديل بيانات الموظف
                </h3>
                <p style={{
                  margin: '0.25rem 0 0 0',
                  color: '#666',
                  fontSize: '0.9rem'
                }}>
                  تحديث معلومات الموظف في النظام
                </p>
              </div>
            </div>

            {/* Form Fields */}
            <div style={{
              display: 'grid',
              gap: '1.5rem',
              marginBottom: '2rem'
            }}>
              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '0.5rem',
                  color: '#6d4c41',
                  fontWeight: '600'
                }}>
                  <i className="fas fa-user" style={{ marginLeft: '0.5rem', color: '#ffab40' }}></i>
                  اسم المستخدم
                </label>
                <input
                  type="text"
                  value={editData.username || ''}
                  onChange={e => setEditData(d => ({ ...d, username: e.target.value }))}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '2px solid #e0e0e0',
                    borderRadius: '12px',
                    fontSize: '1rem',
                    background: 'white',
                    transition: 'border-color 0.3s ease',
                    boxSizing: 'border-box'
                  }}
                  placeholder="اسم المستخدم"
                />
              </div>              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '0.5rem',
                  color: '#6d4c41',
                  fontWeight: '600'
                }}>
                  <i className="fas fa-id-badge" style={{ marginLeft: '0.5rem', color: '#ffab40' }}></i>
                  الاسم للعرض
                </label>
                <input
                  type="text"
                  value={editData.name || ''}
                  onChange={e => setEditData(d => ({ ...d, name: e.target.value }))}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '2px solid #e0e0e0',
                    borderRadius: '12px',
                    fontSize: '1rem',
                    background: 'white',
                    transition: 'border-color 0.3s ease',
                    boxSizing: 'border-box'
                  }}
                  placeholder="الاسم للعرض"
                />
              </div>

              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '0.5rem',
                  color: '#6d4c41',
                  fontWeight: '600'
                }}>
                  <i className="fas fa-briefcase" style={{ marginLeft: '0.5rem', color: '#ffab40' }}></i>
                  الدور الوظيفي
                </label>
                <select
                  value={roleToArabic(editData.role || 'waiter')}
                  onChange={e => {
                    const arabicRole = e.target.value;
                    const englishRole = roleToEnglish(arabicRole);
                    setEditData(d => ({ ...d, role: englishRole as Employee['role'] }));
                  }}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '2px solid #e0e0e0',
                    borderRadius: '12px',
                    fontSize: '1rem',
                    background: 'white',
                    transition: 'border-color 0.3s ease',
                    boxSizing: 'border-box'
                  }}
                >
                  <option value="نادل">نادل</option>
                  <option value="طباخ">طباخ</option>
                  <option value="مدير">مدير</option>
                </select>
              </div>

              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '0.5rem',
                  color: '#6d4c41',
                  fontWeight: '600'
                }}>
                  <i className="fas fa-lock" style={{ marginLeft: '0.5rem', color: '#ffab40' }}></i>
                  كلمة المرور (اتركها فارغة للاحتفاظ بالحالية)
                </label>
                <input
                  type="password"
                  value={editData.password || ''}
                  onChange={e => setEditData(d => ({ ...d, password: e.target.value }))}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '2px solid #e0e0e0',
                    borderRadius: '12px',
                    fontSize: '1rem',
                    background: 'white',
                    transition: 'border-color 0.3s ease',
                    boxSizing: 'border-box'
                  }}
                  placeholder="كلمة مرور جديدة (اختيارية)"
                />
              </div>

              <div style={{
                background: 'rgba(109, 76, 65, 0.05)',
                padding: '1rem',
                borderRadius: '12px',
                border: '1px solid rgba(109, 76, 65, 0.1)'
              }}>
                <label style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.75rem',
                  cursor: 'pointer',
                  color: '#6d4c41',
                  fontWeight: '600'
                }}>
                  <div style={{
                    position: 'relative',
                    width: '50px',
                    height: '28px',
                    background: editData.active ? '#4caf50' : '#ccc',
                    borderRadius: '14px',
                    transition: 'background 0.3s ease'
                  }}>
                    <div style={{
                      position: 'absolute',
                      top: '2px',
                      left: editData.active ? '24px' : '2px',
                      width: '24px',
                      height: '24px',
                      background: 'white',
                      borderRadius: '50%',
                      transition: 'left 0.3s ease',
                      boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)'
                    }}></div>
                    <input
                      type="checkbox"
                      checked={editData.active ?? true}
                      onChange={e => setEditData(d => ({ ...d, active: e.target.checked }))}
                      style={{
                        position: 'absolute',
                        opacity: 0,
                        width: '100%',
                        height: '100%',
                        cursor: 'pointer'
                      }}
                    />
                  </div>
                  <div>
                    <div style={{ fontSize: '1rem' }}>حالة الموظف</div>
                    <div style={{ fontSize: '0.8rem', color: '#666' }}>
                      {editData.active ? 'نشط - يمكن للموظف تسجيل الدخول' : 'معطل - لا يمكن للموظف تسجيل الدخول'}
                    </div>
                  </div>
                </label>
              </div>
            </div>

            {/* Action Buttons */}
            <div style={{
              display: 'flex',
              gap: '1rem',
              justifyContent: 'flex-end',
              paddingTop: '1rem',
              borderTop: '2px solid #e0e0e0'
            }}>
              <button
                onClick={() => setEditId(null)}
                style={{
                  background: 'linear-gradient(135deg, #9e9e9e, #bdbdbd)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '12px',
                  padding: '0.75rem 1.5rem',
                  cursor: 'pointer',
                  fontWeight: '600',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  transition: 'all 0.3s ease'
                }}
              >
                <i className="fas fa-times"></i>
                إلغاء
              </button>
              <button
                onClick={handleSaveEdit}
                disabled={loading}
                style={{
                  background: loading
                    ? 'linear-gradient(135deg, #bdbdbd, #e0e0e0)'
                    : 'linear-gradient(135deg, #4caf50, #66bb6a)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '12px',
                  padding: '0.75rem 1.5rem',
                  cursor: loading ? 'not-allowed' : 'pointer',
                  fontWeight: '600',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  transition: 'all 0.3s ease'
                }}
              >
                <i className={`fas ${loading ? 'fa-spinner fa-spin' : 'fa-save'}`}></i>
                {loading ? 'جاري الحفظ...' : 'حفظ التغييرات'}              </button>
            </div>
          </div>
        </div>
      )}
      
      <ToastContainer toasts={toasts} onRemove={removeToast} />
    </div>
  );
}
