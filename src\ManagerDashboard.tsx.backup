import React, { useState, useEffect, useCallback } from 'react';
import { authenticatedGet, authenticatedPost, authenticatedPut, authenticatedDelete } from './utils/apiHelpers';
import { useToast } from './hooks/useToast';
import { notificationSound } from './utils/notificationSound';
import socket from './socket';
import './ManagerDashboard.css';

interface ManagerDashboardProps {
  user?: any;
  onLogout?: () => void;
}

interface Order {
  _id: string;
  orderNumber: string;
  items: any[];
  totalAmount?: number;  // Legacy field
  totalPrice?: number;   // Common field
  totals?: {             // New structure
    subtotal: number;
    tax: number;
    discount: number;
    total: number;
  };
  status: 'pending' | 'preparing' | 'ready' | 'completed' | 'delivered';
  tableNumber?: number;
  customerName?: string;
  waiterName?: string;
  chefName?: string;
  createdAt: string;
  updatedAt: string;
}

interface Employee {
  _id: string;
  username: string;
  name: string;
  email?: string;
  phone?: string;
  role: 'waiter' | 'chef' | 'manager';
  status: 'active' | 'inactive';
  isActive: boolean;
  currentShift?: any;
}

interface TableAccount {
  _id: string;
  tableNumber: string;
  waiterName: string;
  totalAmount: number;
  isOpen: boolean;
  status: 'active' | 'closed';
  orders: Order[];
  createdAt: string;
}

interface DashboardStats {
  totalOrders: number;
  totalSales: number;
  activeEmployees: number;
  activeTables: number;
  pendingOrders: number;
  preparingOrders: number;
  readyOrders: number;
  completedOrders: number;
}

interface DiscountRequest {
  _id: string;
  orderId: string;
  orderNumber: string;
  waiterName: string;
  amount: number;
  reason: string;
  status: 'pending' | 'approved' | 'rejected';
  createdAt: string;
  order?: Order;
}

interface Shift {
  _id: string;
  employeeId: string;
  employeeName: string;
  role: 'waiter' | 'chef' | 'manager';
  startTime: string;
  endTime?: string;
  duration?: string;
  status: 'active' | 'completed';
  ordersCount: number;
  salesAmount: number;
  createdAt: string;
}

interface MenuItem {
  _id: string;
  name: string;
  price: number;
  description?: string;
  categories?: string[];
  available: boolean;
  stock?: number | { quantity: number };
}

interface Category {
  _id: string;
  name: string;
  color: string;
  icon?: string;
  description?: string;
}

export default function ManagerDashboard({ user: propUser, onLogout }: ManagerDashboardProps) {
  const [currentScreen, setCurrentScreen] = useState<'home' | 'orders' | 'employees' | 'tables' | 'reports' | 'inventory' | 'menu' | 'categories'>('home');
  const [loading, setLoading] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  
  // Data states
  const [orders, setOrders] = useState<Order[]>([]);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [tableAccounts, setTableAccounts] = useState<TableAccount[]>([]);
  const [discountRequests, setDiscountRequests] = useState<DiscountRequest[]>([]);
  const [shifts, setShifts] = useState<Shift[]>([]);
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [stats, setStats] = useState<DashboardStats>({
    totalOrders: 0,
    totalSales: 0,
    activeEmployees: 0,
    activeTables: 0,
    pendingOrders: 0,
    preparingOrders: 0,
    readyOrders: 0,
    completedOrders: 0
  });
  // Filter states
  const [orderStatusFilter, setOrderStatusFilter] = useState<'all' | 'pending' | 'preparing' | 'ready' | 'completed'>('all');
  const [waiterFilter, setWaiterFilter] = useState<string>('all');
  const [dateFilter, setDateFilter] = useState<string>('today');
  
  // Menu filter states
  const [selectedCategoryFilter, setSelectedCategoryFilter] = useState<string | null>(null);
  const [availabilityFilter, setAvailabilityFilter] = useState<string>('all');
  const [menuSearchTerm, setMenuSearchTerm] = useState<string>('');

  // Modal states
  const [showDiscountModal, setShowDiscountModal] = useState(false);
  const [selectedDiscountRequest, setSelectedDiscountRequest] = useState<DiscountRequest | null>(null);
  const [showShiftModal, setShowShiftModal] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null);
  const [showOrderDetailsModal, setShowOrderDetailsModal] = useState(false);  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);

  // Menu and Category Modal states
  const [showMenuModal, setShowMenuModal] = useState(false);
  const [selectedMenuItem, setSelectedMenuItem] = useState<MenuItem | null>(null);
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);

  const { showSuccess, showError, showInfo } = useToast();

  // الحصول على بيانات المستخدم
  const user = propUser || JSON.parse(localStorage.getItem('user') || 'null');
  const managerName = user?.username || user?.name || localStorage.getItem('username') || 'المدير';
  const managerId = user?._id || user?.id || 'manager-user';

  // جلب البيانات
  const fetchOrders = useCallback(async () => {
    try {
      const response = await authenticatedGet('/api/orders');
      console.log('📊 استجابة API للطلبات:', response);

      let ordersData: any[] = [];
      if (Array.isArray(response)) {
        ordersData = response;
      } else if (response.success && Array.isArray(response.data)) {
        ordersData = response.data;
      } else if (response.data && Array.isArray(response.data)) {
        ordersData = response.data;
      }

      console.log('✅ تم جلب الطلبات:', ordersData.length, 'طلب');
      
      // تسجيل عينة من البيانات للتحقق من بنية البيانات
      if (ordersData.length > 0) {
        console.log('📋 عينة من بيانات الطلبات:', {
          firstOrder: {
            _id: ordersData[0]._id,
            totalAmount: ordersData[0].totalAmount,
            totalPrice: ordersData[0].totalPrice,
            totals: ordersData[0].totals,
            items: ordersData[0].items?.length || 0,
            status: ordersData[0].status
          }
        });
      }
      
      setOrders(ordersData);
    } catch (error) {
      console.error('خطأ في جلب الطلبات:', error);
      showError('فشل في جلب الطلبات');
      setOrders([]);
    }
  }, [showError]);

  const fetchEmployees = useCallback(async () => {
    try {
      const response = await authenticatedGet('/api/users');
      console.log('👥 استجابة API للمستخدمين:', response);

      let employeesData: any[] = [];
      if (Array.isArray(response)) {
        employeesData = response;
      } else if (response.success && Array.isArray(response.data)) {
        employeesData = response.data;
      } else if (response.data && Array.isArray(response.data)) {
        employeesData = response.data;
      }

      // فلترة الموظفين (استبعاد المديرين)
      const filteredEmployees = employeesData.filter((emp: Employee) =>
        emp.role === 'waiter' || emp.role === 'chef'
      );

      console.log('✅ تم جلب الموظفين:', filteredEmployees.length, 'موظف');
      setEmployees(filteredEmployees);
    } catch (error) {
      console.error('خطأ في جلب الموظفين:', error);
      showError('فشل في جلب الموظفين');
      setEmployees([]);
    }
  }, [showError]);

  const fetchTableAccounts = useCallback(async () => {
    try {
      const response = await authenticatedGet('/api/table-accounts');
      const tablesData = Array.isArray(response) ? response : (response.data || []);
      setTableAccounts(tablesData);
    } catch (error) {
      console.error('خطأ في جلب الطاولات:', error);
      showError('فشل في جلب الطاولات');
    }
  }, [showError]);

  const fetchDiscountRequests = useCallback(async () => {
    try {
      const response = await authenticatedGet('/api/discount-requests');
      console.log('💰 استجابة API لطلبات الخصم:', response);

      let discountData: any[] = [];
      if (Array.isArray(response)) {
        discountData = response;
      } else if (response.success && Array.isArray(response.data)) {
        discountData = response.data;
      } else if (response.data && Array.isArray(response.data)) {
        discountData = response.data;
      }

      console.log('✅ تم جلب طلبات الخصم:', discountData.length, 'طلب خصم');
      setDiscountRequests(discountData);
    } catch (error) {
      console.error('خطأ في جلب طلبات الخصم:', error);
      showError('فشل في جلب طلبات الخصم');
      setDiscountRequests([]);
    }  }, [showError]);  const fetchMenuItems = useCallback(async () => {
    try {
      console.log('🔄 جاري جلب عناصر القائمة...');
      
      const response = await authenticatedGet('/api/products');
      console.log('☕ استجابة API لعناصر القائمة:', response);

      let menuData: any[] = [];
      
      if (Array.isArray(response)) {
        menuData = response;
      } else if (response.success && Array.isArray(response.data)) {
        menuData = response.data;
      } else if (response.data && Array.isArray(response.data)) {
        menuData = response.data;
      } else if (response.products && Array.isArray(response.products)) {
        menuData = response.products;
      }

      // تسجيل تفاصيل المشروبات المجلبة
      const drinks = menuData.filter(item => 
        item.category === 'drinks' || 
        item.categoryName === 'مشروبات' ||
        (item.categories && item.categories.some((cat: any) => 
          typeof cat === 'string' ? cat.includes('drink') || cat.includes('مشروب') :
          cat.name && (cat.name.includes('drink') || cat.name.includes('مشروب'))
        ))
      );

      console.log('✅ تم جلب عناصر القائمة:', {
        total: menuData.length,
        drinks: drinks.length,
        categories: [...new Set(menuData.map(item => item.categoryName || item.category))]
      });

      setMenuItems(menuData);
    } catch (error) {
      console.error('خطأ في جلب عناصر القائمة:', error);
      showError('فشل في جلب عناصر القائمة');
      setMenuItems([]);
    }
  }, [showError]);

  const fetchCategories = useCallback(async () => {
    try {
      const response = await authenticatedGet('/api/categories');
      console.log('🏷️ استجابة API للفئات:', response);

      let categoriesData: any[] = [];
      if (Array.isArray(response)) {
        categoriesData = response;
      } else if (response.success && Array.isArray(response.data)) {
        categoriesData = response.data;
      } else if (response.data && Array.isArray(response.data)) {
        categoriesData = response.data;
      }

      console.log('✅ تم جلب الفئات:', categoriesData.length, 'فئة');
      setCategories(categoriesData);
    } catch (error) {
      console.error('خطأ في جلب الفئات:', error);
      showError('فشل في جلب الفئات');
      setCategories([]);
    }
  }, [showError]);

  // حساب الإحصائيات
  const calculateStats = useCallback(() => {
    console.log('🔄 حساب الإحصائيات:', {
      ordersCount: orders.length,
      employeesCount: employees.length,
      tablesCount: tableAccounts.length
    });

    const totalOrders = orders.length;
    
    // حساب إجمالي المبيعات بطريقة صحيحة
    const totalSales = orders.reduce((sum, order) => {
      // التحقق من جميع الحقول المحتملة للمبلغ الإجمالي
      let orderTotal = 0;
      
      if (order.totalAmount && typeof order.totalAmount === 'number') {
        orderTotal = order.totalAmount;
      } else if (order.totalPrice && typeof order.totalPrice === 'number') {
        orderTotal = order.totalPrice;
      } else if (order.totals && order.totals.total && typeof order.totals.total === 'number') {
        orderTotal = order.totals.total;
      } else if (order.items && Array.isArray(order.items)) {
        // حساب من العناصر كـ fallback
        orderTotal = order.items.reduce((itemSum, item) => {
          const itemPrice = (item.price || 0) * (item.quantity || 0);
          return itemSum + itemPrice;
        }, 0);
      }
      
      return sum + orderTotal;
    }, 0);

    const activeEmployees = employees.filter(emp => emp.isActive || emp.status === 'active').length;
    const activeTables = tableAccounts.filter(table => table.isOpen || table.status === 'active').length;
    const pendingOrders = orders.filter(order => order.status === 'pending').length;
    const preparingOrders = orders.filter(order => order.status === 'preparing').length;
    const readyOrders = orders.filter(order => order.status === 'ready').length;
    const completedOrders = orders.filter(order => order.status === 'completed' || order.status === 'delivered').length;

    console.log('📊 الإحصائيات المحسوبة:', {
      totalOrders,
      totalSales: totalSales.toFixed(2),
      activeEmployees,
      activeTables,
      pendingOrders,
      preparingOrders,
      readyOrders,
      completedOrders
    });

    setStats({
      totalOrders,
      totalSales,
      activeEmployees,
      activeTables,
      pendingOrders,
      preparingOrders,
      readyOrders,
      completedOrders
    });
  }, [orders, employees, tableAccounts]);

  // تسجيل الخروج
  const handleLogout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('username');
    localStorage.removeItem('userId');
    localStorage.removeItem('userRole');
    localStorage.removeItem('waiterName');
    localStorage.removeItem('waiterId');
    
    if (socket) {
      socket.emit('user-logout', {
        userId: managerId,
        username: managerName
      });
    }
    
    if (onLogout) {
      onLogout();
    } else {
      window.location.href = '/';
    }
  };

  // دوال إدارة طلبات الخصم
  const handleDiscountRequest = async (requestId: string, action: 'approve' | 'reject') => {
    try {
      const response = await authenticatedPut(`/api/discount-requests/${requestId}`, {
        status: action === 'approve' ? 'approved' : 'rejected'
      });

      if (response.success) {
        showSuccess(`تم ${action === 'approve' ? 'الموافقة على' : 'رفض'} طلب الخصم`);
        fetchDiscountRequests();
        fetchOrders();
      } else {
        showError('فشل في معالجة طلب الخصم');
      }
    } catch (error) {
      console.error('خطأ في معالجة طلب الخصم:', error);
      showError('فشل في معالجة طلب الخصم');
    }
  };

  // دوال إدارة المناوبات
  const startShift = async (employeeId: string) => {
    try {
      const employee = employees.find(emp => emp._id === employeeId);
      if (!employee) {
        showError('لم يتم العثور على الموظف');
        return;
      }

      // محاولة استخدام API إذا كان متوفراً
      try {
        const response = await authenticatedPost('/api/shifts', {
          employeeId,
          employeeName: employee.username || employee.name,
          role: employee.role,
          startTime: new Date().toISOString(),
          status: 'active'
        });        if (response.success) {
          showSuccess('تم بدء المناوبة بنجاح');
          // fetchShifts(); // تم إزالة API المناوبات
          fetchEmployees();

          // إرسال إشعار Socket.IO
          socket.emit('shift-started', {
            employeeId,
            employeeName: employee.username || employee.name,
            shiftId: response.data._id
          });
          return;
        }
      } catch (apiError) {
        console.log('⚠️ API المناوبات غير متوفر، استخدام إدارة محلية');
      }

      // إدارة محلية للمناوبات
      const newShift: Shift = {
        _id: `shift_${Date.now()}`,
        employeeId,
        employeeName: employee.username || employee.name,
        role: employee.role,
        startTime: new Date().toISOString(),
        status: 'active',
        ordersCount: 0,
        salesAmount: 0,
        createdAt: new Date().toISOString()
      };

      setShifts(prev => [...prev, newShift]);
      showSuccess('تم بدء المناوبة بنجاح');

      // إرسال إشعار Socket.IO
      socket.emit('shift-started', {
        employeeId,
        employeeName: employee.username || employee.name,
        shiftId: newShift._id
      });

    } catch (error) {
      console.error('خطأ في بدء المناوبة:', error);
      showError('فشل في بدء المناوبة');
    }
  };

  const endShift = async (shiftId: string) => {
    try {
      const shift = shifts.find(s => s._id === shiftId);
      if (!shift) {
        showError('لم يتم العثور على المناوبة');
        return;
      }

      // محاولة استخدام API إذا كان متوفراً
      try {
        const response = await authenticatedPut(`/api/shifts/${shiftId}`, {
          endTime: new Date().toISOString(),
          status: 'completed'
        });        if (response.success) {
          showSuccess('تم إنهاء المناوبة بنجاح');
          // fetchShifts(); // تم إزالة API المناوبات
          fetchEmployees();

          // إرسال إشعار Socket.IO
          socket.emit('shift-ended', {
            shiftId,
            employeeName: shift.employeeName
          });
          return;
        }
      } catch (apiError) {
        console.log('⚠️ API المناوبات غير متوفر، استخدام إدارة محلية');
      }

      // إدارة محلية للمناوبات
      setShifts(prev => prev.map(s =>
        s._id === shiftId
          ? { ...s, endTime: new Date().toISOString(), status: 'completed' as const }
          : s
      ));

      showSuccess('تم إنهاء المناوبة بنجاح');

      // إرسال إشعار Socket.IO
      socket.emit('shift-ended', {
        shiftId,
        employeeName: shift.employeeName
      });

    } catch (error) {
      console.error('خطأ في إنهاء المناوبة:', error);
      showError('فشل في إنهاء المناوبة');
    }
  };

  // وظائف إدارة الموظفين - تم نقلها إلى الأسفل



  // وظائف إدارة طلبات إغلاق الطاولات
  const handleTableCloseRequest = async (requestId: string, action: 'approve' | 'reject') => {
    try {
      const response = await authenticatedPut(`/api/table-close-requests/${requestId}`, {
        status: action === 'approve' ? 'approved' : 'rejected',
        managerId: managerId,
        managerName: managerName
      });

      if (response.success) {
        showSuccess(action === 'approve' ? 'تم الموافقة على إغلاق الطاولة' : 'تم رفض طلب إغلاق الطاولة');
        fetchTableAccounts();
        return true;
      } else {
        showError(response.message || 'فشل في معالجة الطلب');
        return false;
      }
    } catch (error) {
      console.error('خطأ في معالجة طلب إغلاق الطاولة:', error);
      showError('فشل في معالجة الطلب');
      return false;
    }
  };

  // وظائف إدارة القائمة
  const addMenuItem = async (itemData: { name: string; price: number; description?: string; category: string; available: boolean }) => {
    try {
      const response = await authenticatedPost('/api/products', itemData);

      if (response.success) {
        showSuccess('تم إضافة المنتج بنجاح');
        fetchMenuItems();
        return true;
      } else {
        showError(response.message || 'فشل في إضافة المنتج');
        return false;
      }
    } catch (error) {
      console.error('خطأ في إضافة المنتج:', error);
      showError('فشل في إضافة المنتج');
      return false;
    }
  };

  const editMenuItem = async (itemId: string, itemData: { name?: string; price?: number; description?: string; category?: string; available?: boolean }) => {
    try {
      const response = await authenticatedPut(`/api/products/${itemId}`, itemData);

      if (response.success) {
        showSuccess('تم تعديل المنتج بنجاح');
        fetchMenuItems();
        return true;
      } else {
        showError(response.message || 'فشل في تعديل المنتج');
        return false;
      }
    } catch (error) {
      console.error('خطأ في تعديل المنتج:', error);
      showError('فشل في تعديل المنتج');
      return false;
    }
  };

  const deleteMenuItem = async (itemId: string) => {
    if (!window.confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
      return false;
    }

    try {
      const response = await authenticatedDelete(`/api/products/${itemId}`);

      if (response.success) {
        showSuccess('تم حذف المنتج بنجاح');
        fetchMenuItems();
        return true;
      } else {
        showError(response.message || 'فشل في حذف المنتج');
        return false;
      }
    } catch (error) {
      console.error('خطأ في حذف المنتج:', error);
      showError('فشل في حذف المنتج');
      return false;
    }
  };

  const toggleMenuItemAvailability = async (itemId: string, available: boolean) => {
    try {
      const response = await authenticatedPut(`/api/products/${itemId}`, { available });

      if (response.success) {
        showSuccess(`تم ${available ? 'تفعيل' : 'إلغاء'} توفر المنتج`);
        fetchMenuItems();
        return true;
      } else {
        showError('فشل في تحديث حالة التوفر');
        return false;
      }
    } catch (error) {
      console.error('خطأ في تحديث حالة التوفر:', error);
      showError('فشل في تحديث حالة التوفر');
      return false;
    }
  };

  // وظائف إدارة الفئات
  const addCategory = async (categoryData: { name: string; description?: string; color: string; icon?: string }) => {
    try {
      const response = await authenticatedPost('/api/categories', categoryData);

      if (response.success) {
        showSuccess('تم إضافة الفئة بنجاح');
        fetchCategories();
        return true;
      } else {
        showError(response.message || 'فشل في إضافة الفئة');
        return false;
      }
    } catch (error) {
      console.error('خطأ في إضافة الفئة:', error);
      showError('فشل في إضافة الفئة');
      return false;
    }
  };

  const editCategory = async (categoryId: string, categoryData: { name?: string; description?: string; color?: string; icon?: string }) => {
    try {
      const response = await authenticatedPut(`/api/categories/${categoryId}`, categoryData);

      if (response.success) {
        showSuccess('تم تعديل الفئة بنجاح');
        fetchCategories();
        return true;
      } else {
        showError(response.message || 'فشل في تعديل الفئة');
        return false;
      }
    } catch (error) {
      console.error('خطأ في تعديل الفئة:', error);
      showError('فشل في تعديل الفئة');
      return false;    }
  };

  const deleteCategory = async (categoryId: string) => {
    // التحقق من وجود منتجات مرتبطة بالفئة
    const categoryItems = menuItems.filter(item => item.categories?.includes(categoryId));
    
    if (categoryItems.length > 0) {
      showError(`لا يمكن حذف الفئة لأنها تحتوي على ${categoryItems.length} منتج. يرجى حذف المنتجات أولاً أو نقلها لفئة أخرى.`);
      return false;
    }

    if (!window.confirm('هل أنت متأكد من حذف هذه الفئة؟')) {
      return false;
    }

    try {
      const response = await authenticatedDelete(`/api/categories/${categoryId}`);

      if (response.success) {
        showSuccess('تم حذف الفئة بنجاح');
        fetchCategories();
        return true;
      } else {
        showError(response.message || 'فشل في حذف الفئة');
        return false;
      }
    } catch (error) {
      console.error('خطأ في حذف الفئة:', error);
      showError('فشل في حذف الفئة');
      return false;
    }
  };

  // جلب جميع البيانات
  const fetchAllData = useCallback(async () => {
    setLoading(true);
    try {
      await Promise.all([
        fetchOrders(),
        fetchEmployees(),        fetchTableAccounts(),
        fetchDiscountRequests(),
        // fetchShifts(), // تم إزالة API المناوبات
        fetchMenuItems(),
        fetchCategories()
      ]);
    } catch (error) {
      console.error('خطأ في جلب البيانات:', error);
    } finally {
      setLoading(false);
    }
  }, [fetchOrders, fetchEmployees, fetchTableAccounts, fetchDiscountRequests, fetchMenuItems, fetchCategories]);

  // إعداد Socket.IO والتحديثات
  useEffect(() => {
    fetchAllData();

    // تسجيل المدير في Socket.IO
    socket.emit('register-user', {
      userId: managerId,
      role: 'manager',
      name: managerName
    });

    socket.on('registration-confirmed', (data) => {
      console.log('✅ تم تسجيل المدير في Socket.IO:', data);
    });    // استقبال التحديثات
    socket.on('new-order-notification', (data) => {
      console.log('🔔 طلب جديد وصل للمدير:', data);
      notificationSound.playNotification();
      showInfo(`🔔 طلب جديد رقم ${data.orderNumber || 'غير محدد'} من الطاولة ${data.tableNumber || 'غير محدد'}`);
      fetchOrders();
      fetchTableAccounts();
    });

    socket.on('order-status-update', (data) => {
      console.log('🔄 تحديث حالة طلب:', data);
      fetchOrders();
      fetchTableAccounts();
    });    socket.on('shift-started', (data) => {
      console.log('👤 بدء مناوبة:', data);
      showInfo(`بدأ ${data.employeeName} مناوبته`);
      fetchEmployees();
      // fetchShifts(); // تم إزالة API المناوبات
    });

    socket.on('shift-ended', (data) => {
      console.log('👤 انتهاء مناوبة:', data);
      showInfo(`انتهت مناوبة ${data.employeeName}`);
      fetchEmployees();
      // fetchShifts(); // تم إزالة API المناوبات
    });

    socket.on('discount-request', (data) => {
      console.log('💰 طلب خصم جديد:', data);
      notificationSound.playNotification();
      showInfo(`🔔 طلب خصم جديد من ${data.waiterName}`);
      fetchDiscountRequests();
    });

    socket.on('table-close-request', (data) => {
      console.log('🏪 طلب إغلاق طاولة:', data);
      showInfo(`طلب إغلاق طاولة ${data.tableNumber} من ${data.waiterName}`);
      fetchTableAccounts();
    });

    socket.on('connect', () => {
      console.log('🔌 المدير متصل بـ Socket.IO');
      socket.emit('register-user', {
        userId: managerId,
        role: 'manager',
        name: managerName
      });
    });

    return () => {
      socket.off('registration-confirmed');
      socket.off('new-order-notification');
      socket.off('order-status-update');
      socket.off('shift-started');
      socket.off('shift-ended');
      socket.off('discount-request');
      socket.off('table-close-request');
      socket.off('connect');
    };
  }, [fetchAllData, managerId, managerName, showInfo, fetchOrders, fetchEmployees]);

  // حساب الإحصائيات عند تغيير البيانات
  useEffect(() => {
    calculateStats();
  }, [calculateStats]);

  // رندر الشاشة الرئيسية
  const renderHomeScreen = () => (
    <div className="manager-home">
      <div className="manager-header">
        <div className="welcome-section">
          <h1>
            <i className="fas fa-user-shield"></i>
            مرحباً، {managerName}
          </h1>
          <p className="role-badge">مدير</p>
        </div>
      </div>

      {/* إحصائيات سريعة */}
      <div className="stats-grid">
        <div className="stat-card orders">
          <div className="stat-icon">
            <i className="fas fa-shopping-cart"></i>
          </div>
          <div className="stat-content">
            <h3>{stats.totalOrders}</h3>
            <p>إجمالي الطلبات</p>
          </div>
        </div>

        <div className="stat-card sales">
          <div className="stat-icon">
            <i className="fas fa-money-bill-wave"></i>
          </div>
          <div className="stat-content">
            <h3>{stats.totalSales.toFixed(2)}</h3>
            <p>إجمالي المبيعات (ج.م)</p>
          </div>
        </div>

        <div className="stat-card employees">
          <div className="stat-icon">
            <i className="fas fa-users"></i>
          </div>
          <div className="stat-content">
            <h3>{stats.activeEmployees}</h3>
            <p>الموظفون النشطون</p>
          </div>
        </div>

        <div className="stat-card tables">
          <div className="stat-icon">
            <i className="fas fa-table"></i>
          </div>
          <div className="stat-content">
            <h3>{stats.activeTables}</h3>
            <p>الطاولات النشطة</p>
          </div>
        </div>
      </div>

      {/* إحصائيات الطلبات */}
      <div className="orders-stats">
        <h2>حالة الطلبات</h2>
        <div className="orders-stats-grid">
          <div className="order-stat pending">
            <i className="fas fa-clock"></i>
            <span className="count">{stats.pendingOrders}</span>
            <span className="label">قيد الانتظار</span>
          </div>
          <div className="order-stat preparing">
            <i className="fas fa-fire"></i>
            <span className="count">{stats.preparingOrders}</span>
            <span className="label">قيد التحضير</span>
          </div>
          <div className="order-stat ready">
            <i className="fas fa-check-circle"></i>
            <span className="count">{stats.readyOrders}</span>
            <span className="label">جاهزة</span>
          </div>
          <div className="order-stat completed">
            <i className="fas fa-check-double"></i>
            <span className="count">{stats.completedOrders}</span>
            <span className="label">مكتملة</span>
          </div>
        </div>
      </div>
    </div>
  );
  // رندر شاشة الطلبات
  const renderOrdersScreen = () => {
    console.log('🔍 فلترة الطلبات:', {
      totalOrders: orders.length,
      statusFilter: orderStatusFilter,
      waiterFilter: waiterFilter,
      dateFilter: dateFilter
    });

    // Log بعض البيانات للتشخيص
    if (orders.length > 0) {
      console.log('📋 أول طلب:', {
        status: orders[0].status,
        waiterName: orders[0].waiterName,
        createdAt: orders[0].createdAt
      });
    }

    const filteredOrders = orders.filter(order => {
      let isValid = true;
      
      // فلتر الحالة
      if (orderStatusFilter !== 'all') {
        const orderStatus = order.status?.toLowerCase();
        const filterStatus = orderStatusFilter.toLowerCase();
        
        if (orderStatus !== filterStatus) {
          console.log(`❌ طلب ${order._id} مرفوض: status مختلف (${orderStatus} !== ${filterStatus})`);
          isValid = false;
        }
      }

      // فلتر النادل
      if (isValid && waiterFilter !== 'all') {
        const orderWaiter = order.waiterName?.toLowerCase() || '';
        const filterWaiter = waiterFilter.toLowerCase();
        
        if (orderWaiter !== filterWaiter) {
          console.log(`❌ طلب ${order._id} مرفوض: waiter مختلف (${orderWaiter} !== ${filterWaiter})`);
          isValid = false;
        }
      }

      // فلتر التاريخ
      if (isValid && dateFilter !== 'all') {
        const orderDate = new Date(order.createdAt);
        const today = new Date();
        let dateMatch = true;

        if (dateFilter === 'today') {
          dateMatch = today.toDateString() === orderDate.toDateString();
        } else if (dateFilter === 'week') {
          const weekStart = new Date(today);
          weekStart.setDate(today.getDate() - today.getDay());
          weekStart.setHours(0, 0, 0, 0);
          dateMatch = orderDate >= weekStart;
        } else if (dateFilter === 'month') {
          const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
          monthStart.setHours(0, 0, 0, 0);
          dateMatch = orderDate >= monthStart;
        }

        if (!dateMatch) {
          console.log(`❌ طلب ${order._id} مرفوض: تاريخ مختلف (${orderDate.toDateString()} لا يطابق ${dateFilter})`);
          isValid = false;
        }
      }

      if (isValid) {
        console.log(`✅ طلب ${order._id} مقبول`);
      }

      return isValid;
    });

    console.log('✅ الطلبات المفلترة:', filteredOrders.length);    // إحصائيات النُدل
    const waiterStats = employees.filter(emp => emp.role === 'waiter').map(waiter => {
      const waiterOrders = filteredOrders.filter(order => {
        // البحث بالاسم المستخدم أو الاسم الكامل
        const nameMatch = order.waiterName === waiter.username || 
                         order.waiterName === waiter.name ||
                         order.waiterName === `${waiter.name}` ||
                         order.waiterName === `${waiter.username}`;
        
        console.log(`🔍 فحص النادل ${waiter.name} (${waiter.username}):`, {
          orderWaiter: order.waiterName,
          match: nameMatch,
          orderNumber: order.orderNumber
        });
        
        return nameMatch;
      });
      
      // حساب مبيعات النادل بطريقة صحيحة
      const waiterSales = waiterOrders.reduce((sum, order) => {
        let orderTotal = 0;
        
        if (order.totalAmount && typeof order.totalAmount === 'number') {
          orderTotal = order.totalAmount;
        } else if (order.totalPrice && typeof order.totalPrice === 'number') {
          orderTotal = order.totalPrice;
        } else if (order.totals && order.totals.total && typeof order.totals.total === 'number') {
          orderTotal = order.totals.total;
        } else if (order.items && Array.isArray(order.items)) {
          orderTotal = order.items.reduce((itemSum, item) => {
            const itemPrice = (item.price || 0) * (item.quantity || 0);
            return itemSum + itemPrice;
          }, 0);
        }
        
        return sum + orderTotal;
      }, 0);

      console.log(`📊 إحصائيات النادل ${waiter.name}:`, {
        ordersCount: waiterOrders.length,
        sales: waiterSales
      });

      return {
        name: waiter.name || waiter.username,
        username: waiter.username,
        ordersCount: waiterOrders.length,
        sales: waiterSales
      };
    });

    // إحصائيات الطباخين
    const chefStats = employees.filter(emp => emp.role === 'chef').map(chef => {
      const chefOrders = filteredOrders.filter(order => {
        // البحث بالاسم المستخدم أو الاسم الكامل
        const nameMatch = order.chefName === chef.username || 
                         order.chefName === chef.name ||
                         order.chefName === `${chef.name}` ||
                         order.chefName === `${chef.username}`;
        
        console.log(`🔍 فحص الطباخ ${chef.name} (${chef.username}):`, {
          orderChef: order.chefName,
          match: nameMatch,
          orderNumber: order.orderNumber
        });
        
        return nameMatch;
      });
      
      // حساب مبيعات الطباخ بطريقة صحيحة
      const chefSales = chefOrders.reduce((sum, order) => {
        let orderTotal = 0;
        
        if (order.totalAmount && typeof order.totalAmount === 'number') {
          orderTotal = order.totalAmount;
        } else if (order.totalPrice && typeof order.totalPrice === 'number') {
          orderTotal = order.totalPrice;
        } else if (order.totals && order.totals.total && typeof order.totals.total === 'number') {
          orderTotal = order.totals.total;
        } else if (order.items && Array.isArray(order.items)) {
          orderTotal = order.items.reduce((itemSum, item) => {
            const itemPrice = (item.price || 0) * (item.quantity || 0);
            return itemSum + itemPrice;
          }, 0);
        }
        
        return sum + orderTotal;
      }, 0);

      console.log(`📊 إحصائيات الطباخ ${chef.name}:`, {
        ordersCount: chefOrders.length,
        sales: chefSales
      });

      return {
        name: chef.name || chef.username,
        username: chef.username,
        ordersCount: chefOrders.length,
        sales: chefSales
      };
    });

    console.log('📊 إحصائيات النُدل:', waiterStats);

    return (
      <div className="orders-screen">
        <div className="orders-header">
          <h1>
            <i className="fas fa-shopping-cart"></i>
            إدارة الطلبات
          </h1>

          {/* فلاتر */}
          <div className="orders-filters">
            <select
              value={orderStatusFilter}
              onChange={(e) => setOrderStatusFilter(e.target.value as any)}
              className="filter-select"
            >
              <option value="all">جميع الحالات</option>
              <option value="pending">قيد الانتظار</option>
              <option value="preparing">قيد التحضير</option>
              <option value="ready">جاهز</option>
              <option value="completed">مكتمل</option>
            </select>

            <select
              value={waiterFilter}
              onChange={(e) => setWaiterFilter(e.target.value)}
              className="filter-select"
            >
              <option value="all">جميع النُدل</option>
              {employees.filter(emp => emp.role === 'waiter').map(waiter => (
                <option key={waiter._id} value={waiter.username}>
                  {waiter.username}
                </option>
              ))}
            </select>

            <select
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value)}
              className="filter-select"
            >
              <option value="today">اليوم</option>
              <option value="week">هذا الأسبوع</option>
              <option value="month">هذا الشهر</option>
              <option value="all">جميع الأوقات</option>
            </select>
          </div>
        </div>

        {/* إحصائيات النُدل */}
        <div className="waiter-stats">
          <h2>إحصائيات النُدل</h2>
          <div className="waiter-stats-grid">
            {waiterStats.map(waiter => (
              <div key={waiter.name} className="waiter-stat-card">
                <div className="waiter-info">
                  <i className="fas fa-user-tie"></i>
                  <h3>{waiter.name}</h3>
                </div>
                <div className="waiter-numbers">
                  <div className="stat">
                    <span className="number">{waiter.ordersCount}</span>
                    <span className="label">طلب</span>
                  </div>
                  <div className="stat">
                    <span className="number">{waiter.sales.toFixed(2)}</span>
                    <span className="label">ج.م</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* طلبات الخصم */}
        {discountRequests.filter(req => req.status === 'pending').length > 0 && (
          <div className="discount-requests">
            <h2>طلبات الخصم المعلقة</h2>
            <div className="discount-requests-grid">
              {discountRequests.filter(req => req.status === 'pending').map(request => (
                <div key={request._id} className="discount-request-card">
                  <div className="request-header">
                    <span className="order-number">#{request.orderNumber}</span>
                    <span className="waiter-name">{request.waiterName}</span>
                  </div>
                  <div className="request-details">
                    <div className="amount">خصم: {request.amount} ج.م</div>
                    <div className="reason">السبب: {request.reason}</div>
                  </div>
                  <div className="request-actions">
                    <button
                      className="approve-btn"
                      onClick={() => handleDiscountRequest(request._id, 'approve')}
                    >
                      موافقة
                    </button>
                    <button
                      className="reject-btn"
                      onClick={() => handleDiscountRequest(request._id, 'reject')}
                    >
                      رفض
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* قائمة الطلبات */}
        <div className="orders-list">
          <h2>الطلبات ({filteredOrders.length})</h2>
          <div className="orders-grid">
            {filteredOrders.map(order => (
              <div key={order._id} className={`order-card ${order.status}`}>
                <div className="order-header">
                  <span className="order-number">#{order.orderNumber}</span>
                  <span className={`order-status ${order.status}`}>
                    {order.status === 'pending' && 'قيد الانتظار'}
                    {order.status === 'preparing' && 'قيد التحضير'}
                    {order.status === 'ready' && 'جاهز'}
                    {order.status === 'completed' && 'مكتمل'}
                  </span>
                </div>

                <div className="order-info">
                  <div className="order-detail">
                    <i className="fas fa-table"></i>
                    طاولة {order.tableNumber}
                  </div>
                  <div className="order-detail">
                    <i className="fas fa-user-tie"></i>
                    {order.waiterName}
                  </div>
                  {order.chefName && (
                    <div className="order-detail">
                      <i className="fas fa-chef-hat"></i>
                      {order.chefName}
                    </div>
                  )}
                </div>

                <div className="order-summary">
                  <div className="items-count">{order.items.length} صنف</div>
                  <div className="total-amount">{order.totalAmount} ج.م</div>
                </div>

                <div className="order-time">
                  <i className="fas fa-clock"></i>
                  {new Date(order.createdAt).toLocaleTimeString('ar-SA')}
                </div>

                <button
                  className="details-btn"
                  onClick={() => {
                    setSelectedOrder(order);
                    setShowOrderDetailsModal(true);
                  }}
                >
                  التفاصيل
                </button>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };
  // دوال إدارة الموظفين المحسنة
  const [showAddEmployeeModal, setShowAddEmployeeModal] = useState(false);
  const [showEditEmployeeModal, setShowEditEmployeeModal] = useState(false);
  const [selectedEmployeeForEdit, setSelectedEmployeeForEdit] = useState<Employee | null>(null);
  const [newEmployee, setNewEmployee] = useState({
    username: '',
    name: '',
    email: '',
    role: 'waiter' as 'waiter' | 'chef',
    password: '',
    phone: ''
  });

  // إضافة موظف جديد
  const addEmployee = async () => {
    try {
      if (!newEmployee.username || !newEmployee.name || !newEmployee.email || !newEmployee.password) {
        showError('جميع الحقول مطلوبة');
        return;
      }

      console.log('🔄 إضافة موظف جديد:', newEmployee);      const response = await authenticatedPost('/api/users', {
        username: newEmployee.username,
        name: newEmployee.name,
        email: newEmployee.email,
        password: newEmployee.password,
        role: newEmployee.role,
        phone: newEmployee.phone,
        status: 'active'
      });

      console.log('📊 استجابة إضافة الموظف:', response);

      if (response.success) {
        showSuccess('تم إضافة الموظف بنجاح');
        setShowAddEmployeeModal(false);
        setNewEmployee({
          username: '',
          name: '',
          email: '',
          role: 'waiter',
          password: '',
          phone: ''
        });
        fetchEmployees();
      } else {
        showError(response.message || 'فشل في إضافة الموظف');
      }
    } catch (error) {
      console.error('خطأ في إضافة الموظف:', error);
      showError('فشل في إضافة الموظف');
    }
  };

  // حذف موظف
  const deleteEmployee = async (employeeId: string) => {
    try {
      if (!confirm('هل أنت متأكد من حذف هذا الموظف؟')) return;

      const response = await authenticatedDelete(`/api/users/${employeeId}`);

      if (response.success) {
        showSuccess('تم حذف الموظف بنجاح');
        fetchEmployees();
      } else {
        showError(response.message || 'فشل في حذف الموظف');
      }
    } catch (error) {
      console.error('خطأ في حذف الموظف:', error);
      showError('فشل في حذف الموظف');
    }
  };

  // تعديل موظف
  const updateEmployee = async () => {
    try {
      if (!selectedEmployeeForEdit) return;

      const response = await authenticatedPut(`/api/users/${selectedEmployeeForEdit._id}`, {
        username: selectedEmployeeForEdit.username,
        name: selectedEmployeeForEdit.name,
        email: selectedEmployeeForEdit.email,
        role: selectedEmployeeForEdit.role,
        phone: selectedEmployeeForEdit.phone,
        status: selectedEmployeeForEdit.status
      });

      if (response.success) {
        showSuccess('تم تحديث الموظف بنجاح');
        setShowEditEmployeeModal(false);
        setSelectedEmployeeForEdit(null);
        fetchEmployees();
      } else {
        showError(response.message || 'فشل في تحديث الموظف');
      }
    } catch (error) {
      console.error('خطأ في تحديث الموظف:', error);
      showError('فشل في تحديث الموظف');
    }
  };

  // تغيير حالة الموظف
  const toggleEmployeeStatus = async (employeeId: string, currentStatus: string) => {
    try {
      const newStatus = currentStatus === 'active' ? 'inactive' : 'active';

      const response = await authenticatedPut(`/api/users/${employeeId}`, {
        status: newStatus
      });

      if (response.success) {
        showSuccess(`تم ${newStatus === 'active' ? 'تفعيل' : 'إلغاء تفعيل'} الموظف بنجاح`);
        fetchEmployees();
      } else {
        showError(response.message || 'فشل في تغيير حالة الموظف');
      }
    } catch (error) {
      console.error('خطأ في تغيير حالة الموظف:', error);
      showError('فشل في تغيير حالة الموظف');
    }
  };

  // رندر شاشة الموظفين
  const renderEmployeesScreen = () => {
    console.log('👥 عرض شاشة الموظفين:', {
      totalEmployees: employees.length,
      totalShifts: shifts.length,
      activeShifts: shifts.filter(s => s.status === 'active').length
    });

    const activeShifts = shifts.filter(shift => shift.status === 'active');
    const inactiveEmployees = employees.filter(emp =>
      !activeShifts.some(shift => shift.employeeId === emp._id)
    );

    console.log('📊 تفاصيل الموظفين:', {
      activeShifts: activeShifts.length,
      inactiveEmployees: inactiveEmployees.length
    });

    return (
      <div className="employees-screen">
        <div className="employees-header">
          <h1>
            <i className="fas fa-users"></i>
            إدارة الموظفين والمناوبات
          </h1>
          <button
            className="add-employee-btn"
            onClick={() => setShowAddEmployeeModal(true)}
          >
            <i className="fas fa-plus"></i>
            إضافة موظف جديد
          </button>
        </div>

        {/* جدول الموظفين */}
        <div className="employees-table-section">
          <h2>جميع الموظفين ({employees.length})</h2>
          <div className="employees-table-container">
            <table className="employees-table">
              <thead>
                <tr>
                  <th>الاسم</th>
                  <th>اسم المستخدم</th>
                  <th>البريد الإلكتروني</th>
                  <th>الدور</th>
                  <th>الهاتف</th>
                  <th>الحالة</th>
                  <th>المناوبة</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {employees.map(employee => {
                  const activeShift = activeShifts.find(shift => shift.employeeId === employee._id);
                  const employeeOrders = orders.filter(order =>
                    employee.role === 'waiter'
                      ? order.waiterName === employee.username
                      : order.chefName === employee.username
                  );
                  const employeeSales = employeeOrders.reduce((sum, order) => sum + (order.totalAmount || 0), 0);

                  return (
                    <tr key={employee._id}>
                      <td>
                        <div className="employee-name">
                          <i className={`fas ${employee.role === 'waiter' ? 'fa-user-tie' : 'fa-chef-hat'}`}></i>
                          {employee.name}
                        </div>
                      </td>
                      <td>{employee.username}</td>
                      <td>{employee.email}</td>
                      <td>
                        <span className={`role-badge ${employee.role}`}>
                          {employee.role === 'waiter' ? 'نادل' : 'طباخ'}
                        </span>
                      </td>
                      <td>{employee.phone || '-'}</td>
                      <td>
                        <span className={`status-badge ${employee.status}`}>
                          {employee.status === 'active' ? 'نشط' : 'غير نشط'}
                        </span>
                      </td>
                      <td>
                        {activeShift ? (
                          <div className="shift-info">
                            <span className="shift-active">نشط</span>
                            <small>
                              {Math.floor((new Date().getTime() - new Date(activeShift.startTime).getTime()) / (1000 * 60 * 60))}س
                              {Math.floor(((new Date().getTime() - new Date(activeShift.startTime).getTime()) % (1000 * 60 * 60)) / (1000 * 60))}د
                            </small>
                          </div>
                        ) : (
                          <span className="shift-inactive">غير نشط</span>
                        )}
                      </td>
                      <td>
                        <div className="employee-actions">
                          <button
                            className="action-btn edit"
                            onClick={() => {
                              setSelectedEmployeeForEdit(employee);
                              setShowEditEmployeeModal(true);
                            }}
                            title="تعديل"
                          >
                            <i className="fas fa-edit"></i>
                          </button>

                          <button
                            className={`action-btn toggle ${employee.status === 'active' ? 'deactivate' : 'activate'}`}
                            onClick={() => toggleEmployeeStatus(employee._id, employee.status)}
                            title={employee.status === 'active' ? 'إلغاء التفعيل' : 'تفعيل'}
                          >
                            <i className={`fas ${employee.status === 'active' ? 'fa-ban' : 'fa-check'}`}></i>
                          </button>

                          {activeShift ? (
                            <button
                              className="action-btn end-shift"
                              onClick={() => endShift(activeShift._id)}
                              title="إنهاء المناوبة"
                            >
                              <i className="fas fa-stop"></i>
                            </button>
                          ) : (
                            <button
                              className="action-btn start-shift"
                              onClick={() => startShift(employee._id)}
                              disabled={employee.status !== 'active'}
                              title="بدء المناوبة"
                            >
                              <i className="fas fa-play"></i>
                            </button>
                          )}

                          <button
                            className="action-btn delete"
                            onClick={() => deleteEmployee(employee._id)}
                            title="حذف"
                          >
                            <i className="fas fa-trash"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>

        {/* المناوبات النشطة */}
        <div className="active-shifts-section">
          <h2>المناوبات النشطة ({activeShifts.length})</h2>
          <div className="shifts-grid">
            {activeShifts.map(shift => {
              const employee = employees.find(emp => emp._id === shift.employeeId);
              const shiftDuration = new Date().getTime() - new Date(shift.startTime).getTime();
              const hours = Math.floor(shiftDuration / (1000 * 60 * 60));
              const minutes = Math.floor((shiftDuration % (1000 * 60 * 60)) / (1000 * 60));

              return (
                <div key={shift._id} className="shift-card active">
                  <div className="employee-info">
                    <div className="employee-avatar">
                      <i className={`fas ${shift.role === 'waiter' ? 'fa-user-tie' : 'fa-chef-hat'}`}></i>
                    </div>
                    <div className="employee-details">
                      <h3>{shift.employeeName}</h3>
                      <span className="role">{shift.role === 'waiter' ? 'نادل' : 'طباخ'}</span>
                    </div>
                  </div>

                  <div className="shift-stats">
                    <div className="stat">
                      <span className="label">مدة المناوبة</span>
                      <span className="value">{hours}س {minutes}د</span>
                    </div>
                    <div className="stat">
                      <span className="label">الطلبات</span>
                      <span className="value">{shift.ordersCount}</span>
                    </div>
                    {shift.role === 'waiter' && (
                      <div className="stat">
                        <span className="label">المبيعات</span>
                        <span className="value">{shift.salesAmount.toFixed(2)} ج.م</span>
                      </div>
                    )}
                  </div>

                  <div className="shift-actions">
                    <button
                      className="end-shift-btn"
                      onClick={() => endShift(shift._id)}
                    >
                      إنهاء المناوبة
                    </button>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* الموظفون غير النشطون */}
        <div className="inactive-employees-section">
          <h2>الموظفون المتاحون ({inactiveEmployees.length})</h2>
          <div className="employees-grid">
            {inactiveEmployees.map(employee => (
              <div key={employee._id} className="employee-card">
                <div className="employee-info">
                  <div className="employee-avatar">
                    <i className={`fas ${employee.role === 'waiter' ? 'fa-user-tie' : 'fa-chef-hat'}`}></i>
                  </div>
                  <div className="employee-details">
                    <h3>{employee.username}</h3>
                    <span className="role">{employee.role === 'waiter' ? 'نادل' : 'طباخ'}</span>
                    <span className={`status ${employee.status}`}>
                      {employee.status === 'active' ? 'نشط' : 'غير نشط'}
                    </span>
                  </div>
                </div>

                <div className="employee-actions">
                  <button
                    className="start-shift-btn"
                    onClick={() => startShift(employee._id)}
                    disabled={employee.status !== 'active'}
                  >
                    بدء المناوبة
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* إحصائيات الأداء */}
        <div className="performance-stats">
          <h2>إحصائيات الأداء</h2>
          <div className="performance-grid">
            {employees.map(employee => {
              const employeeOrders = orders.filter(order =>
                employee.role === 'waiter'
                  ? order.waiterName === employee.username
                  : order.chefName === employee.username
              );
              
              // حساب مبيعات الموظف بطريقة صحيحة
              const employeeSales = employeeOrders.reduce((sum, order) => {
                let orderTotal = 0;
                
                if (order.totalAmount && typeof order.totalAmount === 'number') {
                  orderTotal = order.totalAmount;
                } else if (order.totalPrice && typeof order.totalPrice === 'number') {
                  orderTotal = order.totalPrice;
                } else if (order.totals && order.totals.total && typeof order.totals.total === 'number') {
                  orderTotal = order.totals.total;
                } else if (order.items && Array.isArray(order.items)) {
                  orderTotal = order.items.reduce((itemSum, item) => {
                    const itemPrice = (item.price || 0) * (item.quantity || 0);
                    return itemSum + itemPrice;
                  }, 0);
                }
                
                return sum + orderTotal;
              }, 0);
              
              const employeeShifts = shifts.filter(shift => shift.employeeId === employee._id);
              const totalShiftHours = employeeShifts.reduce((sum, shift) => {
                const duration = shift.endTime
                  ? new Date(shift.endTime).getTime() - new Date(shift.startTime).getTime()
                  : new Date().getTime() - new Date(shift.startTime).getTime();
                return sum + (duration / (1000 * 60 * 60));
              }, 0);

              return (
                <div key={employee._id} className="performance-card">
                  <div className="employee-header">
                    <i className={`fas ${employee.role === 'waiter' ? 'fa-user-tie' : 'fa-chef-hat'}`}></i>
                    <h3>{employee.username}</h3>
                    <span className="role">{employee.role === 'waiter' ? 'نادل' : 'طباخ'}</span>
                  </div>

                  <div className="performance-stats-grid">
                    <div className="perf-stat">
                      <span className="number">{employeeOrders.length}</span>
                      <span className="label">طلب</span>
                    </div>
                    {employee.role === 'waiter' && (
                      <div className="perf-stat">
                        <span className="number">{employeeSales.toFixed(2)}</span>
                        <span className="label">ج.م</span>
                      </div>
                    )}
                    <div className="perf-stat">
                      <span className="number">{employeeShifts.length}</span>
                      <span className="label">مناوبة</span>
                    </div>
                    <div className="perf-stat">
                      <span className="number">{totalShiftHours.toFixed(1)}</span>
                      <span className="label">ساعة</span>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    );
  };

  // رندر شاشة الطاولات
  const renderTablesScreen = () => (
    <div className="tables-screen">
      <div className="tables-header">
        <h1>
          <i className="fas fa-table"></i>
          إدارة الطاولات
        </h1>
      </div>

      <div className="tables-grid">
        {tableAccounts.map(table => (
          <div key={table._id} className={`table-card ${table.isOpen ? 'open' : 'closed'}`}>
            <div className="table-header">
              <span className="table-number">طاولة {table.tableNumber}</span>
              <span className={`table-status ${table.status}`}>
                {table.isOpen ? 'مفتوحة' : 'مغلقة'}
              </span>
            </div>            <div className="table-info">
              <div className="waiter-info">
                <i className="fas fa-user-tie"></i>
                <span>{table.waiterName || 'غير محدد'}</span>
              </div>
              
              {table.isOpen ? (
                <>
                  <div className="orders-count">
                    <i className="fas fa-shopping-cart"></i>
                    <span>{table.orders?.length || 0} طلب</span>
                  </div>
                  <div className="total-amount">
                    <i className="fas fa-money-bill-wave"></i>
                    <span>{(table.totalAmount || 0).toFixed(2)} ج.م</span>
                  </div>
                  <div className="table-time">
                    <i className="fas fa-clock"></i>
                    <span>فُتحت: {new Date(table.createdAt).toLocaleTimeString('ar-SA')}</span>
                  </div>
                </>
              ) : (
                <div className="closed-info">
                  <i className="fas fa-info-circle"></i>
                  <span>طاولة مغلقة</span>
                </div>
              )}
            </div>

            {table.isOpen && table.orders && table.orders.length > 0 && (
              <div className="table-orders">
                <h4>الطلبات الحديثة:</h4>
                {table.orders.slice(0, 3).map(order => (
                  <div key={order._id} className="table-order">
                    <span className="order-number">#{order.orderNumber}</span>
                    <span className={`status ${order.status}`}>
                      {order.status === 'pending' && 'انتظار'}
                      {order.status === 'preparing' && 'تحضير'}
                      {order.status === 'ready' && 'جاهز'}
                      {order.status === 'completed' && 'مكتمل'}
                      {order.status === 'delivered' && 'تم التوصيل'}
                    </span>
                    <span className="order-total">
                      {(order.totalAmount || order.totalPrice || 0).toFixed(2)} ج.م
                    </span>
                  </div>
                ))}
                {table.orders.length > 3 && (
                  <div className="more-orders">
                    <i className="fas fa-ellipsis-h"></i>
                    +{table.orders.length - 3} طلب آخر
                  </div>
                )}
              </div>
            )}

            {table.isOpen && (!table.orders || table.orders.length === 0) && (
              <div className="no-orders">
                <i className="fas fa-clipboard-list"></i>
                <span>لا توجد طلبات بعد</span>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );

  // رندر شاشة التقارير
  const renderReportsScreen = () => {
    console.log('📈 عرض شاشة التقارير:', {
      totalOrders: orders.length,
      totalEmployees: employees.length
    });

    const today = new Date();
    const todayOrders = orders.filter(order => {
      const orderDate = new Date(order.createdAt);
      return orderDate.toDateString() === today.toDateString();
    });

    const thisWeekOrders = orders.filter(order => {
      const orderDate = new Date(order.createdAt);
      const weekStart = new Date(today);
      weekStart.setDate(today.getDate() - today.getDay());
      return orderDate >= weekStart;
    });

    // حساب مبيعات اليوم بطريقة صحيحة
    const todaySales = todayOrders.reduce((sum, order) => {
      let orderTotal = 0;
      
      if (order.totalAmount && typeof order.totalAmount === 'number') {
        orderTotal = order.totalAmount;
      } else if (order.totalPrice && typeof order.totalPrice === 'number') {
        orderTotal = order.totalPrice;
      } else if (order.totals && order.totals.total && typeof order.totals.total === 'number') {
        orderTotal = order.totals.total;
      } else if (order.items && Array.isArray(order.items)) {
        orderTotal = order.items.reduce((itemSum, item) => {
          const itemPrice = (item.price || 0) * (item.quantity || 0);
          return itemSum + itemPrice;
        }, 0);
      }
      
      return sum + orderTotal;
    }, 0);

    // حساب مبيعات الأسبوع بطريقة صحيحة
    const weekSales = thisWeekOrders.reduce((sum, order) => {
      let orderTotal = 0;
      
      if (order.totalAmount && typeof order.totalAmount === 'number') {
        orderTotal = order.totalAmount;
      } else if (order.totalPrice && typeof order.totalPrice === 'number') {
        orderTotal = order.totalPrice;
      } else if (order.totals && order.totals.total && typeof order.totals.total === 'number') {
        orderTotal = order.totals.total;
      } else if (order.items && Array.isArray(order.items)) {
        orderTotal = order.items.reduce((itemSum, item) => {
          const itemPrice = (item.price || 0) * (item.quantity || 0);
          return itemSum + itemPrice;
        }, 0);
      }
      
      return sum + orderTotal;
    }, 0);

    console.log('💰 إحصائيات المبيعات:', {
      todayOrders: todayOrders.length,
      todaySales,
      weekOrders: thisWeekOrders.length,
      weekSales,
      totalSales: stats.totalSales
    });

    return (
      <div className="reports-screen">
        <div className="reports-header">
          <h1>
            <i className="fas fa-chart-bar"></i>
            التقارير والإحصائيات
          </h1>
        </div>

        {/* تقارير المبيعات */}
        <div className="sales-reports">
          <h2>تقارير المبيعات</h2>
          <div className="sales-stats-grid">
            <div className="sales-stat-card today">
              <div className="stat-icon">
                <i className="fas fa-calendar-day"></i>
              </div>
              <div className="stat-content">
                <h3>{todaySales.toFixed(2)} ج.م</h3>
                <p>مبيعات اليوم</p>
                <span className="orders-count">{todayOrders.length} طلب</span>
              </div>
            </div>

            <div className="sales-stat-card week">
              <div className="stat-icon">
                <i className="fas fa-calendar-week"></i>
              </div>
              <div className="stat-content">
                <h3>{weekSales.toFixed(2)} ج.م</h3>
                <p>مبيعات الأسبوع</p>
                <span className="orders-count">{thisWeekOrders.length} طلب</span>
              </div>
            </div>

            <div className="sales-stat-card total">
              <div className="stat-icon">
                <i className="fas fa-chart-line"></i>
              </div>
              <div className="stat-content">
                <h3>{stats.totalSales.toFixed(2)} ج.م</h3>
                <p>إجمالي المبيعات</p>
                <span className="orders-count">{stats.totalOrders} طلب</span>
              </div>
            </div>
          </div>
        </div>        {/* إحصائيات النُدل */}
        <div className="waiters-reports">
          <h2>إحصائيات النُدل</h2>
          <div className="waiters-stats-table">
            <table>
              <thead>
                <tr>
                  <th>النادل</th>
                  <th>عدد الطلبات</th>
                  <th>المبيعات</th>
                  <th>متوسط الطلب</th>
                  <th>الحالة</th>
                </tr>
              </thead>
              <tbody>
                {employees.filter(emp => emp.role === 'waiter').map(waiter => {
                  // البحث بعدة طرق للعثور على طلبات النادل
                  const waiterOrders = orders.filter(order => 
                    order.waiterName === waiter.username || 
                    order.waiterName === waiter.name ||
                    order.waiterId === waiter._id
                  );
                  
                  // حساب مبيعات النادل بطريقة صحيحة
                  const waiterSales = waiterOrders.reduce((sum, order) => {
                    let orderTotal = 0;
                    
                    if (order.totalAmount && typeof order.totalAmount === 'number') {
                      orderTotal = order.totalAmount;
                    } else if (order.totalPrice && typeof order.totalPrice === 'number') {
                      orderTotal = order.totalPrice;
                    } else if (order.totals && order.totals.total && typeof order.totals.total === 'number') {
                      orderTotal = order.totals.total;
                    } else if (order.items && Array.isArray(order.items)) {
                      orderTotal = order.items.reduce((itemSum, item) => {
                        const itemPrice = (item.price || 0) * (item.quantity || 0);
                        return itemSum + itemPrice;
                      }, 0);
                    }
                    
                    return sum + orderTotal;
                  }, 0);

                  const averageOrder = waiterOrders.length > 0 ? (waiterSales / waiterOrders.length) : 0;

                  return (
                    <tr key={waiter._id}>
                      <td>
                        <div className="waiter-info">
                          <strong>{waiter.name}</strong>
                          <small>({waiter.username})</small>
                        </div>
                      </td>
                      <td>{waiterOrders.length}</td>
                      <td>{waiterSales.toFixed(2)} ج.م</td>
                      <td>{averageOrder.toFixed(2)} ج.م</td>
                      <td>
                        <span className={`status-badge ${waiter.status}`}>
                          {waiter.status === 'active' ? 'نشط' : 'غير نشط'}
                        </span>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>

        {/* إحصائيات الطباخين */}
        <div className="chefs-reports">
          <h2>إحصائيات الطباخين</h2>
          <div className="chefs-stats-table">
            <table>
              <thead>
                <tr>
                  <th>الطباخ</th>
                  <th>الطلبات المحضرة</th>
                  <th>قيمة الطلبات</th>
                  <th>متوسط الطلب</th>
                  <th>الحالة</th>
                </tr>
              </thead>
              <tbody>
                {employees.filter(emp => emp.role === 'chef').map(chef => {
                  // البحث بعدة طرق للعثور على طلبات الطباخ
                  const chefOrders = orders.filter(order => 
                    (order.chefName === chef.username || 
                     order.chefName === chef.name ||
                     order.chefId === chef._id) &&
                    (order.status === 'preparing' || order.status === 'ready' || order.status === 'completed')
                  );
                  
                  // حساب مبيعات الطباخ بطريقة صحيحة
                  const chefSales = chefOrders.reduce((sum, order) => {
                    let orderTotal = 0;
                    
                    if (order.totalAmount && typeof order.totalAmount === 'number') {
                      orderTotal = order.totalAmount;
                    } else if (order.totalPrice && typeof order.totalPrice === 'number') {
                      orderTotal = order.totalPrice;
                    } else if (order.totals && order.totals.total && typeof order.totals.total === 'number') {
                      orderTotal = order.totals.total;
                    } else if (order.items && Array.isArray(order.items)) {
                      orderTotal = order.items.reduce((itemSum, item) => {
                        const itemPrice = (item.price || 0) * (item.quantity || 0);
                        return itemSum + itemPrice;
                      }, 0);
                    }
                    
                    return sum + orderTotal;
                  }, 0);

                  const averageOrder = chefOrders.length > 0 ? (chefSales / chefOrders.length) : 0;

                  return (
                    <tr key={chef._id}>
                      <td>
                        <div className="chef-info">
                          <strong>{chef.name}</strong>
                          <small>({chef.username})</small>
                        </div>
                      </td>
                      <td>{chefOrders.length}</td>
                      <td>{chefSales.toFixed(2)} ج.م</td>
                      <td>{averageOrder.toFixed(2)} ج.م</td>
                      <td>
                        <span className={`status-badge ${chef.status}`}>
                          {chef.status === 'active' ? 'نشط' : 'غير نشط'}
                        </span>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>        </div>
      </div>
    );
  };
  };
  // دوال إدارة المنتجات المحسنة
  const [showAddProductModal, setShowAddProductModal] = useState(false);
  const [showEditProductModal, setShowEditProductModal] = useState(false);
  const [selectedProductForEdit, setSelectedProductForEdit] = useState<MenuItem | null>(null);
  const [newProduct, setNewProduct] = useState({
    name: '',
    description: '',
    price: 0,
    categories: [] as string[],
    available: true,
    stock: 0
  });

  // إضافة منتج جديد  const addProduct = async () => {
    try {
      if (!newProduct.name || newProduct.price <= 0) {
        showError('اسم المنتج والسعر مطلوبان');
        return;
      }

      if (newProduct.categories.length === 0) {
        showError('يجب اختيار فئة واحدة على الأقل');
        return;
      }

      console.log('🔄 إضافة منتج جديد:', newProduct);

      const response = await authenticatedPost('/api/products', {
        name: newProduct.name,
        description: newProduct.description,
        price: newProduct.price,
        category: newProduct.categories[0], // First category as main category
        categories: newProduct.categories, // All categories
        available: newProduct.available,
        stock: {
          quantity: newProduct.stock,
          unit: 'قطعة',
          lowStockAlert: 10
        }
      });

      console.log('📊 استجابة إضافة المنتج:', response);

      if (response.success || response._id) {
        showSuccess('تم إضافة المنتج بنجاح');
        setShowAddProductModal(false);
        setNewProduct({
          name: '',
          description: '',
          price: 0,
          categories: [],
          available: true,
          stock: 0
        });
        await fetchMenuItems();
      } else {
        showError(response.message || 'فشل في إضافة المنتج');
      }
    } catch (error) {
      console.error('خطأ في إضافة المنتج:', error);
      showError('فشل في إضافة المنتج');
    }
  };

  // تعديل منتج
  const updateProduct = async () => {
    try {
      if (!selectedProductForEdit) return;

      const response = await authenticatedPut(`/api/products/${selectedProductForEdit._id}`, {
        name: selectedProductForEdit.name,
        description: selectedProductForEdit.description,
        price: selectedProductForEdit.price,
        categories: selectedProductForEdit.categories,
        available: selectedProductForEdit.available,        stock: {
          quantity: typeof selectedProductForEdit.stock === 'number' 
            ? selectedProductForEdit.stock 
            : selectedProductForEdit.stock?.quantity || 0,
          unit: 'قطعة',
          lowStockAlert: 10
        }
      });

      if (response.success) {
        showSuccess('تم تحديث المنتج بنجاح');
        setShowEditProductModal(false);
        setSelectedProductForEdit(null);
        fetchMenuItems();
      } else {
        showError(response.message || 'فشل في تحديث المنتج');
      }
    } catch (error) {
      console.error('خطأ في تحديث المنتج:', error);
      showError('فشل في تحديث المنتج');
    }
  };
  // حذف منتج مع تأكيد محسن
  const handleDeleteProduct = async (product: MenuItem) => {
    const confirmed = window.confirm(
      `هل أنت متأكد من حذف المنتج "${product.name}"؟\n\n` +
      `السعر: ${product.price} ج.م\n` +
      `الحالة: ${product.available ? 'متوفر' : 'غير متوفر'}\n\n` +
      `⚠️ هذا الإجراء لا يمكن التراجع عنه!`
    );
    
    if (!confirmed) return;

    try {
      const response = await authenticatedDelete(`/api/products/${product._id}`);

      if (response.success) {
        showSuccess(`تم حذف منتج "${product.name}" بنجاح`);
        fetchMenuItems();
      } else {
        showError(response.message || 'فشل في حذف المنتج');
      }
    } catch (error) {
      console.error('خطأ في حذف المنتج:', error);
      showError('فشل في حذف المنتج');
    }
  };

  // حذف منتج (الدالة القديمة للتوافق)
  const deleteProduct = async (productId: string) => {
    const product = menuItems.find(item => item._id === productId);
    if (product) {
      await handleDeleteProduct(product);
    }
  };

  // تغيير حالة توفر المنتج
  const toggleProductAvailability = async (productId: string, currentAvailability: boolean) => {
    try {
      const response = await authenticatedPut(`/api/products/${productId}`, {
        available: !currentAvailability
      });

      if (response.success) {
        showSuccess(`تم ${!currentAvailability ? 'تفعيل' : 'إلغاء تفعيل'} المنتج بنجاح`);
        fetchMenuItems();
      } else {
        showError(response.message || 'فشل في تغيير حالة المنتج');
      }
    } catch (error) {
      console.error('خطأ في تغيير حالة المنتج:', error);
      showError('فشل في تغيير حالة المنتج');
    }
  };

  // رندر شاشة المخزون
  const renderInventoryScreen = () => (
    <div className="inventory-screen">
      <div className="inventory-header">
        <h1>
          <i className="fas fa-boxes"></i>
          إدارة المخزون
        </h1>
      </div>

      <div className="inventory-grid">
        {menuItems.map(item => {          // التحقق من وجود بيانات المخزون وتجنب React Error #31
          const stockQuantity = typeof item.stock === 'number' 
            ? item.stock 
            : item.stock?.quantity || 0;
          const isLowStock = stockQuantity < 10;

          return (
            <div key={item._id} className={`inventory-item ${!item.available ? 'unavailable' : ''}`}>
              <div className="item-header">
                <h3>{item.name}</h3>
                <span className={`availability ${item.available ? 'available' : 'unavailable'}`}>
                  {item.available ? 'متوفر' : 'غير متوفر'}
                </span>
              </div>

              <div className="item-details">
                <div className="price">{item.price} ج.م</div>
                <div className={`stock ${isLowStock ? 'low' : ''}`}>
                  المخزون: {stockQuantity}
                  {isLowStock && <i className="fas fa-exclamation-triangle"></i>}
                </div>
              </div>

              {item.description && (
                <div className="item-description">{item.description}</div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
  // رندر شاشة القائمة
  const renderMenuScreen = () => (
    <div className="menu-screen">
      <div className="menu-header">
        <h1>
          <i className="fas fa-coffee"></i>
          إدارة القائمة
        </h1>
        <div className="header-actions">
          <button
            className="add-product-btn"
            onClick={() => setShowAddProductModal(true)}
          >
            <i className="fas fa-plus"></i>
            إضافة منتج جديد
          </button>
          <button
            className="refresh-btn"
            onClick={() => {
              fetchMenuItems();
              fetchCategories();
            }}
            title="تحديث القائمة"
          >
            <i className="fas fa-sync-alt"></i>
            تحديث
          </button>
        </div>
      </div>

      <div className="menu-stats">
        <div className="stat-card">
          <i className="fas fa-coffee"></i>
          <div className="stat-content">
            <span className="count">{menuItems.length}</span>
            <span className="label">إجمالي المنتجات</span>
          </div>
        </div>
        <div className="stat-card available">
          <i className="fas fa-check-circle"></i>
          <div className="stat-content">
            <span className="count">{menuItems.filter(item => item.available).length}</span>
            <span className="label">منتجات متوفرة</span>
          </div>
        </div>
        <div className="stat-card unavailable">
          <i className="fas fa-times-circle"></i>
          <div className="stat-content">
            <span className="count">{menuItems.filter(item => !item.available).length}</span>
            <span className="label">منتجات غير متوفرة</span>
          </div>
        </div>
        <div className="stat-card">
          <i className="fas fa-tags"></i>
          <div className="stat-content">
            <span className="count">{categories.length}</span>
            <span className="label">الفئات</span>
          </div>
        </div>
      </div>

      {/* فلتر القائمة */}
      <div className="menu-filters">
        <div className="filter-group">
          <label>فلترة حسب الفئة:</label>
          <select 
            value={selectedCategoryFilter || 'all'} 
            onChange={(e) => setSelectedCategoryFilter(e.target.value === 'all' ? null : e.target.value)}
            className="category-filter"
          >
            <option value="all">جميع الفئات</option>
            {categories.map(category => (
              <option key={category._id} value={category._id}>
                {category.name}
              </option>
            ))}
          </select>
        </div>
        
        <div className="filter-group">
          <label>فلترة حسب التوفر:</label>
          <select 
            value={availabilityFilter} 
            onChange={(e) => setAvailabilityFilter(e.target.value)}
            className="availability-filter"
          >
            <option value="all">الكل</option>
            <option value="available">متوفر فقط</option>
            <option value="unavailable">غير متوفر فقط</option>
          </select>
        </div>

        <div className="filter-group">
          <label>البحث:</label>
          <div className="search-input-group">
            <i className="fas fa-search"></i>
            <input
              type="text"
              placeholder="البحث في المنتجات..."
              value={menuSearchTerm}
              onChange={(e) => setMenuSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>
        </div>
      </div>

      <div className="menu-categories">
        {categories
          .filter(category => !selectedCategoryFilter || category._id === selectedCategoryFilter)
          .map(category => {
            const categoryItems = menuItems
              .filter(item => {
                // فلتر الفئة
                const categoryMatch = item.categories?.includes(category._id);
                
                // فلتر التوفر
                const availabilityMatch = availabilityFilter === 'all' || 
                  (availabilityFilter === 'available' && item.available) ||
                  (availabilityFilter === 'unavailable' && !item.available);
                
                // فلتر البحث
                const searchMatch = menuSearchTerm === '' ||
                  item.name.toLowerCase().includes(menuSearchTerm.toLowerCase()) ||
                  (item.description && item.description.toLowerCase().includes(menuSearchTerm.toLowerCase()));
                
                return categoryMatch && availabilityMatch && searchMatch;
              });

            // لا تعرض الفئة إذا لم تحتوي على منتجات بعد الفلترة
            if (categoryItems.length === 0 && (selectedCategoryFilter || availabilityFilter !== 'all' || menuSearchTerm)) {
              return null;
            }

            return (
              <div key={category._id} className="category-section">
                <div className="category-header" style={{ backgroundColor: category.color }}>
                  <div className="category-info">
                    {category.icon && <i className={category.icon}></i>}
                    <h2>{category.name}</h2>
                    <span className="items-count">({categoryItems.length} عنصر)</span>
                  </div>
                  <div className="category-actions">
                    <button
                      className="category-stats-btn"
                      title={`${categoryItems.filter(item => item.available).length} متوفر من ${categoryItems.length}`}
                    >
                      <i className="fas fa-chart-bar"></i>
                      {categoryItems.filter(item => item.available).length}/{categoryItems.length}
                    </button>
                  </div>
                </div>

                <div className="category-items">
                  {categoryItems.length === 0 ? (
                    <div className="empty-category">
                      <i className="fas fa-coffee"></i>
                      <p>لا توجد منتجات في هذه الفئة</p>
                      <button 
                        className="add-product-to-category-btn"
                        onClick={() => {
                          setNewProduct(prev => ({
                            ...prev,
                            categories: [category._id]
                          }));
                          setShowAddProductModal(true);
                        }}
                      >
                        <i className="fas fa-plus"></i>
                        إضافة منتج لهذه الفئة
                      </button>
                    </div>
                  ) : (
                    categoryItems.map(item => (
                      <div key={item._id} className={`menu-item ${!item.available ? 'unavailable' : ''}`}>
                        <div className="item-info">
                          <div className="item-header">
                            <h3>{item.name}</h3>
                            <div className={`item-status-badge ${item.available ? 'available' : 'unavailable'}`}>
                              <i className={`fas ${item.available ? 'fa-check' : 'fa-times'}`}></i>
                              {item.available ? 'متوفر' : 'غير متوفر'}
                            </div>
                          </div>
                          {item.description && <p className="item-description">{item.description}</p>}
                          <div className="item-details">
                            <div className="item-price">
                              <i className="fas fa-money-bill-wave"></i>
                              {item.price} ج.م
                            </div>
                            {item.categories && item.categories.length > 1 && (
                              <div className="item-categories">
                                <i className="fas fa-tags"></i>
                                {item.categories.length - 1} فئة أخرى
                              </div>
                            )}
                          </div>
                        </div>
                        
                        <div className="item-actions">
                          <div className="action-buttons">
                            <button
                              className="edit-btn"
                              onClick={() => {
                                setSelectedProductForEdit(item);
                                setShowEditProductModal(true);
                              }}
                              title="تعديل المنتج"
                            >
                              <i className="fas fa-edit"></i>
                              <span>تعديل</span>
                            </button>
                            
                            <button
                              className={`toggle-btn ${item.available ? 'disable' : 'enable'}`}
                              onClick={() => toggleProductAvailability(item._id, item.available)}
                              title={item.available ? 'إلغاء التوفر' : 'تفعيل التوفر'}
                            >
                              <i className={`fas ${item.available ? 'fa-eye-slash' : 'fa-eye'}`}></i>
                              <span>{item.available ? 'إلغاء' : 'تفعيل'}</span>
                            </button>
                            
                            <button
                              className="delete-btn"
                              onClick={() => handleDeleteProduct(item)}
                              title="حذف المنتج"
                            >
                              <i className="fas fa-trash"></i>
                              <span>حذف</span>
                            </button>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>
            );
          })}

        {categories.length === 0 && (
          <div className="empty-menu">
            <i className="fas fa-coffee"></i>
            <h3>لا توجد فئات</h3>
            <p>قم بإضافة فئة أولاً لتتمكن من إدارة المنتجات</p>
            <button 
              className="add-category-btn"
              onClick={() => {
                setSelectedCategory(null);
                setShowCategoryModal(true);
              }}
            >
              <i className="fas fa-plus"></i>
              إضافة فئة جديدة
            </button>
          </div>
        )}
      </div>
    </div>
  );

  // رندر شاشة الفئات
  const renderCategoriesScreen = () => (
    <div className="categories-screen">
      <div className="categories-header">
        <h1>
          <i className="fas fa-tags"></i>
          إدارة الفئات
        </h1>
        <button 
          className="add-category-btn"
          onClick={() => {
            setSelectedCategory(null);
            setShowCategoryModal(true);
          }}
        >
          <i className="fas fa-plus"></i>
          إضافة فئة جديدة
        </button>
      </div>

      <div className="categories-stats">
        <div className="stat-card">
          <i className="fas fa-tags"></i>
          <span className="count">{categories.length}</span>
          <span className="label">إجمالي الفئات</span>
        </div>
        <div className="stat-card">
          <i className="fas fa-coffee"></i>
          <span className="count">{menuItems.length}</span>
          <span className="label">إجمالي المنتجات</span>
        </div>
      </div>

      <div className="categories-grid">
        {categories.map(category => {
          const categoryItems = menuItems.filter(item =>
            item.categories?.includes(category._id)
          );

          return (
            <div key={category._id} className="category-card">
              <div className="category-header" style={{ backgroundColor: category.color }}>
                {category.icon && <i className={category.icon}></i>}
                <h3>{category.name}</h3>
              </div>

              <div className="category-info">
                {category.description && (
                  <p className="category-description">{category.description}</p>
                )}
                <div className="category-stats">
                  <span className="items-count">{categoryItems.length} عنصر</span>
                  <span className="color-code" style={{ backgroundColor: category.color }}>
                    {category.color}
                  </span>
                </div>
              </div>

              <div className="category-actions">
                <button
                  className="edit-category-btn"
                  onClick={() => {
                    setSelectedCategory(category);
                    setShowCategoryModal(true);
                  }}
                  title="تعديل الفئة"
                >
                  <i className="fas fa-edit"></i>
                  تعديل
                </button>
                
                <button
                  className="delete-category-btn"
                  onClick={() => deleteCategory(category._id)}
                  title="حذف الفئة"
                  disabled={categoryItems.length > 0}
                >
                  <i className="fas fa-trash"></i>
                  حذف
                </button>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );

  return (
    <div className="manager-dashboard">
      {/* Header */}
      <header className="manager-header">
        <div className="header-content">
          <div className="header-left">
            <button
              className="sidebar-toggle"
              onClick={() => setSidebarOpen(!sidebarOpen)}
            >
              ☰
            </button>
            <h1>لوحة المدير</h1>
          </div>
          <div className="header-right">
            <span className="manager-name">مرحباً، {managerName}</span>
            <button className="logout-btn" onClick={handleLogout}>
              تسجيل الخروج
            </button>
          </div>
        </div>
      </header>

      <div className="dashboard-content">
        {/* Sidebar */}
        <aside className={`manager-sidebar ${sidebarOpen ? 'open' : ''}`}>
          <div className="sidebar-content">
            <div className="manager-profile">
              <div className="manager-avatar">👨‍💼</div>
              <h3>{managerName}</h3>
              <p>مدير</p>
            </div>

            <nav className="manager-nav">
              <button
                className={`nav-btn ${currentScreen === 'home' ? 'active' : ''}`}
                onClick={() => setCurrentScreen('home')}
              >
                <i className="fas fa-home"></i>
                <span>الرئيسية</span>
              </button>

              <button
                className={`nav-btn ${currentScreen === 'orders' ? 'active' : ''}`}
                onClick={() => setCurrentScreen('orders')}
              >
                <i className="fas fa-shopping-cart"></i>
                <span>الطلبات</span>
              </button>

              <button
                className={`nav-btn ${currentScreen === 'employees' ? 'active' : ''}`}
                onClick={() => setCurrentScreen('employees')}
              >
                <i className="fas fa-users"></i>
                <span>الموظفون</span>
              </button>

              <button
                className={`nav-btn ${currentScreen === 'tables' ? 'active' : ''}`}
                onClick={() => setCurrentScreen('tables')}
              >
                <i className="fas fa-table"></i>
                <span>الطاولات</span>
              </button>

              <button
                className={`nav-btn ${currentScreen === 'reports' ? 'active' : ''}`}
                onClick={() => setCurrentScreen('reports')}
              >
                <i className="fas fa-chart-bar"></i>
                <span>التقارير</span>
              </button>

              <button
                className={`nav-btn ${currentScreen === 'inventory' ? 'active' : ''}`}
                onClick={() => setCurrentScreen('inventory')}
              >
                <i className="fas fa-boxes"></i>
                <span>المخزون</span>
              </button>

              <button
                className={`nav-btn ${currentScreen === 'menu' ? 'active' : ''}`}
                onClick={() => setCurrentScreen('menu')}
              >
                <i className="fas fa-coffee"></i>
                <span>القائمة</span>
              </button>

              <button
                className={`nav-btn ${currentScreen === 'categories' ? 'active' : ''}`}
                onClick={() => setCurrentScreen('categories')}
              >
                <i className="fas fa-tags"></i>
                <span>الفئات</span>
              </button>
            </nav>
          </div>
        </aside>

        {/* Main Content */}
        <main className="manager-main">
          {loading ? (
            <div className="loading">
              <i className="fas fa-spinner fa-spin"></i>
              <span>جاري التحميل...</span>
            </div>
          ) : (
            <>
              {currentScreen === 'home' && renderHomeScreen()}
              {currentScreen === 'orders' && renderOrdersScreen()}
              {currentScreen === 'employees' && renderEmployeesScreen()}
              {currentScreen === 'tables' && renderTablesScreen()}
              {currentScreen === 'reports' && renderReportsScreen()}
              {currentScreen === 'inventory' && renderInventoryScreen()}
              {currentScreen === 'menu' && renderMenuScreen()}
              {currentScreen === 'categories' && renderCategoriesScreen()}
            </>
          )}
        </main>
      </div>

      {/* Order Details Modal */}
      {showOrderDetailsModal && selectedOrder && (
        <div className="modal-overlay" onClick={() => setShowOrderDetailsModal(false)}>
          <div className="modal-content" onClick={e => e.stopPropagation()}>
            <div className="modal-header">
              <h3>تفاصيل الطلب #{selectedOrder.orderNumber}</h3>
              <button
                className="close-btn"
                onClick={() => setShowOrderDetailsModal(false)}
              >
                ×
              </button>
            </div>

            <div className="modal-body">
              <div className="order-details">
                <div className="detail-row">
                  <span className="label">الحالة:</span>
                  <span className={`value status ${selectedOrder.status}`}>
                    {selectedOrder.status === 'pending' && 'قيد الانتظار'}
                    {selectedOrder.status === 'preparing' && 'قيد التحضير'}
                    {selectedOrder.status === 'ready' && 'جاهز'}
                    {selectedOrder.status === 'completed' && 'مكتمل'}
                  </span>
                </div>

                {selectedOrder.tableNumber && (
                  <div className="detail-row">
                    <span className="label">الطاولة:</span>
                    <span className="value">{selectedOrder.tableNumber}</span>
                  </div>
                )}

                {selectedOrder.customerName && (
                  <div className="detail-row">
                    <span className="label">العميل:</span>
                    <span className="value">{selectedOrder.customerName}</span>
                  </div>
                )}

                {selectedOrder.waiterName && (
                  <div className="detail-row">
                    <span className="label">النادل:</span>
                    <span className="value">{selectedOrder.waiterName}</span>
                  </div>
                )}

                {selectedOrder.chefName && (
                  <div className="detail-row">
                    <span className="label">الطباخ:</span>
                    <span className="value">{selectedOrder.chefName}</span>
                  </div>
                )}

                <div className="detail-row">
                  <span className="label">الوقت:</span>
                  <span className="value">
                    {new Date(selectedOrder.createdAt).toLocaleString('ar-SA')}
                  </span>
                </div>

                <div className="detail-row">
                  <span className="label">المجموع:</span>
                  <span className="value total">{selectedOrder.totalAmount} ج.م</span>
                </div>
              </div>

              <div className="order-items-details">
                <h4>الأصناف:</h4>
                <div className="items-list">
                  {selectedOrder.items.map((item, index) => (
                    <div key={index} className="item-detail">
                      <div className="item-info">
                        <span className="item-name">{item.name}</span>
                        <span className="item-quantity">× {item.quantity}</span>
                      </div>
                      <div className="item-price">{(item.price * item.quantity).toFixed(2)} ج.م</div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Add Product Modal */}
      {showAddProductModal && (
        <div className="modal-overlay" onClick={() => setShowAddProductModal(false)}>
          <div className="modal-content" onClick={e => e.stopPropagation()}>
            <div className="modal-header">
              <h3>
                <i className="fas fa-plus"></i>
                إضافة منتج جديد
              </h3>
              <button
                className="close-btn"
                onClick={() => setShowAddProductModal(false)}
              >
                ×
              </button>
            </div>

            <div className="modal-body">
              <form
                onSubmit={async (e) => {
                  e.preventDefault();
                  await addProduct();
                }}
              >
                <div className="form-group">
                  <label>اسم المنتج</label>
                  <input
                    type="text"
                    value={newProduct.name}
                    onChange={(e) => setNewProduct({...newProduct, name: e.target.value})}
                    required
                    placeholder="أدخل اسم المنتج"
                  />
                </div>

                <div className="form-group">
                  <label>الوصف</label>
                  <textarea
                    value={newProduct.description}
                    onChange={(e) => setNewProduct({...newProduct, description: e.target.value})}
                    placeholder="وصف المنتج (اختياري)"
                    rows={3}
                  />
                </div>

                <div className="form-group">
                  <label>السعر (ج.م)</label>
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    value={newProduct.price}
                    onChange={(e) => setNewProduct({...newProduct, price: parseFloat(e.target.value) || 0})}
                    required
                    placeholder="0.00"
                  />
                </div>

                <div className="form-group">
                  <label>الفئات</label>
                  <select
                    multiple
                    value={newProduct.categories}
                    onChange={(e) => {
                      const selectedCategories = Array.from(e.target.selectedOptions, option => option.value);
                      setNewProduct({...newProduct, categories: selectedCategories});
                    }}
                  >
                    {categories.map(category => (
                      <option key={category._id} value={category._id}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                  <small>اضغط Ctrl للاختيار المتعدد</small>
                </div>

                <div className="form-group">
                  <label>المخزون</label>
                  <input
                    type="number"
                    min="0"
                    value={newProduct.stock}
                    onChange={(e) => setNewProduct({...newProduct, stock: parseInt(e.target.value) || 0})}
                    placeholder="الكمية المتوفرة"
                  />
                </div>

                <div className="form-group checkbox-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={newProduct.available}
                      onChange={(e) => setNewProduct({...newProduct, available: e.target.checked})}
                    />
                    متوفر للطلب
                  </label>
                </div>

                <div className="modal-actions">
                  <button type="submit" className="save-btn">
                    <i className="fas fa-save"></i>
                    إضافة المنتج
                  </button>
                  <button
                    type="button"
                    className="cancel-btn"
                    onClick={() => setShowAddProductModal(false)}
                  >
                    إلغاء
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>      )}

      {/* Edit Product Modal */}
      {showEditProductModal && selectedProductForEdit && (
        <div className="modal-overlay" onClick={() => setShowEditProductModal(false)}>
          <div className="modal-content" onClick={e => e.stopPropagation()}>
            <div className="modal-header">
              <h3>
                <i className="fas fa-edit"></i>
                تعديل المنتج
              </h3>
              <button
                className="close-btn"
                onClick={() => setShowEditProductModal(false)}
              >
                ×
              </button>
            </div>

            <div className="modal-body">
              <form
                onSubmit={async (e) => {
                  e.preventDefault();
                  await updateProduct();
                }}
              >
                <div className="form-group">
                  <label>اسم المنتج</label>
                  <input
                    type="text"
                    value={selectedProductForEdit.name}
                    onChange={(e) => setSelectedProductForEdit({...selectedProductForEdit, name: e.target.value})}
                    required
                    placeholder="أدخل اسم المنتج"
                  />
                </div>

                <div className="form-group">
                  <label>الوصف</label>
                  <textarea
                    value={selectedProductForEdit.description || ''}
                    onChange={(e) => setSelectedProductForEdit({...selectedProductForEdit, description: e.target.value})}
                    placeholder="وصف المنتج (اختياري)"
                    rows={3}
                  />
                </div>

                <div className="form-group">
                  <label>السعر (ج.م)</label>
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    value={selectedProductForEdit.price}
                    onChange={(e) => setSelectedProductForEdit({...selectedProductForEdit, price: parseFloat(e.target.value) || 0})}
                    required
                    placeholder="0.00"
                  />
                </div>

                <div className="form-group">
                  <label>الفئات</label>
                  <select
                    multiple
                    value={selectedProductForEdit.categories || []}
                    onChange={(e) => {
                      const selectedCategories = Array.from(e.target.selectedOptions, option => option.value);
                      setSelectedProductForEdit({...selectedProductForEdit, categories: selectedCategories});
                    }}
                  >
                    {categories.map(category => (
                      <option key={category._id} value={category._id}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                  <small>اضغط Ctrl للاختيار المتعدد</small>
                </div>

                <div className="form-group">
                  <label>المخزون</label>
                  <input
                    type="number"
                    min="0"
                    value={typeof selectedProductForEdit.stock === 'number' 
                      ? selectedProductForEdit.stock 
                      : selectedProductForEdit.stock?.quantity || 0}
                    onChange={(e) => {
                      const stockValue = parseInt(e.target.value) || 0;
                      setSelectedProductForEdit({
                        ...selectedProductForEdit, 
                        stock: stockValue
                      });
                    }}
                    placeholder="الكمية المتوفرة"
                  />
                </div>

                <div className="form-group checkbox-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={selectedProductForEdit.available}
                      onChange={(e) => setSelectedProductForEdit({...selectedProductForEdit, available: e.target.checked})}
                    />
                    متوفر للطلب
                  </label>
                </div>

                <div className="modal-actions">
                  <button type="submit" className="save-btn">
                    <i className="fas fa-save"></i>
                    تحديث المنتج
                  </button>
                  <button
                    type="button"
                    className="cancel-btn"
                    onClick={() => setShowEditProductModal(false)}
                  >
                    إلغاء
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Menu Item Modal */}
      {showMenuModal && (
        <div className="modal-overlay" onClick={() => setShowMenuModal(false)}>
          <div className="modal-content" onClick={e => e.stopPropagation()}>
            <div className="modal-header">
              <h3>
                <i className="fas fa-coffee"></i>
                {selectedMenuItem ? 'تعديل المنتج' : 'إضافة منتج جديد'}
              </h3>
              <button
                className="close-btn"
                onClick={() => setShowMenuModal(false)}
              >
                ×
              </button>
            </div>

            <div className="modal-body">
              <form
                onSubmit={async (e) => {
                  e.preventDefault();
                  const formData = new FormData(e.target as HTMLFormElement);
                  
                  const itemData = {
                    name: formData.get('name') as string,
                    price: parseFloat(formData.get('price') as string),
                    description: formData.get('description') as string,
                    category: formData.get('category') as string,
                    available: formData.get('available') === 'on'
                  };

                  let success = false;
                  if (selectedMenuItem) {
                    success = await editMenuItem(selectedMenuItem._id, itemData);
                  } else {
                    success = await addMenuItem(itemData);
                  }

                  if (success) {
                    setShowMenuModal(false);
                    setSelectedMenuItem(null);
                  }
                }}
              >
                <div className="form-group">
                  <label>اسم المنتج</label>
                  <input
                    type="text"
                    name="name"
                    defaultValue={selectedMenuItem?.name || ''}
                    required
                    placeholder="أدخل اسم المنتج"
                  />
                </div>

                <div className="form-group">
                  <label>السعر (ج.م)</label>
                  <input
                    type="number"
                    name="price"
                    step="0.01"
                    min="0"
                    defaultValue={selectedMenuItem?.price || ''}
                    required
                    placeholder="0.00"
                  />
                </div>

                <div className="form-group">
                  <label>الوصف</label>
                  <textarea
                    name="description"
                    rows={3}
                    defaultValue={selectedMenuItem?.description || ''}
                    placeholder="وصف المنتج (اختياري)"
                  />
                </div>

                <div className="form-group">
                  <label>الفئة</label>
                  <select
                    name="category"
                    defaultValue={selectedMenuItem?.categories?.[0] || ''}
                    required
                  >
                    <option value="">اختر الفئة</option>
                    {categories.map(category => (
                      <option key={category._id} value={category._id}>
                                               {category.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="form-group checkbox-group">
                  <label>
                    <input
                      type="checkbox"
                      name="available"
                      defaultChecked={selectedMenuItem?.available ?? true}
                    />
                    متوفر للطلب
                  </label>
                </div>

                <div className="modal-actions">
                  <button type="submit" className="save-btn">
                    <i className="fas fa-save"></i>
                    {selectedMenuItem ? 'تحديث' : 'إضافة'}
                  </button>
                  <button
                    type="button"
                    className="cancel-btn"
                    onClick={() => setShowMenuModal(false)}
                  >
                    إلغاء
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Category Modal */}
      {showCategoryModal && (
        <div className="modal-overlay" onClick={() => setShowCategoryModal(false)}>
          <div className="modal-content" onClick={e => e.stopPropagation()}>
            <div className="modal-header">
              <h3>
                <i className="fas fa-tags"></i>
                {selectedCategory ? 'تعديل الفئة' : 'إضافة فئة جديدة'}
              </h3>
              <button
                className="close-btn"
                onClick={() => setShowCategoryModal(false)}
              >
                ×
              </button>
            </div>

            <div className="modal-body">
              <form
                onSubmit={async (e) => {
                  e.preventDefault();
                  const formData = new FormData(e.target as HTMLFormElement);
                  
                  const categoryData = {
                    name: formData.get('name') as string,
                    description: formData.get('description') as string,
                    color: formData.get('color') as string,
                    icon: formData.get('icon') as string
                  };

                  let success = false;
                  if (selectedCategory) {
                    success = await editCategory(selectedCategory._id, categoryData);
                  } else {
                    success = await addCategory(categoryData);
                  }

                  if (success) {
                    setShowCategoryModal(false);
                    setSelectedCategory(null);
                  }
                }}
              >
                <div className="form-group">
                  <label>اسم الفئة</label>
                  <input
                    type="text"
                    name="name"
                    defaultValue={selectedCategory?.name || ''}
                    required
                    placeholder="أدخل اسم الفئة"
                  />
                </div>

                <div className="form-group">
                  <label>الوصف</label>
                  <textarea
                    name="description"
                    rows={3}
                    defaultValue={selectedCategory?.description || ''}
                    placeholder="وصف الفئة (اختياري)"
                  />
                </div>

                <div className="form-group">
                  <label>اللون</label>
                  <div className="color-input-group">
                    <input
                      type="color"
                      name="color"
                      defaultValue={selectedCategory?.color || '#8B4513'}
                    />
                    <span className="color-preview" style={{ backgroundColor: selectedCategory?.color || '#8B4513' }}></span>
                  </div>
                </div>

                <div className="form-group">
                  <label>الأيقونة (اختياري)</label>
                  <input
                    type="text"
                    name="icon"
                    defaultValue={selectedCategory?.icon || ''}
                    placeholder="مثال: fas fa-coffee"
                  />
                  <small>استخدم أيقونات Font Awesome مثل: fas fa-coffee, fas fa-hamburger</small>
                </div>

                <div className="modal-actions">
                  <button type="submit" className="save-btn">
                    <i className="fas fa-save"></i>
                    {selectedCategory ? 'تحديث' : 'إضافة'}
                  </button>
                  <button
                    type="button"
                    className="cancel-btn"
                    onClick={() => setShowCategoryModal(false)}
                  >
                    إلغاء
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Employee Add Modal */}
      {showAddEmployeeModal && (
        <div className="modal-overlay" onClick={() => setShowAddEmployeeModal(false)}>
          <div className="modal-content" onClick={e => e.stopPropagation()}>
            <div className="modal-header">
              <h3>
                <i className="fas fa-user-plus"></i>
                إضافة موظف جديد
              </h3>
              <button
                className="close-btn"
                onClick={() => setShowAddEmployeeModal(false)}
              >
                ×
              </button>
            </div>

            <div className="modal-body">
              <form
                onSubmit={async (e) => {
                  e.preventDefault();
                  await addEmployee();
                }}
              >
                <div className="form-group">
                  <label>اسم المستخدم</label>
                  <input
                    type="text"
                    value={newEmployee.username}
                    onChange={(e) => setNewEmployee({...newEmployee, username: e.target.value})}
                    required
                    placeholder="أدخل اسم المستخدم"
                  />
                </div>

                <div className="form-group">
                  <label>الاسم الكامل</label>
                  <input
                    type="text"
                    value={newEmployee.name}
                    onChange={(e) => setNewEmployee({...newEmployee, name: e.target.value})}
                    required
                    placeholder="أدخل الاسم الكامل"
                  />
                </div>

                <div className="form-group">
                  <label>البريد الإلكتروني</label>
                  <input
                    type="email"
                    value={newEmployee.email}
                    onChange={(e) => setNewEmployee({...newEmployee, email: e.target.value})}
                    required
                    placeholder="أدخل البريد الإلكتروني"
                  />
                </div>

                <div className="form-group">
                  <label>كلمة المرور</label>
                  <input
                    type="password"
                    value={newEmployee.password}
                    onChange={(e) => setNewEmployee({...newEmployee, password: e.target.value})}
                    required
                    placeholder="أدخل كلمة المرور"
                  />
                </div>

                <div className="form-group">
                  <label>الدور</label>
                  <select
                    value={newEmployee.role}
                    onChange={(e) => setNewEmployee({...newEmployee, role: e.target.value as 'waiter' | 'chef'})}
                    required
                  >
                    <option value="waiter">نادل</option>
                    <option value="chef">طباخ</option>
                  </select>
                </div>

                <div className="form-group">
                  <label>رقم الهاتف</label>
                  <input
                    type="tel"
                    value={newEmployee.phone}
                    onChange={(e) => setNewEmployee({...newEmployee, phone: e.target.value})}
                    placeholder="أدخل رقم الهاتف (اختياري)"
                  />
                </div>

                <div className="modal-actions">
                  <button type="submit" className="save-btn">
                    <i className="fas fa-save"></i>
                    إضافة الموظف
                  </button>
                  <button
                    type="button"
                    className="cancel-btn"
                    onClick={() => setShowAddEmployeeModal(false)}
                  >
                    إلغاء
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Employee Edit Modal */}
      {showEditEmployeeModal && selectedEmployeeForEdit && (
        <div className="modal-overlay" onClick={() => setShowEditEmployeeModal(false)}>
          <div className="modal-content" onClick={e => e.stopPropagation()}>
            <div className="modal-header">
              <h3>
                <i className="fas fa-user-edit"></i>
                تعديل بيانات الموظف
              </h3>
              <button
                className="close-btn"
                onClick={() => setShowEditEmployeeModal(false)}
              >
                ×
              </button>
            </div>

            <div className="modal-body">
              <form
                onSubmit={async (e) => {
                  e.preventDefault();
                  await updateEmployee();
                }}
              >
                <div className="form-group">
                  <label>اسم المستخدم</label>
                  <input
                    type="text"
                    value={selectedEmployeeForEdit.username}
                    onChange={(e) => setSelectedEmployeeForEdit({...selectedEmployeeForEdit, username: e.target.value})}
                    required
                    placeholder="أدخل اسم المستخدم"
                  />
                </div>

                <div className="form-group">
                  <label>الاسم الكامل</label>
                  <input
                    type="text"
                    value={selectedEmployeeForEdit.name}
                    onChange={(e) => setSelectedEmployeeForEdit({...selectedEmployeeForEdit, name: e.target.value})}
                    required
                    placeholder="أدخل الاسم الكامل"
                  />
                </div>

                <div className="form-group">
                  <label>البريد الإلكتروني</label>
                  <input
                    type="email"
                    value={selectedEmployeeForEdit.email}
                    onChange={(e) => setSelectedEmployeeForEdit({...selectedEmployeeForEdit, email: e.target.value})}
                    required
                    placeholder="أدخل البريد الإلكتروني"
                  />
                </div>

                <div className="form-group">
                  <label>الدور</label>
                  <select
                    value={selectedEmployeeForEdit.role}
                    onChange={(e) => setSelectedEmployeeForEdit({...selectedEmployeeForEdit, role: e.target.value as 'waiter' | 'chef'})}
                    required
                  >
                    <option value="waiter">نادل</option>
                    <option value="chef">طباخ</option>
                  </select>
                </div>

                <div className="form-group">
                  <label>رقم الهاتف</label>
                  <input
                    type="tel"
                    value={selectedEmployeeForEdit.phone || ''}
                    onChange={(e) => setSelectedEmployeeForEdit({...selectedEmployeeForEdit, phone: e.target.value})}
                    placeholder="أدخل رقم الهاتف (اختياري)"
                  />
                </div>

                <div className="form-group">
                  <label>الحالة</label>
                  <select
                    value={selectedEmployeeForEdit.status}
                    onChange={(e) => setSelectedEmployeeForEdit({...selectedEmployeeForEdit, status: e.target.value as 'active' | 'inactive'})}
                    required
                  >
                    <option value="active">نشط</option>
                    <option value="inactive">غير نشط</option>
                  </select>
                </div>

                <div className="modal-actions">
                  <button type="submit" className="save-btn">
                    <i className="fas fa-save"></i>
                    تحديث البيانات
                  </button>
                  <button
                    type="button"
                    className="cancel-btn"
                    onClick={() => setShowEditEmployeeModal(false)}
                  >
                    إلغاء
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>      )}    </div>
  );
}
