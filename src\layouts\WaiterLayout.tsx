import { Outlet, Link, useLocation } from 'react-router-dom';
import { useEffect, useState } from 'react';
import Modal from '../components/Modal';
import Button from '../components/Button';
import './WaiterLayout.css';

export default function WaiterLayout() {
  const location = useLocation();
  const [showLogoutModal, setShowLogoutModal] = useState(false);

  useEffect(() => {
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    if (!user.role || user.role !== 'نادل') {
      localStorage.clear();
      window.location.href = '/';
    }
  }, []);

  const handleLogout = () => {
    localStorage.clear();
    window.location.href = '/';
  };

  const isActive = (path: string) => location.pathname === path;

  return (
    <div style={{ 
      direction: 'rtl',
      display: 'flex',
      minHeight: '100vh',
      background: 'var(--background)'
    }}>
      <div className="sidebar">
        <div className="logo-container">
          <img src="/coffee-logo.svg" alt="شعار المقهى" />
          <h3>قائمة النادل</h3>
        </div>
        
        <Link
          to="/waiter"
          className={`sidebar-link ${isActive('/waiter') ? 'active' : ''}`}
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M18 8h1a4 4 0 0 1 0 8h-1"></path>
            <path d="M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8z"></path>
            <line x1="6" y1="1" x2="6" y2="4"></line>
            <line x1="10" y1="1" x2="10" y2="4"></line>
            <line x1="14" y1="1" x2="14" y2="4"></line>
          </svg>
          المشروبات
        </Link>
        
        <Link
          to="/waiter/orders"
          className={`sidebar-link ${isActive('/waiter/orders') ? 'active' : ''}`}
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <line x1="8" y1="6" x2="21" y2="6"></line>
            <line x1="8" y1="12" x2="21" y2="12"></line>
            <line x1="8" y1="18" x2="21" y2="18"></line>
            <line x1="3" y1="6" x2="3.01" y2="6"></line>
            <line x1="3" y1="12" x2="3.01" y2="12"></line>
            <line x1="3" y1="18" x2="3.01" y2="18"></line>
          </svg>
          الطلبات
        </Link>

        <button
          onClick={() => setShowLogoutModal(true)}
          className="logout-button"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
            <polyline points="16 17 21 12 16 7"></polyline>
            <line x1="21" y1="12" x2="9" y2="12"></line>
          </svg>
          تسجيل الخروج
        </button>
      </div>

      <main style={{ flex: 1, overflow: 'auto', padding: '2rem' }}>
        <Outlet />
      </main>

      <Modal
        isOpen={showLogoutModal}
        onClose={() => setShowLogoutModal(false)}
        title="تأكيد تسجيل الخروج"
      >
        <div style={{ padding: '1rem', textAlign: 'center' }}>
          <p style={{ marginBottom: '1rem' }}>هل أنت متأكد من رغبتك في تسجيل الخروج؟</p>
          <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center' }}>
            <Button onClick={handleLogout} variant="error">
              نعم، تسجيل الخروج
            </Button>
            <Button onClick={() => setShowLogoutModal(false)} variant="secondary">
              إلغاء
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
}
