// مكون عرض القائمة - لوحة النادل
import React, { useState, useEffect } from 'react';
import type { WaiterProduct, WaiterCategory, CartItem } from '../../models/WaiterModels';
import { WaiterDataService } from '../../services/WaiterDataService';
import { useToast } from '../../hooks/useToast';

interface MenuViewProps {
  onAddToCart: (item: CartItem) => void;
  cart: CartItem[];
  onUpdateCartQuantity: (productId: string, quantity: number) => void;
  onRemoveFromCart: (productId: string) => void;
  categoryFilter: string | null;
  onCategoryFilterChange: (categoryId: string | null) => void;
  searchTerm: string;
  onSearchChange: (search: string) => void;
}

export const MenuView: React.FC<MenuViewProps> = ({
  onAddToCart,
  cart,
  onUpdateCartQuantity,
  onRemoveFromCart,
  categoryFilter,
  onCategoryFilterChange,
  searchTerm,
  onSearchChange
}) => {
  const [products, setProducts] = useState<WaiterProduct[]>([]);
  const [categories, setCategories] = useState<WaiterCategory[]>([]);
  const [loading, setLoading] = useState(false);
  const { showError } = useToast();

  // جلب المنتجات والفئات
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const waiterService = WaiterDataService.getInstance();
        
        const [productsData, categoriesData] = await Promise.all([
          waiterService.getProducts(),
          waiterService.getCategories()
        ]);

        setProducts(productsData);
        setCategories(categoriesData);
      } catch (error) {
        console.error('❌ خطأ في جلب بيانات القائمة:', error);
        showError('فشل في جلب بيانات القائمة');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [showError]);

  // تصفية المنتجات
  const filteredProducts = products.filter(product => {
    const matchesCategory = !categoryFilter || product.categories?.includes(categoryFilter);
    const matchesSearch = !searchTerm || 
      product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.description?.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch && product.available;
  });
  // إضافة منتج للسلة
  const handleAddToCart = (product: WaiterProduct) => {
    const cartItem: CartItem = {
      ...product,
      productId: product._id,
      quantity: 1
    };
    onAddToCart(cartItem);
  };

  // تحديث الكمية في السلة
  const handleQuantityChange = (productId: string, quantity: number) => {
    if (quantity <= 0) {
      onRemoveFromCart(productId);
    } else {
      onUpdateCartQuantity(productId, quantity);
    }
  };

  // الحصول على كمية المنتج في السلة
  const getCartQuantity = (productId: string): number => {
    const cartItem = cart.find(item => item._id === productId);
    return cartItem?.quantity || 0;
  };

  return (
    <div className="menu-view">
      {/* شريط البحث والفلترة */}
      <div className="menu-header">
        <div className="search-bar">
          <input
            type="text"
            placeholder="بحث في القائمة..."
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
            className="search-input"
          />
          <span className="search-icon">🔍</span>
        </div>

        {/* فلاتر الفئات */}
        <div className="category-filters">
          <button
            className={`category-btn ${!categoryFilter ? 'active' : ''}`}
            onClick={() => onCategoryFilterChange(null)}
          >
            الكل
          </button>
          {categories.map(category => (
            <button
              key={category._id}
              className={`category-btn ${categoryFilter === category._id ? 'active' : ''}`}
              onClick={() => onCategoryFilterChange(category._id)}
              style={{ backgroundColor: category.color }}
            >
              {category.icon && <span className="category-icon">{category.icon}</span>}
              {category.name}
            </button>
          ))}
        </div>
      </div>

      {/* قائمة المنتجات */}
      <div className="products-grid">
        {loading ? (
          <div className="loading-spinner">
            <div className="spinner"></div>
            <p>جاري تحميل القائمة...</p>
          </div>
        ) : filteredProducts.length === 0 ? (
          <div className="empty-state">
            <p>لا توجد منتجات متاحة</p>
            {searchTerm && (
              <button 
                onClick={() => onSearchChange('')}
                className="clear-search-btn"
              >
                مسح البحث
              </button>
            )}
          </div>
        ) : (
          filteredProducts.map(product => {
            const quantity = getCartQuantity(product._id);
            return (
              <div key={product._id} className="product-card">
                <div className="product-info">
                  <h3 className="product-name">{product.name}</h3>
                  {product.description && (
                    <p className="product-description">{product.description}</p>
                  )}
                  <div className="product-price">
                    {product.price.toFixed(2)} ريال
                  </div>
                  
                  {/* عرض الفئات */}
                  {product.categoryDetails && product.categoryDetails.length > 0 && (
                    <div className="product-categories">
                      {product.categoryDetails.map(cat => (
                        <span 
                          key={cat._id} 
                          className="category-tag"
                          style={{ backgroundColor: cat.color }}
                        >
                          {cat.name}
                        </span>
                      ))}
                    </div>
                  )}
                </div>

                {/* أزرار التحكم */}
                <div className="product-actions">
                  {quantity === 0 ? (
                    <button
                      onClick={() => handleAddToCart(product)}
                      className="add-to-cart-btn"
                    >
                      إضافة للسلة
                    </button>
                  ) : (
                    <div className="quantity-controls">
                      <button
                        onClick={() => handleQuantityChange(product._id, quantity - 1)}
                        className="quantity-btn decrease"
                      >
                        -
                      </button>
                      <span className="quantity-display">{quantity}</span>
                      <button
                        onClick={() => handleQuantityChange(product._id, quantity + 1)}
                        className="quantity-btn increase"
                      >
                        +
                      </button>
                    </div>
                  )}
                </div>
              </div>
            );
          })
        )}
      </div>
    </div>
  );
};
