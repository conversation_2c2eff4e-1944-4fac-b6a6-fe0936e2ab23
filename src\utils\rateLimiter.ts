// Rate Limiter محسن لحل مشكلة 429 (Too Many Requests)
class RateLimiter {
  private requests: Map<string, number[]> = new Map();
  private readonly maxRequests: number = 5; // تقليل عدد الطلبات المسموحة
  private readonly windowMs: number = 60000; // نافزة زمنية دقيقة واحدة
  private readonly minInterval: number = 2000; // 2 ثانية بين الطلبات

  canMakeRequest(endpoint: string): boolean {
    const now = Date.now();
    const requests = this.requests.get(endpoint) || [];

    // إزالة الطلبات القديمة
    const validRequests = requests.filter(time => now - time < this.windowMs);

    // التحقق من تجاوز الحد الأقصى
    if (validRequests.length >= this.maxRequests) {
      console.warn(`🚫 Rate limit exceeded for ${endpoint}: ${validRequests.length}/${this.maxRequests}`);
      return false;
    }

    // التحقق من الفترة الزمنية الدنيا
    if (validRequests.length > 0) {
      const lastRequest = Math.max(...validRequests);
      if (now - lastRequest < this.minInterval) {
        console.warn(`⏱️ Too soon for ${endpoint}: ${now - lastRequest}ms < ${this.minInterval}ms`);
        return false;
      }
    }

    // إضافة الطلب الحالي
    validRequests.push(now);
    this.requests.set(endpoint, validRequests);
    return true;
  }

  getWaitTime(endpoint: string): number {
    const requests = this.requests.get(endpoint) || [];
    if (requests.length === 0) return 0;

    const now = Date.now();
    const lastRequest = Math.max(...requests);
    const intervalWait = this.minInterval - (now - lastRequest);

    if (requests.length >= this.maxRequests) {
      const oldestRequest = Math.min(...requests);
      const windowWait = this.windowMs - (now - oldestRequest);
      return Math.max(intervalWait, windowWait);
    }

    return Math.max(0, intervalWait);
  }

  async waitIfNeeded(endpoint: string): Promise<void> {
    if (!this.canMakeRequest(endpoint)) {
      const waitTime = this.getWaitTime(endpoint);
      if (waitTime > 0) {
        console.log(`⏳ Waiting ${waitTime}ms for rate limit: ${endpoint}`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }
  }
}

// Global instance
export const globalRateLimiter = new RateLimiter();

// Rate-limited fetch wrapper
export async function rateLimitedFetch(url: string, options: RequestInit = {}): Promise<Response> {
  const endpoint = new URL(url).pathname;
  
  await globalRateLimiter.waitIfNeeded(endpoint);
  
  return fetch(url, options);
}
