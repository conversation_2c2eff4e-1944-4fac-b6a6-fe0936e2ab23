// routes/reports.js
const express = require('express');
const router = express.Router();
const Order = require('../models/Order');
const User = require('../models/User');
const { authenticateToken } = require('../middleware/auth');

/**
 * @route   GET /api/reports/daily-sales
 * @desc    Get daily sales report
 * @access  Private
 */
router.get('/daily-sales', authenticateToken, async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    
    // تحديد نطاق التاريخ (افتراضياً: آخر 30 يوم)
    const end = endDate ? new Date(endDate) : new Date();
    const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    
    // جلب الطلبات المكتملة في النطاق المحدد
    const orders = await Order.find({
      'timing.orderTime': { $gte: start, $lte: end },
      status: { $in: ['delivered', 'completed'] }
    }).populate('items.product', 'name price');

    // تجميع البيانات حسب التاريخ
    const dailySales = {};
    
    orders.forEach(order => {
      const date = order.timing.orderTime.toISOString().split('T')[0];
      
      if (!dailySales[date]) {
        dailySales[date] = {
          date,
          totalSales: 0,
          ordersCount: 0,
          averageOrderValue: 0
        };
      }
      
      const orderTotal = order.total || order.items.reduce((sum, item) => 
        sum + (item.price * item.quantity), 0);
      
      dailySales[date].totalSales += orderTotal;
      dailySales[date].ordersCount += 1;
    });

    // حساب متوسط قيمة الطلب
    Object.values(dailySales).forEach(day => {
      day.averageOrderValue = day.ordersCount > 0 ? day.totalSales / day.ordersCount : 0;
    });

    const result = Object.values(dailySales).sort((a, b) => new Date(a.date) - new Date(b.date));

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('خطأ في تقرير المبيعات اليومية:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب تقرير المبيعات اليومية',
      error: error.message
    });
  }
});

/**
 * @route   GET /api/reports/weekly-sales
 * @desc    Get weekly sales report
 * @access  Private
 */
router.get('/weekly-sales', authenticateToken, async (req, res) => {
  try {
    const { weeks = 4 } = req.query;
    
    // حساب بداية الفترة (عدد الأسابيع المطلوب)
    const endDate = new Date();
    const startDate = new Date(Date.now() - weeks * 7 * 24 * 60 * 60 * 1000);
    
    const orders = await Order.find({
      'timing.orderTime': { $gte: startDate, $lte: endDate },
      status: { $in: ['delivered', 'completed'] }
    }).populate('items.product', 'name price');

    // تجميع البيانات حسب الأسبوع
    const weeklySales = {};
    
    orders.forEach(order => {
      const orderDate = new Date(order.timing.orderTime);
      const weekStart = new Date(orderDate);
      weekStart.setDate(orderDate.getDate() - orderDate.getDay());
      const weekKey = weekStart.toISOString().split('T')[0];
      
      if (!weeklySales[weekKey]) {
        weeklySales[weekKey] = {
          week: `أسبوع ${weekStart.toLocaleDateString('ar-EG')}`,
          totalSales: 0,
          ordersCount: 0,
          averageOrderValue: 0
        };
      }
      
      const orderTotal = order.total || order.items.reduce((sum, item) => 
        sum + (item.price * item.quantity), 0);
      
      weeklySales[weekKey].totalSales += orderTotal;
      weeklySales[weekKey].ordersCount += 1;
    });

    // حساب متوسط قيمة الطلب
    Object.values(weeklySales).forEach(week => {
      week.averageOrderValue = week.ordersCount > 0 ? week.totalSales / week.ordersCount : 0;
    });

    const result = Object.values(weeklySales).sort((a, b) => new Date(a.week) - new Date(b.week));

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('خطأ في تقرير المبيعات الأسبوعية:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب تقرير المبيعات الأسبوعية',
      error: error.message
    });
  }
});

/**
 * @route   GET /api/reports/waiter-stats
 * @desc    Get waiter statistics
 * @access  Private
 */
router.get('/waiter-stats', authenticateToken, async (req, res) => {
  try {
    const { period = '30' } = req.query; // افتراضياً: آخر 30 يوم
    
    const startDate = new Date(Date.now() - parseInt(period) * 24 * 60 * 60 * 1000);
    const endDate = new Date();
    
    // جلب النُدل
    const waiters = await User.find({ role: 'waiter', isActive: true });
    
    // جلب الطلبات في الفترة المحددة
    const orders = await Order.find({
      'timing.orderTime': { $gte: startDate, $lte: endDate },
      status: { $in: ['delivered', 'completed'] }
    }).populate('staff.waiter', 'name username');

    const waiterStats = waiters.map(waiter => {
      const waiterOrders = orders.filter(order => 
        order.staff.waiter && 
        (order.staff.waiter._id.toString() === waiter._id.toString() ||
         order.waiterName === waiter.username ||
         order.waiterName === waiter.name)
      );
      
      const totalSales = waiterOrders.reduce((sum, order) => {
        const orderTotal = order.total || order.items.reduce((itemSum, item) => 
          itemSum + (item.price * item.quantity), 0);
        return sum + orderTotal;
      }, 0);
      
      const averageOrderValue = waiterOrders.length > 0 ? totalSales / waiterOrders.length : 0;
      
      // تقدير ساعات العمل (افتراضياً 8 ساعات/يوم × أيام العمل)
      const workingDays = Math.min(parseInt(period), 30);
      const workingHours = workingDays * 8;
      
      return {
        waiterName: waiter.name || waiter.username,
        ordersCount: waiterOrders.length,
        totalSales: parseFloat(totalSales.toFixed(2)),
        averageOrderValue: parseFloat(averageOrderValue.toFixed(2)),
        workingHours
      };
    });

    // ترتيب حسب المبيعات
    waiterStats.sort((a, b) => b.totalSales - a.totalSales);

    res.json({
      success: true,
      data: waiterStats
    });
  } catch (error) {
    console.error('خطأ في إحصائيات النُدل:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب إحصائيات النُدل',
      error: error.message
    });
  }
});

/**
 * @route   GET /api/reports/chef-stats
 * @desc    Get chef statistics
 * @access  Private
 */
router.get('/chef-stats', authenticateToken, async (req, res) => {
  try {
    const { period = '30' } = req.query;
    
    const startDate = new Date(Date.now() - parseInt(period) * 24 * 60 * 60 * 1000);
    const endDate = new Date();
    
    // جلب الطباخين
    const chefs = await User.find({ role: 'chef', isActive: true });
    
    // جلب الطلبات في الفترة المحددة
    const orders = await Order.find({
      'timing.orderTime': { $gte: startDate, $lte: endDate },
      status: { $in: ['delivered', 'completed'] }
    }).populate('staff.chef', 'name username');

    const chefStats = chefs.map(chef => {
      const chefOrders = orders.filter(order => 
        order.staff.chef && 
        (order.staff.chef._id.toString() === chef._id.toString() ||
         order.chefName === chef.username ||
         order.chefName === chef.name)
      );
      
      // حساب متوسط وقت التحضير
      const ordersWithPrepTime = chefOrders.filter(order => 
        order.timing.preparingAt && order.timing.readyAt
      );
      
      const totalPrepTime = ordersWithPrepTime.reduce((sum, order) => {
        const prepTime = (new Date(order.timing.readyAt) - new Date(order.timing.preparingAt)) / (1000 * 60); // بالدقائق
        return sum + prepTime;
      }, 0);
      
      const averagePreparationTime = ordersWithPrepTime.length > 0 ? 
        totalPrepTime / ordersWithPrepTime.length : 0;
      
      // تقدير ساعات العمل
      const workingDays = Math.min(parseInt(period), 30);
      const workingHours = workingDays * 8;
      
      return {
        chefName: chef.name || chef.username,
        ordersProcessed: chefOrders.length,
        averagePreparationTime: parseFloat(averagePreparationTime.toFixed(1)),
        workingHours
      };
    });

    // ترتيب حسب عدد الطلبات المُعالجة
    chefStats.sort((a, b) => b.ordersProcessed - a.ordersProcessed);

    res.json({
      success: true,
      data: chefStats
    });
  } catch (error) {
    console.error('خطأ في إحصائيات الطباخين:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب إحصائيات الطباخين',
      error: error.message
    });
  }
});

/**
 * @route   GET /api/reports/top-items
 * @desc    Get top selling items
 * @access  Private
 */
router.get('/top-items', authenticateToken, async (req, res) => {
  try {
    const { period = '30', limit = '10' } = req.query;
    
    const startDate = new Date(Date.now() - parseInt(period) * 24 * 60 * 60 * 1000);
    const endDate = new Date();
    
    // جلب الطلبات المكتملة
    const orders = await Order.find({
      'timing.orderTime': { $gte: startDate, $lte: endDate },
      status: { $in: ['delivered', 'completed'] }
    }).populate('items.product', 'name price');

    // تجميع الأصناف
    const itemStats = {};
    
    orders.forEach(order => {
      order.items.forEach(item => {
        const itemName = item.product ? item.product.name : item.name || 'غير محدد';
        const itemPrice = item.price || (item.product ? item.product.price : 0);
        
        if (!itemStats[itemName]) {
          itemStats[itemName] = {
            itemName,
            quantitySold: 0,
            totalRevenue: 0
          };
        }
        
        itemStats[itemName].quantitySold += item.quantity;
        itemStats[itemName].totalRevenue += itemPrice * item.quantity;
      });
    });

    // تحويل إلى مصفوفة وترتيب حسب الكمية المباعة
    const result = Object.values(itemStats)
      .sort((a, b) => b.quantitySold - a.quantitySold)
      .slice(0, parseInt(limit))
      .map(item => ({
        ...item,
        totalRevenue: parseFloat(item.totalRevenue.toFixed(2))
      }));

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('خطأ في أفضل الأصناف مبيعاً:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب أفضل الأصناف مبيعاً',
      error: error.message
    });
  }
});

/**
 * @route   GET /api/reports/summary
 * @desc    Get general summary report
 * @access  Private
 */
router.get('/summary', authenticateToken, async (req, res) => {
  try {
    const { period = '30' } = req.query;
    
    const startDate = new Date(Date.now() - parseInt(period) * 24 * 60 * 60 * 1000);
    const endDate = new Date();
    
    // جلب جميع الطلبات في الفترة
    const allOrders = await Order.find({
      'timing.orderTime': { $gte: startDate, $lte: endDate }
    });
    
    const completedOrders = allOrders.filter(order => 
      order.status === 'delivered' || order.status === 'completed'
    );
    
    const totalSales = completedOrders.reduce((sum, order) => {
      const orderTotal = order.total || order.items.reduce((itemSum, item) => 
        itemSum + (item.price * item.quantity), 0);
      return sum + orderTotal;
    }, 0);
    
    const averageOrderValue = completedOrders.length > 0 ? totalSales / completedOrders.length : 0;
    
    // إحصائيات اليوم
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayOrders = allOrders.filter(order => 
      new Date(order.timing.orderTime) >= today
    );
    
    const todayCompleted = todayOrders.filter(order => 
      order.status === 'delivered' || order.status === 'completed'
    );
    
    const todaySales = todayCompleted.reduce((sum, order) => {
      const orderTotal = order.total || order.items.reduce((itemSum, item) => 
        itemSum + (item.price * item.quantity), 0);
      return sum + orderTotal;
    }, 0);

    res.json({
      success: true,
      data: {
        period: `آخر ${period} يوم`,
        totalOrders: allOrders.length,
        completedOrders: completedOrders.length,
        pendingOrders: allOrders.filter(o => o.status === 'pending').length,
        preparingOrders: allOrders.filter(o => o.status === 'preparing').length,
        totalSales: parseFloat(totalSales.toFixed(2)),
        averageOrderValue: parseFloat(averageOrderValue.toFixed(2)),
        today: {
          orders: todayOrders.length,
          completed: todayCompleted.length,
          sales: parseFloat(todaySales.toFixed(2))
        }
      }
    });
  } catch (error) {
    console.error('خطأ في تقرير الملخص:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب تقرير الملخص',
      error: error.message
    });
  }
});

module.exports = router;
