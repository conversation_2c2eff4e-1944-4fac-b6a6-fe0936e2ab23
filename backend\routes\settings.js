// backend/routes/settings.js
const express = require('express');
const router = express.Router();
const Setting = require('../models/Setting');
const { isAdmin } = require('../middleware/auth'); // Assuming you have auth middleware

// GET all settings
router.get('/', isAdmin, async (req, res) => {
  try {
    const settings = await Setting.getAllSettings();
    res.json({ success: true, data: settings });
  } catch (error) {
    console.error('Error fetching settings:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch settings' });
  }
});

// PUT (update) settings
router.put('/', isAdmin, async (req, res) => {
  try {
    // Expects req.body to be an array of { key: string, value: any } objects
    const settingsToUpdate = req.body;
    if (!Array.isArray(settingsToUpdate) || settingsToUpdate.some(s => !s.key || s.value === undefined)) {
      return res.status(400).json({ success: false, error: 'Invalid settings format. Expected array of {key, value} objects.' });
    }
    const updatedSettings = await Setting.updateSettings(settingsToUpdate);
    res.json({ success: true, data: updatedSettings, message: 'Settings updated successfully' });
  } catch (error) {
    console.error('Error updating settings:', error);
    res.status(500).json({ success: false, error: 'Failed to update settings' });
  }
});

// POST a new setting (optional, if you want to add settings dynamically, otherwise seed them)
router.post('/', isAdmin, async (req, res) => {
    try {
        const { key, value, label, description, type, options } = req.body;
        if (!key || value === undefined || !label) {
            return res.status(400).json({ success: false, error: 'Key, value, and label are required for a new setting.' });
        }
        const newSetting = new Setting({ key, value, label, description, type, options });
        await newSetting.save();
        res.status(201).json({ success: true, data: newSetting, message: 'Setting created successfully' });
    } catch (error) {
        console.error('Error creating setting:', error);
        if (error.code === 11000) { // Duplicate key error
            return res.status(409).json({ success: false, error: `Setting with key '${error.keyValue.key}' already exists.` });
        }
        res.status(500).json({ success: false, error: 'Failed to create setting' });
    }
});


module.exports = router;
