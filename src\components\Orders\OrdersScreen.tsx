import React, { useState, useEffect } from 'react';
import { authenticatedGet, authenticatedPut } from '../../utils/apiHelpers';
import { useToast } from '../../hooks/useToast';
import socket from '../../socket';
import './OrdersScreen.css';

interface Order {
  _id: string;
  orderNumber: string;
  tableNumber: string;
  customerName: string;
  waiterName: string;
  chefName?: string;
  items: Array<{
    _id: string;
    name: string;
    price: number;
    quantity: number;
    notes?: string;
  }>;
  totalPrice: number;
  status: 'pending' | 'preparing' | 'ready' | 'completed';
  createdAt: string;
  updatedAt: string;
  preparationTime?: number;
  discountRequests?: Array<{
    amount: number;
    reason: string;
    status: 'pending' | 'approved' | 'rejected';
    requestedBy: string;
    requestedAt: string;
  }>;
}

interface OrdersScreenProps {
  userRole: 'waiter' | 'chef' | 'manager';
  userName: string;
}

const OrdersScreen: React.FC<OrdersScreenProps> = ({ userRole, userName }) => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [filteredOrders, setFilteredOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [showOrderModal, setShowOrderModal] = useState(false);
  
  // Filters
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [waiterFilter, setWaiterFilter] = useState<string>('all');
  const [dateFilter, setDateFilter] = useState<string>('today');
  
  // Stats
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    preparing: 0,
    ready: 0,
    completed: 0,
    totalSales: 0,
    avgPreparationTime: 0
  });

  const { showSuccess, showError, showInfo } = useToast();

  // جلب الطلبات
  const fetchOrders = async () => {
    try {
      setLoading(true);
      const response = await authenticatedGet('/api/orders');
      const ordersData = response.data || [];
      
      // فلترة الطلبات حسب الدور
      let userOrders = ordersData;
      if (userRole === 'waiter') {
        userOrders = ordersData.filter((order: Order) => order.waiterName === userName);
      }
      
      setOrders(userOrders);
      calculateStats(userOrders);
    } catch (error) {
      console.error('خطأ في جلب الطلبات:', error);
      showError('فشل في تحميل الطلبات');
    } finally {
      setLoading(false);
    }
  };

  // حساب الإحصائيات
  const calculateStats = (ordersData: Order[]) => {
    const total = ordersData.length;
    const pending = ordersData.filter(o => o.status === 'pending').length;
    const preparing = ordersData.filter(o => o.status === 'preparing').length;
    const ready = ordersData.filter(o => o.status === 'ready').length;
    const completed = ordersData.filter(o => o.status === 'completed').length;
    const totalSales = ordersData.reduce((sum, order) => sum + order.totalPrice, 0);
    
    // حساب متوسط وقت التحضير
    const completedOrders = ordersData.filter(o => o.status === 'completed' && o.preparationTime);
    const avgPreparationTime = completedOrders.length > 0
      ? completedOrders.reduce((sum, order) => sum + (order.preparationTime || 0), 0) / completedOrders.length
      : 0;

    setStats({
      total,
      pending,
      preparing,
      ready,
      completed,
      totalSales,
      avgPreparationTime
    });
  };

  // تطبيق الفلاتر
  const applyFilters = () => {
    let filtered = [...orders];

    // فلتر الحالة
    if (statusFilter !== 'all') {
      filtered = filtered.filter(order => order.status === statusFilter);
    }

    // فلتر النادل (للمدير فقط)
    if (waiterFilter !== 'all' && userRole === 'manager') {
      filtered = filtered.filter(order => order.waiterName === waiterFilter);
    }

    // فلتر التاريخ
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    if (dateFilter === 'today') {
      filtered = filtered.filter(order => {
        const orderDate = new Date(order.createdAt);
        orderDate.setHours(0, 0, 0, 0);
        return orderDate.getTime() === today.getTime();
      });
    } else if (dateFilter === 'week') {
      const weekAgo = new Date(today);
      weekAgo.setDate(weekAgo.getDate() - 7);
      filtered = filtered.filter(order => new Date(order.createdAt) >= weekAgo);
    }

    setFilteredOrders(filtered);
    calculateStats(filtered);
  };

  // تحديث حالة الطلب
  const updateOrderStatus = async (orderId: string, newStatus: string) => {
    try {
      const updateData: any = { status: newStatus };
      
      // إضافة اسم الطباخ عند بدء التحضير
      if (newStatus === 'preparing' && userRole === 'chef') {
        updateData.chefName = userName;
        updateData.preparationStartTime = new Date().toISOString();
      }
      
      // حساب وقت التحضير عند الانتهاء
      if (newStatus === 'ready' || newStatus === 'completed') {
        const order = orders.find(o => o._id === orderId);
        if (order && order.status === 'preparing') {
          const preparationTime = Date.now() - new Date(order.updatedAt).getTime();
          updateData.preparationTime = Math.round(preparationTime / 1000 / 60); // بالدقائق
        }
      }

      const response = await authenticatedPut(`/api/orders/${orderId}`, updateData);
      
      if (response.success) {
        showSuccess('تم تحديث حالة الطلب بنجاح');
        
        // إرسال إشعار Socket.IO
        if (socket) {
          socket.emit('order-updated', {
            orderId,
            newStatus,
            updatedBy: userName,
            userRole
          });
        }
        
        fetchOrders();
      }
    } catch (error) {
      console.error('خطأ في تحديث حالة الطلب:', error);
      showError('فشل في تحديث حالة الطلب');
    }
  };

  // الموافقة على طلب خصم
  const approveDiscount = async (orderId: string, discountIndex: number) => {
    try {
      const response = await authenticatedPut(`/api/orders/${orderId}/discount/${discountIndex}/approve`, {
        approvedBy: userName
      });
      
      if (response.success) {
        showSuccess('تم الموافقة على طلب الخصم');
        fetchOrders();
      }
    } catch (error) {
      console.error('خطأ في الموافقة على الخصم:', error);
      showError('فشل في الموافقة على الخصم');
    }
  };

  // رفض طلب خصم
  const rejectDiscount = async (orderId: string, discountIndex: number) => {
    try {
      const response = await authenticatedPut(`/api/orders/${orderId}/discount/${discountIndex}/reject`, {
        rejectedBy: userName
      });
      
      if (response.success) {
        showSuccess('تم رفض طلب الخصم');
        fetchOrders();
      }
    } catch (error) {
      console.error('خطأ في رفض الخصم:', error);
      showError('فشل في رفض الخصم');
    }
  };

  // إعداد Socket.IO
  useEffect(() => {
    if (socket) {
      socket.on('order-updated', () => {
        fetchOrders();
      });

      socket.on('new-order', () => {
        fetchOrders();
      });

      socket.on('discount-requested', () => {
        fetchOrders();
      });
    }

    return () => {
      if (socket) {
        socket.off('order-updated');
        socket.off('new-order');
        socket.off('discount-requested');
      }
    };
  }, []);

  // تحميل البيانات الأولية
  useEffect(() => {
    fetchOrders();
  }, [userRole, userName]);

  // تطبيق الفلاتر عند تغييرها
  useEffect(() => {
    applyFilters();
  }, [orders, statusFilter, waiterFilter, dateFilter]);

  // الحصول على قائمة النادلين (للمدير)
  const getWaiters = () => {
    const waiters = [...new Set(orders.map(order => order.waiterName))];
    return waiters.filter(Boolean);
  };

  // تنسيق الوقت
  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('ar-EG', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // تنسيق التاريخ
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-EG');
  };

  // الحصول على لون الحالة
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return '#f39c12';
      case 'preparing': return '#3498db';
      case 'ready': return '#27ae60';
      case 'completed': return '#95a5a6';
      default: return '#2c3e50';
    }
  };

  // الحصول على نص الحالة
  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'قيد الانتظار';
      case 'preparing': return 'قيد التحضير';
      case 'ready': return 'جاهز';
      case 'completed': return 'مكتمل';
      default: return status;
    }
  };

  if (loading) {
    return (
      <div className="orders-screen loading">
        <div className="loading-spinner">
          <i className="fas fa-spinner fa-spin"></i>
          <span>جاري تحميل الطلبات...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="orders-screen">
      {/* Header */}
      <div className="orders-header">
        <h1>
          <i className="fas fa-shopping-cart"></i>
          إدارة الطلبات
        </h1>
        <p>عرض وإدارة جميع الطلبات مع التحديث الفوري</p>
      </div>

      {/* Stats Cards */}
      <div className="orders-stats">
        <div className="stat-card total">
          <div className="stat-icon">
            <i className="fas fa-shopping-cart"></i>
          </div>
          <div className="stat-content">
            <h3>{stats.total}</h3>
            <p>إجمالي الطلبات</p>
          </div>
        </div>

        <div className="stat-card pending">
          <div className="stat-icon">
            <i className="fas fa-clock"></i>
          </div>
          <div className="stat-content">
            <h3>{stats.pending}</h3>
            <p>قيد الانتظار</p>
          </div>
        </div>

        <div className="stat-card preparing">
          <div className="stat-icon">
            <i className="fas fa-fire"></i>
          </div>
          <div className="stat-content">
            <h3>{stats.preparing}</h3>
            <p>قيد التحضير</p>
          </div>
        </div>

        <div className="stat-card ready">
          <div className="stat-icon">
            <i className="fas fa-check-circle"></i>
          </div>
          <div className="stat-content">
            <h3>{stats.ready}</h3>
            <p>جاهزة</p>
          </div>
        </div>

        <div className="stat-card sales">
          <div className="stat-icon">
            <i className="fas fa-money-bill-wave"></i>
          </div>
          <div className="stat-content">
            <h3>{stats.totalSales.toFixed(2)}</h3>
            <p>إجمالي المبيعات (ج.م)</p>
          </div>
        </div>

        <div className="stat-card time">
          <div className="stat-icon">
            <i className="fas fa-stopwatch"></i>
          </div>
          <div className="stat-content">
            <h3>{stats.avgPreparationTime.toFixed(1)}</h3>
            <p>متوسط وقت التحضير (دقيقة)</p>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="orders-filters">
        <div className="filter-group">
          <label>الحالة:</label>
          <select 
            value={statusFilter} 
            onChange={(e) => setStatusFilter(e.target.value)}
          >
            <option value="all">جميع الحالات</option>
            <option value="pending">قيد الانتظار</option>
            <option value="preparing">قيد التحضير</option>
            <option value="ready">جاهزة</option>
            <option value="completed">مكتملة</option>
          </select>
        </div>

        {userRole === 'manager' && (
          <div className="filter-group">
            <label>النادل:</label>
            <select 
              value={waiterFilter} 
              onChange={(e) => setWaiterFilter(e.target.value)}
            >
              <option value="all">جميع النادلين</option>
              {getWaiters().map(waiter => (
                <option key={waiter} value={waiter}>{waiter}</option>
              ))}
            </select>
          </div>
        )}

        <div className="filter-group">
          <label>التاريخ:</label>
          <select 
            value={dateFilter} 
            onChange={(e) => setDateFilter(e.target.value)}
          >
            <option value="today">اليوم</option>
            <option value="week">هذا الأسبوع</option>
            <option value="all">جميع التواريخ</option>
          </select>
        </div>

        <button 
          className="refresh-btn"
          onClick={fetchOrders}
          title="تحديث البيانات"
        >
          <i className="fas fa-sync-alt"></i>
          تحديث
        </button>
      </div>

      {/* Orders List */}
      <div className="orders-list">
        {filteredOrders.length === 0 ? (
          <div className="no-orders">
            <i className="fas fa-shopping-cart"></i>
            <p>لا توجد طلبات تطابق الفلاتر المحددة</p>
          </div>
        ) : (
          <div className="orders-grid">
            {filteredOrders.map(order => (
              <div 
                key={order._id} 
                className={`order-card ${order.status}`}
                onClick={() => {
                  setSelectedOrder(order);
                  setShowOrderModal(true);
                }}
              >
                <div className="order-header">
                  <div className="order-number">
                    <i className="fas fa-hashtag"></i>
                    {order.orderNumber}
                  </div>
                  <div 
                    className="order-status"
                    style={{ backgroundColor: getStatusColor(order.status) }}
                  >
                    {getStatusText(order.status)}
                  </div>
                </div>

                <div className="order-info">
                  <div className="info-row">
                    <i className="fas fa-table"></i>
                    <span>طاولة {order.tableNumber}</span>
                  </div>
                  <div className="info-row">
                    <i className="fas fa-user"></i>
                    <span>{order.customerName}</span>
                  </div>
                  <div className="info-row">
                    <i className="fas fa-user-tie"></i>
                    <span>{order.waiterName}</span>
                  </div>
                  {order.chefName && (
                    <div className="info-row">
                      <i className="fas fa-chef-hat"></i>
                      <span>{order.chefName}</span>
                    </div>
                  )}
                </div>

                <div className="order-details">
                  <div className="items-count">
                    <i className="fas fa-coffee"></i>
                    <span>{order.items.length} عنصر</span>
                  </div>
                  <div className="order-total">
                    <i className="fas fa-money-bill-wave"></i>
                    <span>{order.totalPrice.toFixed(2)} ج.م</span>
                  </div>
                </div>

                <div className="order-time">
                  <i className="fas fa-clock"></i>
                  <span>{formatTime(order.createdAt)}</span>
                  <span className="date">{formatDate(order.createdAt)}</span>
                </div>

                {/* Discount Requests */}
                {order.discountRequests && order.discountRequests.some(req => req.status === 'pending') && (
                  <div className="discount-alert">
                    <i className="fas fa-exclamation-triangle"></i>
                    <span>طلب خصم معلق</span>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="order-actions" onClick={(e) => e.stopPropagation()}>
                  {userRole === 'chef' && order.status === 'pending' && (
                    <button 
                      className="action-btn start"
                      onClick={() => updateOrderStatus(order._id, 'preparing')}
                    >
                      <i className="fas fa-play"></i>
                      بدء التحضير
                    </button>
                  )}
                  
                  {userRole === 'chef' && order.status === 'preparing' && (
                    <button 
                      className="action-btn complete"
                      onClick={() => updateOrderStatus(order._id, 'ready')}
                    >
                      <i className="fas fa-check"></i>
                      جاهز
                    </button>
                  )}
                  
                  {userRole === 'waiter' && order.status === 'ready' && (
                    <button 
                      className="action-btn deliver"
                      onClick={() => updateOrderStatus(order._id, 'completed')}
                    >
                      <i className="fas fa-check-double"></i>
                      تم التسليم
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Order Details Modal */}
      {showOrderModal && selectedOrder && (
        <div className="modal-overlay" onClick={() => setShowOrderModal(false)}>
          <div className="modal-content order-modal" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>
                <i className="fas fa-shopping-cart"></i>
                تفاصيل الطلب #{selectedOrder.orderNumber}
              </h2>
              <button 
                className="modal-close"
                onClick={() => setShowOrderModal(false)}
              >
                <i className="fas fa-times"></i>
              </button>
            </div>
            
            <div className="modal-body">
              {/* Order Info */}
              <div className="order-modal-info">
                <div className="info-section">
                  <h3>معلومات الطلب</h3>
                  <div className="info-grid">
                    <div className="info-item">
                      <span className="label">رقم الطلب:</span>
                      <span className="value">{selectedOrder.orderNumber}</span>
                    </div>
                    <div className="info-item">
                      <span className="label">رقم الطاولة:</span>
                      <span className="value">{selectedOrder.tableNumber}</span>
                    </div>
                    <div className="info-item">
                      <span className="label">اسم العميل:</span>
                      <span className="value">{selectedOrder.customerName}</span>
                    </div>
                    <div className="info-item">
                      <span className="label">النادل:</span>
                      <span className="value">{selectedOrder.waiterName}</span>
                    </div>
                    {selectedOrder.chefName && (
                      <div className="info-item">
                        <span className="label">الطباخ:</span>
                        <span className="value">{selectedOrder.chefName}</span>
                      </div>
                    )}
                    <div className="info-item">
                      <span className="label">الحالة:</span>
                      <span 
                        className="value status"
                        style={{ color: getStatusColor(selectedOrder.status) }}
                      >
                        {getStatusText(selectedOrder.status)}
                      </span>
                    </div>
                    <div className="info-item">
                      <span className="label">وقت الطلب:</span>
                      <span className="value">
                        {formatTime(selectedOrder.createdAt)} - {formatDate(selectedOrder.createdAt)}
                      </span>
                    </div>
                    {selectedOrder.preparationTime && (
                      <div className="info-item">
                        <span className="label">وقت التحضير:</span>
                        <span className="value">{selectedOrder.preparationTime} دقيقة</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Order Items */}
                <div className="items-section">
                  <h3>الأصناف المطلوبة</h3>
                  <div className="items-list">
                    {selectedOrder.items.map((item, index) => (
                      <div key={index} className="item-row">
                        <div className="item-info">
                          <span className="item-name">{item.name}</span>
                          {item.notes && (
                            <span className="item-notes">
                              <i className="fas fa-sticky-note"></i>
                              {item.notes}
                            </span>
                          )}
                        </div>
                        <div className="item-quantity">
                          <span>×{item.quantity}</span>
                        </div>
                        <div className="item-price">
                          <span>{(item.price * item.quantity).toFixed(2)} ج.م</span>
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  <div className="order-total-section">
                    <div className="total-row">
                      <span className="total-label">الإجمالي:</span>
                      <span className="total-value">{selectedOrder.totalPrice.toFixed(2)} ج.م</span>
                    </div>
                  </div>
                </div>

                {/* Discount Requests */}
                {selectedOrder.discountRequests && selectedOrder.discountRequests.length > 0 && (
                  <div className="discounts-section">
                    <h3>طلبات الخصم</h3>
                    <div className="discounts-list">
                      {selectedOrder.discountRequests.map((discount, index) => (
                        <div key={index} className={`discount-item ${discount.status}`}>
                          <div className="discount-info">
                            <div className="discount-amount">
                              <i className="fas fa-percentage"></i>
                              <span>{discount.amount} ج.م</span>
                            </div>
                            <div className="discount-reason">
                              <span>{discount.reason}</span>
                            </div>
                            <div className="discount-meta">
                              <span>طلب من: {discount.requestedBy}</span>
                              <span>{formatTime(discount.requestedAt)}</span>
                            </div>
                          </div>
                          
                          <div className="discount-status">
                            <span className={`status-badge ${discount.status}`}>
                              {discount.status === 'pending' ? 'معلق' : 
                               discount.status === 'approved' ? 'موافق عليه' : 'مرفوض'}
                            </span>
                          </div>
                          
                          {userRole === 'manager' && discount.status === 'pending' && (
                            <div className="discount-actions">
                              <button 
                                className="approve-btn"
                                onClick={() => approveDiscount(selectedOrder._id, index)}
                              >
                                <i className="fas fa-check"></i>
                                موافقة
                              </button>
                              <button 
                                className="reject-btn"
                                onClick={() => rejectDiscount(selectedOrder._id, index)}
                              >
                                <i className="fas fa-times"></i>
                                رفض
                              </button>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
            
            <div className="modal-footer">
              {/* Status Update Buttons */}
              {userRole === 'chef' && selectedOrder.status === 'pending' && (
                <button 
                  className="btn-action start"
                  onClick={() => {
                    updateOrderStatus(selectedOrder._id, 'preparing');
                    setShowOrderModal(false);
                  }}
                >
                  <i className="fas fa-play"></i>
                  بدء التحضير
                </button>
              )}
              
              {userRole === 'chef' && selectedOrder.status === 'preparing' && (
                <button 
                  className="btn-action complete"
                  onClick={() => {
                    updateOrderStatus(selectedOrder._id, 'ready');
                    setShowOrderModal(false);
                  }}
                >
                  <i className="fas fa-check"></i>
                  جاهز للتسليم
                </button>
              )}
              
              {userRole === 'waiter' && selectedOrder.status === 'ready' && (
                <button 
                  className="btn-action deliver"
                  onClick={() => {
                    updateOrderStatus(selectedOrder._id, 'completed');
                    setShowOrderModal(false);
                  }}
                >
                  <i className="fas fa-check-double"></i>
                  تم التسليم
                </button>
              )}
              
              <button 
                className="btn-close"
                onClick={() => setShowOrderModal(false)}
              >
                إغلاق
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default OrdersScreen;
