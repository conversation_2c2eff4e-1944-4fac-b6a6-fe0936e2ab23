/* Orders Screen Styles */
.orders-screen {
  padding: 1.5rem;
  max-width: 1400px;
  margin: 0 auto;
  background: var(--light-bg, #f8f9fa);
  min-height: 100vh;
}

/* Loading State */
.orders-screen.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  color: var(--primary-color, #2c3e50);
}

.loading-spinner i {
  font-size: 2rem;
  color: var(--primary-color, #2c3e50);
}

/* Header */
.orders-header {
  text-align: center;
  margin-bottom: 2rem;
}

.orders-header h1 {
  margin: 0 0 0.5rem 0;
  color: var(--primary-color, #2c3e50);
  font-size: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}

.orders-header p {
  color: var(--text-muted, #95a5a6);
  font-size: 1.1rem;
  margin: 0;
}

/* Stats Cards */
.orders-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: var(--white, #ffffff);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: var(--white, #ffffff);
}

.stat-card.total .stat-icon {
  background: var(--primary-color, #2c3e50);
}

.stat-card.pending .stat-icon {
  background: var(--warning-color, #f39c12);
}

.stat-card.preparing .stat-icon {
  background: var(--info-color, #3498db);
}

.stat-card.ready .stat-icon {
  background: var(--success-color, #27ae60);
}

.stat-card.sales .stat-icon {
  background: var(--secondary-color, #f39c12);
}

.stat-card.time .stat-icon {
  background: var(--purple, #9b59b6);
}

.stat-content h3 {
  margin: 0 0 0.25rem 0;
  font-size: 1.75rem;
  font-weight: bold;
  color: var(--primary-color, #2c3e50);
}

.stat-content p {
  margin: 0;
  color: var(--text-muted, #95a5a6);
  font-size: 0.875rem;
}

/* Filters */
.orders-filters {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
  padding: 1rem;
  background: var(--white, #ffffff);
  border-radius: 0.75rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-group label {
  font-weight: 500;
  color: var(--text-color, #2c3e50);
  white-space: nowrap;
}

.filter-group select {
  padding: 0.5rem 1rem;
  border: 1px solid var(--border-color, #ecf0f1);
  border-radius: 0.375rem;
  background: var(--white, #ffffff);
  color: var(--text-color, #2c3e50);
  font-size: 0.9rem;
  cursor: pointer;
}

.refresh-btn {
  background: var(--info-color, #3498db);
  color: var(--white, #ffffff);
  border: none;
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-right: auto;
}

.refresh-btn:hover {
  background: #2980b9;
}

/* Orders List */
.orders-list {
  margin-bottom: 2rem;
}

.orders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.order-card {
  background: var(--white, #ffffff);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  cursor: pointer;
  position: relative;
  border-left: 4px solid transparent;
}

.order-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.order-card.pending {
  border-left-color: #f39c12;
}

.order-card.preparing {
  border-left-color: #3498db;
}

.order-card.ready {
  border-left-color: #27ae60;
}

.order-card.completed {
  border-left-color: #95a5a6;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.order-number {
  font-size: 1.25rem;
  font-weight: bold;
  color: var(--primary-color, #2c3e50);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.order-status {
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  color: var(--white, #ffffff);
  font-size: 0.75rem;
  font-weight: 500;
}

.order-info {
  margin-bottom: 1rem;
}

.info-row {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  color: var(--text-color, #2c3e50);
  font-size: 0.9rem;
}

.info-row i {
  color: var(--secondary-color, #f39c12);
  width: 1rem;
  text-align: center;
}

.order-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: var(--light-bg, #f8f9fa);
  border-radius: 0.5rem;
  margin-bottom: 1rem;
}

.items-count,
.order-total {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: var(--text-color, #2c3e50);
}

.items-count i,
.order-total i {
  color: var(--secondary-color, #f39c12);
}

.order-time {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-muted, #95a5a6);
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

.order-time i {
  color: var(--secondary-color, #f39c12);
}

.order-time .date {
  margin-right: 0.5rem;
}

.discount-alert {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: rgba(231, 76, 60, 0.1);
  color: var(--error-color, #e74c3c);
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 1rem;
}

.order-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.action-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.375rem;
}

.action-btn.start {
  background: var(--info-color, #3498db);
  color: var(--white, #ffffff);
}

.action-btn.start:hover {
  background: #2980b9;
}

.action-btn.complete {
  background: var(--success-color, #27ae60);
  color: var(--white, #ffffff);
}

.action-btn.complete:hover {
  background: #229954;
}

.action-btn.deliver {
  background: var(--warning-color, #f39c12);
  color: var(--white, #ffffff);
}

.action-btn.deliver:hover {
  background: #e67e22;
}

/* No Orders */
.no-orders {
  text-align: center;
  padding: 4rem 2rem;
  color: var(--text-muted, #95a5a6);
}

.no-orders i {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.no-orders p {
  font-size: 1.1rem;
  margin: 0;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-content {
  background: var(--white, #ffffff);
  border-radius: 1rem;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.order-modal {
  max-width: 800px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color, #ecf0f1);
}

.modal-header h2 {
  margin: 0;
  color: var(--primary-color, #2c3e50);
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: var(--text-muted, #95a5a6);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: var(--light-bg, #f8f9fa);
  color: var(--text-color, #2c3e50);
}

.modal-body {
  padding: 1.5rem;
}

.order-modal-info {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.info-section h3,
.items-section h3,
.discounts-section h3 {
  margin: 0 0 1rem 0;
  color: var(--primary-color, #2c3e50);
  font-size: 1.1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--border-color, #ecf0f1);
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
}

.info-item .label {
  color: var(--text-muted, #95a5a6);
  font-weight: 500;
}

.info-item .value {
  font-weight: 500;
  color: var(--text-color, #2c3e50);
}

.info-item .value.status {
  font-weight: bold;
}

.items-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.item-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0.75rem;
  background: var(--light-bg, #f8f9fa);
  border-radius: 0.5rem;
}

.item-info {
  flex: 1;
}

.item-name {
  font-weight: 500;
  color: var(--text-color, #2c3e50);
  margin-bottom: 0.25rem;
}

.item-notes {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--text-muted, #95a5a6);
  font-size: 0.875rem;
}

.item-quantity,
.item-price {
  font-weight: 500;
  color: var(--text-color, #2c3e50);
  min-width: 60px;
  text-align: center;
}

.order-total-section {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 2px solid var(--border-color, #ecf0f1);
}

.total-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 1.1rem;
  font-weight: bold;
  color: var(--primary-color, #2c3e50);
}

.modal-footer {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid var(--border-color, #ecf0f1);
}

.btn-action {
  flex: 1;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  color: var(--white, #ffffff);
}

.btn-action.start {
  background: var(--info-color, #3498db);
}

.btn-action.start:hover {
  background: #2980b9;
}

.btn-action.complete {
  background: var(--success-color, #27ae60);
}

.btn-action.complete:hover {
  background: #229954;
}

.btn-action.deliver {
  background: var(--warning-color, #f39c12);
}

.btn-action.deliver:hover {
  background: #e67e22;
}

.btn-close {
  background: var(--border-color, #ecf0f1);
  color: var(--text-color, #2c3e50);
  border: none;
  border-radius: 0.5rem;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-close:hover {
  background: #bdc3c7;
}

/* Responsive Design */
@media (max-width: 768px) {
  .orders-screen {
    padding: 1rem;
  }

  .orders-header h1 {
    font-size: 1.5rem;
  }

  .orders-stats {
    grid-template-columns: 1fr;
  }

  .stat-card {
    padding: 1rem;
  }

  .stat-icon {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }

  .stat-content h3 {
    font-size: 1.5rem;
  }

  .orders-filters {
    flex-direction: column;
    gap: 1rem;
  }

  .filter-group {
    width: 100%;
    justify-content: space-between;
  }

  .filter-group select {
    flex: 1;
    margin-right: 0.5rem;
  }

  .refresh-btn {
    margin-right: 0;
    width: 100%;
    justify-content: center;
  }

  .orders-grid {
    grid-template-columns: 1fr;
  }

  .order-card {
    padding: 1rem;
  }

  .order-actions {
    justify-content: center;
  }

  .action-btn {
    flex: 1;
    min-width: 120px;
  }
}

@media (max-width: 480px) {
  .orders-screen {
    padding: 0.75rem;
  }

  .orders-header h1 {
    font-size: 1.25rem;
  }

  .stat-card {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }

  .order-header {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }

  .order-details {
    flex-direction: column;
    gap: 0.5rem;
  }

  .order-actions {
    flex-direction: column;
  }

  .action-btn {
    width: 100%;
  }

  .modal-content {
    margin: 0.5rem;
    max-height: 95vh;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 1rem;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .item-row {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }

  .modal-footer {
    flex-direction: column;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .orders-screen {
    background: #1a1a1a;
  }

  .stat-card,
  .orders-filters,
  .order-card,
  .modal-content {
    background: #2d2d2d;
    color: #ffffff;
  }

  .item-row {
    background: #3d3d3d;
  }

  .filter-group select {
    background: #3d3d3d;
    color: #ffffff;
    border-color: #555555;
  }
}
