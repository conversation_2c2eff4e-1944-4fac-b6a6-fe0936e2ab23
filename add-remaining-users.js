import mongoose from 'mongoose';
import bcrypt from 'bcrypt';
import dotenv from 'dotenv';

dotenv.config();

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb+srv://besomustafa:<EMAIL>/deshacoffee?retryWrites=true&w=majority&appName=MyCoffeChop';

// المستخدمين المتبقيين الذين يجب إضافتهم
const remainingUsers = [
  {
    username: 'azz',
    password: '253040',
    email: '<EMAIL>',
    name: 'عز',
    role: 'waiter',
    status: 'active',
    phone: '01234567891'
  },
  {
    username: '<PERSON><PERSON>',
    password: '253040',
    email: '<EMAIL>',
    name: 'بوسي',
    role: 'waiter',
    status: 'active',
    phone: '01234567892'
  },
  {
    username: 'khaled',
    password: '253040',
    email: '<EMAIL>',
    name: 'خالد',
    role: 'chef',
    status: 'active',
    phone: '01234567893'
  }
];

async function hashPassword(password) {
  const saltRounds = 12;
  return await bcrypt.hash(password, saltRounds);
}

async function addRemainingUsers() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('✅ متصل بقاعدة البيانات');

    const db = mongoose.connection.db;
    const usersCollection = db.collection('users');
    
    console.log('👥 إضافة المستخدمين المتبقيين...');
    console.log('');

    for (const userData of remainingUsers) {
      try {
        // التحقق من وجود المستخدم
        const existingUser = await usersCollection.findOne({ 
          $or: [
            { username: userData.username },
            { email: userData.email }
          ]
        });
        
        if (existingUser) {
          console.log(`⚠️  المستخدم ${userData.username} موجود بالفعل، سيتم تحديثه...`);
          
          // تشفير كلمة المرور
          const hashedPassword = await hashPassword(userData.password);
          
          await usersCollection.updateOne(
            { username: userData.username },
            { 
              $set: {
                password: hashedPassword,
                email: userData.email,
                name: userData.name,
                role: userData.role,
                status: userData.status,
                phone: userData.phone
              }
            }
          );
          
          console.log(`✅ تم تحديث المستخدم: ${userData.username}`);
        } else {
          console.log(`➕ إضافة مستخدم جديد: ${userData.username}`);
          
          // تشفير كلمة المرور
          const hashedPassword = await hashPassword(userData.password);
          
          await usersCollection.insertOne({
            username: userData.username,
            password: hashedPassword,
            email: userData.email,
            name: userData.name,
            role: userData.role,
            status: userData.status,
            phone: userData.phone,
            createdAt: new Date(),
            loginAttempts: 0,
            lockUntil: null
          });
          
          console.log(`✅ تم إضافة المستخدم: ${userData.username}`);
        }
        
        console.log(`   📧 البريد الإلكتروني: ${userData.email}`);
        console.log(`   👤 الاسم: ${userData.name}`);
        console.log(`   🎭 الدور: ${userData.role}`);
        console.log(`   📱 الهاتف: ${userData.phone}`);
        console.log('');
        
      } catch (userError) {
        console.error(`❌ خطأ في معالجة المستخدم ${userData.username}:`, userError.message);
      }
    }

    // عرض جميع المستخدمين النهائيين
    console.log('📊 جميع المستخدمين في النظام:');
    const allUsers = await usersCollection.find({}).toArray();
    
    allUsers.forEach((user, index) => {
      const roleArabic = {
        'manager': 'مدير',
        'waiter': 'نادل', 
        'chef': 'طباخ',
        'admin': 'مشرف',
        'employee': 'موظف'
      }[user.role] || user.role;
      
      console.log(`${index + 1}. ${user.username} - ${roleArabic} - ${user.status} - ${user.name}`);
    });
    
    console.log('');
    console.log('🎉 تمت إضافة جميع المستخدمين بنجاح!');
    console.log('');
    console.log('📝 بيانات تسجيل الدخول:');
    console.log('');
    
    const loginData = [
      { username: 'Beso', password: 'MOHAMEDmostafa123', role: 'مدير' },
      { username: 'azz', password: '253040', role: 'نادل' },
      { username: 'Bosy', password: '253040', role: 'نادل' },
      { username: 'khaled', password: '253040', role: 'طباخ' }
    ];
    
    loginData.forEach((data, index) => {
      console.log(`${index + 1}. اسم المستخدم: ${data.username}`);
      console.log(`   كلمة المرور: ${data.password}`);
      console.log(`   الدور: ${data.role}`);
      console.log('');
    });

  } catch (error) {
    console.error('❌ خطأ:', error.message);
    console.error(error.stack);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

console.log('🚀 بدء عملية إضافة المستخدمين المتبقيين...');
console.log('⏰ الوقت:', new Date().toLocaleString('ar-EG'));
console.log('');

addRemainingUsers()
  .then(() => {
    console.log('✅ انتهت العملية بنجاح');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ فشلت العملية:', error);
    process.exit(1);
  });
