const express = require('express');
const router = express.Router();
const Category = require('../models/Category');
const { authenticateToken } = require('../middleware/auth');

// Get all categories
router.get('/', async (req, res) => {
  try {
    const { active } = req.query;
    let filter = {};

    if (active !== undefined) {
      filter.active = active === 'true';
    }

    const categories = await Category.find(filter).sort({ order: 1, createdAt: 1 });

    // Return direct array for consistency with frontend expectations
    res.json(categories);
  } catch (error) {
    console.error('Get categories error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب الفئات',
      error: error.message
    });
  }
});

// Get category by ID
router.get('/:id', async (req, res) => {
  try {
    const category = await Category.findById(req.params.id);

    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'الفئة غير موجودة'
      });
    }

    res.json({
      success: true,
      data: category
    });
  } catch (error) {
    console.error('Get category error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب الفئة'
    });
  }
});

// Get products by category
router.get('/:id/products', async (req, res) => {
  try {
    const category = await Category.findById(req.params.id);

    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'الفئة غير موجودة'
      });
    }

    // This would normally fetch products from Product model
    // For now, we'll need to import Product model when it's ready
    const Product = require('../models/Product');
    const products = await Product.find({ categories: req.params.id, available: true });

    res.json({
      success: true,
      data: products,
      category: category,
      total: products.length
    });
  } catch (error) {
    console.error('Get category products error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب منتجات الفئة'
    });
  }
});

// Create new category
router.post('/', authenticateToken, async (req, res) => {
  try {
    const { name, description, icon, color, active, featured, sortOrder } = req.body;

    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'اسم الفئة مطلوب'
      });
    }

    // Check if category already exists
    const existingCategory = await Category.findOne({ name });
    if (existingCategory) {
      return res.status(400).json({
        success: false,
        message: 'اسم الفئة موجود بالفعل'
      });
    }

    const newCategory = new Category({
      name,
      description: description || '',
      icon: icon || '📦',
      color: color || '#6B7280',
      active: active !== undefined ? active : true,
      featured: featured || false,
      sortOrder: sortOrder || 0
    });

    await newCategory.save();

    // Send Socket notifications for new category
    if (global.socketHandlers) {
      try {
        // Notify managers about new category
        global.socketHandlers.sendRoleNotification('manager', 
          `تم إضافة فئة جديدة: ${newCategory.name}`, {
          type: 'category-created',
          categoryId: newCategory._id,
          categoryName: newCategory.name,
          timestamp: new Date().toISOString()
        });

        console.log(`📂 تم إرسال إشعار إضافة فئة جديدة: ${newCategory.name}`);
      } catch (socketError) {
        console.error('خطأ في إرسال إشعار Socket:', socketError);
      }
    }

    res.status(201).json({
      success: true,
      message: 'تم إنشاء الفئة بنجاح',
      data: newCategory
    });
  } catch (error) {
    console.error('Create category error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في إنشاء الفئة',
      error: error.message
    });
  }
});

// Update category
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const category = await Category.findById(req.params.id);

    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'الفئة غير موجودة'
      });
    }

    // Check if new name already exists for another category
    if (req.body.name && req.body.name !== category.name) {
      const existingCategory = await Category.findOne({ 
        name: req.body.name,
        _id: { $ne: req.params.id }
      });
      
      if (existingCategory) {
        return res.status(400).json({
          success: false,
          message: 'اسم الفئة موجود بالفعل'
        });
      }
    }

    Object.assign(category, req.body);
    await category.save();

    // Send Socket notifications for updated category
    if (global.socketHandlers) {
      try {
        // Notify managers about updated category
        global.socketHandlers.sendRoleNotification('manager', 
          `تم تحديث فئة: ${category.name}`, {
          type: 'category-updated',
          categoryId: category._id,
          categoryName: category.name,
          timestamp: new Date().toISOString()
        });

        console.log(`🔄 تم إرسال إشعار تحديث فئة: ${category.name}`);
      } catch (socketError) {
        console.error('خطأ في إرسال إشعار Socket:', socketError);
      }
    }

    res.json({
      success: true,
      message: 'تم تحديث الفئة بنجاح',
      data: category
    });
  } catch (error) {
    console.error('Update category error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في تحديث الفئة',
      error: error.message
    });
  }
});

// Toggle category active status
router.patch('/:id/toggle', authenticateToken, async (req, res) => {
  try {
    const category = await Category.findById(req.params.id);

    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'الفئة غير موجودة'
      });
    }

    // Toggle active status
    category.active = !category.active;
    await category.save();

    res.json({
      success: true,
      message: `تم ${category.active ? 'تفعيل' : 'إلغاء تفعيل'} الفئة بنجاح`,
      data: category
    });
  } catch (error) {
    console.error('Toggle category error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في تغيير حالة الفئة',
      error: error.message
    });
  }
});

// Delete category
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const category = await Category.findById(req.params.id);

    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'الفئة غير موجودة'
      });
    }

    // Check if category has products
    const Product = require('../models/Product');
    const productsCount = await Product.countDocuments({ category: req.params.id, status: 'active' });
    
    if (productsCount > 0) {
      return res.status(400).json({
        success: false,
        message: `لا يمكن حذف الفئة لأنها تحتوي على ${productsCount} منتج. يرجى حذف المنتجات أولاً أو تغيير فئتها.`
      });
    }

    // Hard delete - remove from database completely
    await Category.findByIdAndDelete(req.params.id);

    // Send Socket notifications for deleted category
    if (global.socketHandlers) {
      try {
        // Notify managers about deleted category
        global.socketHandlers.sendRoleNotification('manager', 
          `تم حذف فئة: ${category.name}`, {
          type: 'category-deleted',
          categoryId: category._id,
          categoryName: category.name,
          timestamp: new Date().toISOString()
        });

        console.log(`🗑️ تم إرسال إشعار حذف فئة: ${category.name}`);
      } catch (socketError) {
        console.error('خطأ في إرسال إشعار Socket:', socketError);
      }
    }

    res.json({
      success: true,
      message: 'تم حذف الفئة نهائياً بنجاح'
    });
  } catch (error) {
    console.error('Delete category error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في حذف الفئة',
      error: error.message
    });
  }
});

module.exports = router;
