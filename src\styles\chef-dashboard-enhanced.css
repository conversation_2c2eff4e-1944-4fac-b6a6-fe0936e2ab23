/* Enhanced Chef Dashboard Styles - OOP Version */

/* Main Dashboard Container */
.chef-dashboard {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
  overflow: hidden;
}

/* Header Styles */
.dashboard-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 1rem 2rem;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.sidebar-toggle {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #2c3e50;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.sidebar-toggle:hover {
  background: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

.header-left h1 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.8rem;
  font-weight: 600;
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.quick-filters {
  display: flex;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.8);
  padding: 0.5rem;
  border-radius: 25px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.filter-btn {
  background: transparent;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 20px;
  color: #2c3e50;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.filter-btn:hover {
  background: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

.filter-btn.active {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  box-shadow: 0 2px 10px rgba(52, 152, 219, 0.3);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  color: #2c3e50;
  font-weight: 500;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-outline {
  background: transparent;
  border: 2px solid #e74c3c;
  color: #e74c3c;
}

.btn-outline:hover {
  background: #e74c3c;
  color: white;
}

.btn-primary {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.btn-success {
  background: linear-gradient(135deg, #27ae60, #229954);
  color: white;
}

.btn-success:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

.btn-complete {
  background: linear-gradient(135deg, #9b59b6, #8e44ad);
  color: white;
}

.btn-complete:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(155, 89, 182, 0.3);
}

.btn-secondary {
  background: #95a5a6;
  color: white;
}

.btn-secondary:hover {
  background: #7f8c8d;
}

/* Dashboard Content */
.dashboard-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* Sidebar */
.sidebar {
  width: 280px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  overflow-y: auto;
  box-shadow: 2px 0 20px rgba(0, 0, 0, 0.1);
}

.sidebar.open {
  width: 280px;
}

.sidebar-content {
  padding: 2rem 1.5rem;
}

.nav-section h3 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.1rem;
  font-weight: 600;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid rgba(52, 152, 219, 0.2);
}

.nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-list li {
  margin-bottom: 0.5rem;
}

.nav-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 1rem 1.5rem;
  background: rgba(255, 255, 255, 0.8);
  border: none;
  border-radius: 12px;
  color: #2c3e50;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border-right: 4px solid transparent;
}

.nav-item:hover {
  background: rgba(52, 152, 219, 0.1);
  border-right-color: #3498db;
  color: #3498db;
  transform: translateX(-5px);
}

.nav-item.active {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  border-right-color: #2980b9;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.notification-badge {
  background: #e74c3c;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: 600;
}

/* Main Content */
.main-content {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
  background: transparent;
}

/* Kitchen View */
.kitchen-view {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.kitchen-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.kitchen-header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.8rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.orders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.order-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border-right: 4px solid #3498db;
  cursor: pointer;
}

.order-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.order-card.pending {
  border-right-color: #f39c12;
}

.order-card.preparing {
  border-right-color: #e74c3c;
}

.order-card.ready {
  border-right-color: #9b59b6;
}

.order-card.completed {
  border-right-color: #27ae60;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.order-number {
  font-size: 1.3rem;
  font-weight: 700;
  color: #2c3e50;
}

.order-status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.order-status.pending {
  background: #f39c12;
  color: white;
}

.order-status.preparing {
  background: #e74c3c;
  color: white;
}

.order-status.ready {
  background: #9b59b6;
  color: white;
}

.order-status.completed {
  background: #27ae60;
  color: white;
}

.order-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.order-detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
  color: #7f8c8d;
}

.order-detail strong {
  color: #2c3e50;
}

.order-items {
  margin-bottom: 1rem;
}

.order-items h4 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 1rem;
}

.items-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.item-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 8px;
  font-size: 0.9rem;
}

.item-name {
  font-weight: 500;
  color: #2c3e50;
}

.item-quantity {
  color: #7f8c8d;
}

.order-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

.action-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.action-btn.accept {
  background: #27ae60;
  color: white;
}

.action-btn.accept:hover {
  background: #229954;
}

.action-btn.ready {
  background: #9b59b6;
  color: white;
}

.action-btn.ready:hover {
  background: #8e44ad;
}

.action-btn.complete {
  background: #3498db;
  color: white;
}

.action-btn.complete:hover {
  background: #2980b9;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
  backdrop-filter: blur(5px);
}

.modal-content {
  background: white;
  border-radius: 16px;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  border-bottom: 1px solid #ecf0f1;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
}

.modal-header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.8rem;
  cursor: pointer;
  color: #7f8c8d;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  background: #e74c3c;
  color: white;
  transform: rotate(90deg);
}

.modal-body {
  padding: 2rem;
}

.order-details {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.order-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.info-item {
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-right: 4px solid #3498db;
}

.order-notes {
  padding: 1rem;
  background: #fff3cd;
  border-radius: 8px;
  border-right: 4px solid #ffc107;
}

.order-notes p {
  margin: 0.5rem 0 0 0;
  color: #856404;
}

.order-items h3 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
}

.items-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.item-card {
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-right: 4px solid #27ae60;
}

.item-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.item-info h4 {
  margin: 0;
  color: #2c3e50;
}

.item-quantity,
.item-price {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.item-notes {
  margin-top: 0.5rem;
  padding: 0.5rem;
  background: #e9ecef;
  border-radius: 4px;
  font-size: 0.9rem;
  color: #6c757d;
}

.order-total {
  text-align: center;
  padding: 1rem;
  background: #d4edda;
  border-radius: 8px;
  border: 2px solid #27ae60;
  font-size: 1.2rem;
  color: #155724;
}

.modal-footer {
  padding: 2rem;
  border-top: 1px solid #ecf0f1;
  background: #f8f9fa;
}

.order-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: #7f8c8d;
}

.empty-state i {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-state h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
}

.empty-state p {
  margin: 0;
  font-size: 1rem;
}

/* Loading State */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #7f8c8d;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #ecf0f1;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .orders-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
  
  .quick-filters {
    flex-wrap: wrap;
  }
}

@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    left: -280px;
    top: 0;
    height: 100vh;
    z-index: 1000;
    transition: left 0.3s ease;
  }
  
  .sidebar.open {
    left: 0;
  }
  
  .main-content {
    padding: 1rem;
  }
  
  .dashboard-header {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
  }
  
  .header-center {
    order: 3;
  }
  
  .quick-filters {
    justify-content: center;
  }
  
  .filter-btn {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }
  
  .orders-grid {
    grid-template-columns: 1fr;
  }
  
  .order-info-grid {
    grid-template-columns: 1fr;
  }
  
  .order-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: 0.5rem;
  }
  
  .kitchen-view {
    padding: 1rem;
  }
  
  .order-card {
    padding: 1rem;
  }
  
  .modal-content {
    margin: 0.5rem;
    max-width: calc(100% - 1rem);
  }
  
  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 1rem;
  }
}
