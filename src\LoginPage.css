/* تصميم محسن لواجهة تسجيل الدخول مع دعم العربية */
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--background);
  background-image: linear-gradient(135deg, rgba(121, 85, 72, 0.1), rgba(161, 136, 127, 0.1));
  direction: rtl;
  position: relative;
}

.theme-toggle-container {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 10;
}

.login-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 400px;
  width: 100%;
}

.app-logo {
  width: 120px;
  height: 120px;
  margin-bottom: var(--spacing-lg);
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

.error-message {
  background-color: rgba(244, 67, 54, 0.1);
  color: var(--error);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-md);
  text-align: center;
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-md);
  border: 1px solid rgba(244, 67, 54, 0.2);
}

.login-form {
  background: var(--surface);
  padding: var(--spacing-xl);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  width: 100%;
  max-width: 380px;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  font-family: var(--font-family);
  position: relative;
  overflow: hidden;
}

.login-form::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100px;
  background: var(--primary-light);
  opacity: 0.1;
  border-radius: 50%;
  transform: translate(30%, -30%);
}

.login-form h2 {
  text-align: center;
  margin-bottom: var(--spacing-md);
  color: var(--primary);
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  position: relative;
}

.login-form h2::after {
  content: '';
  display: block;
  width: 60px;
  height: 3px;
  background: var(--primary);
  margin: var(--spacing-sm) auto 0;
  border-radius: 2px;
}

.login-form label {
  display: flex;
  flex-direction: column;
  font-size: var(--font-size-md);
  color: var(--text-primary);
  gap: var(--spacing-xs);
  font-weight: var(--font-weight-medium);
  position: relative;
}

.input-icon {
  position: absolute;
  left: 10px;
  top: 38px;
  color: var(--primary);
  opacity: 0.7;
}

.login-form input,
.login-form select {
  padding: var(--spacing-md);
  padding-left: 40px;
  border: 1px solid var(--border);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-md);
  direction: rtl;
  transition: border-color 0.2s, box-shadow 0.2s;
  background-color: rgba(255, 255, 255, 0.8);
}

.login-form input:focus,
.login-form select:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(121, 85, 72, 0.2);
}

.login-form button {
  background: var(--primary);
  color: white;
  border: none;
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  margin-top: var(--spacing-sm);
  transition: all 0.2s;
  position: relative;
  overflow: hidden;
  width: 100%;
}

.login-form button:hover:not(:disabled) {
  background: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.login-form button:active:not(:disabled) {
  transform: translateY(0);
}

.login-form button:disabled {
  background: var(--primary-light);
  cursor: not-allowed;
  opacity: 0.7;
}

.login-form button.loading {
  position: relative;
}

.login-form button.loading::after {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
  left: 15px;
  top: calc(50% - 10px);
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.login-help {
  margin-top: var(--spacing-lg);
  padding: var(--spacing-md);
  background-color: rgba(121, 85, 72, 0.05);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  text-align: center;
}

.login-help p {
  margin: var(--spacing-xs) 0;
}

@media (max-width: 480px) {
  .login-form {
    padding: var(--spacing-lg);
    max-width: 90%;
  }

  .app-logo {
    width: 80px;
    height: 80px;
  }
}
