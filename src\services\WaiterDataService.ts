// خدمة إدارة البيانات للوحة النادل
import { authenticatedGet, authenticatedPost, authenticatedPut, authenticatedDelete } from '../utils/apiHelpers';
import type { 
  WaiterOrder, 
  WaiterTableAccount, 
  WaiterProduct, 
  WaiterCategory,
  NewOrderData,
  DiscountRequestData,
  WaiterNotification
} from '../models/WaiterModels';

export class WaiterDataService {
  private static instance: WaiterDataService;

  // Singleton pattern
  static getInstance(): WaiterDataService {
    if (!WaiterDataService.instance) {
      WaiterDataService.instance = new WaiterDataService();
    }
    return WaiterDataService.instance;
  }

  private constructor() {}
    // جلب حسابات الطاولات للنادل الحالي
  async getMyTables(waiterId?: string, waiterName?: string): Promise<WaiterTableAccount[]> {
    return WaiterDataService.fetchMyTableAccounts(waiterId || '', waiterName || '');
  }

  // جلب الطلبات للنادل الحالي
  async getMyOrders(waiterId?: string, waiterName?: string): Promise<WaiterOrder[]> {
    return WaiterDataService.fetchMyOrders(waiterId || '', waiterName || '');
  }
  // جلب المنتجات
  async getProducts(): Promise<WaiterProduct[]> {
    return WaiterDataService.fetchAvailableProducts();
  }

  // جلب الفئات
  async getCategories(): Promise<WaiterCategory[]> {
    return WaiterDataService.fetchCategories();
  }

  // إرسال طلب جديد
  async createOrder(orderData: NewOrderData): Promise<boolean> {
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    const waiterId = user._id || user.id || 'unknown';
    const waiterName = user.username || user.name || 'نادل';
    
    const result = await WaiterDataService.createNewOrder(orderData, waiterId, waiterName);
    return result.success;
  }

  // تحديد الإشعار كمقروء
  async markNotificationAsRead(notificationId: string): Promise<boolean> {
    const result = await WaiterDataService.markNotificationAsRead(notificationId);
    return result.success;
  }

  // طلب خصم
  async requestDiscount(discountData: DiscountRequestData): Promise<boolean> {
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    const waiterId = user._id || user.id || 'unknown';
    const waiterName = user.username || user.name || 'نادل';
    
    const result = await WaiterDataService.createDiscountRequest(discountData, waiterId, waiterName);
    return result.success;
  }
  // جلب حسابات الطاولات (alias)
  static async fetchTableAccounts(waiterId?: string): Promise<WaiterTableAccount[]> {
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    const id = waiterId || user._id || user.id || '';
    const name = user.username || user.name || '';
    return this.fetchMyTableAccounts(id, name);
  }

  // جلب الطلبات (alias)
  static async fetchOrders(waiterId?: string): Promise<WaiterOrder[]> {
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    const id = waiterId || user._id || user.id || '';
    const name = user.username || user.name || '';
    return this.fetchMyOrders(id, name);
  }

  // جلب المنتجات (alias)
  static async fetchMenuItems(): Promise<WaiterProduct[]> {
    return this.fetchAvailableProducts();
  }

  // إنشاء طلب جديد (alias)
  static async createOrder(orderData: NewOrderData): Promise<{ success: boolean; message?: string }> {
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    const waiterId = user._id || user.id || '';
    const waiterName = user.username || user.name || 'نادل';
    return this.createNewOrder(orderData, waiterId, waiterName);
  }

  // إكمال طلب
  static async completeOrder(orderId: string): Promise<{ success: boolean; message?: string }> {
    try {
      const response = await authenticatedPut(`/api/orders/${orderId}`, {
        status: 'delivered'
      });

      if (response.success) {
        return { success: true };
      } else {
        return { success: false, message: response.message || 'فشل في إكمال الطلب' };
      }
    } catch (error) {
      console.error('خطأ في إكمال الطلب:', error);
      return { success: false, message: 'فشل في إكمال الطلب' };
    }
  }

  // طلب خصم
  static async requestDiscount(orderId: string, discountData: any): Promise<{ success: boolean; message?: string }> {
    try {
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      const waiterName = user?.name || user?.username || 'النادل';

      const response = await authenticatedPost('/api/discount-requests', {
        orderId,
        waiterName,
        ...discountData
      });

      if (response.success) {
        return { success: true };
      } else {
        return { success: false, message: response.message || 'فشل في إرسال طلب الخصم' };
      }
    } catch (error) {
      console.error('خطأ في طلب الخصم:', error);
      return { success: false, message: 'فشل في إرسال طلب الخصم' };
    }
  }

  // جلب حسابات الطاولات للنادل الحالي
  static async fetchMyTableAccounts(waiterId: string, waiterName: string): Promise<WaiterTableAccount[]> {
    try {
      const response = await authenticatedGet('/api/table-accounts');
      console.log('🍽️ استجابة API للطاولات:', response);

      let tablesData: any[] = [];
      if (Array.isArray(response)) {
        tablesData = response;
      } else if (response.success && Array.isArray(response.data)) {
        tablesData = response.data;
      } else if (response.data && Array.isArray(response.data)) {
        tablesData = response.data;
      }

      // فلترة الطاولات للنادل الحالي فقط
      const myTables = tablesData.filter(table => 
        table.waiterName === waiterName || 
        table.waiterId === waiterId ||
        table.waiterName === waiterId
      );

      console.log('✅ تم جلب طاولاتي:', myTables.length, 'طاولة');
      return myTables;
    } catch (error) {
      console.error('خطأ في جلب الطاولات:', error);
      throw new Error('فشل في جلب الطاولات');
    }
  }

  // جلب الطلبات للنادل الحالي
  static async fetchMyOrders(waiterId: string, waiterName: string): Promise<WaiterOrder[]> {
    try {
      const response = await authenticatedGet('/api/orders');
      console.log('📊 استجابة API للطلبات:', response);

      let ordersData: any[] = [];
      if (Array.isArray(response)) {
        ordersData = response;
      } else if (response.success && Array.isArray(response.data)) {
        ordersData = response.data;
      } else if (response.data && Array.isArray(response.data)) {
        ordersData = response.data;
      }

      // فلترة الطلبات للنادل الحالي فقط
      const myOrders = ordersData.filter(order => 
        order.waiterName === waiterName || 
        order.waiterId === waiterId ||
        order.waiterName === waiterId
      );

      console.log('✅ تم جلب طلباتي:', myOrders.length, 'طلب');
      return myOrders;
    } catch (error) {
      console.error('خطأ في جلب الطلبات:', error);
      throw new Error('فشل في جلب الطلبات');
    }
  }

  // جلب المنتجات المتاحة
  static async fetchAvailableProducts(): Promise<WaiterProduct[]> {
    try {
      const response = await authenticatedGet('/api/products');
      console.log('🍕 استجابة API للمنتجات:', response);

      let productsData: any[] = [];
      if (Array.isArray(response)) {
        productsData = response;
      } else if (response.success && Array.isArray(response.data)) {
        productsData = response.data;
      } else if (response.data && Array.isArray(response.data)) {
        productsData = response.data;
      }

      // فلترة المنتجات المتاحة فقط
      const availableProducts = productsData.filter(product => product.available);

      console.log('✅ تم جلب المنتجات المتاحة:', availableProducts.length, 'منتج');
      return availableProducts;
    } catch (error) {
      console.error('خطأ في جلب المنتجات:', error);
      throw new Error('فشل في جلب المنتجات');
    }
  }

  // جلب الفئات
  static async fetchCategories(): Promise<WaiterCategory[]> {
    try {
      const response = await authenticatedGet('/api/categories');
      console.log('🏷️ استجابة API للفئات:', response);

      let categoriesData: any[] = [];
      if (Array.isArray(response)) {
        categoriesData = response;
      } else if (response.success && Array.isArray(response.data)) {
        categoriesData = response.data;
      } else if (response.data && Array.isArray(response.data)) {
        categoriesData = response.data;
      }

      console.log('✅ تم جلب الفئات:', categoriesData.length, 'فئة');
      return categoriesData;
    } catch (error) {
      console.error('خطأ في جلب الفئات:', error);
      return [];
    }
  }

  // إنشاء طلب جديد
  static async createNewOrder(orderData: NewOrderData, waiterId: string, waiterName: string): Promise<{ success: boolean; message?: string; data?: any }> {
    try {
      const response = await authenticatedPost('/api/orders', {
        ...orderData,
        waiterId,
        waiterName,
        status: 'pending'
      });

      if (response.success) {
        return { success: true, data: response.data };
      } else {
        return { success: false, message: response.message || 'فشل في إنشاء الطلب' };
      }
    } catch (error) {
      console.error('خطأ في إنشاء الطلب:', error);
      return { success: false, message: 'فشل في إنشاء الطلب' };
    }
  }

  // تحديث حالة الطلب
  static async updateOrderStatus(orderId: string, status: string): Promise<{ success: boolean; message?: string }> {
    try {
      const response = await authenticatedPut(`/api/orders/${orderId}`, { status });

      if (response.success) {
        return { success: true };
      } else {
        return { success: false, message: response.message || 'فشل في تحديث حالة الطلب' };
      }
    } catch (error) {
      console.error('خطأ في تحديث حالة الطلب:', error);
      return { success: false, message: 'فشل في تحديث حالة الطلب' };
    }
  }

  // إنشاء طلب خصم
  static async createDiscountRequest(discountData: DiscountRequestData, waiterId: string, waiterName: string): Promise<{ success: boolean; message?: string }> {
    try {
      const response = await authenticatedPost('/api/discount-requests', {
        ...discountData,
        waiterId,
        waiterName,
        status: 'pending'
      });

      if (response.success) {
        return { success: true };
      } else {
        return { success: false, message: response.message || 'فشل في إرسال طلب الخصم' };
      }
    } catch (error) {
      console.error('خطأ في إرسال طلب الخصم:', error);
      return { success: false, message: 'فشل في إرسال طلب الخصم' };
    }
  }

  // طلب إغلاق طاولة
  static async requestTableClose(tableId: string, reason?: string): Promise<{ success: boolean; message?: string }> {
    try {
      const response = await authenticatedPost('/api/table-close-requests', {
        tableId,
        reason: reason || 'طلب إغلاق طاولة',
        status: 'pending'
      });

      if (response.success) {
        return { success: true };
      } else {
        return { success: false, message: response.message || 'فشل في طلب إغلاق الطاولة' };
      }
    } catch (error) {
      console.error('خطأ في طلب إغلاق الطاولة:', error);
      return { success: false, message: 'فشل في طلب إغلاق الطاولة' };
    }
  }

  // إغلاق طاولة محلياً (للطاولات المكتملة)
  static async closeTableLocally(tableId: string): Promise<{ success: boolean; message?: string }> {
    try {
      const response = await authenticatedPut(`/api/table-accounts/${tableId}`, {
        isOpen: false,
        status: 'closed'
      });

      if (response.success) {
        return { success: true };
      } else {
        return { success: false, message: response.message || 'فشل في إغلاق الطاولة' };
      }
    } catch (error) {
      console.error('خطأ في إغلاق الطاولة:', error);
      return { success: false, message: 'فشل في إغلاق الطاولة' };
    }
  }

  // جلب الإشعارات للنادل
  static async fetchNotifications(waiterId: string): Promise<WaiterNotification[]> {
    try {
      const response = await authenticatedGet(`/api/notifications/waiter/${waiterId}`);
      
      let notificationsData: any[] = [];
      if (Array.isArray(response)) {
        notificationsData = response;
      } else if (response.success && Array.isArray(response.data)) {
        notificationsData = response.data;
      }

      console.log('🔔 تم جلب الإشعارات:', notificationsData.length);
      return notificationsData;
    } catch (error) {
      console.error('خطأ في جلب الإشعارات:', error);
      return [];
    }
  }

  // تحديد الإشعار كمقروء
  static async markNotificationAsRead(notificationId: string): Promise<{ success: boolean }> {
    try {
      const response = await authenticatedPut(`/api/notifications/${notificationId}`, {
        isRead: true
      });

      return { success: response.success };
    } catch (error) {
      console.error('خطأ في تحديث الإشعار:', error);
      return { success: false };
    }
  }

  // حذف الطاولات المغلقة محلياً
  static async clearClosedTables(waiterId: string, waiterName: string): Promise<{ success: boolean; count: number }> {
    try {
      // جلب الطاولات المغلقة للنادل
      const tables = await this.fetchMyTableAccounts(waiterId, waiterName);
      const closedTables = tables.filter(table => !table.isOpen);
      
      let clearedCount = 0;
      for (const table of closedTables) {
        // التحقق من أن جميع الطلبات مكتملة
        const allOrdersCompleted = table.orders.every(order => 
          order.status === 'completed' || order.status === 'delivered'
        );
        
        if (allOrdersCompleted) {
          const result = await authenticatedDelete(`/api/table-accounts/${table._id}`);
          if (result.success) {
            clearedCount++;
          }
        }
      }

      return { success: true, count: clearedCount };
    } catch (error) {
      console.error('خطأ في مسح الطاولات المغلقة:', error);
      return { success: false, count: 0 };
    }
  }
}
