import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import HttpApi from 'i18next-http-backend'; // If loading translations from backend/public folder

i18n
  .use(HttpApi) // Use HttpApi if you plan to load translations from a path like /locales/{{lng}}/{{ns}}.json
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    supportedLngs: ['en', 'ar'],
    fallbackLng: 'en',
    debug: process.env.NODE_ENV === 'development',
    ns: ['common', 'waiter', 'chef', 'manager'], // Define namespaces for different parts of the app
    defaultNS: 'common',
    detection: {
      order: ['querystring', 'cookie', 'localStorage', 'sessionStorage', 'navigator', 'htmlTag'],
      caches: ['localStorage', 'cookie'],
    },
    backend: {
      loadPath: '/locales/{{lng}}/{{ns}}.json', // Path to translation files
    },
    react: {
      useSuspense: true, // Recommended for better user experience
    },
  });

export default i18n;
