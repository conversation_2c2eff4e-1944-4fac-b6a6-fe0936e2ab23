{"name": "desha-coffee-backend", "version": "1.0.0", "description": "Backend API for Desha Coffee Management System", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "seed": "node scripts/seedDatabase.js", "seed:prod": "NODE_ENV=production node scripts/seedDatabase.js", "seed:dev": "NODE_ENV=development node scripts/seedDatabase.js"}, "keywords": ["coffee", "management", "api", "nodejs", "express"], "author": "MediaFuture", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^6.10.0", "express-validator": "^7.0.1", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.2", "mongoose": "^7.5.0", "socket.io": "^4.7.2", "validator": "^13.11.0"}, "devDependencies": {"jest": "^29.6.2", "node-fetch": "^3.3.2", "nodemon": "^3.0.1"}, "engines": {"node": ">=18.0.0"}}