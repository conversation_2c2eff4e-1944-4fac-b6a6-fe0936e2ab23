/* Employees/Users Management Styles */
.employees-container {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px #0001;
  padding: 2rem;
  margin-top: 2rem;
}

.employees-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 2rem;
}

.employees-table th, .employees-table td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #eee;
  text-align: right;
}

.employees-table th {
  background: #f5f5f5;
  font-weight: 600;
}

.employees-table tr:last-child td {
  border-bottom: none;
}

.modal-overlay {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  background: #0005;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: #fff;
  border-radius: 10px;
  padding: 2rem;
  min-width: 320px;
  max-width: 95vw;
  box-shadow: 0 4px 24px #0002;
}

.modal-content h3 {
  margin-bottom: 1.5rem;
}

.modal-content label {
  display: block;
  margin-bottom: 1rem;
  font-weight: 500;
}

.modal-content input,
.modal-content select {
  width: 100%;
  padding: 0.5rem;
  margin-top: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 6px;
  font-size: 1rem;
}

.modal-content input[type="checkbox"] {
  width: auto;
  margin-left: 0.5rem;
}

.modal-content .btn {
  min-width: 90px;
}
