const express = require('express');
const Inventory = require('../models/Inventory');
const { authenticateToken } = require('../middleware/auth');
const router = express.Router();

// Get all inventory items
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { category, lowStock, outOfStock, search } = req.query;
    let query = { isActive: true };

    // Build query filters
    if (category) {
      query.category = category;
    }

    if (lowStock === 'true') {
      query.$expr = { $lte: ['$quantity', '$min'] };
    }

    if (outOfStock === 'true') {
      query.quantity = 0;
    }

    let items;
    if (search) {
      items = await Inventory.search(search);
    } else {
      items = await Inventory.find(query)
        .populate('createdBy', 'name username')
        .populate('updatedBy', 'name username')
        .sort({ createdAt: -1 });
    }

    res.json(items);
  } catch (error) {
    console.error('Get inventory error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب المخزون',
      error: error.message
    });
  }
});

// Get low stock items
router.get('/low-stock', authenticateToken, async (req, res) => {
  try {
    const lowStockItems = await Inventory.getLowStock();
    
    res.json(lowStockItems);
  } catch (error) {
    console.error('Get low stock error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب المواد منخفضة المخزون',
      error: error.message
    });
  }
});

// Get out of stock items
router.get('/out-of-stock', authenticateToken, async (req, res) => {
  try {
    const outOfStockItems = await Inventory.getOutOfStock();
    
    res.json(outOfStockItems);
  } catch (error) {
    console.error('Get out of stock error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب المواد النافدة',
      error: error.message
    });
  }
});

// Get inventory item by ID
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const item = await Inventory.findById(req.params.id)
      .populate('createdBy', 'name username')
      .populate('updatedBy', 'name username');

    if (!item) {
      return res.status(404).json({
        success: false,
        message: 'الصنف غير موجود'
      });
    }

    res.json({
      success: true,
      data: item
    });
  } catch (error) {
    console.error('Get inventory item error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب الصنف'
    });
  }
});

// Create new inventory item
router.post('/', authenticateToken, async (req, res) => {
  try {
    const { name, quantity, min, unit, price, category, supplier, notes } = req.body;

    if (!name || quantity === undefined || min === undefined) {
      return res.status(400).json({
        success: false,
        message: 'الاسم والكمية والحد الأدنى مطلوبة'
      });
    }

    // Check if item already exists
    const existingItem = await Inventory.findOne({ name: name.trim(), isActive: true });
    if (existingItem) {
      return res.status(400).json({
        success: false,
        message: 'صنف بهذا الاسم موجود بالفعل'
      });
    }

    const newItem = new Inventory({
      name: name.trim(),
      quantity: Number(quantity),
      min: Number(min),
      unit: unit || 'قطعة',
      price: Number(price) || 0,
      category: category || 'عام',
      supplier,
      notes,
      createdBy: req.user._id
    });

    await newItem.save();

    const populatedItem = await Inventory.findById(newItem._id)
      .populate('createdBy', 'name username')
      .populate('updatedBy', 'name username');

    // Send Socket notifications for new inventory item
    if (global.socketHandlers) {
      try {
        const userDisplayName = req.user.name || req.user.username || 'مستخدم';

        // Notify managers about new inventory item
        global.socketHandlers.sendRoleNotification('manager', 
          `تم إضافة صنف جديد: ${populatedItem.name}`, {
          type: 'inventory-created',
          itemId: populatedItem._id,
          itemName: populatedItem.name,
          quantity: populatedItem.quantity,
          category: populatedItem.category,
          createdBy: userDisplayName,
          timestamp: new Date().toISOString()
        });

        console.log(`📦 تم إرسال إشعار إضافة صنف جديد: ${populatedItem.name}`);
      } catch (socketError) {
        console.error('خطأ في إرسال إشعار Socket:', socketError);
      }
    }

    res.status(201).json({
      success: true,
      message: 'تم إنشاء الصنف بنجاح',
      data: populatedItem
    });
  } catch (error) {
    console.error('Create inventory item error:', error);
    
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: 'صنف بهذا الاسم موجود بالفعل'
      });
    }

    res.status(500).json({
      success: false,
      message: 'خطأ في إنشاء الصنف'
    });
  }
});

// Update inventory item
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const item = await Inventory.findById(req.params.id);

    if (!item) {
      return res.status(404).json({
        success: false,
        message: 'الصنف غير موجود'
      });
    }

    const { name, quantity, min, unit, price, category, supplier, notes } = req.body;

    // If name is being updated, check for duplicates
    if (name && name.trim() !== item.name) {
      const existingItem = await Inventory.findOne({ 
        name: name.trim(), 
        isActive: true,
        _id: { $ne: item._id }
      });
      if (existingItem) {
        return res.status(400).json({
          success: false,
          message: 'صنف بهذا الاسم موجود بالفعل'
        });
      }
    }

    // Update fields
    if (name !== undefined) item.name = name.trim();
    if (quantity !== undefined) item.quantity = Number(quantity);
    if (min !== undefined) item.min = Number(min);
    if (unit !== undefined) item.unit = unit;
    if (price !== undefined) item.price = Number(price);
    if (category !== undefined) item.category = category;
    if (supplier !== undefined) item.supplier = supplier;
    if (notes !== undefined) item.notes = notes;
    
    item.updatedBy = req.user._id;

    await item.save();

    const updatedItem = await Inventory.findById(item._id)
      .populate('createdBy', 'name username')
      .populate('updatedBy', 'name username');

    // Send Socket notifications for updated inventory item
    if (global.socketHandlers) {
      try {
        const userDisplayName = req.user.name || req.user.username || 'مستخدم';

        // Notify managers about updated inventory item
        global.socketHandlers.sendRoleNotification('manager', 
          `تم تحديث صنف: ${updatedItem.name}`, {
          type: 'inventory-updated',
          itemId: updatedItem._id,
          itemName: updatedItem.name,
          quantity: updatedItem.quantity,
          category: updatedItem.category,
          updatedBy: userDisplayName,
          timestamp: new Date().toISOString()
        });

        console.log(`🔄 تم إرسال إشعار تحديث صنف: ${updatedItem.name}`);
      } catch (socketError) {
        console.error('خطأ في إرسال إشعار Socket:', socketError);
      }
    }

    res.json({
      success: true,
      message: 'تم تحديث الصنف بنجاح',
      data: updatedItem
    });
  } catch (error) {
    console.error('Update inventory item error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في تحديث الصنف'
    });
  }
});

// Update stock quantity
router.put('/:id/stock', authenticateToken, async (req, res) => {
  try {
    const { quantity, operation = 'set' } = req.body;

    if (quantity === undefined) {
      return res.status(400).json({
        success: false,
        message: 'الكمية مطلوبة'
      });
    }

    const item = await Inventory.findById(req.params.id);

    if (!item) {
      return res.status(404).json({
        success: false,
        message: 'الصنف غير موجود'
      });
    }

    await item.updateStock(Number(quantity), operation, req.user._id);

    const updatedItem = await Inventory.findById(item._id)
      .populate('createdBy', 'name username')
      .populate('updatedBy', 'name username');

    // Send Socket notifications for stock update
    if (global.socketHandlers) {
      try {
        const userDisplayName = req.user.name || req.user.username || 'مستخدم';

        // Notify managers about stock update
        global.socketHandlers.sendRoleNotification('manager', 
          `تم تحديث مخزون صنف: ${updatedItem.name}`, {
          type: 'stock-updated',
          itemId: updatedItem._id,
          itemName: updatedItem.name,
          quantity: updatedItem.quantity,
          operation,
          updatedBy: userDisplayName,
          timestamp: new Date().toISOString()
        });

        console.log(`📦 تم إرسال إشعار تحديث مخزون صنف: ${updatedItem.name}`);
      } catch (socketError) {
        console.error('خطأ في إرسال إشعار Socket:', socketError);
      }
    }

    res.json({
      success: true,
      message: 'تم تحديث المخزون بنجاح',
      data: updatedItem
    });
  } catch (error) {
    console.error('Update stock error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في تحديث المخزون'
    });
  }
});

// Delete inventory item (soft delete)
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const item = await Inventory.findById(req.params.id);

    if (!item) {
      return res.status(404).json({
        success: false,
        message: 'الصنف غير موجود'
      });
    }

    // Soft delete
    item.isActive = false;
    item.updatedBy = req.user._id;
    await item.save();

    // Send Socket notifications for deleted inventory item
    if (global.socketHandlers) {
      try {
        // Notify managers about deleted inventory item
        global.socketHandlers.sendRoleNotification('manager', 
          `تم حذف صنف: ${item.name}`, {
          type: 'inventory-deleted',
          itemId: item._id,
          itemName: item.name,
          timestamp: new Date().toISOString()
        });

        console.log(`🗑️ تم إرسال إشعار حذف صنف: ${item.name}`);
      } catch (socketError) {
        console.error('خطأ في إرسال إشعار Socket:', socketError);
      }
    }

    res.json({
      success: true,
      message: 'تم حذف الصنف بنجاح'
    });
  } catch (error) {
    console.error('Delete inventory item error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في حذف الصنف'
    });
  }
});

// Get inventory statistics
router.get('/stats/summary', authenticateToken, async (req, res) => {
  try {
    const [
      totalItems,
      lowStockItems,
      outOfStockItems,
      totalValue
    ] = await Promise.all([
      Inventory.countDocuments({ isActive: true }),
      Inventory.countDocuments({ 
        isActive: true,
        $expr: { $lte: ['$quantity', '$min'] }
      }),
      Inventory.countDocuments({ 
        isActive: true,
        quantity: 0 
      }),
      Inventory.aggregate([
        { $match: { isActive: true } },
        { $group: { _id: null, total: { $sum: { $multiply: ['$quantity', '$price'] } } } }
      ])
    ]);

    res.json({
      success: true,
      data: {
        totalItems,
        lowStockItems,
        outOfStockItems,
        totalValue: totalValue[0]?.total || 0
      }
    });
  } catch (error) {
    console.error('Get inventory stats error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب إحصائيات المخزون'
    });
  }
});

module.exports = router;
