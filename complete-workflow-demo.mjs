// Complete Coffee Shop Workflow Demonstration
// This script demonstrates the full workflow from waiter to chef to manager

import { io } from 'socket.io-client';

const API_BASE = 'http://localhost:4003';

// Real user credentials
const USERS = {
  waiter: { username: 'azza', password: '253040' },
  chef: { username: 'khale<PERSON>', password: '253040' },
  manager: { username: '<PERSON><PERSON>', password: 'MOHAMEDmostafa123' }
};

// Real product IDs from the system
const PRODUCTS = {
  mocha: '683c105102b04d587b825b60',    // موكا - 50 ريال
  latte: '683c103602b04d587b825b58',    // لاتيه - 50 ريال
  cappuccino: '683c102702b04d587b825b50' // كابتشينو - 40 ريال
};

class WorkflowDemo {
  constructor() {
    this.tokens = {};
    this.socketConnections = {};
    this.notifications = [];
  }

  async authenticate(userType) {
    const credentials = USERS[userType];
    const response = await fetch(`${API_BASE}/api/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(credentials)
    });

    const data = await response.json();
    if (!data.success) throw new Error(`Authentication failed for ${userType}`);
    
    this.tokens[userType] = data.token;
    console.log(`🔐 ${userType} authenticated: ${data.user.name} (${data.user.role})`);
    return data;
  }

  setupSocketConnection(userType) {
    const socket = io(API_BASE, {
      auth: { token: this.tokens[userType] }
    });

    socket.on('connect', () => {
      console.log(`🔌 ${userType} connected to Socket.IO`);
    });

    socket.on('orderCreated', (data) => {
      console.log(`🔔 ${userType} received: Order Created - ${data.orderNumber}`);
      this.notifications.push({ user: userType, event: 'orderCreated', data });
    });

    socket.on('orderStatusUpdated', (data) => {
      console.log(`🔔 ${userType} received: Order Status Updated - ${data.orderNumber} → ${data.newStatus}`);
      this.notifications.push({ user: userType, event: 'orderStatusUpdated', data });
    });

    socket.on('tableStatusUpdated', (data) => {
      console.log(`🔔 ${userType} received: Table Status Updated - Table ${data.tableNumber}`);
      this.notifications.push({ user: userType, event: 'tableStatusUpdated', data });
    });

    this.socketConnections[userType] = socket;
    return socket;
  }

  async makeRequest(endpoint, options = {}, userType = 'waiter') {
    const response = await fetch(`${API_BASE}${endpoint}`, {
      ...options,
      headers: {
        'Authorization': `Bearer ${this.tokens[userType]}`,
        'Content-Type': 'application/json',
        ...options.headers
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${await response.text()}`);
    }

    return response.json();
  }

  async demonstrateCompleteWorkflow() {
    console.log('🚀 Starting Complete Coffee Shop Workflow Demonstration\n');

    // Step 1: Authenticate all users
    console.log('STEP 1: Authentication');
    console.log('======================');
    await this.authenticate('waiter');
    await this.authenticate('chef');
    await this.authenticate('manager');
    
    // Step 2: Setup Socket.IO connections
    console.log('\nSTEP 2: Setting up real-time notifications');
    console.log('==========================================');
    this.setupSocketConnection('waiter');
    this.setupSocketConnection('chef');
    this.setupSocketConnection('manager');
    
    // Wait for connections to establish
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Step 3: Waiter opens a table
    console.log('\nSTEP 3: Waiter opens Table 12');
    console.log('==============================');
    const tableData = {
      tableNumber: 12,
      action: 'open',
      waiterName: 'عزة'
    };
    
    const tableResult = await this.makeRequest('/api/table-accounts', {
      method: 'POST',
      body: JSON.stringify(tableData)
    }, 'waiter');
    
    console.log(`✅ Table 12 opened successfully`);

    // Step 4: Waiter creates an order
    console.log('\nSTEP 4: Waiter creates order for Table 12');
    console.log('==========================================');
    const orderData = {
      tableNumber: 12,
      customerName: 'عميل مميز',
      items: [
        {
          product: PRODUCTS.mocha,
          name: 'موكا',
          quantity: 2,
          price: 50,
          notes: 'ساخن جداً'
        },
        {
          product: PRODUCTS.cappuccino,
          name: 'كابتشينو',
          quantity: 1,
          price: 40,
          notes: 'بدون سكر'
        }
      ],
      notes: 'طلب عاجل - عميل مهم'
    };

    const orderResult = await this.makeRequest('/api/orders', {
      method: 'POST',
      body: JSON.stringify(orderData)
    }, 'waiter');

    const orderId = orderResult.order._id;
    const orderNumber = orderResult.order.orderNumber;
    console.log(`✅ Order created: ${orderNumber} (Total: ${orderResult.order.totalPrice} ريال)`);

    // Wait for Socket.IO notifications
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Step 5: Chef receives and processes the order
    console.log('\nSTEP 5: Chef processes the order');
    console.log('=================================');
    
    // Chef marks order as preparing
    console.log('👨‍🍳 Chef starts preparing the order...');
    await this.makeRequest(`/api/orders/${orderId}`, {
      method: 'PUT',
      body: JSON.stringify({ status: 'preparing' })
    }, 'chef');
    
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Chef marks order as ready
    console.log('👨‍🍳 Chef marks order as ready...');
    await this.makeRequest(`/api/orders/${orderId}`, {
      method: 'PUT',
      body: JSON.stringify({ status: 'ready' })
    }, 'chef');

    await new Promise(resolve => setTimeout(resolve, 1000));

    // Step 6: Waiter serves the order
    console.log('\nSTEP 6: Waiter serves the order');
    console.log('================================');
    await this.makeRequest(`/api/orders/${orderId}`, {
      method: 'PUT',
      body: JSON.stringify({ status: 'served' })
    }, 'waiter');
    
    console.log('✅ Order served to customer');

    await new Promise(resolve => setTimeout(resolve, 1000));

    // Step 7: Manager views reports
    console.log('\nSTEP 7: Manager checks reports');
    console.log('==============================');
    const orders = await this.makeRequest('/api/orders', {}, 'manager');
    const recentOrders = orders.filter(order => 
      new Date(order.createdAt) > new Date(Date.now() - 24 * 60 * 60 * 1000)
    );
    
    console.log(`📊 Manager sees ${orders.length} total orders, ${recentOrders.length} today`);

    // Step 8: Final status check
    console.log('\nSTEP 8: Final workflow verification');
    console.log('====================================');
    const finalOrder = await this.makeRequest(`/api/orders/${orderId}`, {}, 'manager');
    console.log(`📋 Final order status: ${finalOrder.status}`);
    console.log(`💰 Order total: ${finalOrder.totalPrice} ريال`);
    console.log(`🕐 Total time: ${new Date(finalOrder.updatedAt) - new Date(finalOrder.createdAt)} ms`);

    // Summary
    console.log('\n📊 WORKFLOW SUMMARY');
    console.log('===================');
    console.log('✅ 1. All users authenticated successfully');
    console.log('✅ 2. Real-time notifications working');
    console.log('✅ 3. Table management functional');
    console.log('✅ 4. Order creation with validation');
    console.log('✅ 5. Chef workflow (pending → preparing → ready)');
    console.log('✅ 6. Waiter order serving');
    console.log('✅ 7. Manager reporting access');
    console.log('✅ 8. Complete order lifecycle tracking');

    console.log(`\n🔔 Total notifications received: ${this.notifications.length}`);
    
    // Cleanup
    Object.values(this.socketConnections).forEach(socket => socket.disconnect());
    console.log('\n🏁 Workflow demonstration completed successfully!');
  }
}

// Run the demonstration
const demo = new WorkflowDemo();
demo.demonstrateCompleteWorkflow().catch(console.error);
