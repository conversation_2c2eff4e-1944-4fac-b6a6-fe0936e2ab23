import React, { useState } from 'react';
import OrderDetails from './OrderDetails';
import type { Order } from '../types/Order';
import { getOrderTotal, formatOrderPrice } from '../types/Order';

// تم نقل interface Order إلى src/types/Order.ts

export interface EnhancedOrdersTableProps {
  orders: Order[];
  userRole: 'waiter' | 'chef' | 'manager';
  onStatusChange?: (orderId: string, newStatus: Order['status']) => void;
  onCancel?: (orderId: string) => void;
  loading?: boolean;
  selectedOrders?: string[];
  onOrderSelection?: (orderId: string) => void;
  onSelectAll?: () => void;
  onDeselectAll?: () => void;
  showWaiterColumn?: boolean;
}

const EnhancedOrdersTable: React.FC<EnhancedOrdersTableProps> = ({
  orders,
  userRole,
  // onStatusChange,
  // onCancel,
  loading = false,
  selectedOrders = [],
  onOrderSelection,
  onSelectAll,
  onDeselectAll
}) => {
  const getStatusColor = (status: Order['status']) => {
    switch (status) {
      case 'pending': return '#f57c00';
      case 'preparing': return '#1976d2';
      case 'ready': return '#388e3c';
      case 'delivered': return '#4caf50';
      case 'cancelled': return '#d32f2f';
      default: return '#666';
    }
  };

  const getStatusText = (status: Order['status']) => {
    switch (status) {
      case 'pending': return 'في الانتظار';
      case 'preparing': return 'قيد التحضير';
      case 'ready': return 'جاهز';
      case 'delivered': return 'تم التسليم';
      case 'cancelled': return 'ملغي';
      default: return status;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (orders.length === 0) {
    return (
      <div style={{
        textAlign: 'center',
        padding: '3rem',
        color: '#666',
        background: '#f9f9f9',
        borderRadius: '8px',
        border: '2px dashed #ddd'
      }}>
        <h3>لا توجد طلبات</h3>
        <p>لم يتم العثور على أي طلبات حالياً</p>
      </div>
    );
  }

  return (
    <div style={{ direction: 'rtl' }}>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '1rem',
        padding: '1rem',
        background: '#f5f5f5',
        borderRadius: '8px'
      }}>
        <h3 style={{ margin: 0, color: '#6d4c41' }}>
          جدول الطلبات ({orders.length} طلب)
        </h3>
        {userRole === 'manager' && onSelectAll && (
          <div>
            <button
              onClick={onSelectAll}
              style={{
                marginLeft: '0.5rem',
                padding: '0.5rem 1rem',
                background: '#6d4c41',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              تحديد الكل
            </button>
            <button
              onClick={onDeselectAll}
              style={{
                padding: '0.5rem 1rem',
                background: '#999',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              إلغاء التحديد
            </button>
          </div>
        )}
      </div>

      <div style={{
        overflowX: 'auto',
        background: 'white',
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}>
        <table style={{
          width: '100%',
          borderCollapse: 'collapse',
          minWidth: '800px'
        }}>
          <thead>
            <tr style={{ background: '#6d4c41', color: 'white' }}>
              {userRole === 'manager' && onOrderSelection && (
                <th style={{ padding: '1rem', textAlign: 'center' }}>تحديد</th>
              )}
              <th style={{ padding: '1rem', textAlign: 'center' }}>رقم الطلب</th>
              <th style={{ padding: '1rem', textAlign: 'center' }}>النادل</th>
              <th style={{ padding: '1rem', textAlign: 'center' }}>الطباخ</th>
              <th style={{ padding: '1rem', textAlign: 'center' }}>العميل</th>
              <th style={{ padding: '1rem', textAlign: 'center' }}>الإجمالي</th>
              <th style={{ padding: '1rem', textAlign: 'center' }}>الأصناف</th>
              <th style={{ padding: '1rem', textAlign: 'center' }}>الحالة</th>
              <th style={{ padding: '1rem', textAlign: 'center' }}>وقت الطلب</th>
              <th style={{ padding: '1rem', textAlign: 'center' }}>مدة التحضير</th>
            </tr>
          </thead>
          <tbody>
            {orders.map((order) => (
              <tr
                key={order._id}
                style={{
                  borderBottom: '1px solid #eee',
                  background: order._id && selectedOrders.includes(order._id) ? '#e8f5e8' : 'white'
                }}
              >
                {userRole === 'manager' && onOrderSelection && (
                  <td style={{ padding: '1rem', textAlign: 'center' }}>
                    <input
                      type="checkbox"
                      checked={!!(order._id && selectedOrders.includes(order._id))} // Ensured boolean value
                      onChange={() => order._id && onOrderSelection(order._id)}
                      style={{ transform: 'scale(1.2)' }}
                    />
                  </td>
                )}
                <td style={{ padding: '1rem', textAlign: 'center', fontWeight: 'bold' }}>
                  {order.orderNumber}
                </td>
                <td style={{ padding: '1rem', textAlign: 'center' }}>
                  {'waiterName' in order ? order.waiterName : '-'}
                </td>
                <td style={{ padding: '1rem', textAlign: 'center' }}>
                  {'chefName' in order ? order.chefName : '-'}
                </td>
                <td style={{ padding: '1rem', textAlign: 'center' }}>
                  {'customerName' in order ? order.customerName : '-'}
                </td>
                <td style={{ padding: '1rem', textAlign: 'center', fontWeight: 'bold' }}>
                  {formatOrderPrice(order, 'ر.س')}
                </td>
                <td style={{ padding: '1rem', textAlign: 'center' }}>
                  <div>
                    {order.items.map((item, index) => (
                      <div key={index} style={{ marginBottom: '0.25rem' }}>
                        {item.name} × {item.quantity}
                      </div>
                    ))}
                  </div>
                </td>
                <td style={{ padding: '1rem', textAlign: 'center' }}>
                  <span style={{
                    padding: '0.5rem 1rem',
                    borderRadius: '20px',
                    color: 'white',
                    background: getStatusColor(order.status),
                    fontSize: '0.9rem',
                    fontWeight: 'bold'
                  }}>
                    {getStatusText(order.status)}
                  </span>
                </td>
                <td style={{ padding: '1rem', textAlign: 'center', fontSize: '0.9rem' }}>
                  {order.createdAt ? formatDate(order.createdAt) : '-'}
                </td>
                <td style={{ padding: '1rem', textAlign: 'center' }}>
                  {'preparationTime' in order && order.preparationTime ? `${order.preparationTime} دقيقة` : '-'}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {loading && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            padding: '2rem',
            borderRadius: '8px',
            textAlign: 'center'
          }}>
            <p>جاري التحديث...</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default EnhancedOrdersTable;