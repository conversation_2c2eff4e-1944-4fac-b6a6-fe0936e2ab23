// Debug Order Creation Issue
import https from 'https';

const PRODUCTION_BACKEND = 'https://deshacoffee-production.up.railway.app';

async function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const requestOptions = {
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'DebugTester/1.0',
        ...options.headers
      }
    };

    const req = https.request(url, requestOptions, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const result = {
            status: res.statusCode,
            headers: res.headers,
            data: data ? JSON.parse(data) : null
          };
          resolve(result);
        } catch (e) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: data
          });
        }
      });
    });

    req.on('error', reject);
    
    if (options.body) {
      req.write(JSON.stringify(options.body));
    }
    
    req.end();
  });
}

async function debugOrderCreation() {
  console.log('🔍 Debugging Order Creation...\n');

  try {
    // Step 1: Login as waiter
    console.log('1️⃣ Logging in as waiter...');
    const loginResponse = await makeRequest(`${PRODUCTION_BACKEND}/api/auth/login`, {
      method: 'POST',
      body: { username: 'azza', password: '253040' }
    });

    if (loginResponse.status !== 200) {
      throw new Error(`Login failed: ${JSON.stringify(loginResponse.data)}`);
    }

    const token = loginResponse.data.token;
    console.log('✅ Login successful');

    // Step 2: Get products
    console.log('\n2️⃣ Getting products...');
    const productsResponse = await makeRequest(`${PRODUCTION_BACKEND}/api/products`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });

    if (productsResponse.status !== 200) {
      throw new Error(`Products fetch failed: ${JSON.stringify(productsResponse.data)}`);
    }

    const products = productsResponse.data;
    const product = products[0];
    console.log(`✅ Found ${products.length} products. Using: ${product.name}`);

    // Step 3: Create order with detailed logging
    console.log('\n3️⃣ Creating order...');
    const orderData = {
      tableNumber: '15', // String format
      customerName: 'عميل اختبار التفصيلي',
      items: [{
        product: product._id,
        name: product.name,
        quantity: 1,
        price: product.price,
        notes: 'اختبار تفصيلي'
      }],
      notes: 'طلب اختبار تفصيلي'
    };

    console.log('📋 Order Data:', JSON.stringify(orderData, null, 2));

    const orderResponse = await makeRequest(`${PRODUCTION_BACKEND}/api/orders`, {
      method: 'POST',
      headers: { 'Authorization': `Bearer ${token}` },
      body: orderData
    });

    console.log('\n📊 Order Response:');
    console.log('Status:', orderResponse.status);
    console.log('Data:', JSON.stringify(orderResponse.data, null, 2));

    if (orderResponse.status === 201 || orderResponse.status === 200) {
      console.log('\n✅ Order created successfully!');
      if (orderResponse.data && orderResponse.data.order) {
        console.log('📝 Order ID:', orderResponse.data.order._id);
        console.log('🏠 Table Number:', orderResponse.data.order.tableNumber);
        console.log('👤 Customer:', orderResponse.data.order.customerName);
      }
    } else {
      console.log('\n❌ Order creation failed');
    }

  } catch (error) {
    console.error('\n🚨 Error:', error.message);
    console.error('Stack:', error.stack);
  }
}

debugOrderCreation();
