# Notification System Test Plan

## Overview
Testing the updated notification system to ensure all notifications display "table number and customer name" instead of "order number" format.

## Test Scenarios

### 1. Order Creation Notifications
- [ ] Customer places order from table
- [ ] Waiter receives notification with table number and customer name
- [ ] Chef receives notification with table number and customer name
- [ ] Manager sees order in system with correct format

### 2. Order Status Update Notifications
- [ ] Chef accepts order → Waiter gets "بدأ تحضير الطلب من الطاولة رقم [X] للعميل [Customer]"
- [ ] Chef marks order ready → Waiter gets "الطلب جاهز من الطاولة رقم [X] للعميل [Customer]"
- [ ] Waiter delivers order → Chef gets confirmation with table and customer info

### 3. Real-time Socket Notifications
- [ ] Socket events include table number and customer name
- [ ] All connected clients receive updated format
- [ ] No old "order number" format appears anywhere

### 4. Error Handling
- [ ] Missing table number shows "غير محدد"
- [ ] Missing customer name shows "غير محدد"
- [ ] System remains stable with incomplete data

## Expected Results
All notifications should follow the format:
`[Action] من الطاولة رقم [TableNumber] للعميل [CustomerName]`

## Test Status
- [ ] Frontend server running on http://localhost:5173
- [ ] Backend server running on http://localhost:3000
- [ ] Socket.IO connections working
- [ ] Database accessible
- [ ] All test scenarios completed

## Next Steps
1. Perform manual testing through the web interface
2. Create test orders and verify notification formats
3. Test all user roles (Waiter, Chef, Manager)
4. Verify socket real-time updates
5. Document any issues found
