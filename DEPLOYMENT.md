# 🚀 دليل النشر - نظام إدارة مقهى ديشة

هذا الدليل يوضح كيفية نشر نظام إدارة مقهى ديشة على منصات الاستضافة المختلفة.

## 🏗️ البنية التحتية الحالية

### Frontend - Vercel
- **المنصة**: Vercel
- **الرابط**: https://desha-coffee.vercel.app
- **التقنية**: React + Vite
- **النشر**: تلقائي من GitHub

### Backend - Railway
- **المنصة**: Railway
- **الرابط**: https://deshacoffee-production.up.railway.app
- **التقنية**: Node.js + Express
- **النشر**: تلقائي من GitHub

### Database - MongoDB Atlas
- **المنصة**: MongoDB Atlas
- **النوع**: قاعدة بيانات مدارة
- **المجموعة**: MyCoffeChop

## 📋 خطوات النشر

### 1. إعداد قاعدة البيانات (MongoDB Atlas)

```bash
# رابط الاتصال
mongodb+srv://besomustafa:<EMAIL>/deshacoffee?retryWrites=true&w=majority&appName=MyCoffeChop
```

#### المجموعات المطلوبة:
- `users` - المستخدمون
- `products` - المنتجات
- `categories` - الفئات
- `orders` - الطلبات
- `tables` - الطاولات
- `inventory` - المخزون

### 2. نشر Backend على Railway

#### متطلبات البيئة:
```env
NODE_ENV=production
PORT=4003
MONGODB_URI=mongodb+srv://besomustafa:<EMAIL>/deshacoffee?retryWrites=true&w=majority&appName=MyCoffeChop
JWT_SECRET=desha-coffee-super-secret-jwt-key-production-2024
FRONTEND_URL=https://desha-coffee.vercel.app
CORS_ORIGIN=https://desha-coffee.vercel.app
BCRYPT_SALT_ROUNDS=12
```

#### خطوات النشر:
1. ربط مستودع GitHub بـ Railway
2. اختيار مجلد `backend` كمجلد الجذر
3. تعيين متغيرات البيئة
4. تفعيل النشر التلقائي

### 3. نشر Frontend على Vercel

#### متطلبات البيئة:
```env
NODE_ENV=production
VITE_API_URL=https://deshacoffee-production.up.railway.app
VITE_SOCKET_URL=https://deshacoffee-production.up.railway.app
VITE_API_TIMEOUT=30000
VITE_SOCKET_TIMEOUT=20000
VITE_APP_NAME=نظام إدارة المقهى
VITE_APP_VERSION=1.0.2
```

#### خطوات النشر:
1. ربط مستودع GitHub بـ Vercel
2. اختيار إعدادات Vite للبناء
3. تعيين متغيرات البيئة
4. تفعيل النشر التلقائي

## 🔧 ملفات التكوين

### vercel.json
```json
{
  "version": 2,
  "builds": [
    {
      "src": "package.json",
      "use": "@vercel/static-build"
    }
  ],
  "env": {
    "NODE_ENV": "production",
    "VITE_API_URL": "https://deshacoffee-production.up.railway.app",
    "VITE_SOCKET_URL": "https://deshacoffee-production.up.railway.app"
  },
  "rewrites": [
    {
      "source": "/((?!api/.*).*)",
      "destination": "/index.html"
    }
  ]
}
```

### railway.json (Backend)
```json
{
  "build": {
    "builder": "NIXPACKS"
  },
  "deploy": {
    "startCommand": "npm start",
    "healthcheckPath": "/api/health"
  }
}
```

## 📊 مراقبة النشر

### حالة الخدمات
- **Frontend**: ✅ متاح على Vercel
- **Backend**: ✅ متاح على Railway  
- **Database**: ✅ متاح على MongoDB Atlas

### نقاط التحقق الصحي
- Backend Health: `https://deshacoffee-production.up.railway.app/api/health`
- Database Connection: مراقبة من Railway Dashboard

## 🚨 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ CORS
```javascript
// في backend/server.js
app.use(cors({
  origin: process.env.FRONTEND_URL || 'https://desha-coffee.vercel.app',
  credentials: true
}));
```

#### 2. خطأ الاتصال بقاعدة البيانات
- تحقق من صحة `MONGODB_URI`
- تأكد من وجود قاعدة البيانات

#### 3. خطأ متغيرات البيئة
- تحقق من تعيين جميع المتغيرات المطلوبة
- أعد تشغيل الخدمة بعد التحديث

## 🔄 النشر التلقائي

### GitHub Actions (اختياري)
```yaml
name: Deploy to Production

on:
  push:
    branches: [ main ]

jobs:
  deploy-frontend:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v20
        
  deploy-backend:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Deploy to Railway
        run: railway deploy
```

## 📈 إحصائيات الأداء

### Frontend (Vercel)
- **وقت البناء**: ~3-5 دقائق
- **وقت التحميل**: <2 ثانية
- **CDN**: عالمي

### Backend (Railway)
- **وقت البناء**: ~2-3 دقائق
- **وقت الاستجابة**: <500ms
- **الذاكرة**: 512MB

### Database (MongoDB Atlas)
- **المساحة**: 512MB (مجاني)
- **الاتصالات**: 500 اتصال متزامن
- **النسخ الاحتياطي**: تلقائي

## 🔐 الأمان

### إعدادات الحماية
- HTTPS فقط
- CORS محدود
- JWT آمن
- متغيرات البيئة مشفرة

### النسخ الاحتياطية
- قاعدة البيانات: نسخ احتياطية يومية تلقائية
- الكود: محفوظ في GitHub
- الإعدادات: موثقة في هذا الدليل

## 📞 الدعم

للحصول على المساعدة في النشر:
- GitHub Issues
- البريد الإلكتروني: <EMAIL>

---

**آخر تحديث**: يونيو 2025
