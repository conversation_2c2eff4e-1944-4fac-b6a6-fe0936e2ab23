/* Categories Manager Styles */
.categories-manager {
  padding: 1.5rem;
  max-width: 1400px;
  margin: 0 auto;
  background: var(--light-bg, #f8f9fa);
  min-height: 100vh;
}

/* Loading State */
.categories-manager.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  color: var(--primary-color, #2c3e50);
}

.loading-spinner i {
  font-size: 2rem;
  color: var(--primary-color, #2c3e50);
}

/* Header */
.categories-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  gap: 2rem;
}

.header-left h1 {
  margin: 0 0 0.5rem 0;
  color: var(--primary-color, #2c3e50);
  font-size: 2rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.header-left p {
  color: var(--text-muted, #95a5a6);
  font-size: 1.1rem;
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.btn-new-category {
  background: var(--success-color, #27ae60);
  color: var(--white, #ffffff);
  border: none;
  border-radius: 0.5rem;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-new-category:hover {
  background: #229954;
  transform: translateY(-1px);
}

/* Stats Cards */
.categories-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: var(--white, #ffffff);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: var(--white, #ffffff);
}

.stat-card.total .stat-icon {
  background: var(--primary-color, #2c3e50);
}

.stat-card.active .stat-icon {
  background: var(--success-color, #27ae60);
}

.stat-card.inactive .stat-icon {
  background: var(--error-color, #e74c3c);
}

.stat-card.items .stat-icon {
  background: var(--secondary-color, #f39c12);
}

.stat-content h3 {
  margin: 0 0 0.25rem 0;
  font-size: 1.75rem;
  font-weight: bold;
  color: var(--primary-color, #2c3e50);
}

.stat-content p {
  margin: 0;
  color: var(--text-muted, #95a5a6);
  font-size: 0.875rem;
}

/* Search */
.categories-search {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
  padding: 1rem;
  background: var(--white, #ffffff);
  border-radius: 0.75rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.search-group {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.search-group i {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted, #95a5a6);
}

.search-group input {
  width: 100%;
  padding: 0.75rem 2.5rem 0.75rem 1rem;
  border: 1px solid var(--border-color, #ecf0f1);
  border-radius: 0.5rem;
  font-size: 0.9rem;
  background: var(--white, #ffffff);
  color: var(--text-color, #2c3e50);
}

.search-group input:focus {
  outline: none;
  border-color: var(--primary-color, #2c3e50);
}

.refresh-btn {
  background: var(--info-color, #3498db);
  color: var(--white, #ffffff);
  border: none;
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.refresh-btn:hover {
  background: #2980b9;
}

/* Categories List */
.categories-list {
  margin-bottom: 2rem;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.category-card {
  background: var(--white, #ffffff);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  position: relative;
  border-left: 4px solid var(--success-color, #27ae60);
}

.category-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.category-card.inactive {
  border-left-color: var(--error-color, #e74c3c);
  opacity: 0.7;
}

.category-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.category-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: var(--white, #ffffff);
}

.category-info {
  flex: 1;
}

.category-info h3 {
  margin: 0 0 0.25rem 0;
  color: var(--primary-color, #2c3e50);
  font-size: 1.25rem;
}

.category-info p {
  margin: 0;
  color: var(--text-muted, #95a5a6);
  font-size: 0.9rem;
  line-height: 1.4;
}

.category-status {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  font-size: 1rem;
}

.category-status.active {
  color: var(--success-color, #27ae60);
}

.category-status.inactive {
  color: var(--error-color, #e74c3c);
}

.category-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: var(--light-bg, #f8f9fa);
  border-radius: 0.5rem;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: var(--text-color, #2c3e50);
}

.detail-item i {
  color: var(--secondary-color, #f39c12);
}

.category-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.order-controls {
  display: flex;
  gap: 0.25rem;
}

.order-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 0.375rem;
  background: var(--border-color, #ecf0f1);
  color: var(--text-color, #2c3e50);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
}

.order-btn:hover:not(:disabled) {
  background: var(--info-color, #3498db);
  color: var(--white, #ffffff);
}

.order-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  color: var(--white, #ffffff);
}

.action-btn.edit {
  background: var(--info-color, #3498db);
}

.action-btn.edit:hover {
  background: #2980b9;
}

.action-btn.toggle.enable {
  background: var(--success-color, #27ae60);
}

.action-btn.toggle.enable:hover {
  background: #229954;
}

.action-btn.toggle.disable {
  background: var(--warning-color, #f39c12);
}

.action-btn.toggle.disable:hover {
  background: #e67e22;
}

.action-btn.delete {
  background: var(--error-color, #e74c3c);
}

.action-btn.delete:hover {
  background: #c0392b;
}

/* No Categories */
.no-categories {
  text-align: center;
  padding: 4rem 2rem;
  color: var(--text-muted, #95a5a6);
}

.no-categories i {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.no-categories p {
  font-size: 1.1rem;
  margin: 0;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-content {
  background: var(--white, #ffffff);
  border-radius: 1rem;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color, #ecf0f1);
}

.modal-header h2 {
  margin: 0;
  color: var(--primary-color, #2c3e50);
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: var(--text-muted, #95a5a6);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: var(--light-bg, #f8f9fa);
  color: var(--text-color, #2c3e50);
}

.modal-body {
  padding: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-color, #2c3e50);
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color, #ecf0f1);
  border-radius: 0.5rem;
  font-family: inherit;
  font-size: 0.9rem;
  transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color, #2c3e50);
}

.color-picker {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.color-option {
  cursor: pointer;
  position: relative;
}

.color-option input[type="radio"] {
  display: none;
}

.color-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 3px solid transparent;
  transition: all 0.2s ease;
  display: block;
}

.color-option input[type="radio"]:checked + .color-circle {
  border-color: var(--primary-color, #2c3e50);
  transform: scale(1.1);
}

.icon-picker {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.icon-option {
  cursor: pointer;
  position: relative;
}

.icon-option input[type="radio"] {
  display: none;
}

.icon-preview {
  width: 40px;
  height: 40px;
  border: 2px solid var(--border-color, #ecf0f1);
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: var(--text-color, #2c3e50);
  transition: all 0.2s ease;
}

.icon-option input[type="radio"]:checked + .icon-preview {
  border-color: var(--primary-color, #2c3e50);
  background: var(--primary-color, #2c3e50);
  color: var(--white, #ffffff);
}

.checkbox-group {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-weight: 500;
  color: var(--text-color, #2c3e50);
}

.checkbox-label input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-color, #ecf0f1);
  border-radius: 4px;
  position: relative;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
  background: var(--success-color, #27ae60);
  border-color: var(--success-color, #27ae60);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--white, #ffffff);
  font-size: 12px;
  font-weight: bold;
}

.modal-footer {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid var(--border-color, #ecf0f1);
}

.btn-confirm {
  flex: 1;
  background: var(--success-color, #27ae60);
  color: var(--white, #ffffff);
  border: none;
  border-radius: 0.5rem;
  padding: 0.75rem 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-confirm:hover {
  background: #229954;
}

.btn-cancel {
  flex: 1;
  background: var(--border-color, #ecf0f1);
  color: var(--text-color, #2c3e50);
  border: none;
  border-radius: 0.5rem;
  padding: 0.75rem 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-cancel:hover {
  background: #bdc3c7;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .categories-manager {
    padding: 1rem;
  }

  .categories-header {
    flex-direction: column;
    gap: 1rem;
  }

  .header-actions {
    width: 100%;
    justify-content: center;
  }

  .categories-search {
    flex-direction: column;
    gap: 1rem;
  }

  .search-group {
    max-width: none;
    width: 100%;
  }

  .refresh-btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .categories-manager {
    padding: 0.75rem;
  }

  .header-left h1 {
    font-size: 1.5rem;
  }

  .categories-stats {
    grid-template-columns: 1fr;
  }

  .stat-card {
    padding: 1rem;
  }

  .stat-icon {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }

  .stat-content h3 {
    font-size: 1.5rem;
  }

  .categories-grid {
    grid-template-columns: 1fr;
  }

  .category-card {
    padding: 1rem;
  }

  .category-header {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }

  .category-details {
    flex-direction: column;
    gap: 0.5rem;
  }

  .category-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .color-picker {
    grid-template-columns: repeat(4, 1fr);
  }

  .icon-picker {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 480px) {
  .categories-manager {
    padding: 0.5rem;
  }

  .header-left h1 {
    font-size: 1.25rem;
  }

  .header-left p {
    font-size: 0.9rem;
  }

  .stat-card {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }

  .category-card {
    padding: 0.75rem;
  }

  .modal-content {
    margin: 0.5rem;
    max-height: 95vh;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 1rem;
  }

  .modal-footer {
    flex-direction: column;
  }

  .color-picker {
    grid-template-columns: repeat(3, 1fr);
  }

  .icon-picker {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .categories-manager {
    background: #1a1a1a;
  }

  .stat-card,
  .categories-search,
  .category-card,
  .modal-content {
    background: #2d2d2d;
    color: #ffffff;
  }

  .category-details {
    background: #3d3d3d;
  }

  .search-group input,
  .form-group input,
  .form-group textarea {
    background: #3d3d3d;
    color: #ffffff;
    border-color: #555555;
  }

  .order-btn {
    background: #555555;
    color: #ffffff;
  }

  .icon-preview {
    border-color: #555555;
    color: #ffffff;
  }

  .checkmark {
    border-color: #555555;
  }
}
