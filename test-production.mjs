#!/usr/bin/env node
/**
 * 🧪 اختبار سريع للتحقق من إعدادات الإنتاج
 */

import fetch from 'node-fetch';

const API_BASE = 'http://localhost:4003/api';

async function testProductionConfig() {
  console.log('🚀 اختبار إعدادات الإنتاج...\n');

  try {
    // 1. اختبار Health Check
    console.log('🔍 اختبار Health Check...');
    const healthResponse = await fetch('http://localhost:4003/health');
    const health = await healthResponse.json();
    
    console.log('✅ Health Check:', health.status);
    console.log('📊 Database:', health.database?.connected ? 'متصل' : 'غير متصل');
    console.log('👥 Users:', health.database?.userCount || 0);
    console.log('🌍 Environment:', health.environment);

    // 2. اختبار المصادقة مع بيانات الإنتاج
    console.log('\n🔐 اختبار المصادقة مع بيانات الإنتاج...');
    const loginResponse = await fetch(`${API_BASE}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username: 'admin',
        password: 'DeshaCoffee2024Admin!'
      })
    });

    if (loginResponse.ok) {
      const loginData = await loginResponse.json();
      console.log('✅ تسجيل الدخول نجح بكلمة مرور الإنتاج');
      console.log('👤 المستخدم:', loginData.user?.name);
      console.log('🔑 الدور:', loginData.user?.role);
    } else {
      console.log('❌ فشل في تسجيل الدخول');
    }

    // 3. اختبار Monitoring Endpoints
    console.log('\n📊 اختبار endpoints المراقبة...');
    
    const monitoringResponse = await fetch(`${API_BASE}/monitoring/status`);
    if (monitoringResponse.ok) {
      console.log('✅ Monitoring Status متاح');
    }

    console.log('\n🎉 اختبار الإعدادات مكتمل بنجاح!');
    console.log('✅ النظام جاهز للنشر في الإنتاج');

  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error.message);
  }
}

testProductionConfig();
