// نماذج موحدة للطلبات - متوافقة مع Backend
export interface OrderItem {
  id?: string;
  product?: string;          // ObjectId for backend
  productId?: string;        // Legacy support
  productName?: string;      // Backend expects this
  name: string;              // Frontend uses this
  quantity: number;
  price: number;
  subtotal?: number;         // Backend calculates this
  notes?: string;
}

export interface Customer {
  name: string;
  phone?: string;
  email?: string;
}

export interface Table {
  number: number;
  section?: string;
}

export interface Payment {
  method: 'cash' | 'card' | 'online' | 'wallet';
  status: 'pending' | 'paid' | 'failed' | 'refunded';
  transactionId?: string;
  paidAt?: string;
}

export interface OrderTotals {
  subtotal: number;
  tax: number;
  discount: number;
  total: number;
}

export interface Staff {
  waiter?: string;           // ObjectId
  chef?: string;            // ObjectId
  cashier?: string;         // ObjectId
}

export interface DiscountApplied {
  type: 'percentage' | 'fixed';
  value: number;
  amount: number;
  reason: string;
  approvedBy: string;
}

export interface OrderTiming {
  orderTime?: string;
  confirmedAt?: string;
  preparingAt?: string;
  readyAt?: string;
  servedAt?: string;
  completedAt?: string;
  estimatedPreparationTime?: number;
}

// البنية الكاملة للطلب - متوافقة مع Backend Model
export interface OrderBackend {
  _id?: string;
  orderNumber?: string;
  customer: Customer;
  items: OrderItem[];
  totals: OrderTotals;
  status: 'pending' | 'confirmed' | 'preparing' | 'ready' | 'served' | 'completed' | 'cancelled';
  orderType: 'dine-in' | 'takeaway' | 'delivery';
  table?: Table;
  delivery?: any;            // For future delivery support
  payment: Payment;
  timing?: OrderTiming;
  staff?: Staff;
  notes?: {
    customer?: string;
    kitchen?: string;
    waiter?: string;
  };
  rating?: {
    score: number;
    comment?: string;
    ratedAt?: string;
  };
  createdAt?: string;
  updatedAt?: string;
}

// البنية المبسطة للطلب - كما تستخدمها لوحة النادل حالياً
export interface OrderFrontend {
  _id?: string;
  orderNumber?: string;
  waiterName?: string;       // Legacy field
  chefName?: string;
  customerName?: string;     // Legacy field
  tableNumber?: string | number; // Legacy field
  status: 'pending' | 'preparing' | 'ready' | 'delivered' | 'cancelled';
  items: OrderItem[];
  
  // دعم للطريقتين القديمة والجديدة
  totalPrice?: number;       // Legacy field
  totals?: OrderTotals;      // New structure
  
  originalPrice?: number;
  discountApplied?: DiscountApplied;
  preparationTime?: number;
  createdAt?: string;
  updatedAt?: string;
  notes?: string;
  paid?: boolean;
  tableAccountId?: string;
}

// Type Union للدعم المختلط
export type Order = OrderFrontend | OrderBackend;

// دالة للتحويل من Frontend إلى Backend format
export const convertFrontendToBackend = (frontendOrder: Partial<OrderFrontend>): Partial<OrderBackend> => {
  const backendOrder: Partial<OrderBackend> = {
    orderType: 'dine-in',
    status: frontendOrder.status === 'delivered' ? 'completed' : 
            frontendOrder.status === 'cancelled' ? 'cancelled' :
            frontendOrder.status || 'pending',
    payment: {
      method: 'cash',
      status: 'pending'
    }
  };

  // تحويل بيانات العميل
  if (frontendOrder.customerName) {
    backendOrder.customer = {
      name: frontendOrder.customerName,
      phone: '',
      email: ''
    };
  }

  // تحويل بيانات الطاولة
  if (frontendOrder.tableNumber) {
    const tableNum = typeof frontendOrder.tableNumber === 'string' 
      ? parseInt(frontendOrder.tableNumber) 
      : frontendOrder.tableNumber;
    backendOrder.table = {
      number: tableNum,
      section: 'القسم الرئيسي'
    };
  }

  // تحويل العناصر
  if (frontendOrder.items) {
    backendOrder.items = frontendOrder.items.map(item => ({
      ...item,
      product: item.product || item.productId,
      productName: item.productName || item.name,
      subtotal: item.price * item.quantity
    }));
  }

  // تحويل الإجماليات
  if (frontendOrder.totalPrice) {
    const subtotal = frontendOrder.totalPrice;
    const tax = subtotal * 0.15;
    backendOrder.totals = {
      subtotal,
      tax,
      discount: 0,
      total: subtotal + tax
    };
  } else if (frontendOrder.totals) {
    backendOrder.totals = frontendOrder.totals;
  }

  return backendOrder;
};

// دالة للتحويل من Backend إلى Frontend format
export const convertBackendToFrontend = (backendOrder: OrderBackend): OrderFrontend => {
  return {
    _id: backendOrder._id,
    orderNumber: backendOrder.orderNumber,
    customerName: backendOrder.customer?.name,
    tableNumber: backendOrder.table?.number,
    status: backendOrder.status === 'completed' ? 'delivered' : 
            backendOrder.status === 'served' ? 'delivered' :
            backendOrder.status as any,
    items: backendOrder.items || [],
    totalPrice: backendOrder.totals?.total,
    totals: backendOrder.totals,
    createdAt: backendOrder.createdAt,
    updatedAt: backendOrder.updatedAt
  };
};

// دالة مساعدة للحصول على السعر الإجمالي
export const getOrderTotal = (order: Order): number => {
  if ('totalPrice' in order && order.totalPrice !== undefined && order.totalPrice !== null) {
    return order.totalPrice;
  }
  if ('totals' in order && order.totals?.total !== undefined) {
    return order.totals.total;
  }
  // حساب من العناصر كـ fallback
  if (order.items && Array.isArray(order.items)) {
    return order.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  }
  return 0;
};

// دالة للتحقق من صحة البيانات
export const validateOrder = (order: Partial<OrderFrontend>): string[] => {
  const errors: string[] = [];
  
  if (!order.customerName) { // Simplified condition
    errors.push('اسم العميل مطلوب');
  }
  
  if (!order.items || !Array.isArray(order.items) || order.items.length === 0) {
    errors.push('يجب إضافة منتجات للطلب');
  }
  
  if (!order.tableNumber) { // Simplified condition
    errors.push('رقم الطاولة مطلوب');
  }
  
  if (order.items) {
    order.items.forEach((item, index) => {
      if (!item.product && !item.productId) {
        errors.push(`المنتج مطلوب للعنصر رقم ${index + 1}`);
      }
      if (!item.quantity || item.quantity <= 0) {
        errors.push(`الكمية يجب أن تكون أكبر من صفر للعنصر رقم ${index + 1}`);
      }
    });
  }
  
  return errors;
};

// دالة مساعدة للحصول على السعر الإجمالي (نسخة محدثة)
export const getOrderTotalPrice = (order: Order): number => {
  if ('totalPrice' in order && order.totalPrice !== undefined && order.totalPrice !== null) {
    return order.totalPrice;
  }
  
  if (order.totals?.total !== undefined && order.totals.total !== null) {
    return order.totals.total;
  }
  
  // حساب السعر من العناصر كـ fallback
  if (order.items && Array.isArray(order.items)) {
    return order.items.reduce((sum, item) => {
      const itemPrice = (item.price || 0) * (item.quantity || 0);
      return sum + itemPrice;
    }, 0);
  }
  
  return 0;
};

// دالة مساعدة لتنسيق السعر
export const formatOrderPrice = (order: Order, currency: string = 'ر.س'): string => {
  const total = getOrderTotal(order);
  return `${total.toFixed(2)} ${currency}`;
};

// دالة للتحقق من صحة الطلب
export const isValidOrder = (order: any): order is Order => {
  return (
    order &&
    typeof order === 'object' &&
    typeof order._id === 'string' &&
    typeof order.orderNumber === 'string' &&
    typeof order.waiterName === 'string' &&
    typeof order.status === 'string' &&
    Array.isArray(order.items) &&
    typeof order.createdAt === 'string'
  );
};
