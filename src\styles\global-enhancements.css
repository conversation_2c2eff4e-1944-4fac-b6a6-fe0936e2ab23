/* Global Enhancements for All Dashboards */

/* CSS Variables for Consistency */
:root {
  /* Primary Colors */
  --primary-blue: #3498db;
  --primary-blue-dark: #2980b9;
  --primary-blue-light: #5dade2;
  
  /* Secondary Colors */
  --secondary-purple: #9b59b6;
  --secondary-purple-dark: #8e44ad;
  --secondary-purple-light: #bb8fce;
  
  /* Status Colors */
  --success-green: #27ae60;
  --success-green-dark: #229954;
  --success-green-light: #58d68d;
  
  --warning-orange: #f39c12;
  --warning-orange-dark: #e67e22;
  --warning-orange-light: #f7dc6f;
  
  --danger-red: #e74c3c;
  --danger-red-dark: #c0392b;
  --danger-red-light: #f1948a;
  
  --info-cyan: #17a2b8;
  --info-cyan-dark: #138496;
  --info-cyan-light: #7dd3fc;
  
  /* Neutral Colors */
  --dark-gray: #2c3e50;
  --medium-gray: #7f8c8d;
  --light-gray: #bdc3c7;
  --very-light-gray: #ecf0f1;
  --white: #ffffff;
  
  /* Background Colors */
  --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --bg-light: #f8f9fa;
  --bg-card: rgba(255, 255, 255, 0.95);
  
  /* Shadows */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.15);
  
  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-full: 50%;
  
  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
  
  /* Spacing */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  
  /* Typography */
  --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  
  /* Z-Index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

/* Global Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family);
  line-height: 1.6;
  color: var(--dark-gray);
  background: var(--bg-light);
  direction: rtl;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography Utilities */
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }
.text-4xl { font-size: var(--font-size-4xl); }

.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

/* Color Utilities */
.text-primary { color: var(--primary-blue); }
.text-secondary { color: var(--secondary-purple); }
.text-success { color: var(--success-green); }
.text-warning { color: var(--warning-orange); }
.text-danger { color: var(--danger-red); }
.text-info { color: var(--info-cyan); }
.text-dark { color: var(--dark-gray); }
.text-muted { color: var(--medium-gray); }
.text-light { color: var(--light-gray); }
.text-white { color: var(--white); }

/* Background Utilities */
.bg-primary { background-color: var(--primary-blue); }
.bg-secondary { background-color: var(--secondary-purple); }
.bg-success { background-color: var(--success-green); }
.bg-warning { background-color: var(--warning-orange); }
.bg-danger { background-color: var(--danger-red); }
.bg-info { background-color: var(--info-cyan); }
.bg-dark { background-color: var(--dark-gray); }
.bg-light { background-color: var(--bg-light); }
.bg-white { background-color: var(--white); }

/* Gradient Backgrounds */
.bg-gradient-primary {
  background: linear-gradient(135deg, var(--primary-blue), var(--primary-blue-dark));
}

.bg-gradient-secondary {
  background: linear-gradient(135deg, var(--secondary-purple), var(--secondary-purple-dark));
}

.bg-gradient-success {
  background: linear-gradient(135deg, var(--success-green), var(--success-green-dark));
}

.bg-gradient-warning {
  background: linear-gradient(135deg, var(--warning-orange), var(--warning-orange-dark));
}

.bg-gradient-danger {
  background: linear-gradient(135deg, var(--danger-red), var(--danger-red-dark));
}

/* Spacing Utilities */
.m-0 { margin: 0; }
.m-1 { margin: var(--space-xs); }
.m-2 { margin: var(--space-sm); }
.m-3 { margin: var(--space-md); }
.m-4 { margin: var(--space-lg); }
.m-5 { margin: var(--space-xl); }
.m-6 { margin: var(--space-2xl); }

.p-0 { padding: 0; }
.p-1 { padding: var(--space-xs); }
.p-2 { padding: var(--space-sm); }
.p-3 { padding: var(--space-md); }
.p-4 { padding: var(--space-lg); }
.p-5 { padding: var(--space-xl); }
.p-6 { padding: var(--space-2xl); }

/* Margin/Padding Directional */
.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--space-xs); }
.mt-2 { margin-top: var(--space-sm); }
.mt-3 { margin-top: var(--space-md); }
.mt-4 { margin-top: var(--space-lg); }
.mt-5 { margin-top: var(--space-xl); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--space-xs); }
.mb-2 { margin-bottom: var(--space-sm); }
.mb-3 { margin-bottom: var(--space-md); }
.mb-4 { margin-bottom: var(--space-lg); }
.mb-5 { margin-bottom: var(--space-xl); }

/* Border Radius Utilities */
.rounded-none { border-radius: 0; }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-full { border-radius: var(--radius-full); }

/* Shadow Utilities */
.shadow-none { box-shadow: none; }
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

/* Display Utilities */
.d-none { display: none; }
.d-block { display: block; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

/* Flexbox Utilities */
.flex-row { flex-direction: row; }
.flex-col { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.items-stretch { align-items: stretch; }

.flex-1 { flex: 1; }
.flex-auto { flex: auto; }
.flex-none { flex: none; }

/* Grid Utilities */
.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
.grid-cols-5 { grid-template-columns: repeat(5, 1fr); }
.grid-cols-6 { grid-template-columns: repeat(6, 1fr); }

.gap-1 { gap: var(--space-xs); }
.gap-2 { gap: var(--space-sm); }
.gap-3 { gap: var(--space-md); }
.gap-4 { gap: var(--space-lg); }
.gap-5 { gap: var(--space-xl); }
.gap-6 { gap: var(--space-2xl); }

/* Position Utilities */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

/* Transition Utilities */
.transition-none { transition: none; }
.transition-fast { transition: all var(--transition-fast); }
.transition { transition: all var(--transition-normal); }
.transition-slow { transition: all var(--transition-slow); }

/* Transform Utilities */
.transform { transform: translateZ(0); }
.scale-95 { transform: scale(0.95); }
.scale-100 { transform: scale(1); }
.scale-105 { transform: scale(1.05); }
.scale-110 { transform: scale(1.1); }

.rotate-0 { transform: rotate(0deg); }
.rotate-90 { transform: rotate(90deg); }
.rotate-180 { transform: rotate(180deg); }
.rotate-270 { transform: rotate(270deg); }

/* Opacity Utilities */
.opacity-0 { opacity: 0; }
.opacity-25 { opacity: 0.25; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }

/* Cursor Utilities */
.cursor-auto { cursor: auto; }
.cursor-default { cursor: default; }
.cursor-pointer { cursor: pointer; }
.cursor-wait { cursor: wait; }
.cursor-text { cursor: text; }
.cursor-move { cursor: move; }
.cursor-not-allowed { cursor: not-allowed; }

/* Overflow Utilities */
.overflow-auto { overflow: auto; }
.overflow-hidden { overflow: hidden; }
.overflow-visible { overflow: visible; }
.overflow-scroll { overflow: scroll; }

.overflow-x-auto { overflow-x: auto; }
.overflow-x-hidden { overflow-x: hidden; }
.overflow-y-auto { overflow-y: auto; }
.overflow-y-hidden { overflow-y: hidden; }

/* Width and Height Utilities */
.w-auto { width: auto; }
.w-full { width: 100%; }
.w-screen { width: 100vw; }
.w-min { width: min-content; }
.w-max { width: max-content; }

.h-auto { height: auto; }
.h-full { height: 100%; }
.h-screen { height: 100vh; }
.h-min { height: min-content; }
.h-max { height: max-content; }

/* Z-Index Utilities */
.z-0 { z-index: 0; }
.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }

/* Custom Component Classes */
.card {
  background: var(--bg-card);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  padding: var(--space-xl);
  transition: var(--transition-normal);
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
  padding: var(--space-md) var(--space-lg);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-normal);
  text-decoration: none;
  white-space: nowrap;
}

.btn:hover {
  transform: translateY(-1px);
}

.btn:active {
  transform: translateY(0);
}

.btn-primary {
  background: var(--bg-gradient-primary);
  color: var(--white);
}

.btn-primary:hover {
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.btn-secondary {
  background: var(--bg-gradient-secondary);
  color: var(--white);
}

.btn-secondary:hover {
  box-shadow: 0 4px 15px rgba(155, 89, 182, 0.3);
}

.btn-success {
  background: var(--bg-gradient-success);
  color: var(--white);
}

.btn-success:hover {
  box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

.btn-warning {
  background: var(--bg-gradient-warning);
  color: var(--white);
}

.btn-warning:hover {
  box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
}

.btn-danger {
  background: var(--bg-gradient-danger);
  color: var(--white);
}

.btn-danger:hover {
  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.btn-outline {
  background: transparent;
  border: 2px solid var(--primary-blue);
  color: var(--primary-blue);
}

.btn-outline:hover {
  background: var(--primary-blue);
  color: var(--white);
}

/* Badge Component */
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.badge-primary { background: var(--primary-blue); color: var(--white); }
.badge-secondary { background: var(--secondary-purple); color: var(--white); }
.badge-success { background: var(--success-green); color: var(--white); }
.badge-warning { background: var(--warning-orange); color: var(--white); }
.badge-danger { background: var(--danger-red); color: var(--white); }
.badge-info { background: var(--info-cyan); color: var(--white); }

/* Loading Spinner */
.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--very-light-gray);
  border-top: 4px solid var(--primary-blue);
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Utilities */
@media (max-width: 640px) {
  .sm\:hidden { display: none; }
  .sm\:block { display: block; }
  .sm\:flex { display: flex; }
  .sm\:grid { display: grid; }
  .sm\:text-sm { font-size: var(--font-size-sm); }
  .sm\:text-base { font-size: var(--font-size-base); }
  .sm\:p-2 { padding: var(--space-sm); }
  .sm\:p-3 { padding: var(--space-md); }
}

@media (max-width: 768px) {
  .md\:hidden { display: none; }
  .md\:block { display: block; }
  .md\:flex { display: flex; }
  .md\:grid { display: grid; }
  .md\:text-sm { font-size: var(--font-size-sm); }
  .md\:text-base { font-size: var(--font-size-base); }
  .md\:p-2 { padding: var(--space-sm); }
  .md\:p-3 { padding: var(--space-md); }
}

@media (max-width: 1024px) {
  .lg\:hidden { display: none; }
  .lg\:block { display: block; }
  .lg\:flex { display: flex; }
  .lg\:grid { display: grid; }
  .lg\:text-sm { font-size: var(--font-size-sm); }
  .lg\:text-base { font-size: var(--font-size-base); }
  .lg\:p-2 { padding: var(--space-sm); }
  .lg\:p-3 { padding: var(--space-md); }
}

/* Print Styles */
@media print {
  .no-print { display: none !important; }
  .print-only { display: block !important; }
  
  * {
    color: black !important;
    background: white !important;
    box-shadow: none !important;
  }
  
  .card {
    border: 1px solid #ccc;
    box-shadow: none;
  }
}
