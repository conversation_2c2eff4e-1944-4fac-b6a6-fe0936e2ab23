/* filepath: c:/Users/<USER>/OneDrive/Desktop/PRINT/Coffee/Coffee/src/ModernManagerDashboard.css */
.modern-dashboard {
  display: flex;
  height: 100vh;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f4f7f6; /* خلفية رمادية فاتحة */
  color: #333;
}

.sidebar {
  width: 260px;
  background-color: #2c3e50; /* كحلي غامق */
  color: #ecf0f1; /* أبيض مائل للرمادي */
  padding: 20px;
  display: flex;
  flex-direction: column;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
}

.sidebar-header h2 {
  margin: 0 0 30px 0;
  font-size: 1.8em;
  text-align: center;
  color: #fff; /* أبيض ناصع */
  font-weight: 600;
}

.sidebar-nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 15px 10px;
  margin-bottom: 10px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s ease, color 0.3s ease;
  font-size: 1.1em;
}

.nav-item:hover {
  background-color: #34495e; /* درجة أغمق من الكحلي */
  color: #fff;
}

.nav-item.active {
  background-color: #1abc9c; /* أخضر فيروزي */
  color: #fff;
  font-weight: 500;
}

.nav-item.active:hover {
  background-color: #16a085; /* درجة أغمق من الفيروزي */
}

.nav-item span:first-child { /* للأيقونة */
  margin-right: 15px;
  font-size: 1.3em;
}

.nav-label {
  flex-grow: 1;
}

.main-content {
  flex-grow: 1;
  padding: 30px;
  overflow-y: auto;
  background-color: #fff; /* خلفية بيضاء للمحتوى */
  border-radius: 10px;
  margin: 20px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.08);
}

.main-content section h2 {
  font-size: 2em;
  color: #2c3e50; /* كحلي غامق */
  margin-bottom: 20px;
  border-bottom: 2px solid #1abc9c; /* أخضر فيروزي */
  padding-bottom: 10px;
}

.main-content section p {
  font-size: 1.1em;
  line-height: 1.7;
  color: #555;
}

/* تصميم مبدئي للجداول والمدخلات (يمكن تحسينه لاحقًا) */
table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
}

th, td {
  padding: 12px;
  border: 1px solid #ddd;
  text-align: right; /* للتوافق مع اللغة العربية */
  vertical-align: middle; /* Vertically align content in cells */
}

th {
  background-color: #e9ecef; /* رمادي فاتح جداً للعناوين */
  color: #333;
  font-weight: 600;
}

input[type="text"],
input[type="password"],
input[type="email"],
input[type="number"],
select,
textarea {
  width: 100%;
  padding: 10px;
  margin-bottom: 15px;
  border: 1px solid #ccc;
  border-radius: 5px;
  box-sizing: border-box;
  font-size: 1em;
}

button {
  background-color: #1abc9c; /* أخضر فيروزي */
  color: white;
  padding: 12px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1em;
  transition: background-color 0.3s ease;
}

button:hover {
  background-color: #16a085; /* درجة أغمق من الفيروزي */
}

button.secondary {
  background-color: #7f8c8d; /* رمادي */
}
button.secondary:hover {
  background-color: #6c7a7b; /* رمادي أغمق */
}

button.danger {
  background-color: #e74c3c; /* أحمر */
}
button.danger:hover {
  background-color: #c0392b; /* أحمر أغمق */
}

/* تصميم للبطاقات (Cards) إذا تم استخدامها */
.card-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  padding: 20px;
  flex: 1 1 calc(33.333% - 20px); /* ثلاث بطاقات في الصف مع مسافات */
  min-width: 280px;
}

.card h3 {
  margin-top: 0;
  color: #1abc9c;
}

/* Order Details Modal Specific Styles */
.modal-content.order-details-modal {
  max-width: 700px; /* Wider modal for order details */
}

.order-info-grid,
.order-totals-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
  margin-bottom: 15px;
}

.order-info-grid p,
.order-totals-grid p {
  margin: 5px 0;
  padding: 5px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.modal-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 15px;
}

.modal-table th,
.modal-table td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}

.modal-table th {
  background-color: #f2f2f2;
}

.orders-section table select {
  padding: 6px 10px;
  border-radius: 4px;
  border: 1px solid #ccc;
  background-color: white;
  cursor: pointer;
}

.orders-section table select:hover {
  border-color: #888;
}

.orders-section table select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

/* Settings Section Styles */
.settings-section .setting-item {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #eee;
  border-radius: 5px;
  background-color: #fdfdfd;
}

.settings-section .setting-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: #333;
}

.settings-section .setting-item input[type="text"],
.settings-section .setting-item input[type="number"],
.settings-section .setting-item select {
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-sizing: border-box;
}

.settings-section .setting-item input[type="checkbox"] {
  margin-right: 10px;
  vertical-align: middle; /* Align checkbox nicely with label text if any */
}

/* Styles for the settings form itself */
.settings-form {
  display: flex;
  flex-direction: column;
  gap: 15px; /* Space between setting items */
}

.setting-item {
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background-color: #f9f9f9;
}

.setting-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600; /* Make label bolder */
  color: #333;
}

.setting-item .setting-description {
  font-size: 0.9em;
  color: #666;
  margin-bottom: 10px;
  margin-top: -5px; /* Adjust spacing if needed */
}

.setting-item input[type="text"],
.setting-item input[type="number"],
.setting-item select {
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-sizing: border-box;
  font-size: 1em;
}

.setting-item input[type="checkbox"] {
  width: auto; /* Override full width for checkbox */
  margin-right: 8px;
}

/* Specific style for checkbox label to align it properly */
.checkbox-label {
  display: flex;
  align-items: center;
  font-weight: normal; /* Normal weight for checkbox text if any */
}


.settings-section button.save-settings-button {
  padding: 12px 25px;
  background-color: #28a745; /* Green for save */
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1.05em;
  transition: background-color 0.2s ease-in-out;
  align-self: flex-start; /* Align button to the start of the form */
  margin-top: 10px;
}

.settings-section button.save-settings-button:hover {
  background-color: #218838; /* Darker green on hover */
}

.settings-section button.save-settings-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}


.settings-section button {
  padding: 10px 20px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s ease-in-out;
}

.settings-section button:hover {
  background-color: #0056b3;
}

/* Additional Styles */
table tbody tr:nth-child(odd) {
  background-color: #f9f9f9; /* Light gray for odd rows */
}

table tbody tr:hover {
  background-color: #f1f1f1; /* Slightly darker gray on hover */
}

/* Ensure active status dropdown doesn't get overridden by hover */
.orders-section table select:focus {
  outline: none;
  border-color: #007bff; /* Or your theme's primary color */
  box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25); /* Or your theme's primary color shadow */
}

.no-data-message {
  text-align: center;
  padding: 20px 0; /* Adjusted padding */
  font-style: italic;
  color: #777;
  font-size: 1.1em; /* Slightly larger text */
  margin-top: 15px; /* Add some space above the message */
}

/* Product image styling */
.product-image-container {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden; /* Ensure image fits */
  background-color: #eee; /* Placeholder background */
  border-radius: 4px; /* Optional: rounded corners */
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-image-placeholder-text {
  font-size: 0.8em;
  color: #888;
  text-align: center;
}

/* Status and Priority Styling for Orders */
.status-dropdown {
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #ccc;
  background-color: #fff;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.status-dropdown:hover {
  border-color: #888;
}

.status-dropdown:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

/* Specific status styles */
.status-dropdown.status-pending {
  border-color: #ffc107; /* Yellow */
  background-color: #fff3cd;
}

.status-dropdown.status-completed {
  border-color: #28a745; /* Green */
  background-color: #d4edda;
}

.status-dropdown.status-cancelled {
  border-color: #f44336; /* Red */
  background-color: #ffebee;
}

/* Priority Styling */
.priority-low td,
.priority-low .status-dropdown {
  background-color: #e3f2fd; /* Light blue */
}

.priority-normal td,
.priority-normal .status-dropdown {
  /* No specific style needed for normal priority, or use a very subtle one if desired */
  /* e.g., background-color: #f8f9fa; */
}

.priority-high td,
.priority-high .status-dropdown {
  background-color: #fff9c4; /* Light yellow */
  font-weight: bold;
}

.priority-urgent td,
.priority-urgent .status-dropdown {
  background-color: #ffcdd2; /* Light red */
  font-weight: bold;
  color: #c62828; /* Darker red text for urgency */
}

.order-details-modal .order-info-grid,
.order-details-modal .order-totals-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
  margin-bottom: 15px;
}

.order-details-modal .order-priority-section {
  margin-top: 15px;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.order-details-modal .order-priority-section label {
  font-weight: bold;
}

.order-details-modal .order-priority-section select {
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #ccc;
}

.order-details-modal .order-rating-section {
  margin-top: 15px;
  padding-top: 10px;
  border-top: 1px solid #eee;
}

.order-details-modal .order-rating-section h4 {
  margin-bottom: 5px;
}

.order-details-modal .modal-table {
  width: 100%;
  margin-bottom: 15px;
  border-collapse: collapse;
}

.order-details-modal .modal-table th,
.order-details-modal .modal-table td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}

.order-details-modal .modal-table th {
  background-color: #f9f9f9;
}
