// مكون عرض الإشعارات - لوحة النادل
import React, { useState, useEffect } from 'react';
import type { WaiterNotification } from '../../models/WaiterModels';
import { WaiterDataService } from '../../services/WaiterDataService';
import { useToast } from '../../hooks/useToast';

interface NotificationsViewProps {
  notifications: WaiterNotification[];
  onNotificationRead: (notificationId: string) => void;
  onCloseModal: () => void;
}

export const NotificationsView: React.FC<NotificationsViewProps> = ({
  notifications,
  onNotificationRead,
  onCloseModal
}) => {
  const [loading, setLoading] = useState(false);
  const { showSuccess, showError } = useToast();

  // تحديد الإشعار كمقروء
  const handleMarkAsRead = async (notificationId: string) => {
    try {
      setLoading(true);
      const waiterService = WaiterDataService.getInstance();
      // نحتاج إلى إضافة هذه الدالة للخدمة
      await waiterService.markNotificationAsRead?.(notificationId);
      onNotificationRead(notificationId);
      showSuccess('تم تحديد الإشعار كمقروء');
    } catch (error) {
      console.error('❌ خطأ في تحديد الإشعار كمقروء:', error);
      showError('فشل في تحديد الإشعار كمقروء');
    } finally {
      setLoading(false);
    }
  };

  // تحديد جميع الإشعارات كمقروءة
  const handleMarkAllAsRead = async () => {
    try {
      setLoading(true);
      const unreadNotifications = notifications.filter(n => !n.isRead);
      
      for (const notification of unreadNotifications) {
        await handleMarkAsRead(notification._id);
      }
      
      showSuccess('تم تحديد جميع الإشعارات كمقروءة');
    } catch (error) {
      console.error('❌ خطأ في تحديد جميع الإشعارات:', error);
      showError('فشل في تحديد جميع الإشعارات');
    } finally {
      setLoading(false);
    }
  };

  // تصفية الإشعارات
  const unreadNotifications = notifications.filter(n => !n.isRead);
  const readNotifications = notifications.filter(n => n.isRead);

  // أيقونات الإشعارات
  const getNotificationIcon = (type: WaiterNotification['type']): string => {
    switch (type) {
      case 'order_ready': return '✅';
      case 'order_completed': return '🎉';
      case 'discount_approved': return '💰';
      case 'discount_rejected': return '❌';
      default: return '📢';
    }
  };

  // ألوان الإشعارات
  const getNotificationColor = (type: WaiterNotification['type']): string => {
    switch (type) {
      case 'order_ready': return '#4CAF50';
      case 'order_completed': return '#2196F3';
      case 'discount_approved': return '#FF9800';
      case 'discount_rejected': return '#f44336';
      default: return '#9C27B0';
    }
  };

  // تنسيق الوقت
  const formatTime = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'الآن';
    if (diffInMinutes < 60) return `منذ ${diffInMinutes} دقيقة`;
    if (diffInMinutes < 1440) return `منذ ${Math.floor(diffInMinutes / 60)} ساعة`;
    return `منذ ${Math.floor(diffInMinutes / 1440)} يوم`;
  };

  return (
    <div className="notifications-modal">
      <div className="modal-overlay" onClick={onCloseModal}></div>
      <div className="modal-content">
        <div className="modal-header">
          <h2>الإشعارات</h2>
          <div className="modal-actions">
            {unreadNotifications.length > 0 && (
              <button
                onClick={handleMarkAllAsRead}
                disabled={loading}
                className="mark-all-read-btn"
              >
                تحديد الكل كمقروء
              </button>
            )}
            <button onClick={onCloseModal} className="close-btn">
              ×
            </button>
          </div>
        </div>

        <div className="notifications-content">
          {notifications.length === 0 ? (
            <div className="empty-notifications">
              <div className="empty-icon">🔔</div>
              <h3>لا توجد إشعارات</h3>
              <p>ستظهر إشعاراتك هنا عند توفرها</p>
            </div>
          ) : (
            <>
              {/* الإشعارات غير المقروءة */}
              {unreadNotifications.length > 0 && (
                <div className="notifications-section">
                  <h3 className="section-title">
                    جديد ({unreadNotifications.length})
                  </h3>
                  <div className="notifications-list">
                    {unreadNotifications.map(notification => (
                      <div
                        key={notification._id}
                        className="notification-item unread"
                        onClick={() => handleMarkAsRead(notification._id)}
                        style={{ borderColor: getNotificationColor(notification.type) }}
                      >
                        <div className="notification-icon">
                          {getNotificationIcon(notification.type)}
                        </div>
                        <div className="notification-content">
                          <div className="notification-title">
                            {notification.title}
                          </div>
                          <div className="notification-message">
                            {notification.message}
                          </div>
                          {notification.tableNumber && (
                            <div className="notification-table">
                              طاولة {notification.tableNumber}
                            </div>
                          )}
                          <div className="notification-time">
                            {formatTime(notification.createdAt)}
                          </div>
                        </div>
                        <div className="notification-status">
                          <span className="unread-badge">جديد</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* الإشعارات المقروءة */}
              {readNotifications.length > 0 && (
                <div className="notifications-section">
                  <h3 className="section-title">
                    مقروءة ({readNotifications.length})
                  </h3>
                  <div className="notifications-list">
                    {readNotifications.map(notification => (
                      <div
                        key={notification._id}
                        className="notification-item read"
                      >
                        <div className="notification-icon">
                          {getNotificationIcon(notification.type)}
                        </div>
                        <div className="notification-content">
                          <div className="notification-title">
                            {notification.title}
                          </div>
                          <div className="notification-message">
                            {notification.message}
                          </div>
                          {notification.tableNumber && (
                            <div className="notification-table">
                              طاولة {notification.tableNumber}
                            </div>
                          )}
                          <div className="notification-time">
                            {formatTime(notification.createdAt)}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};
