import fetch from 'node-fetch';

const BASE_URL = 'https://deshacoffee-production.up.railway.app';

async function testWaiterDashboard() {
    console.log('🧪 بدء اختبار لوحة النادل...\n');
    
    try {        // 1. اختبار جلب حسابات الطاولات
        console.log('📋 اختبار جلب حسابات الطاولات...');
        const accountsResponse = await fetch(`${BASE_URL}/api/table-accounts`);
        const accountsData = await accountsResponse.json();
        
        // التحقق من بنية الاستجابة
        console.log('🔍 بنية الاستجابة:', typeof accountsData, Array.isArray(accountsData));
        
        const accounts = Array.isArray(accountsData) ? accountsData : (accountsData.data || []);
        console.log(`✅ تم جلب ${accounts.length} حساب طاولة`);
        
        if (accounts.length > 0) {
            const firstAccount = accounts[0];
            console.log('\n📊 تفاصيل أول حساب:');
            console.log(`   - رقم الطاولة: ${firstAccount.tableNumber}`);
            console.log(`   - المبلغ الإجمالي: ${firstAccount.totalAmount || 'غير محدد'}`);
            console.log(`   - عدد الطلبات: ${firstAccount.orders?.length || 0}`);
            
            if (firstAccount.orders && firstAccount.orders.length > 0) {
                console.log('\n🍽️ تفاصيل الطلبات:');
                firstAccount.orders.forEach((order, index) => {
                    console.log(`   طلب ${index + 1}:`);
                    console.log(`     - رقم الطلب: ${order.orderNumber || order._id?.slice(-6) || 'غير محدد'}`);
                    console.log(`     - المبلغ: ${order.totalPrice || 0} ج.م`);
                    console.log(`     - الحالة: ${order.status || 'غير محدد'}`);
                });
            }
        }
        
        // 2. اختبار حساب الإحصائيات
        console.log('\n📈 اختبار حساب الإحصائيات...');
        
        let totalRevenue = 0;
        let totalOrders = 0;
        let activeTables = 0;
        
        accounts.forEach(account => {
            if (account.orders && account.orders.length > 0) {
                activeTables++;
                totalOrders += account.orders.length;
                
                // حساب الإيرادات
                const accountTotal = account.totalAmount && account.totalAmount > 0
                    ? account.totalAmount
                    : (account.orders?.reduce((sum, order) => {
                        const orderPrice = order.totalPrice || 0;
                        return sum + orderPrice;
                    }, 0) || 0);
                
                totalRevenue += accountTotal;
            }
        });
        
        console.log(`✅ إجمالي الإيرادات: ${totalRevenue.toFixed(2)} ج.م`);
        console.log(`✅ إجمالي الطلبات: ${totalOrders}`);
        console.log(`✅ الطاولات النشطة: ${activeTables}`);
        
        // 3. اختبار واجهة برمجة التطبيقات للطلبات
        console.log('\n🔄 اختبار واجهة الطلبات...');
        const ordersResponse = await fetch(`${BASE_URL}/api/orders`);
        const orders = await ordersResponse.json();
        
        console.log(`✅ تم جلب ${orders.length} طلب من واجهة برمجة التطبيقات`);
        
        console.log('\n✅ اكتملت جميع الاختبارات بنجاح!');
        
        return {
            success: true,
            stats: {
                totalAccounts: accounts.length,
                totalRevenue,
                totalOrders,
                activeTables
            }
        };
        
    } catch (error) {
        console.error('❌ خطأ في الاختبار:', error.message);
        return { success: false, error: error.message };
    }
}

// تشغيل الاختبار
testWaiterDashboard().then(result => {
    if (result.success) {
        console.log('\n🎉 نجح الاختبار الشامل للوحة النادل!');
        console.log('📊 ملخص النتائج:', result.stats);
    } else {
        console.log('\n❌ فشل الاختبار:', result.error);
    }
    process.exit(result.success ? 0 : 1);
});
