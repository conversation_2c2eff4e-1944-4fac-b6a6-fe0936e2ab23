# 🚀 دليل الاستخدام السريع - نظام إدارة المقهى

## 📋 نظرة عامة
نظام إدارة مقهى شامل ومتطور باستخدام React + TypeScript مع هيكلة OOP احترافية.

---

## 🏁 البدء السريع

### 1️⃣ تشغيل النظام الكامل
```bash
npm run dev:all
```
🌐 **الرابط**: http://localhost:5173

### 2️⃣ تشغيل الواجهة فقط
```bash
npm run dev
```

### 3️⃣ تشغيل الخادم فقط
```bash
npm run server
```

---

## 👥 بيانات تسجيل الدخول

### 🏢 المدير (Manager)
- **اسم المستخدم**: `manager`
- **كلمة المرور**: `manager123`
- **الصلاحيات**: إدارة كاملة للنظام

### 👨‍🍳 الطباخ (Chef)
- **اسم المستخدم**: `chef`
- **كلمة المرور**: `chef123`
- **الصلاحيات**: إدارة الطلبات والقائمة

### 🧑‍💼 النادل (Waiter)
- **اسم المستخدم**: `waiter`
- **كلمة المرور**: `waiter123`
- **الصلاحيات**: أخذ الطلبات وإدارة الطاولات

---

## 🎛️ لوحات التحكم

### 🏢 لوحة المدير
**المكونات المتاحة**:
- 🏠 **الرئيسية**: عرض الإحصائيات العامة
- 📊 **التقارير**: تقارير مفصلة للمبيعات والأرباح
- 🍽️ **إدارة القائمة**: إضافة وتعديل الأصناف
- 🪑 **إدارة الطاولات**: تنظيم الطاولات والحجوزات

### 🧑‍💼 لوحة النادل
**المكونات المتاحة**:
- 🪑 **الطاولات**: عرض حالة جميع الطاولات
- 📋 **الطلبات**: إدارة الطلبات الحالية
- 🍽️ **القائمة**: عرض قائمة الطعام والمشروبات
- ➕ **طلب جديد**: إنشاء طلبات جديدة
- 🔔 **الإشعارات**: تتبع التحديثات المهمة

### 👨‍🍳 لوحة الطباخ
**المكونات المتاحة**:
- 📋 **الطلبات**: عرض الطلبات الواردة للمطبخ
- 🍽️ **القائمة**: عرض وإدارة أصناف الطعام

---

## 🛠️ الأوامر المفيدة

### 🔨 البناء
```bash
npm run build
```

### 🧪 الاختبار
```bash
npm test
```

### 🧹 تنظيف الكاش
```bash
npm run clean
```

### 📦 تحديث التبعيات
```bash
npm update
```

---

## 🎨 الميزات الرئيسية

### ✨ الواجهة
- 🌐 **RTL Support**: دعم كامل للعربية
- 📱 **Responsive**: متوافق مع جميع الأجهزة
- 🎯 **Modern UI**: تصميم حديث وجذاب
- ⚡ **Fast Loading**: تحميل سريع مع Lazy Loading

### 🔧 التقنيات
- ⚛️ **React 18**: أحدث إصدار
- 📘 **TypeScript**: كتابة آمنة ومنظمة  
- 🏗️ **OOP Architecture**: هيكلة احترافية
- 🔄 **Real-time Updates**: تحديثات فورية

### 🔒 الأمان
- 🛡️ **JWT Authentication**: مصادقة آمنة
- 🔐 **Role-based Access**: صلاحيات محددة لكل دور
- 🛠️ **Error Handling**: معالجة شاملة للأخطاء

---

## 🌐 الروابط المهمة

- **GitHub**: https://github.com/MediaFuture/DeshaCoffee.git
- **التطبيق**: http://localhost:5173
- **API**: http://localhost:3001

---

## 📞 الدعم الفني

### 🚨 في حالة وجود مشاكل:

1. **تأكد من تشغيل الخادم**:
   ```bash
   npm run dev:all
   ```

2. **امسح الكاش**:
   ```bash
   npm run clean
   npm install
   ```

3. **تحقق من الملفات**:
   - تأكد من وجود `.env` file
   - تحقق من اتصال قاعدة البيانات

4. **إعادة تشغيل النظام**:
   ```bash
   npm run build
   npm run dev:all
   ```

---

## 📈 نصائح للاستخدام الأمثل

### 🏢 للمدير
- راجع التقارير يومياً لمتابعة الأداء
- استخدم إدارة القائمة لتحديث الأسعار
- تابع حالة الطاولات لتحسين الخدمة

### 🧑‍💼 للنادل
- تحقق من الإشعارات باستمرار
- استخدم الطلب الجديد لتسريع الخدمة
- راجع حالة الطاولات قبل استقبال العملاء

### 👨‍🍳 للطباخ
- تابع الطلبات بالترتيب الزمني
- حدث حالة الطلبات فور الانتهاء
- راجع القائمة للتأكد من توفر المكونات

---

## 🏆 النظام جاهز للاستخدام!

✅ **تم الاختبار بالكامل**  
✅ **خالي من الأخطاء**  
✅ **جاهز للإنتاج**  
✅ **موثق بالكامل**

**استمتع بتجربة إدارة مقهى احترافية!** ☕
