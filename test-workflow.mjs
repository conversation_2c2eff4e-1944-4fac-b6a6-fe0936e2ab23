#!/usr/bin/env node

/**
 * 🧪 اختبار سيناريوهات سير العمل
 * يختبر جميع المتطلبات المحددة للتطبيق
 */

import axios from 'axios';
import colors from 'colors';
import fs from 'fs';

// إعدادات الاختبار
const API_BASE_URL = process.env.API_URL || 'http://localhost:4003/api';
const TEST_CONFIG = {
  waiterName: 'azza',
  chefName: 'khaled',
  tableNumber: '5',
  customerName: 'عميل اختبار',
  testItems: [
    { product: '507f1f77bcf86cd799439011', name: 'قهوة تركية', quantity: 2, price: 15 },
    { product: '507f1f77bcf86cd799439012', name: 'شاي أحمر', quantity: 1, price: 10 }
  ]
};

class WorkflowTester {
  constructor() {
    this.testResults = [];
    this.authToken = null;
  }

  // تسجيل نتيجة اختبار
  logResult(testName, passed, message, details = null) {
    const result = {
      test: testName,
      passed,
      message,
      details,
      timestamp: new Date().toISOString()
    };
    
    this.testResults.push(result);
    
    const status = passed ? '✅ نجح'.green : '❌ فشل'.red;
    console.log(`${status} ${testName}: ${message}`);
    
    if (details) {
      console.log(`   📝 التفاصيل: ${JSON.stringify(details, null, 2)}`.gray);
    }
  }

  // محاولة تسجيل الدخول (للحصول على token)
  async authenticate() {
    try {
      const response = await axios.post(`${API_BASE_URL}/auth/login`, {
        username: 'Beso',
        password: 'MOHAMEDmostafa123'
      });
      
      this.authToken = response.data.token;
      this.logResult('المصادقة', true, 'تم تسجيل الدخول بنجاح مع المستخدم الفعلي Beso');
      return true;
    } catch (error) {
      this.logResult('المصادقة', false, 'فشل في تسجيل الدخول', error.response?.data);
      return false;
    }
  }

  // اختبار 1: النادل ينشئ طلب مع رقم طاولة إجباري
  async testWaiterOrderCreation() {
    console.log('\n🧪 اختبار إنشاء طلب من النادل...\n'.yellow);
    
    try {
      // اختبار بدون رقم طاولة (يجب أن يفشل)
      const invalidOrder = {
        waiterName: TEST_CONFIG.waiterName,
        customerName: TEST_CONFIG.customerName,
        items: TEST_CONFIG.testItems,
        totalPrice: 40
        // tableNumber مفقود عمداً
      };

      try {
        await axios.post(`${API_BASE_URL}/orders`, invalidOrder, {
          headers: { Authorization: `Bearer ${this.authToken}` }
        });
        this.logResult('رقم الطاولة الإجباري', false, 'تم قبول طلب بدون رقم طاولة (خطأ!)');
      } catch (error) {
        if (error.response?.status === 400) {
          this.logResult('رقم الطاولة الإجباري', true, 'تم رفض الطلب بدون رقم طاولة بشكل صحيح');
        } else {
          this.logResult('رقم الطاولة الإجباري', false, 'خطأ غير متوقع', error.response?.data);
        }
      }

      // اختبار بطلب صحيح مع النادل الفعلي (azza)
      const validOrder = {
        waiterName: TEST_CONFIG.waiterName,
        customerName: TEST_CONFIG.customerName,
        tableNumber: TEST_CONFIG.tableNumber,
        items: TEST_CONFIG.testItems,
        totalPrice: 40
      };

      const response = await axios.post(`${API_BASE_URL}/orders`, validOrder, {
        headers: { Authorization: `Bearer ${this.authToken}` }
      });

      if (response.status === 201 && response.data.success) {
        this.logResult('إنشاء طلب صحيح', true, `تم إنشاء الطلب بنجاح بواسطة النادل الفعلي ${TEST_CONFIG.waiterName}`, {
          orderId: response.data.data?._id,
          orderNumber: response.data.data?.orderNumber
        });
        return response.data.data;
      } else {
        this.logResult('إنشاء طلب صحيح', false, 'فشل في إنشاء الطلب', response.data);
      }

    } catch (error) {
      this.logResult('إنشاء طلب صحيح', false, 'خطأ في إنشاء الطلب', error.response?.data);
    }

    return null;
  }

  // اختبار 2: إدارة الطاولات وحسابات الطاولة
  async testTableManagement() {
    console.log('\n🧪 اختبار إدارة الطاولات...\n'.yellow);

    try {
      // فحص حسابات الطاولات
      const tablesResponse = await axios.get(`${API_BASE_URL}/table-accounts`, {
        headers: { Authorization: `Bearer ${this.authToken}` }
      });

      this.logResult('جلب حسابات الطاولات', true, `تم جلب ${tablesResponse.data.length} حساب طاولة`);

      // البحث عن الطاولة المحددة
      const existingTable = tablesResponse.data.find(table => 
        table.tableNumber.toString() === TEST_CONFIG.tableNumber
      );

      if (existingTable) {
        this.logResult('حالة الطاولة', true, `الطاولة ${TEST_CONFIG.tableNumber} موجودة`, {
          status: existingTable.status,
          waiter: existingTable.waiter?.name,
          totalAmount: existingTable.totalAmount
        });

        // اختبار منع التداخل
        if (existingTable.status === 'active' && existingTable.waiter?.name !== TEST_CONFIG.waiterName) {
          this.logResult('منع التداخل', true, `الطاولة محجوزة من قبل نادل آخر: ${existingTable.waiter.name}`);
        }
      } else {
        this.logResult('حالة الطاولة', true, `الطاولة ${TEST_CONFIG.tableNumber} غير موجودة - سيتم إنشاؤها`);
      }

    } catch (error) {
      this.logResult('إدارة الطاولات', false, 'فشل في فحص حسابات الطاولات', error.response?.data);
    }
  }

  // اختبار 3: دورة حياة الطلب الكاملة
  async testOrderLifecycle(orderId) {
    if (!orderId) {
      this.logResult('دورة حياة الطلب', false, 'لا يوجد orderId للاختبار');
      return;
    }

    console.log('\n🧪 اختبار دورة حياة الطلب...\n'.yellow);

    try {
      // 1. فحص الطلب في حالة pending
      let orderResponse = await axios.get(`${API_BASE_URL}/orders/${orderId}`, {
        headers: { Authorization: `Bearer ${this.authToken}` }
      });

      if (orderResponse.data.status === 'pending') {
        this.logResult('حالة انتظار', true, 'الطلب في حالة انتظار بشكل صحيح');
      }

      // 2. الطباخ يقبل الطلب (preparing) - باستخدام الطباخ الفعلي (khaled)
      await axios.put(`${API_BASE_URL}/orders/${orderId}`, {
        status: 'preparing',
        chefName: TEST_CONFIG.chefName
      }, {
        headers: { Authorization: `Bearer ${this.authToken}` }
      });

      orderResponse = await axios.get(`${API_BASE_URL}/orders/${orderId}`, {
        headers: { Authorization: `Bearer ${this.authToken}` }
      });

      if (orderResponse.data.status === 'preparing') {
        this.logResult('قبول الطباخ', true, `تم قبول الطلب من الطباخ الفعلي ${TEST_CONFIG.chefName} وتغيير الحالة إلى preparing`);
      }

      // 3. الطباخ ينهي التحضير (ready)
      await axios.put(`${API_BASE_URL}/orders/${orderId}`, {
        status: 'ready'
      }, {
        headers: { Authorization: `Bearer ${this.authToken}` }
      });

      orderResponse = await axios.get(`${API_BASE_URL}/orders/${orderId}`, {
        headers: { Authorization: `Bearer ${this.authToken}` }
      });

      if (orderResponse.data.status === 'ready') {
        this.logResult('إنهاء التحضير', true, 'تم إنهاء التحضير وتغيير الحالة إلى ready');
      }

      // 4. تسليم الطلب (delivered)
      await axios.put(`${API_BASE_URL}/orders/${orderId}`, {
        status: 'delivered'
      }, {
        headers: { Authorization: `Bearer ${this.authToken}` }
      });

      orderResponse = await axios.get(`${API_BASE_URL}/orders/${orderId}`, {
        headers: { Authorization: `Bearer ${this.authToken}` }
      });

      if (orderResponse.data.status === 'delivered') {
        this.logResult('تسليم الطلب', true, 'تم تسليم الطلب وإكمال دورة الحياة');
      }

    } catch (error) {
      this.logResult('دورة حياة الطلب', false, 'خطأ في اختبار دورة حياة الطلب', error.response?.data);
    }
  }

  // اختبار 4: تصفية طلبات الطباخ
  async testChefOrderFiltering() {
    console.log('\n🧪 اختبار تصفية طلبات الطباخ...\n'.yellow);

    try {
      // جلب جميع الطلبات
      const allOrdersResponse = await axios.get(`${API_BASE_URL}/orders`, {
        headers: { Authorization: `Bearer ${this.authToken}` }
      });

      const allOrders = allOrdersResponse.data;

      // تصفية الطلبات قيد الانتظار
      const pendingOrders = allOrders.filter(order => order.status === 'pending');
      this.logResult('طلبات قيد الانتظار', true, `تم العثور على ${pendingOrders.length} طلب قيد الانتظار`);

      // تصفية الطلبات قيد التحضير للطباخ الفعلي
      const preparingOrders = allOrders.filter(order => 
        order.status === 'preparing' && order.chefName === TEST_CONFIG.chefName
      );
      this.logResult('طلبات قيد التحضير', true, `تم العثور على ${preparingOrders.length} طلب قيد التحضير للطباخ الفعلي ${TEST_CONFIG.chefName}`);

      // تصفية الطلبات المكتملة للطباخ الفعلي
      const completedOrders = allOrders.filter(order => 
        ['ready', 'delivered'].includes(order.status) && order.chefName === TEST_CONFIG.chefName
      );
      this.logResult('طلبات مكتملة', true, `تم العثور على ${completedOrders.length} طلب مكتمل للطباخ الفعلي ${TEST_CONFIG.chefName}`);

    } catch (error) {
      this.logResult('تصفية طلبات الطباخ', false, 'فشل في اختبار تصفية الطلبات', error.response?.data);
    }
  }

  // اختبار 5: اختبار الأدوار الفعلية
  async testRealUserRoles() {
    console.log('\n🧪 اختبار الأدوار الفعلية...\n'.yellow);

    const realUsers = [
      { username: 'Beso', expectedRole: 'manager' },
      { username: 'azza', expectedRole: 'waiter' },
      { username: 'khaled', expectedRole: 'chef' },
      { username: 'admin', expectedRole: 'admin' }
    ];

    for (const user of realUsers) {
      try {
        const response = await axios.post(`${API_BASE_URL}/auth/login`, {
          username: user.username,
          password: user.username === 'Beso' ? 'MOHAMEDmostafa123' : 
                   user.username === 'admin' ? 'DeshaCoffee2024Admin!' : '253040'
        });

        if (response.data.user && response.data.user.role === user.expectedRole) {
          this.logResult(`مستخدم فعلي ${user.username}`, true, `تم تسجيل الدخول بنجاح - الدور: ${response.data.user.role}`);
        } else {
          this.logResult(`مستخدم فعلي ${user.username}`, false, `دور غير متطابق - متوقع: ${user.expectedRole}, فعلي: ${response.data.user?.role}`);
        }
      } catch (error) {
        this.logResult(`مستخدم فعلي ${user.username}`, false, 'فشل في تسجيل الدخول', error.response?.data);
      }
    }
  }

  // اختبار 6: نظام الإشعارات (محاكاة)
  async testNotificationSystem() {
    console.log('\n🧪 اختبار نظام الإشعارات (محاكاة)...\n'.yellow);

    // محاكاة اختبار نظام الإشعارات
    const notificationTests = [
      {
        event: 'newOrder',
        description: 'إشعار طلب جديد للطباخ',
        expected: 'يجب أن يصل للطباخ والمدير'
      },
      {
        event: 'orderAccepted', 
        description: 'إشعار قبول الطلب للنادل',
        expected: 'يجب أن يصل للنادل والمدير'
      },
      {
        event: 'orderReady',
        description: 'إشعار جاهزية الطلب للنادل',
        expected: 'يجب أن يصل للنادل والمدير'
      }
    ];

    notificationTests.forEach(test => {
      this.logResult(`إشعار ${test.event}`, true, `${test.description} - ${test.expected}`, {
        note: 'هذا اختبار محاكاة - يتطلب Socket.IO للاختبار الفعلي'
      });
    });
  }

  // تشغيل جميع الاختبارات
  async runAllTests() {
    console.log('🚀 بدء اختبارات سير العمل مع المستخدمين الفعليين...\n'.cyan.bold);

    // المصادقة
    const authenticated = await this.authenticate();
    if (!authenticated) {
      console.log('❌ فشل في المصادقة - توقف الاختبار'.red);
      return;
    }

    // اختبار الأدوار الفعلية
    await this.testRealUserRoles();

    // اختبار إنشاء الطلبات
    const createdOrder = await this.testWaiterOrderCreation();

    // اختبار إدارة الطاولات
    await this.testTableManagement();

    // اختبار دورة حياة الطلب
    if (createdOrder && createdOrder._id) {
      await this.testOrderLifecycle(createdOrder._id);
    }

    // اختبار تصفية طلبات الطباخ
    await this.testChefOrderFiltering();

    // اختبار نظام الإشعارات
    await this.testNotificationSystem();

    // عرض النتائج النهائية
    this.showFinalResults();
  }

  // عرض النتائج النهائية
  showFinalResults() {
    console.log('\n📊 ملخص نتائج الاختبار للمستخدمين الفعليين\n'.cyan.bold);
    console.log('='.repeat(60).gray);

    const passed = this.testResults.filter(r => r.passed).length;
    const failed = this.testResults.filter(r => r.passed === false).length;
    const total = this.testResults.length;

    console.log(`✅ نجح: ${passed}`.green);
    console.log(`❌ فشل: ${failed}`.red);
    console.log(`📊 المجموع: ${total}`);
    console.log(`📈 النسبة: ${((passed / total) * 100).toFixed(1)}%`);

    console.log('\n📋 تفاصيل النتائج:\n'.yellow);

    this.testResults.forEach((result, index) => {
      const status = result.passed ? '✅' : '❌';
      console.log(`${index + 1}. ${status} ${result.test}: ${result.message}`);
    });

    // حفظ النتائج في ملف
    const reportData = {
      summary: { passed, failed, total, percentage: ((passed / total) * 100).toFixed(1) },
      tests: this.testResults,
      realUsers: ['Beso', 'azza', 'khaled', 'admin'],
      timestamp: new Date().toISOString()
    };

    fs.writeFileSync('production-workflow-test-results.json', JSON.stringify(reportData, null, 2));
    console.log('\n💾 تم حفظ النتائج في production-workflow-test-results.json'.green);
    
    console.log('\n🎉 اكتمل اختبار النظام مع المستخدمين الفعليين!'.green.bold);
  }
}

// تشغيل الاختبارات
const tester = new WorkflowTester();
tester.runAllTests().catch(error => {
  console.error('خطأ في تشغيل الاختبارات:', error);
  process.exit(1);
});

export default WorkflowTester;
