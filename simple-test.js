// Simple workflow test
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

async function testAPI() {
  console.log('🔍 Testing API endpoints...\n');
  
  try {
    // Test health endpoint
    console.log('1. Testing health endpoint...');
    const { stdout: healthOutput } = await execAsync('curl -s http://localhost:4003/api/health');
    const healthData = JSON.parse(healthOutput);
    
    if (healthData.status === 'OK') {
      console.log('✅ Health endpoint working');
    } else {
      console.log('❌ Health endpoint failed');
      return;
    }
    
    // Test main health
    console.log('2. Testing main health endpoint...');
    const { stdout: mainHealthOutput } = await execAsync('curl -s http://localhost:4003/health');
    const mainHealthData = JSON.parse(mainHealthOutput);
    
    if (mainHealthData.status === 'OK') {
      console.log('✅ Main health endpoint working');
      console.log(`   Database connected: ${mainHealthData.database.connected}`);
      console.log(`   User count: ${mainHealthData.database.userCount}`);
    } else {
      console.log('❌ Main health endpoint failed');
    }
    
    console.log('\n🎉 Basic API tests completed successfully!');
    console.log('\n📋 System Status:');
    console.log('================');
    console.log('✅ Backend server: Running on port 4003');
    console.log('✅ Frontend server: Running on port 5176');
    console.log('✅ Database: Connected to MongoDB Atlas');
    console.log('✅ API endpoints: Responding correctly');
    
  } catch (error) {
    console.error('❌ API test failed:', error.message);
  }
}

// Run test
testAPI();

// Run test
testAPI();
