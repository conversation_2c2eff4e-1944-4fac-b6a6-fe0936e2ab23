# دليل اختبار نظام الإشعارات والـ Socket.IO

## خطوات الاختبار السريع

### 1. التحضير
1. تأكد من تشغيل Backend Server على `http://localhost:3001`
2. تأكد من تشغيل Frontend Server على `http://localhost:5173`
3. افتح متصفحين أو تبويبين مختلفين

### 2. اختبار تسجيل الدخول والتنقل
1. **في التبويب الأول - الطباخ**:
   - سجل دخول كطباخ
   - انتقل إلى ChefDashboard
   - تحقق من عدم وجود أخطاء في Console

2. **في التبويب الثاني - النادل**:
   - سجل دخول كنادل
   - انتقل إلى WaiterDashboard
   - تحقق من عدم وجود أخطاء في Console

### 3. اختبار تدفق الطلبات الكامل

#### أ. إنشاء طلب جديد (النادل)
1. في تبويب النادل:
   - انتقل إلى قسم "المشروبات"
   - أضف مشروبات إلى السلة
   - أدخل رقم طاولة واسم عميل
   - اضغط "إرسال الطلب"

2. **النتيجة المتوقعة**:
   - رسالة نجاح للنادل
   - ظهور الطلب في قائمة طلبات النادل
   - **في تبويب الطباخ**: ظهور إشعار طلب جديد فوراً

#### ب. قبول الطلب (الطباخ)
1. في تبويب الطباخ:
   - تحقق من ظهور الطلب الجديد في "الطلبات المعلقة"
   - اضغط "قبول الطلب"

2. **النتيجة المتوقعة**:
   - نقل الطلب إلى "الطلبات المكتملة" في الطباخ
   - **في تبويب النادل**: تحديث حالة الطلب إلى "قيد التحضير"
   - ظهور إشعار للنادل

#### ج. إكمال التحضير (الطباخ)
1. في تبويب الطباخ:
   - ابحث عن الطلب في "الطلبات المكتملة"
   - اضغط "إنهاء التحضير"

2. **النتيجة المتوقعة**:
   - إزالة الطلب من قائمة الطباخ
   - **في تبويب النادل**: تحديث حالة الطلب إلى "جاهز"
   - ظهور إشعار "الطلب جاهز للتسليم"

#### د. تسليم الطلب (النادل)
1. في تبويب النادل:
   - انتقل إلى قسم "الطلبات"
   - ابحث عن الطلب الجاهز
   - اضغط "تم التسليم"

2. **النتيجة المتوقعة**:
   - تحديث حالة الطلب إلى "تم التسليم"
   - تحديث حسابات الطاولات
   - إشعار نجاح التسليم

### 4. اختبار الإشعارات في Console

#### فتح Console المطوّر
1. اضغط `F12` أو `Ctrl+Shift+I`
2. انتقل إلى تبويب Console
3. راقب الرسائل التالية:

#### رسائل الطباخ المتوقعة:
```
✅ تم تسجيل الطباخ في Socket.IO
🔔 طلب جديد وصل للطباخ: [Order Data]
📤 تم إرسال إشعار قبول الطلب
📤 تم إرسال إشعار إكمال الطلب
```

#### رسائل النادل المتوقعة:
```
✅ Waiter registered in Socket.IO
🔔 New order created notification
🔄 Order status updated: [Status Data]
📤 تم إرسال إشعار تسليم الطلب
```

### 5. اختبار Socket Events يدوياً

#### استخدام ملف الاختبار
1. في Console المطوّر، اكتب:
```javascript
// تحميل ملف الاختبار
const script = document.createElement('script');
script.src = '/src/test-socket-events.js';
document.head.appendChild(script);

// بعد تحميل الملف
socketTests.runAllTests();
```

2. راقب النتائج في Console

### 6. اختبارات إضافية

#### أ. اختبار إعادة الاتصال
1. في Chrome DevTools:
   - انتقل إلى Network
   - اختر "Offline" لمحاكاة انقطاع الاتصال
   - انتظر 5 ثواني
   - اختر "Online" مرة أخرى

2. **النتيجة المتوقعة**:
   - رسائل انقطاع واتصال في Console
   - إعادة تسجيل المستخدم تلقائياً

#### ب. اختبار Real-time Updates
1. افتح عدة تبويبات بنفس الدور (نادل/طباخ)
2. قم بعمل تحديثات في تبويب واحد
3. تحقق من التحديث الفوري في التبويبات الأخرى

### 7. استكشاف الأخطاء

#### مشاكل شائعة وحلولها:

1. **لا تظهر إشعارات**:
   - تحقق من تشغيل Backend Server
   - تحقق من Socket connection في Network tab
   - تحقق من Console errors

2. **تأخير في الإشعارات**:
   - طبيعي - هناك timeout 500ms مضافة لضمان تحديث قاعدة البيانات

3. **عدم تحديث القوائم**:
   - تحقق من استدعاء `fetchOrders()` و `fetchTableAccounts()`
   - تحقق من Socket listeners

#### التحقق من Socket Connection:
```javascript
// في Console
const socket = window.io();
console.log('Socket connected:', socket.connected);
console.log('Socket ID:', socket.id);
```

## توقيت الاختبار المثالي

### سيناريو كامل (5-10 دقائق):
1. **دقيقة 1**: تسجيل دخول الطباخ والنادل
2. **دقيقة 2**: إنشاء طلب من النادل
3. **دقيقة 3**: قبول الطلب من الطباخ
4. **دقيقة 4**: إكمال التحضير
5. **دقيقة 5**: تسليم الطلب من النادل

### نقاط التحقق الحاسمة:
- ✅ إشعارات فورية بين النادل والطباخ
- ✅ تحديث حالة الطلبات real-time
- ✅ تحديث حسابات الطاولات
- ✅ عدم وجود أخطاء في Console
- ✅ تدفق منطقي للبيانات

## الخلاصة

إذا نجحت جميع الاختبارات أعلاه، فإن نظام الإشعارات والـ Socket.IO يعمل بشكل صحيح ومتكامل.
