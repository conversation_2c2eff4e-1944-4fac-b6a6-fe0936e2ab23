// اختبار سريع لـ Socket Events
// هذا الملف يمكن تشغيله في Console للاختبار

// 1. اختبار تسجيل الطباخ
function testChefRegistration() {
  const socket = window.io();
  socket.emit('register-user', {
    userId: 'test-chef-123',
    role: 'chef',
    name: 'طباخ الاختبار'
  });

  socket.on('registration-confirmed', (data) => {
    console.log('✅ تم تسجيل الطباخ:', data);
  });
}

// 2. اختبار تسجيل النادل
function testWaiterRegistration() {
  const socket = window.io();
  socket.emit('register-user', {
    userId: 'test-waiter-123',
    role: 'waiter',
    name: 'نادل الاختبار'
  });

  socket.on('registration-confirmed', (data) => {
    console.log('✅ تم تسجيل النادل:', data);
  });
}

// 3. اختبار إنشاء طلب جديد
function testOrderCreation() {
  const socket = window.io();
  socket.emit('order-created', {
    orderId: 'test-order-123',
    orderNumber: 'ORD-123',
    tableNumber: '5',
    waiterName: 'نادل الاختبار',
    items: [
      { name: 'قهوة عربية', quantity: 2, price: 15 },
      { name: 'شاي', quantity: 1, price: 10 }
    ],
    status: 'pending',
    customer: 'عميل الاختبار',
    total: 40,
    timestamp: new Date().toISOString()
  });

  console.log('📤 تم إرسال طلب الاختبار');
}

// 4. اختبار تحديث حالة الطلب
function testOrderStatusUpdate() {
  const socket = window.io();
  socket.emit('order-status-update', {
    orderId: 'test-order-123',
    orderNumber: 'ORD-123',
    newStatus: 'preparing',
    chefName: 'طباخ الاختبار',
    waiterName: 'نادل الاختبار',
    tableNumber: '5',
    customer: 'عميل الاختبار',
    items: [
      { name: 'قهوة عربية', quantity: 2, price: 15 }
    ],
    timestamp: new Date().toISOString()
  });

  console.log('📤 تم إرسال تحديث حالة الطلب');
}

// 5. اختبار استقبال الإشعارات
function testReceiveNotifications() {
  const socket = window.io();

  socket.on('new-order-notification', (data) => {
    console.log('🔔 إشعار طلب جديد:', data);
  });

  socket.on('order-status-update', (data) => {
    console.log('🔄 تحديث حالة طلب:', data);
  });

  socket.on('table-status-updated', (data) => {
    console.log('🪑 تحديث حالة طاولة:', data);
  });

  console.log('👂 تم تفعيل استقبال الإشعارات');
}

// تشغيل جميع الاختبارات
function runAllTests() {
  console.log('🧪 بدء اختبارات Socket.IO...');
  
  setTimeout(() => testChefRegistration(), 1000);
  setTimeout(() => testWaiterRegistration(), 2000);
  setTimeout(() => testReceiveNotifications(), 3000);
  setTimeout(() => testOrderCreation(), 4000);
  setTimeout(() => testOrderStatusUpdate(), 5000);
  
  console.log('⏱️ سيتم تشغيل جميع الاختبارات خلال 5 ثواني');
}

// تصدير الدوال للاستخدام
window.socketTests = {
  testChefRegistration,
  testWaiterRegistration,
  testOrderCreation,
  testOrderStatusUpdate,
  testReceiveNotifications,
  runAllTests
};

console.log('🔧 تم تحميل اختبارات Socket.IO');
console.log('📋 الدوال المتاحة: socketTests.runAllTests()');
