#!/usr/bin/env node

/**
 * Chef Dashboard Integration Test
 * Tests the complete workflow: Order Creation → Chef Takes Order → Chef Completes Order
 * With Socket.IO notifications verification
 */

import fetch from 'node-fetch';
import { io } from 'socket.io-client';

const BASE_URL = 'http://localhost:3001';
const SOCKET_URL = 'http://localhost:3001';

class ChefDashboardTester {
    constructor() {
        this.socket = null;
        this.notifications = [];
        this.testResults = {
            orderCreation: false,
            chefTakesOrder: false,
            chefCompletesOrder: false,
            socketNotifications: false,
            uiUpdates: false
        };
    }

    async initializeSocket() {
        return new Promise((resolve) => {
            this.socket = io(SOCKET_URL);
            
            this.socket.on('connect', () => {
                console.log('✅ Socket.IO connected');
                resolve();
            });

            // Listen for all chef-related events
            this.socket.on('newOrder', (data) => {
                this.notifications.push({ type: 'newOrder', data, timestamp: new Date() });
                console.log('📢 New Order notification received:', data.orderNumber);
            });

            this.socket.on('orderUpdated', (data) => {
                this.notifications.push({ type: 'orderUpdated', data, timestamp: new Date() });
                console.log('📢 Order Updated notification:', data.orderNumber, data.status);
            });

            this.socket.on('orderTakenByChef', (data) => {
                this.notifications.push({ type: 'orderTakenByChef', data, timestamp: new Date() });
                console.log('📢 Order Taken by Chef:', data.orderNumber);
            });

            this.socket.on('orderCompletedByChef', (data) => {
                this.notifications.push({ type: 'orderCompletedByChef', data, timestamp: new Date() });
                console.log('📢 Order Completed by Chef:', data.orderNumber);
            });
        });
    }

    async createTestOrder() {
        console.log('\n🔄 Creating test order...');
        
        const orderData = {
            tableNumber: Math.floor(Math.random() * 10) + 1,
            customerName: `Test Customer ${Date.now()}`,
            items: [
                {
                    productId: '1',
                    productName: 'Espresso',
                    quantity: 2,
                    price: 3.50,
                    category: 'coffee'
                },
                {
                    productId: '5',
                    productName: 'Croissant',
                    quantity: 1,
                    price: 2.50,
                    category: 'pastry'
                }
            ],
            totalPrice: 9.50,
            status: 'pending'
        };

        try {
            const response = await fetch(`${BASE_URL}/api/orders`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(orderData)
            });

            if (response.ok) {
                const createdOrder = await response.json();
                console.log('✅ Test order created:', createdOrder.orderNumber);
                this.testResults.orderCreation = true;
                return createdOrder;
            } else {
                throw new Error(`Failed to create order: ${response.status}`);
            }
        } catch (error) {
            console.error('❌ Failed to create test order:', error.message);
            return null;
        }
    }

    async getOrdersForChef() {
        try {
            const response = await fetch(`${BASE_URL}/api/orders`);
            if (response.ok) {
                const orders = await response.json();
                return orders.filter(order => 
                    order.status === 'pending' || 
                    order.status === 'preparing' || 
                    order.status === 'ready'
                );
            }
        } catch (error) {
            console.error('❌ Failed to fetch orders:', error.message);
        }
        return [];
    }

    async chefTakesOrder(orderId) {
        console.log('\n🔄 Chef taking order...');
        
        try {
            const response = await fetch(`${BASE_URL}/api/orders/${orderId}/take`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ chefId: 'test-chef-001' })
            });

            if (response.ok) {
                const updatedOrder = await response.json();
                console.log('✅ Chef successfully took order:', updatedOrder.orderNumber);
                this.testResults.chefTakesOrder = true;
                return updatedOrder;
            } else {
                throw new Error(`Failed to take order: ${response.status}`);
            }
        } catch (error) {
            console.error('❌ Failed for chef to take order:', error.message);
            return null;
        }
    }

    async chefCompletesOrder(orderId) {
        console.log('\n🔄 Chef completing order...');
        
        try {
            const response = await fetch(`${BASE_URL}/api/orders/${orderId}/complete`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ completedBy: 'test-chef-001' })
            });

            if (response.ok) {
                const completedOrder = await response.json();
                console.log('✅ Chef successfully completed order:', completedOrder.orderNumber);
                this.testResults.chefCompletesOrder = true;
                return completedOrder;
            } else {
                throw new Error(`Failed to complete order: ${response.status}`);
            }
        } catch (error) {
            console.error('❌ Failed for chef to complete order:', error.message);
            return null;
        }
    }

    async wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async runCompleteWorkflowTest() {
        console.log('🧪 Starting Chef Dashboard Workflow Test\n');
        console.log('=' .repeat(60));

        try {
            // Initialize Socket.IO connection
            await this.initializeSocket();
            await this.wait(1000);

            // Step 1: Create a test order
            const newOrder = await this.createTestOrder();
            if (!newOrder) return this.generateReport();
            
            await this.wait(2000); // Wait for Socket.IO propagation

            // Step 2: Chef takes the order
            const takenOrder = await this.chefTakesOrder(newOrder._id);
            if (!takenOrder) return this.generateReport();
            
            await this.wait(2000); // Wait for Socket.IO propagation

            // Step 3: Chef completes the order
            const completedOrder = await this.chefCompletesOrder(newOrder._id);
            if (!completedOrder) return this.generateReport();
            
            await this.wait(2000); // Wait for Socket.IO propagation

            // Verify Socket.IO notifications
            this.verifySocketNotifications();

            // Test UI data fetching
            await this.testUIDataFetching();

        } catch (error) {
            console.error('❌ Test workflow failed:', error.message);
        }

        this.generateReport();
    }

    verifySocketNotifications() {
        console.log('\n🔄 Verifying Socket.IO notifications...');
        
        const expectedEvents = ['newOrder', 'orderTakenByChef', 'orderCompletedByChef'];
        const receivedEvents = this.notifications.map(n => n.type);
        
        const allEventsReceived = expectedEvents.every(event => 
            receivedEvents.includes(event)
        );

        if (allEventsReceived) {
            console.log('✅ All required Socket.IO notifications received');
            this.testResults.socketNotifications = true;
        } else {
            console.log('❌ Missing Socket.IO notifications');
            console.log('Expected:', expectedEvents);
            console.log('Received:', receivedEvents);
        }
    }

    async testUIDataFetching() {
        console.log('\n🔄 Testing UI data fetching...');
        
        try {
            const orders = await this.getOrdersForChef();
            if (orders && orders.length >= 0) {
                console.log(`✅ UI data fetching successful (${orders.length} orders)`);
                this.testResults.uiUpdates = true;
            } else {
                console.log('❌ UI data fetching failed');
            }
        } catch (error) {
            console.log('❌ UI data fetching error:', error.message);
        }
    }

    generateReport() {
        console.log('\n📊 TEST RESULTS SUMMARY');
        console.log('=' .repeat(60));
        
        const results = [
            { test: 'Order Creation', status: this.testResults.orderCreation },
            { test: 'Chef Takes Order', status: this.testResults.chefTakesOrder },
            { test: 'Chef Completes Order', status: this.testResults.chefCompletesOrder },
            { test: 'Socket.IO Notifications', status: this.testResults.socketNotifications },
            { test: 'UI Data Updates', status: this.testResults.uiUpdates }
        ];

        results.forEach(result => {
            const icon = result.status ? '✅' : '❌';
            console.log(`${icon} ${result.test}`);
        });

        const passedTests = results.filter(r => r.status).length;
        const totalTests = results.length;
        const successRate = Math.round((passedTests / totalTests) * 100);

        console.log('\n📈 OVERALL RESULT:');
        console.log(`${passedTests}/${totalTests} tests passed (${successRate}%)`);
        
        if (successRate === 100) {
            console.log('🎉 Chef Dashboard is working perfectly!');
        } else if (successRate >= 80) {
            console.log('✅ Chef Dashboard is mostly working with minor issues');
        } else {
            console.log('⚠️  Chef Dashboard needs attention');
        }

        console.log('\n📋 Socket.IO Event Log:');
        this.notifications.forEach(notification => {
            console.log(`  ${notification.timestamp.toISOString()} - ${notification.type}`);
        });

        // Close socket connection
        if (this.socket) {
            this.socket.disconnect();
        }

        // Save results to file
        this.saveResultsToFile(results, successRate);
    }

    async saveResultsToFile(results, successRate) {
        const reportData = {
            timestamp: new Date().toISOString(),
            testResults: results,
            successRate: successRate,
            notifications: this.notifications,
            summary: {
                totalTests: results.length,
                passedTests: results.filter(r => r.status).length,
                status: successRate === 100 ? 'PERFECT' : successRate >= 80 ? 'GOOD' : 'NEEDS_ATTENTION'
            }
        };

        try {
            const fs = await import('fs');
            fs.writeFileSync('chef-dashboard-test-results.json', JSON.stringify(reportData, null, 2));
            console.log('\n💾 Test results saved to chef-dashboard-test-results.json');
        } catch (error) {
            console.log('⚠️  Could not save test results to file:', error.message);
        }
    }
}

// Run the test
const tester = new ChefDashboardTester();
tester.runCompleteWorkflowTest().catch(console.error);
