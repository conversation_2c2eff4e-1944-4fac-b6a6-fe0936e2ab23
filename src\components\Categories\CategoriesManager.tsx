import React, { useState, useEffect } from 'react';
import { authenticatedGet, authenticatedPost, authenticatedPut, authenticatedDelete } from '../../utils/apiHelpers';
import { useToast } from '../../hooks/useToast';
import socket from '../../socket';
import './CategoriesManager.css';

interface Category {
  _id: string;
  name: string;
  description: string;
  color: string;
  icon: string;
  isActive: boolean;
  itemsCount: number;
  order: number;
  createdAt: string;
  updatedAt: string;
}

const CategoriesManager: React.FC = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  const { showSuccess, showError } = useToast();

  // الألوان المتاحة
  const availableColors = [
    '#3498db', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6',
    '#1abc9c', '#34495e', '#e67e22', '#95a5a6', '#f1c40f'
  ];

  // الأيقونات المتاحة
  const availableIcons = [
    'fa-coffee', 'fa-tea', 'fa-glass-whiskey', 'fa-wine-glass',
    'fa-cocktail', 'fa-beer', 'fa-ice-cream', 'fa-cookie',
    'fa-birthday-cake', 'fa-apple-alt', 'fa-lemon', 'fa-pepper-hot'
  ];

  // جلب الفئات
  const fetchCategories = async () => {
    try {
      setLoading(true);
      const response = await authenticatedGet('/api/categories');
      const categoriesData = response.data || [];
      
      // ترتيب الفئات حسب الترتيب المحدد
      const sortedCategories = categoriesData.sort((a: Category, b: Category) => a.order - b.order);
      setCategories(sortedCategories);
      
    } catch (error) {
      console.error('خطأ في جلب الفئات:', error);
      showError('فشل في تحميل الفئات');
      
      // بيانات تجريبية
      setCategories([
        {
          _id: '1',
          name: 'قهوة ساخنة',
          description: 'جميع أنواع القهوة الساخنة',
          color: '#8B4513',
          icon: 'fa-coffee',
          isActive: true,
          itemsCount: 8,
          order: 1,
          createdAt: '2024-01-01',
          updatedAt: '2024-01-01'
        },
        {
          _id: '2',
          name: 'مشروبات باردة',
          description: 'المشروبات الباردة والعصائر',
          color: '#3498db',
          icon: 'fa-glass-whiskey',
          isActive: true,
          itemsCount: 5,
          order: 2,
          createdAt: '2024-01-01',
          updatedAt: '2024-01-01'
        },
        {
          _id: '3',
          name: 'شاي ومشروبات ساخنة',
          description: 'الشاي والمشروبات الساخنة الأخرى',
          color: '#27ae60',
          icon: 'fa-tea',
          isActive: true,
          itemsCount: 6,
          order: 3,
          createdAt: '2024-01-01',
          updatedAt: '2024-01-01'
        },
        {
          _id: '4',
          name: 'حلويات',
          description: 'الحلويات والكيك',
          color: '#e74c3c',
          icon: 'fa-birthday-cake',
          isActive: false,
          itemsCount: 0,
          order: 4,
          createdAt: '2024-01-01',
          updatedAt: '2024-01-01'
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  // حفظ الفئة
  const saveCategory = async (categoryData: Partial<Category>) => {
    try {
      let response;
      
      if (selectedCategory) {
        response = await authenticatedPut(`/api/categories/${selectedCategory._id}`, categoryData);
      } else {
        // تحديد الترتيب للفئة الجديدة
        const maxOrder = Math.max(...categories.map(cat => cat.order), 0);
        const newCategoryData = { ...categoryData, order: maxOrder + 1 };
        response = await authenticatedPost('/api/categories', newCategoryData);
      }
      
      if (response.success) {
        showSuccess(selectedCategory ? 'تم تحديث الفئة بنجاح' : 'تم إضافة الفئة بنجاح');
        
        if (socket) {
          socket.emit('categories-updated', {
            action: selectedCategory ? 'updated' : 'added',
            category: response.data
          });
        }
        
        fetchCategories();
        setShowCategoryModal(false);
        setSelectedCategory(null);
      }
    } catch (error) {
      console.error('خطأ في حفظ الفئة:', error);
      showError('فشل في حفظ الفئة');
    }
  };

  // حذف الفئة
  const deleteCategory = async (categoryId: string) => {
    if (!window.confirm('هل أنت متأكد من حذف هذه الفئة؟ سيتم حذف جميع العناصر المرتبطة بها.')) {
      return;
    }

    try {
      const response = await authenticatedDelete(`/api/categories/${categoryId}`);
      
      if (response.success) {
        showSuccess('تم حذف الفئة بنجاح');
        
        if (socket) {
          socket.emit('categories-updated', {
            action: 'deleted',
            categoryId
          });
        }
        
        fetchCategories();
      }
    } catch (error) {
      console.error('خطأ في حذف الفئة:', error);
      showError('فشل في حذف الفئة');
    }
  };

  // تبديل حالة التفعيل
  const toggleActive = async (category: Category) => {
    try {
      const response = await authenticatedPut(`/api/categories/${category._id}`, {
        isActive: !category.isActive
      });
      
      if (response.success) {
        showSuccess(`تم ${!category.isActive ? 'تفعيل' : 'إلغاء'} الفئة`);
        
        if (socket) {
          socket.emit('categories-updated', {
            action: 'status_changed',
            category: response.data
          });
        }
        
        fetchCategories();
      }
    } catch (error) {
      console.error('خطأ في تغيير حالة الفئة:', error);
      showError('فشل في تغيير حالة الفئة');
    }
  };

  // تحديث ترتيب الفئات
  const updateCategoryOrder = async (categoryId: string, newOrder: number) => {
    try {
      const response = await authenticatedPut(`/api/categories/${categoryId}`, {
        order: newOrder
      });
      
      if (response.success) {
        fetchCategories();
      }
    } catch (error) {
      console.error('خطأ في تحديث الترتيب:', error);
      showError('فشل في تحديث الترتيب');
    }
  };

  // نقل الفئة لأعلى
  const moveCategoryUp = (category: Category) => {
    const currentIndex = categories.findIndex(cat => cat._id === category._id);
    if (currentIndex > 0) {
      const previousCategory = categories[currentIndex - 1];
      updateCategoryOrder(category._id, previousCategory.order);
      updateCategoryOrder(previousCategory._id, category.order);
    }
  };

  // نقل الفئة لأسفل
  const moveCategoryDown = (category: Category) => {
    const currentIndex = categories.findIndex(cat => cat._id === category._id);
    if (currentIndex < categories.length - 1) {
      const nextCategory = categories[currentIndex + 1];
      updateCategoryOrder(category._id, nextCategory.order);
      updateCategoryOrder(nextCategory._id, category.order);
    }
  };

  // إعداد Socket.IO
  useEffect(() => {
    if (socket) {
      socket.on('categories-updated', () => {
        fetchCategories();
      });
    }

    return () => {
      if (socket) {
        socket.off('categories-updated');
      }
    };
  }, []);

  // تحميل البيانات الأولية
  useEffect(() => {
    fetchCategories();
  }, []);

  // فلترة الفئات
  const getFilteredCategories = () => {
    if (!searchTerm) return categories;
    
    return categories.filter(category => 
      category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      category.description.toLowerCase().includes(searchTerm.toLowerCase())
    );
  };

  if (loading) {
    return (
      <div className="categories-manager loading">
        <div className="loading-spinner">
          <i className="fas fa-spinner fa-spin"></i>
          <span>جاري تحميل الفئات...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="categories-manager">
      {/* Header */}
      <div className="categories-header">
        <div className="header-left">
          <h1>
            <i className="fas fa-tags"></i>
            إدارة فئات المشروبات
          </h1>
          <p>تنظيم وإدارة فئات القائمة مع إمكانية الترتيب والتخصيص</p>
        </div>
        
        <div className="header-actions">
          <button 
            className="btn-new-category"
            onClick={() => {
              setSelectedCategory(null);
              setShowCategoryModal(true);
            }}
          >
            <i className="fas fa-plus"></i>
            إضافة فئة جديدة
          </button>
        </div>
      </div>

      {/* Stats */}
      <div className="categories-stats">
        <div className="stat-card total">
          <div className="stat-icon">
            <i className="fas fa-tags"></i>
          </div>
          <div className="stat-content">
            <h3>{categories.length}</h3>
            <p>إجمالي الفئات</p>
          </div>
        </div>

        <div className="stat-card active">
          <div className="stat-icon">
            <i className="fas fa-check-circle"></i>
          </div>
          <div className="stat-content">
            <h3>{categories.filter(cat => cat.isActive).length}</h3>
            <p>فئات نشطة</p>
          </div>
        </div>

        <div className="stat-card inactive">
          <div className="stat-icon">
            <i className="fas fa-times-circle"></i>
          </div>
          <div className="stat-content">
            <h3>{categories.filter(cat => !cat.isActive).length}</h3>
            <p>فئات غير نشطة</p>
          </div>
        </div>

        <div className="stat-card items">
          <div className="stat-icon">
            <i className="fas fa-coffee"></i>
          </div>
          <div className="stat-content">
            <h3>{categories.reduce((sum, cat) => sum + cat.itemsCount, 0)}</h3>
            <p>إجمالي العناصر</p>
          </div>
        </div>
      </div>

      {/* Search */}
      <div className="categories-search">
        <div className="search-group">
          <i className="fas fa-search"></i>
          <input
            type="text"
            placeholder="البحث في الفئات..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <button 
          className="refresh-btn"
          onClick={fetchCategories}
          title="تحديث البيانات"
        >
          <i className="fas fa-sync-alt"></i>
          تحديث
        </button>
      </div>

      {/* Categories List */}
      <div className="categories-list">
        {getFilteredCategories().length === 0 ? (
          <div className="no-categories">
            <i className="fas fa-tags"></i>
            <p>لا توجد فئات تطابق البحث</p>
          </div>
        ) : (
          <div className="categories-grid">
            {getFilteredCategories().map((category, index) => (
              <div 
                key={category._id} 
                className={`category-card ${!category.isActive ? 'inactive' : ''}`}
              >
                <div className="category-header">
                  <div className="category-icon" style={{ backgroundColor: category.color }}>
                    <i className={`fas ${category.icon}`}></i>
                  </div>
                  <div className="category-info">
                    <h3>{category.name}</h3>
                    <p>{category.description}</p>
                  </div>
                  <div className={`category-status ${category.isActive ? 'active' : 'inactive'}`}>
                    <i className={`fas ${category.isActive ? 'fa-check-circle' : 'fa-times-circle'}`}></i>
                  </div>
                </div>

                <div className="category-details">
                  <div className="detail-item">
                    <i className="fas fa-coffee"></i>
                    <span>{category.itemsCount} عنصر</span>
                  </div>
                  <div className="detail-item">
                    <i className="fas fa-sort"></i>
                    <span>ترتيب: {category.order}</span>
                  </div>
                  <div className="detail-item">
                    <i className="fas fa-calendar"></i>
                    <span>{new Date(category.createdAt).toLocaleDateString('ar-EG')}</span>
                  </div>
                </div>

                <div className="category-actions">
                  <div className="order-controls">
                    <button 
                      className="order-btn up"
                      onClick={() => moveCategoryUp(category)}
                      disabled={index === 0}
                      title="نقل لأعلى"
                    >
                      <i className="fas fa-chevron-up"></i>
                    </button>
                    <button 
                      className="order-btn down"
                      onClick={() => moveCategoryDown(category)}
                      disabled={index === categories.length - 1}
                      title="نقل لأسفل"
                    >
                      <i className="fas fa-chevron-down"></i>
                    </button>
                  </div>

                  <div className="action-buttons">
                    <button 
                      className="action-btn edit"
                      onClick={() => {
                        setSelectedCategory(category);
                        setShowCategoryModal(true);
                      }}
                      title="تعديل"
                    >
                      <i className="fas fa-edit"></i>
                    </button>
                    
                    <button 
                      className={`action-btn toggle ${category.isActive ? 'disable' : 'enable'}`}
                      onClick={() => toggleActive(category)}
                      title={category.isActive ? 'إلغاء التفعيل' : 'تفعيل'}
                    >
                      <i className={`fas ${category.isActive ? 'fa-eye-slash' : 'fa-eye'}`}></i>
                    </button>
                    
                    <button 
                      className="action-btn delete"
                      onClick={() => deleteCategory(category._id)}
                      title="حذف"
                    >
                      <i className="fas fa-trash"></i>
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Category Modal */}
      {showCategoryModal && (
        <div className="modal-overlay" onClick={() => setShowCategoryModal(false)}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>
                <i className="fas fa-tags"></i>
                {selectedCategory ? 'تعديل الفئة' : 'إضافة فئة جديدة'}
              </h2>
              <button 
                className="modal-close"
                onClick={() => setShowCategoryModal(false)}
              >
                <i className="fas fa-times"></i>
              </button>
            </div>
            
            <div className="modal-body">
              <form onSubmit={(e) => {
                e.preventDefault();
                const formData = new FormData(e.target as HTMLFormElement);
                
                const categoryData = {
                  name: formData.get('name') as string,
                  description: formData.get('description') as string,
                  color: formData.get('color') as string,
                  icon: formData.get('icon') as string,
                  isActive: formData.get('isActive') === 'on'
                };
                
                saveCategory(categoryData);
              }}>
                <div className="form-group">
                  <label htmlFor="name">اسم الفئة:</label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    defaultValue={selectedCategory?.name || ''}
                    required
                    placeholder="أدخل اسم الفئة"
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="description">الوصف:</label>
                  <textarea
                    id="description"
                    name="description"
                    defaultValue={selectedCategory?.description || ''}
                    required
                    placeholder="أدخل وصف الفئة"
                    rows={3}
                  />
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label htmlFor="color">اللون:</label>
                    <div className="color-picker">
                      {availableColors.map(color => (
                        <label key={color} className="color-option">
                          <input
                            type="radio"
                            name="color"
                            value={color}
                            defaultChecked={selectedCategory?.color === color || (!selectedCategory && color === availableColors[0])}
                          />
                          <span 
                            className="color-circle"
                            style={{ backgroundColor: color }}
                          ></span>
                        </label>
                      ))}
                    </div>
                  </div>

                  <div className="form-group">
                    <label htmlFor="icon">الأيقونة:</label>
                    <div className="icon-picker">
                      {availableIcons.map(icon => (
                        <label key={icon} className="icon-option">
                          <input
                            type="radio"
                            name="icon"
                            value={icon}
                            defaultChecked={selectedCategory?.icon === icon || (!selectedCategory && icon === availableIcons[0])}
                          />
                          <span className="icon-preview">
                            <i className={`fas ${icon}`}></i>
                          </span>
                        </label>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="form-group checkbox-group">
                  <label className="checkbox-label">
                    <input
                      type="checkbox"
                      name="isActive"
                      defaultChecked={selectedCategory?.isActive ?? true}
                    />
                    <span className="checkmark"></span>
                    فئة نشطة
                  </label>
                </div>
                
                <div className="modal-footer">
                  <button type="submit" className="btn-confirm">
                    <i className="fas fa-save"></i>
                    {selectedCategory ? 'تحديث' : 'إضافة'}
                  </button>
                  <button 
                    type="button" 
                    className="btn-cancel"
                    onClick={() => setShowCategoryModal(false)}
                  >
                    إلغاء
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CategoriesManager;
