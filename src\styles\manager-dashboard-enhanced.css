/* Enhanced Manager Dashboard Styles - OOP Version */

/* Main Dashboard Container */
.manager-dashboard {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
  overflow: hidden;
}

/* Header Styles */
.manager-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 1rem 2rem;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.menu-toggle {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #2c3e50;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.menu-toggle:hover {
  background: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

.header-title h1 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.8rem;
  font-weight: 600;
}

.header-subtitle {
  color: #7f8c8d;
  font-size: 0.9rem;
  margin-top: 0.25rem;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 1rem;
  background: rgba(52, 152, 219, 0.1);
  border-radius: 25px;
  color: #2c3e50;
}

.user-avatar {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3498db, #2980b9);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 0.9rem;
}

.logout-btn {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.logout-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

/* Main Content */
.main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* Sidebar */
.sidebar {
  width: 280px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  overflow-y: auto;
}

.sidebar.collapsed {
  width: 70px;
}

.nav-menu {
  padding: 1rem 0;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 1.5rem;
  color: #2c3e50;
  text-decoration: none;
  transition: all 0.3s ease;
  border-right: 3px solid transparent;
  cursor: pointer;
}

.nav-item:hover {
  background: rgba(52, 152, 219, 0.1);
  border-right-color: #3498db;
  color: #3498db;
}

.nav-item.active {
  background: rgba(52, 152, 219, 0.15);
  border-right-color: #3498db;
  color: #3498db;
  font-weight: 600;
}

.nav-item i {
  font-size: 1.2rem;
  width: 20px;
  text-align: center;
}

.sidebar.collapsed .nav-item span {
  display: none;
}

/* Content Area */
.content-area {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
  background: transparent;
}

/* Home Screen Styles */
.home-screen {
  max-width: 1400px;
  margin: 0 auto;
}

.dashboard-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.dashboard-header h1 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 2.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.dashboard-time {
  color: #7f8c8d;
  font-size: 1.1rem;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.stat-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 2rem;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.stat-card.orders {
  border-left-color: #3498db;
}

.stat-card.sales {
  border-left-color: #27ae60;
}

.stat-card.employees {
  border-left-color: #e74c3c;
}

.stat-card.tables {
  border-left-color: #f39c12;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.stat-card.orders .stat-icon {
  background: linear-gradient(135deg, #3498db, #2980b9);
}

.stat-card.sales .stat-icon {
  background: linear-gradient(135deg, #27ae60, #229954);
}

.stat-card.employees .stat-icon {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.stat-card.tables .stat-icon {
  background: linear-gradient(135deg, #f39c12, #e67e22);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.stat-label {
  color: #7f8c8d;
  font-size: 1rem;
  font-weight: 500;
}

/* Orders Status Grid */
.orders-status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.status-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.status-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.status-icon {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: white;
}

.status-card.pending .status-icon {
  background: linear-gradient(135deg, #f39c12, #e67e22);
}

.status-card.preparing .status-icon {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.status-card.ready .status-icon {
  background: linear-gradient(135deg, #9b59b6, #8e44ad);
}

.status-card.completed .status-icon {
  background: linear-gradient(135deg, #27ae60, #229954);
}

.status-number {
  font-size: 1.8rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.status-label {
  color: #7f8c8d;
  font-size: 0.9rem;
}

/* Dashboard Content Grid */
.dashboard-content-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  margin-bottom: 3rem;
}

/* Recent Orders Section */
.recent-orders-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.section-header {
  margin-bottom: 1.5rem;
}

.section-header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.3rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.recent-orders-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.recent-order-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 12px;
  border-right: 4px solid #3498db;
  cursor: pointer;
  transition: all 0.3s ease;
}

.recent-order-item:hover {
  background: #e9ecef;
  transform: translateX(-5px);
}

.order-info {
  flex: 1;
}

.order-number {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.order-details {
  font-size: 0.9rem;
  color: #7f8c8d;
  display: flex;
  gap: 1rem;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status-badge.pending {
  background: #f39c12;
  color: white;
}

.status-badge.preparing {
  background: #e74c3c;
  color: white;
}

.status-badge.ready {
  background: #9b59b6;
  color: white;
}

.status-badge.completed {
  background: #27ae60;
  color: white;
}

.order-total {
  font-weight: 600;
  color: #27ae60;
  font-size: 1.1rem;
}

.order-time {
  color: #7f8c8d;
  font-size: 0.9rem;
}

/* Active Employees Section */
.active-employees-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.active-employees-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.employee-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 12px;
  border-right: 4px solid #e74c3c;
}

.employee-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1rem;
}

.employee-info {
  flex: 1;
}

.employee-name {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.employee-role {
  font-size: 0.9rem;
  color: #7f8c8d;
}

.employee-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #27ae60;
  font-size: 0.9rem;
  font-weight: 500;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #27ae60;
}

/* Performance Summary */
.performance-summary {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.performance-header h2 {
  margin: 0 0 1.5rem 0;
  color: #2c3e50;
  font-size: 1.3rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.performance-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1.5rem;
}

.metric {
  text-align: center;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 12px;
  border-top: 4px solid #3498db;
}

.metric-value {
  font-size: 1.8rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.metric-label {
  color: #7f8c8d;
  font-size: 0.9rem;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 3rem 2rem;
  color: #7f8c8d;
}

.empty-state i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-state p {
  margin: 0;
  font-size: 1rem;
}

/* Screen Placeholder */
.screen-placeholder {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 3rem;
  text-align: center;
  color: #7f8c8d;
  font-size: 1.2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .dashboard-content-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .header-content {
    padding: 0 1rem;
  }
  
  .content-area {
    padding: 1rem;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .orders-status-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .performance-metrics {
    grid-template-columns: 1fr;
  }
  
  .sidebar {
    position: fixed;
    left: -280px;
    top: 0;
    height: 100vh;
    z-index: 1000;
  }
  
  .sidebar.open {
    left: 0;
  }
  
  .main-content {
    margin-left: 0;
  }
}
