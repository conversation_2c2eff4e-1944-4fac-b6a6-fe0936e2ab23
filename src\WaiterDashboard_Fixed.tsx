import { APP_CONFIG } from './config/app.config';
import { authenticatedGet, authenticatedPost, authenticatedPut, handleApiError } from './utils/apiHelpers';
import { useToast } from './hooks/useToast';
import { getOrderFinalPrice } from './utils/orderHelpers';
import React, { useState, useEffect, useCallback } from 'react'; 
import './WaiterDashboard.css';

// Define interfaces used in the component
interface MenuItem {
  _id: string;
  name: string;
  price: number;
  description?: string;
  categories?: string[];
  categoryDetails?: Category[];
  available: boolean;
  notes?: string; 
}

interface CartItem extends MenuItem {
  quantity: number;
}

interface Category {
  _id: string;
  name: string;
  color: string;
  icon?: string;
}

interface OrderItem {
  product: string;
  name: string;
  quantity: number;
  price: number;
  notes?: string;
  id?: string;
}

interface Order {
  _id: string;
  orderNumber: string;
  waiterName: string;
  items: OrderItem[];
  totalPrice: number;
  tableNumber: string;
  customerName?: string;
  status: 'pending' | 'preparing' | 'ready' | 'delivered' | 'cancelled';
  createdAt: string;
  tableAccountId?: string;
  discountStatus?: 'pending' | 'approved' | 'rejected';
  discountAmount?: number;
}

interface TableAccount {
  _id: string;
  tableNumber: string;
  waiterId: string;
  waiterName: string;
  waiter?: {
    id: string;
    name?: string;
    username?: string;
  };
  orders: Order[];
  totalAmount: number;
  isOpen: boolean;
  status: 'active' | 'closed';
  createdAt: string;
  updatedAt: string;
  lastActivityAt?: string;
  customerName?: string; 
  paymentMethod?: string;
  discountApplied?: number;
  notes?: string;
}

// Helper function to get API URL
const getApiUrl = () => {
  return APP_CONFIG.API.BASE_URL;
};

export default function WaiterDashboard() {
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [cart, setCart] = useState<CartItem[]>([]);
  const [tableNumber, setTableNumber] = useState('');
  const [customerName, setCustomerName] = useState('');
  const [loading, setLoading] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  
  // Toast notifications
  const {
    toasts,
    removeToast,
    showSuccess,
    showError,
    showInfo
  } = useToast();
  
  // Orders state
  const [orders, setOrders] = useState<Order[]>([]);
  const [orderStatusFilter, setOrderStatusFilter] = useState<'all' | 'pending' | 'preparing' | 'ready' | 'delivered'>('all');
  
  // Screen state
  const [currentScreen, setCurrentScreen] = useState<'drinks' | 'orders' | 'tables' | 'cart'>('drinks');

  // Discount request states
  const [showDiscountModal, setShowDiscountModal] = useState(false);
  const [selectedOrderForDiscount, setSelectedOrderForDiscount] = useState<Order | null>(null);
  const [discountAmount, setDiscountAmount] = useState('');
  const [discountReason, setDiscountReason] = useState('');

  // Table account states
  const [showTableAccountModal, setShowTableAccountModal] = useState(false);
  const [tableAccounts, setTableAccounts] = useState<TableAccount[]>([]);
  const [existingTableAccount, setExistingTableAccount] = useState<any>(null);

  // Order details modal states
  const [showOrderDetailsModal, setShowOrderDetailsModal] = useState(false);
  const [selectedOrderDetails, setSelectedOrderDetails] = useState<Order | null>(null);

  // Table details modal states
  const [showTableDetailsModal, setShowTableDetailsModal] = useState(false);
  const [selectedTableAccountDetails, setSelectedTableAccountDetails] = useState<TableAccount | null>(null);

  // Sidebar states
  const [sidebarOpen, setSidebarOpen] = useState(window.innerWidth > 1024);
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 1024);

  // Helper functions inside component
  const getAccountStatus = (account: any): string => {
    if (!account) return 'غير معروف';
    if (account.isClosed === true) return 'مغلق';
    if (Array.isArray(account.orders) && account.orders.length > 0) return 'مفتوح';
    return 'فارغ';
  };

  const getAccountTotal = (account: any): number => {
    if (!account) return 0;
    if (typeof account.totalAmount === 'number') {
      return account.totalAmount;
    }
    if (Array.isArray(account.orders)) {
      return account.orders.reduce((sum: number, order: any) => {
        const price = (order && typeof order.totalPrice === 'number') ? order.totalPrice : 0;
        return sum + price;
      }, 0);
    }
    return 0;
  };

  const formatTime = (timestamp: string | Date | undefined): string => {
    if (!timestamp) return '-';
    try {
      const date = new Date(timestamp);
      if (isNaN(date.getTime())) {
        console.warn('Invalid date/timestamp provided to formatTime:', timestamp);
        return '-';
      }
      return date.toLocaleTimeString('ar-EG', { hour: '2-digit', minute: '2-digit' });
    } catch (e) {
      console.error("Error formatting time for timestamp:", timestamp, e);
      return '-';
    }
  };

  const closeTableAccount = async (accountId: string) => {
    if (!accountId) {
      if (typeof showError === 'function') {
        showError('معرف الحساب غير صالح.');
      } else {
        console.error('showError function not available. Account ID missing for closeTableAccount.');
      }
      return;
    }
    try {
      const url = `${APP_CONFIG.API.BASE_URL}/table-accounts/${accountId}/close`;
      const response = await authenticatedPut(url, {});

      if (response && response.data) {
        if (typeof showSuccess === 'function') {
          showSuccess('تم إغلاق الحساب بنجاح.');
        } else {
          console.log('Account closed successfully. showSuccess function not available.');
        }
        if (typeof fetchTableAccounts === 'function') {
          fetchTableAccounts();
        } else {
          console.warn('fetchTableAccounts function not available to refresh data after closing account.');
        }
      } else {
        const errorMessage = response?.error || 'فشل في إغلاق الحساب. لم يتم تلقي بيانات تأكيد.';
        if (typeof showError === 'function') {
          showError(errorMessage);
        } else {
          console.error('showError function not available. Error closing account:', errorMessage);
        }
      }
    } catch (error: any) {
      console.error('Exception during closeTableAccount for account ID:', accountId, error);
      const message = error.response?.data?.message || error.message || 'حدث خطأ أثناء إغلاق الحساب.';
      if (typeof showError === 'function') {
        showError(message);
      } else {
        console.error('showError function not available. Exception message for closeTableAccount:', message);
      }
    }
  };

  // Rest of the component logic will go here...
  // For now, let's return a simple structure
  return (
    <div className="waiter-dashboard">
      <div>محتوى الداشبورد سيتم إضافته</div>
    </div>
  );
}
