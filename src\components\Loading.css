.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  backdrop-filter: blur(2px);
}

.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
}

.spinner {
  border-radius: 50%;
  border-style: solid;
  animation: spin 1s linear infinite;
}

/* أحجام مختلفة للـ spinner */
.loading-sm .spinner {
  width: 20px;
  height: 20px;
  border-width: 2px;
}

.loading-md .spinner {
  width: 40px;
  height: 40px;
  border-width: 4px;
}

.loading-lg .spinner {
  width: 60px;
  height: 60px;
  border-width: 6px;
}

/* ألوان مختلفة للـ spinner */
.loading-primary .spinner {
  border-color: var(--primary-light);
  border-top-color: var(--primary);
}

.loading-secondary .spinner {
  border-color: var(--text-secondary);
  border-top-color: var(--text-primary);
}

.loading-white .spinner {
  border-color: rgba(255, 255, 255, 0.3);
  border-top-color: white;
}

.loading-text {
  margin: 0;
  color: var(--text-primary);
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  text-align: center;
}

.loading-overlay .loading-text {
  color: white;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* تأثير نبض للنص */
.loading-text {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}
