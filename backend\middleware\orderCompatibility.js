/**
 * Order Compatibility Middleware - محدث للعمل مع Railway + Vercel + MongoDB
 * يحول البيانات المرسلة من Frontend (Vercel) إلى الصيغة المتوقعة في Backend (Railway)
 */
const orderCompatibilityMiddleware = (req, res, next) => {
  try {
    console.log('🔄 Order Compatibility Middleware - البيانات المستلمة:', JSON.stringify(req.body, null, 2));
    
    const { 
      waiterName, 
      customerName, 
      tableNumber, 
      totalPrice,
      items,
      customer,
      table,
      orderType,
      status 
    } = req.body;

    // 1. تحويل بنية العميل
    if (!req.body.customer && customerName) {
      req.body.customer = { 
        name: customerName.trim(),
        phone: req.body.customerPhone || '',
        email: req.body.customerEmail || ''
      };
      console.log('✅ تم تحويل customerName إلى customer object');
    }

    // 2. تحويل بنية الطاولة
    // Ensure tableNumber is processed correctly, especially for dine-in orders.
    if (req.body.orderType !== 'delivery') {
      // For dine-in or takeaway, tableNumber might be relevant.
      // tableNumber comes from req.body.tableNumber (string)
      const originalTableNumberString = req.body.tableNumber; // Keep original for logging if needed

      if (originalTableNumberString !== undefined && originalTableNumberString !== null && originalTableNumberString.trim() !== '') {
        const tableNum = parseInt(originalTableNumberString, 10); // Use radix 10

        if (isNaN(tableNum) || tableNum <= 0) {
          console.error(`Invalid table number received in compatibility middleware: "${originalTableNumberString}"`);
          return res.status(400).json({ 
            success: false, 
            message: `رقم الطاولة غير صالح: "${originalTableNumberString}". يجب أن يكون رقماً صحيحاً أكبر من صفر.` 
          });
        }
        // If tableNum is valid, set it in req.body.table
        req.body.table = {
          number: tableNum,
          section: req.body.tableSection || 'القسم الرئيسي' // Default section if not provided
        };
        console.log('✅ Compatibility: Converted tableNumber to table object:', req.body.table);
      } else if (req.body.orderType === 'dine-in') {
        // If it's dine-in and tableNumber was empty, null, or undefined, it's an error.
        // Also covers the case where req.body.table might exist but not have a valid number.
        if (!req.body.table || req.body.table.number === undefined || req.body.table.number === null || isNaN(Number(req.body.table.number)) || Number(req.body.table.number) <= 0) {
          console.error(`Missing or invalid table number for dine-in order. Original tableNumber string: "${originalTableNumberString}", current req.body.table: ${JSON.stringify(req.body.table)}`);
          return res.status(400).json({ 
            success: false, 
            message: 'رقم الطاولة مطلوب ويجب أن يكون صالحاً لطلبات الصالة.' 
          });
        }
      }
    } else { 
      // For 'delivery' orders, table object might not be needed or can be explicitly empty.
      // If tableNumber was passed for a delivery order, it will be ignored unless specific logic handles it.
      if (req.body.table === undefined) {
        req.body.table = {}; // Ensure table object exists, can be empty for delivery.
      }
    }

    // 3. إضافة orderType الافتراضي
    if (!req.body.orderType) {
      req.body.orderType = 'dine-in';
      console.log('✅ تم إضافة orderType افتراضي: dine-in');
    }

    // 4. إصلاح بنية المنتجات - تحويل productId إلى product
    if (req.body.items && Array.isArray(req.body.items)) {
      req.body.items = req.body.items.map(item => {
        const fixedItem = { ...item };
        
        if (item.productId && !item.product) {
          fixedItem.product = item.productId;
          delete fixedItem.productId;
          console.log('✅ تم تحويل productId إلى product لمنتج:', item.name || item.product);
        }
        
        if (item.name && !fixedItem.productName) {
          fixedItem.productName = item.name;
        }
        
        // Ensure subtotal is present and is a number
        if (typeof fixedItem.subtotal !== 'number' || isNaN(fixedItem.subtotal)) {
          if (typeof fixedItem.price === 'number' && typeof fixedItem.quantity === 'number' && !isNaN(fixedItem.price) && !isNaN(fixedItem.quantity)) {
            fixedItem.subtotal = fixedItem.price * fixedItem.quantity;
          } else {
            fixedItem.subtotal = 0; 
            console.log(`⚠️ Item \\"${fixedItem.productName || fixedItem.product || 'Unknown'}\\" subtotal defaulted to 0 due to missing/invalid price/quantity or missing/invalid subtotal.`);
          }
        } else {
          // Ensure it's a number, even if it was provided as a string that looks like a number
          fixedItem.subtotal = parseFloat(fixedItem.subtotal);
          if (isNaN(fixedItem.subtotal)) {
            console.log(`⚠️ Item \\"${fixedItem.productName || fixedItem.product || 'Unknown'}\\" provided subtotal \\"${item.subtotal}\\" is not a valid number, defaulted to 0.`);
            fixedItem.subtotal = 0;
          }
        }
        
        return fixedItem;
      });
      console.log('✅ Items processed, ensuring subtotal initialization.');
    }

    // 5. إصلاح بنية الإجماليات - Initialize required fields for validation
    if (!req.body.totals) {
      req.body.totals = {};
    }

    // Initialize/ensure numeric values for discount and tax, defaulting to 0.
    req.body.totals.discount = parseFloat(req.body.totals.discount || req.body.discountAmount);
    if (isNaN(req.body.totals.discount)) {
      req.body.totals.discount = 0;
    }

    req.body.totals.tax = parseFloat(req.body.totals.tax);
    if (isNaN(req.body.totals.tax)) {
      req.body.totals.tax = 0;
    }

    // Calculate initial subtotal from items if not already provided or if invalid.
    // This ensures totals.subtotal is present and numeric for validation.
    let currentSubtotal = parseFloat(req.body.totals.subtotal);
    if (isNaN(currentSubtotal)) {
        if (req.body.items && Array.isArray(req.body.items)) {
            req.body.totals.subtotal = req.body.items.reduce((sum, item) => {
                // item.subtotal should be a number by now from the previous step
                return sum + (item.subtotal || 0); 
            }, 0);
        } else {
            req.body.totals.subtotal = 0; // Default if no items
        }
    } else {
        req.body.totals.subtotal = currentSubtotal; // Use provided valid number
    }
    
    // Initialize total if not already provided or if invalid.
    // This ensures totals.total is present and numeric for validation.
    let currentTotal = parseFloat(req.body.totals.total);
    if (isNaN(currentTotal)) {
        let calculatedTotal = req.body.totals.subtotal + req.body.totals.tax - req.body.totals.discount;
        
        if (req.body.orderType === 'delivery' && req.body.delivery && typeof req.body.delivery.fee === 'number' && !isNaN(req.body.delivery.fee)) {
            calculatedTotal += req.body.delivery.fee;
        }
        req.body.totals.total = Math.max(0, calculatedTotal); // Ensure total is not negative
    } else {
        req.body.totals.total = currentTotal; // Use provided valid number
    }
    
    console.log('✅ Totals object prepared for Order model:', JSON.stringify(req.body.totals, null, 2));
    // 6. إضافة payment الافتراضي
    if (!req.body.payment) {
      req.body.payment = {
        method: 'cash',
        status: 'pending'
      };
      console.log('✅ تم إضافة payment object افتراضي');
    }

    // 7. إصلاح staff object - سيتم التعامل معه في route
    if (waiterName && !req.body.staff) {
      req.body.waiterName = waiterName;
      console.log('✅ تم حفظ waiterName للمعالجة لاحقاً');
    }

    // 8. تنظيف البيانات القديمة لتجنب التعارض
    delete req.body.customerName;
    delete req.body.tableNumber;
    // delete req.body.totalPrice; // Keep totalPrice if used elsewhere, but it won't override model calculations
    delete req.body.discountAmount; // Moved to totals.discount

    console.log('🎯 البيانات النهائية بعد التحويل:', {
      customer: req.body.customer,
      table: req.body.table,
      orderType: req.body.orderType,
      items: req.body.items?.length || 0,
      totals: req.body.totals,
      payment: req.body.payment,
      waiterName: req.body.waiterName
    });

    next();
  } catch (error) {
    console.error('❌ خطأ في Order Compatibility Middleware:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في معالجة بيانات الطلب',
      error: error.message
    });
  }
};

module.exports = orderCompatibilityMiddleware;
