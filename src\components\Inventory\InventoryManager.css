/* Inventory Manager Styles */
.inventory-manager {
  padding: 1.5rem;
  max-width: 1400px;
  margin: 0 auto;
  background: var(--light-bg, #f8f9fa);
  min-height: 100vh;
}

/* Loading State */
.inventory-manager.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  color: var(--primary-color, #2c3e50);
}

.loading-spinner i {
  font-size: 2rem;
  color: var(--primary-color, #2c3e50);
}

/* Header */
.inventory-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  gap: 2rem;
}

.header-left h1 {
  margin: 0 0 0.5rem 0;
  color: var(--primary-color, #2c3e50);
  font-size: 2rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.header-left p {
  color: var(--text-muted, #95a5a6);
  font-size: 1.1rem;
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.btn-new-item {
  background: var(--success-color, #27ae60);
  color: var(--white, #ffffff);
  border: none;
  border-radius: 0.5rem;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-new-item:hover {
  background: #229954;
  transform: translateY(-1px);
}

/* Stats Cards */
.inventory-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: var(--white, #ffffff);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: var(--white, #ffffff);
}

.stat-card.total .stat-icon {
  background: var(--primary-color, #2c3e50);
}

.stat-card.in-stock .stat-icon {
  background: var(--success-color, #27ae60);
}

.stat-card.low-stock .stat-icon {
  background: var(--warning-color, #f39c12);
}

.stat-card.out-of-stock .stat-icon {
  background: var(--error-color, #e74c3c);
}

.stat-card.expired .stat-icon {
  background: var(--purple, #9b59b6);
}

.stat-content h3 {
  margin: 0 0 0.25rem 0;
  font-size: 1.75rem;
  font-weight: bold;
  color: var(--primary-color, #2c3e50);
}

.stat-content p {
  margin: 0;
  color: var(--text-muted, #95a5a6);
  font-size: 0.875rem;
}

/* Filters */
.inventory-filters {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
  padding: 1rem;
  background: var(--white, #ffffff);
  border-radius: 0.75rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.search-group {
  position: relative;
  flex: 1;
  max-width: 300px;
}

.search-group i {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted, #95a5a6);
}

.search-group input {
  width: 100%;
  padding: 0.75rem 2.5rem 0.75rem 1rem;
  border: 1px solid var(--border-color, #ecf0f1);
  border-radius: 0.5rem;
  font-size: 0.9rem;
  background: var(--white, #ffffff);
  color: var(--text-color, #2c3e50);
}

.search-group input:focus {
  outline: none;
  border-color: var(--primary-color, #2c3e50);
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-group label {
  font-weight: 500;
  color: var(--text-color, #2c3e50);
  white-space: nowrap;
}

.filter-group select {
  padding: 0.5rem 1rem;
  border: 1px solid var(--border-color, #ecf0f1);
  border-radius: 0.375rem;
  background: var(--white, #ffffff);
  color: var(--text-color, #2c3e50);
  font-size: 0.9rem;
  cursor: pointer;
}

.refresh-btn {
  background: var(--info-color, #3498db);
  color: var(--white, #ffffff);
  border: none;
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.refresh-btn:hover {
  background: #2980b9;
}

/* Inventory List */
.inventory-list {
  margin-bottom: 2rem;
}

.inventory-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.inventory-card {
  background: var(--white, #ffffff);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  cursor: pointer;
  position: relative;
  border-left: 4px solid transparent;
}

.inventory-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.inventory-card.in_stock {
  border-left-color: #27ae60;
}

.inventory-card.low_stock {
  border-left-color: #f39c12;
}

.inventory-card.out_of_stock {
  border-left-color: #e74c3c;
}

.inventory-card.expired {
  border-left-color: #9b59b6;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.item-name {
  font-size: 1.25rem;
  font-weight: bold;
  color: var(--primary-color, #2c3e50);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.item-status {
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  color: var(--white, #ffffff);
  font-size: 0.75rem;
  font-weight: 500;
}

.item-info {
  margin-bottom: 1rem;
}

.info-row {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  color: var(--text-color, #2c3e50);
  font-size: 0.9rem;
}

.info-row i {
  color: var(--secondary-color, #f39c12);
  width: 1rem;
  text-align: center;
}

.item-details {
  margin-bottom: 1rem;
}

.stock-level {
  margin-bottom: 0.75rem;
}

.stock-bar {
  width: 100%;
  height: 8px;
  background: var(--border-color, #ecf0f1);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.stock-fill {
  height: 100%;
  transition: width 0.3s ease;
}

.stock-text {
  font-size: 0.875rem;
  color: var(--text-muted, #95a5a6);
}

.item-cost {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: var(--text-color, #2c3e50);
}

.item-cost i {
  color: var(--secondary-color, #f39c12);
}

.item-dates {
  margin-bottom: 1rem;
}

.last-restocked,
.expiry-date {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  color: var(--text-muted, #95a5a6);
}

.last-restocked i,
.expiry-date i {
  color: var(--secondary-color, #f39c12);
  width: 1rem;
}

.item-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.action-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.375rem;
}

.action-btn.movement {
  background: var(--info-color, #3498db);
  color: var(--white, #ffffff);
}

.action-btn.movement:hover {
  background: #2980b9;
}

/* No Inventory */
.no-inventory {
  text-align: center;
  padding: 4rem 2rem;
  color: var(--text-muted, #95a5a6);
}

.no-inventory i {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.no-inventory p {
  font-size: 1.1rem;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .inventory-manager {
    padding: 1rem;
  }

  .inventory-header {
    flex-direction: column;
    gap: 1rem;
  }

  .header-actions {
    width: 100%;
    justify-content: center;
  }

  .inventory-filters {
    flex-wrap: wrap;
    gap: 1rem;
  }

  .search-group {
    max-width: none;
    width: 100%;
  }

  .filter-group {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .refresh-btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .inventory-manager {
    padding: 0.75rem;
  }

  .header-left h1 {
    font-size: 1.5rem;
  }

  .inventory-stats {
    grid-template-columns: 1fr;
  }

  .stat-card {
    padding: 1rem;
  }

  .stat-icon {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }

  .stat-content h3 {
    font-size: 1.5rem;
  }

  .inventory-grid {
    grid-template-columns: 1fr;
  }

  .inventory-card {
    padding: 1rem;
  }

  .item-header {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }

  .item-actions {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .inventory-manager {
    padding: 0.5rem;
  }

  .header-left h1 {
    font-size: 1.25rem;
  }

  .header-left p {
    font-size: 0.9rem;
  }

  .stat-card {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }

  .inventory-card {
    padding: 0.75rem;
  }

  .item-details {
    text-align: center;
  }

  .item-dates {
    text-align: center;
  }

  .action-btn {
    width: 100%;
    justify-content: center;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .inventory-manager {
    background: #1a1a1a;
  }

  .stat-card,
  .inventory-filters,
  .inventory-card {
    background: #2d2d2d;
    color: #ffffff;
  }

  .search-group input,
  .filter-group select {
    background: #3d3d3d;
    color: #ffffff;
    border-color: #555555;
  }

  .stock-bar {
    background: #555555;
  }
}
