:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  
  /* منع الفيض الأفقي */
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
}

/* إعادة تعيين عامة لمنع المساحات الحمراء */
*, *::before, *::after {
  box-sizing: border-box;
  max-width: 100%;
}

html {
  overflow-x: hidden !important;
  width: 100%;
  max-width: 100vw;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden !important;
  padding: 0;
}

#root {
  overflow-x: hidden !important;
  width: 100%;
  max-width: 100vw;
  margin: 0;
  padding: 0;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
  padding: 0;
}

/* إصلاح شامل للمساحات الجانبية في الهاتف المحمول */
@media (max-width: 768px) {
  * {
    max-width: 100vw !important;
    box-sizing: border-box !important;
  }
  
  body, html, #root {
    width: 100vw !important;
    max-width: 100vw !important;
    overflow-x: hidden !important;
    margin: 0 !important;
    padding: 0 !important;
  }
  
  /* منع أي عنصر من تجاوز عرض الشاشة */
  div, section, article, main, header, footer, nav {
    max-width: 100vw !important;
    box-sizing: border-box !important;
  }
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}
