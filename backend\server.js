const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const mongoose = require('mongoose');
const { createServer } = require('http');
const { Server } = require('socket.io');
const { connectDB, getConnectionInfo, isConnected } = require('./config/database');
const config = require('./config/environment');
const SocketHandlers = require('./sockets/socketHandlers');
const MonitoringService = require('./services/monitoringService');

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: config.frontend.allowedOrigins,
    credentials: true
  }
});
const PORT = config.server.port;

// Tell Express to trust the first proxy (e.g., Vercel, Nginx, Heroku)
// This is important for express-rate-limit and other middleware that rely on client IP
app.set('trust proxy', 1);

// Security middleware
app.use(helmet());
app.use(compression());

// CORS configuration (moved before rate limiter)
app.use(cors({
  origin: config.frontend.allowedOrigins,
  credentials: true
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: config.rateLimit.windowMs,
  max: config.rateLimit.maxRequests
});
app.use(limiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Health check endpoint
app.get('/health', async (req, res) => {
  try {
    // Check database connection and get user count
    const isConnected = mongoose.connection.readyState === 1;
    let userCount = 0;
    let dbMessage = 'Database not connected';

    if (isConnected) {
      try {
        const User = require('./models/User');
        userCount = await User.countDocuments();
        dbMessage = `MongoDB Atlas connected - ${userCount} users`;
      } catch (error) {
        dbMessage = 'MongoDB Atlas connected but query failed';
        console.error('Health check query error:', error.message);
      }
    } else {
      dbMessage = 'MongoDB Atlas not connected';
      console.error('Health check: MongoDB not connected');
    }

    res.status(200).json({
      status: 'OK',
      message: 'Desha Coffee Backend is running',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      database: {
        connected: isConnected,
        message: dbMessage,
        userCount: userCount
      },
      environment: process.env.NODE_ENV || 'development'
    });
  } catch (error) {
    res.status(500).json({
      status: 'ERROR',
      message: 'Health check failed',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// API Health check endpoint
app.get('/api/health', async (req, res) => {
  try {
    const isConnected = mongoose.connection.readyState === 1;
    res.status(200).json({
      status: 'OK',
      message: 'API is working',
      timestamp: new Date().toISOString(),
      database: { connected: isConnected }
    });
  } catch (error) {
    res.status(500).json({
      status: 'ERROR',
      message: 'API health check failed',
      error: error.message
    });
  }
});

// API Routes
app.use('/api/auth', require('./routes/auth'));
app.use('/api/users', require('./routes/users'));
app.use('/api/products', require('./routes/products'));
app.use('/api/menu', require('./routes/products')); // Alias for products
app.use('/api/menu-items', require('./routes/products')); // Alias for products (WaiterDashboard compatibility)
app.use('/api/orders', require('./routes/orders'));
app.use('/api/categories', require('./routes/categories'));
app.use('/api/discount-requests', require('./routes/discount-requests'));
app.use('/api/inventory', require('./routes/inventory'));
app.use('/api/table-accounts', require('./routes/table-accounts'));
app.use('/api/reports', require('./routes/reports')); // Reports API

// Monitoring endpoints
app.get('/api/monitoring/status', async (req, res) => {
  try {
    if (!global.monitoringService) {
      return res.status(503).json({
        status: 'ERROR',
        message: 'Monitoring service not available'
      });
    }

    const status = await global.monitoringService.getSystemStatus();
    res.json({
      status: 'OK',
      timestamp: new Date().toISOString(),
      monitoring: status
    });
  } catch (error) {
    res.status(500).json({
      status: 'ERROR',
      message: 'Failed to get monitoring status',
      error: error.message
    });
  }
});

app.get('/api/monitoring/stock-alerts', async (req, res) => {
  try {
    if (!global.monitoringService) {
      return res.status(503).json({
        status: 'ERROR',
        message: 'Monitoring service not available'
      });
    }

    const alerts = await global.monitoringService.checkStockLevels();
    res.json({
      status: 'OK',
      timestamp: new Date().toISOString(),
      alerts
    });
  } catch (error) {
    res.status(500).json({
      status: 'ERROR',
      message: 'Failed to get stock alerts',
      error: error.message
    });
  }
});

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'مرحباً بك في خادم مقهى ديشا',
    version: '1.0.0',
    endpoints: {
      health: '/health',
      auth: '/api/auth',
      users: '/api/users',
      products: '/api/products',
      orders: '/api/orders',
      categories: '/api/categories',
      inventory: '/api/inventory',
      discountRequests: '/api/discount-requests',
      tableAccounts: '/api/table-accounts',
      monitoring: {
        status: '/api/monitoring/status',
        stockAlerts: '/api/monitoring/stock-alerts'
      }
    }
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Endpoint not found',
    message: `Cannot ${req.method} ${req.originalUrl}`,
    availableEndpoints: [
      'GET /',
      'GET /health',
      'POST /api/auth/login',
      'GET /api/users',
      'GET /api/products',
      'GET /api/orders',
      'GET /api/categories',
      'GET /api/inventory',
      'GET /api/discount-requests'
    ]
  });
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Error:', error);
  res.status(error.status || 500).json({
    error: 'Internal Server Error',
    message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
  });
});

// Initialize database and start server
const startServer = async () => {
  try {
    console.log('🚀 Starting Desha Coffee Backend...');
    console.log('🗄️ Database: MongoDB Atlas (Required)');

    // Connect to MongoDB Atlas (required)
    const mongoConnection = await connectDB();

    console.log('✅ MongoDB Atlas connected successfully');
    console.log('🎯 All data stored persistently in MongoDB Atlas');

    // Initialize Socket.IO handlers
    const socketHandlers = new SocketHandlers(io);

    // Socket.IO connection handling
    io.on('connection', (socket) => {
      socketHandlers.handleConnection(socket);
    });

    // Make socket handlers available globally for API routes
    global.socketHandlers = socketHandlers;

    // Initialize and start monitoring service
    const monitoringService = new MonitoringService(socketHandlers);
    monitoringService.start();
    console.log('📊 Monitoring service started successfully');

    // Make monitoring service available globally
    global.monitoringService = monitoringService;

    // Start server only after successful MongoDB connection
    server.listen(PORT, () => {
      console.log(`🚀 Desha Coffee Backend running on port ${PORT}`);
      console.log(`📍 Environment: ${process.env.NODE_ENV || 'development'}`);
      console.log(`🌐 Health check: http://localhost:${PORT}/health`);
      console.log(`🌐 API Base: http://localhost:${PORT}/api`);
      console.log(`🔌 Socket.IO: Enabled for real-time updates`);
      console.log(`📊 Database: MongoDB Atlas ✅`);
      console.log(`🔗 Connection: Persistent storage enabled`);
      console.log(`🔐 Security: Full authentication with encrypted passwords`);
      console.log(`📡 Monitoring: Stock alerts, system health, and order tracking`);
      console.log(`⚠️  Alerts: Low stock every 5min, System health every 10min, Long orders every 15min`);
      console.log(`🎯 Endpoints: /api/monitoring/status, /api/monitoring/stock-alerts`);
      console.log('🎉 Server ready to accept connections!');
    });
  } catch (error) {
    console.error('❌ Critical error starting server:', error.message);
    console.error('💥 Server startup failed - exiting');
    process.exit(1);
  }
};

// Start the server
startServer();

module.exports = app;
