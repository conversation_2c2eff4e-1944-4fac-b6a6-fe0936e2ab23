// filepath: backend/services/monitoringService.js
const Inventory = require('../models/Inventory');
const Order = require('../models/Order');
const User = require('../models/User');
const Table = require('../models/Table'); // Add this line

class MonitoringService {
  constructor(socketHandlers) {
    this.socketHandlers = socketHandlers;
    this.stockCheckInterval = null;
    this.systemHealthInterval = null;
    this.longRunningOrdersInterval = null;
    this.longOpenTablesInterval = null; // Add this line
    this.isRunning = false;
  }

  start() {
    if (this.isRunning) {
      console.log('⚠️ Monitoring service is already running');
      return;
    }

    console.log('🚀 Starting monitoring service...');
    this.isRunning = true;

    // Stock monitoring every 5 minutes
    this.stockCheckInterval = setInterval(() => {
      this.checkStockLevels();
    }, 5 * 60 * 1000);

    // System health monitoring every 10 minutes
    this.systemHealthInterval = setInterval(() => {
      this.checkSystemHealth();
    }, 10 * 60 * 1000);

    // Long running orders check every 15 minutes
    this.longRunningOrdersInterval = setInterval(() => {
      this.checkLongRunningOrders();
    }, 15 * 60 * 1000);

    // Long open tables check every 20 minutes (example interval)
    this.longOpenTablesInterval = setInterval(() => {
      this.checkLongOpenTables();
    }, 20 * 60 * 1000);


    // Run initial checks
    setTimeout(() => {
      this.checkStockLevels();
      this.checkSystemHealth();
      this.checkLongRunningOrders();
      this.checkLongOpenTables(); // Add this line
    }, 10000); // Wait 10 seconds after startup

    console.log('✅ Monitoring service started successfully');
  }

  stop() {
    if (!this.isRunning) {
      console.log('⚠️ Monitoring service is not running');
      return;
    }

    console.log('🛑 Stopping monitoring service...');
    
    if (this.stockCheckInterval) {
      clearInterval(this.stockCheckInterval);
      this.stockCheckInterval = null;
    }

    if (this.systemHealthInterval) {
      clearInterval(this.systemHealthInterval);
      this.systemHealthInterval = null;
    }

    if (this.longRunningOrdersInterval) {
      clearInterval(this.longRunningOrdersInterval);
      this.longRunningOrdersInterval = null;
    }

    if (this.longOpenTablesInterval) { // Add this block
      clearInterval(this.longOpenTablesInterval);
      this.longOpenTablesInterval = null;
    }

    this.isRunning = false;
    console.log('✅ Monitoring service stopped');
  }

  async checkStockLevels() {
    try {
      console.log('📦 Checking stock levels...');

      // Get low stock items
      const lowStockItems = await Inventory.getLowStock();
      
      // Get out of stock items
      const outOfStockItems = await Inventory.getOutOfStock();

      // Send alerts if needed
      if (lowStockItems.length > 0) {
        await this.sendLowStockAlert(lowStockItems);
      }

      if (outOfStockItems.length > 0) {
        await this.sendOutOfStockAlert(outOfStockItems);
      }

      // Check for critical stock levels (very low)
      const criticalStockItems = await Inventory.find({
        isActive: true,
        $expr: { 
          $and: [
            { $gt: ['$quantity', 0] },
            { $lte: ['$quantity', { $multiply: ['$min', 0.5] }] }
          ]
        }
      });

      if (criticalStockItems.length > 0) {
        await this.sendCriticalStockAlert(criticalStockItems);
      }

      console.log(`📊 Stock check completed: ${lowStockItems.length} low, ${outOfStockItems.length} out, ${criticalStockItems.length} critical`);

    } catch (error) {
      console.error('❌ Error checking stock levels:', error);
      this.socketHandlers.sendSystemHealthNotification('error', 'فشل في فحص مستويات المخزون');
    }
  }

  async sendLowStockAlert(items) {
    try {
      const message = `تحذير: ${items.length} صنف في المخزون المنخفض`;
      
      this.socketHandlers.sendRoleNotification('manager', message, {
        type: 'low-stock-batch-alert',
        itemsCount: items.length,
        items: items.map(item => ({
          id: item._id,
          name: item.name,
          quantity: item.quantity,
          min: item.min,
          category: item.category
        })),
        timestamp: new Date().toISOString()
      });

      // Also send individual alerts for critical items
      items.forEach(item => {
        this.socketHandlers.io.emit('low-stock-alert', {
          itemId: item._id,
          itemName: item.name,
          quantity: item.quantity,
          min: item.min,
          message: `المخزون منخفض: ${item.name} (${item.quantity}/${item.min})`,
          timestamp: new Date().toISOString()
        });
      });

      console.log(`⚠️ Sent low stock alerts for ${items.length} items`);
    } catch (error) {
      console.error('❌ Error sending low stock alert:', error);
    }
  }

  async sendOutOfStockAlert(items) {
    try {
      const message = `تحذير: ${items.length} صنف نفد من المخزون`;
      
      this.socketHandlers.sendRoleNotification('manager', message, {
        type: 'out-of-stock-batch-alert',
        itemsCount: items.length,
        items: items.map(item => ({
          id: item._id,
          name: item.name,
          category: item.category
        })),
        timestamp: new Date().toISOString()
      });

      // Also send individual alerts
      items.forEach(item => {
        this.socketHandlers.io.emit('out-of-stock-alert', {
          itemId: item._id,
          itemName: item.name,
          message: `نفد المخزون: ${item.name}`,
          timestamp: new Date().toISOString()
        });
      });

      console.log(`🚫 Sent out of stock alerts for ${items.length} items`);
    } catch (error) {
      console.error('❌ Error sending out of stock alert:', error);
    }
  }

  async sendCriticalStockAlert(items) {
    try {
      const message = `تحذير عاجل: ${items.length} صنف في مستوى حرج`;
      
      this.socketHandlers.broadcastUrgentNotification(message, 'critical_stock');

      this.socketHandlers.sendRoleNotification('manager', message, {
        type: 'critical-stock-alert',
        itemsCount: items.length,
        items: items.map(item => ({
          id: item._id,
          name: item.name,
          quantity: item.quantity,
          min: item.min,
          category: item.category
        })),
        timestamp: new Date().toISOString()
      });

      console.log(`🔥 Sent critical stock alerts for ${items.length} items`);
    } catch (error) {
      console.error('❌ Error sending critical stock alert:', error);
    }
  }

  async checkSystemHealth() {
    try {
      console.log('🏥 Checking system health...');

      const healthChecks = {
        database: false,
        socketConnections: 0,
        memoryUsage: 0,
        activeOrders: 0,
        errorCount: 0
      };

      // Check database connection
      const mongoose = require('mongoose');
      healthChecks.database = mongoose.connection.readyState === 1;

      // Check socket connections
      healthChecks.socketConnections = this.socketHandlers.getConnectedUsersByRole().total;

      // Check memory usage
      const memoryUsage = process.memoryUsage();
      healthChecks.memoryUsage = Math.round(memoryUsage.heapUsed / 1024 / 1024); // MB

      // Check active orders
      healthChecks.activeOrders = await Order.countDocuments({
        status: { $in: ['pending', 'preparing'] }
      });

      // Send health status
      const isHealthy = healthChecks.database && healthChecks.memoryUsage < 500;
      
      if (!isHealthy) {
        const issues = [];
        if (!healthChecks.database) issues.push('قاعدة البيانات غير متصلة');
        if (healthChecks.memoryUsage > 500) issues.push(`استخدام ذاكرة مرتفع: ${healthChecks.memoryUsage}MB`);

        this.socketHandlers.sendSystemHealthNotification('warning', 
          `مشاكل في النظام: ${issues.join(', ')}`);
      }

      // Send health update to managers
      this.socketHandlers.sendRoleNotification('manager', 
        `تحديث حالة النظام`, {
        type: 'system-health-update',
        health: healthChecks,
        status: isHealthy ? 'healthy' : 'warning',
        timestamp: new Date().toISOString()
      });

      console.log(`🏥 System health check completed:`, healthChecks);

    } catch (error) {
      console.error('❌ Error checking system health:', error);
      this.socketHandlers.sendSystemHealthNotification('error', 'فشل في فحص حالة النظام');
    }
  }

  async checkLongRunningOrders() {
    try {
      console.log('⏰ Checking for long running orders...');

      // Orders that have been preparing for more than 30 minutes
      const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);
      
      const longRunningOrders = await Order.find({
        status: 'preparing',
        'timing.preparingAt': { $lt: thirtyMinutesAgo }
      }).populate('staff.waiter', 'name username');

      if (longRunningOrders.length > 0) {
        const message = `تحذير: ${longRunningOrders.length} طلب قيد التحضير لأكثر من 30 دقيقة`;
        
        this.socketHandlers.sendRoleNotification('manager', message, {
          type: 'long-running-orders-alert',
          ordersCount: longRunningOrders.length,
          orders: longRunningOrders.map(order => ({
            id: order._id,
            orderNumber: order.orderNumber,
            waiterName: order.staff?.waiter?.name || 'غير محدد',
            preparingAt: order.timing?.preparingAt,
            duration: Math.round((Date.now() - new Date(order.timing?.preparingAt).getTime()) / (1000 * 60))
          })),
          timestamp: new Date().toISOString()
        });

        console.log(`⏰ Found ${longRunningOrders.length} long running orders`);
      }

      // Also check for very old pending orders (more than 15 minutes)
      const fifteenMinutesAgo = new Date(Date.now() - 15 * 60 * 1000);
      const oldPendingOrders = await Order.find({
        status: 'pending',
        createdAt: { $lt: fifteenMinutesAgo }
      }).populate('staff.waiter', 'name username');

      if (oldPendingOrders.length > 0) {
        const message = `تحذير: ${oldPendingOrders.length} طلب في الانتظار لأكثر من 15 دقيقة`;
        
        this.socketHandlers.sendRoleNotification('chef', message, {
          type: 'old-pending-orders-alert',
          ordersCount: oldPendingOrders.length,
          orders: oldPendingOrders.map(order => ({
            id: order._id,
            orderNumber: order.orderNumber,
            waiterName: order.staff?.waiter?.name || 'غير محدد',
            createdAt: order.createdAt,
            duration: Math.round((Date.now() - new Date(order.createdAt).getTime()) / (1000 * 60))
          })),
          timestamp: new Date().toISOString()
        });

        console.log(`⏰ Found ${oldPendingOrders.length} old pending orders`);
      }

    } catch (error) {
      console.error('❌ Error checking long running orders:', error);
    }
  }

  async checkLongOpenTables() {
    try {
      console.log('⏰ Checking for long open tables (more than 1 hour)...');
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);

      const longOpenTables = await Table.find({
        status: 'occupied',
        'stats.lastUsed': { $lte: oneHourAgo },
        isActive: true
      }).populate('assignedWaiter', 'name username');

      if (longOpenTables.length > 0) {
        const message = `تنبيه: ${longOpenTables.length} طاولة مفتوحة لأكثر من ساعة`;
        this.socketHandlers.sendRoleNotification('manager', message, {
          type: 'long-open-tables-alert',
          tablesCount: longOpenTables.length,
          tables: longOpenTables.map(table => {
            const openDurationMinutes = table.stats.lastUsed ? Math.round((Date.now() - new Date(table.stats.lastUsed).getTime()) / (1000 * 60)) : 0;
            return {
              id: table._id,
              tableNumber: table.number,
              section: table.section,
              waiterName: table.assignedWaiter ? (table.assignedWaiter.name || table.assignedWaiter.username) : 'غير محدد',
              openedAt: table.stats.lastUsed,
              durationInMinutes: openDurationMinutes
            };
          }),
          timestamp: new Date().toISOString()
        });
        console.log(`⏰ Found ${longOpenTables.length} long open tables.`);
      } else {
        console.log('⏰ No long open tables found.');
      }
    } catch (error) {
      console.error('❌ Error checking long open tables:', error);
      // Optionally notify system health about this specific failure
      // this.socketHandlers.sendSystemHealthNotification('error', 'فشل في فحص الطاولات المفتوحة لفترة طويلة');
    }
  }

  // Get monitoring statistics
  getStats() {
    return {
      isRunning: this.isRunning,
      intervals: {
        stockCheck: !!this.stockCheckInterval,
        systemHealth: !!this.systemHealthInterval,
        longRunningOrders: !!this.longRunningOrdersInterval,
        longOpenTables: !!this.longOpenTablesInterval // Add this line
      },
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      socketConnections: this.socketHandlers.getConnectedUsersByRole()
    };
  }

  async getSystemStatus() {
    try {
      // Get stock statistics
      const [lowStockItems, outOfStockItems, totalItems] = await Promise.all([
        Inventory.countDocuments({ 
          isActive: true,
          $expr: { $lte: ['$quantity', '$min'] }
        }),
        Inventory.countDocuments({ 
          isActive: true,
          quantity: 0 
        }),
        Inventory.countDocuments({ isActive: true })
      ]);

      // Get order statistics
      const [pendingOrders, preparingOrders, totalOrders] = await Promise.all([
        Order.countDocuments({ status: 'pending' }),
        Order.countDocuments({ status: 'preparing' }),
        Order.countDocuments({})
      ]);

      // Get user statistics
      const [activeUsers, totalUsers] = await Promise.all([
        User.countDocuments({ status: 'نشط' }),
        User.countDocuments({})
      ]);

      return {
        monitoring: this.getStats(),
        inventory: {
          total: totalItems,
          lowStock: lowStockItems,
          outOfStock: outOfStockItems
        },
        orders: {
          total: totalOrders,
          pending: pendingOrders,
          preparing: preparingOrders
        },
        users: {
          total: totalUsers,
          active: activeUsers
        },
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('❌ Error getting system status:', error);
      throw error;
    }
  }
}

module.exports = MonitoringService;
