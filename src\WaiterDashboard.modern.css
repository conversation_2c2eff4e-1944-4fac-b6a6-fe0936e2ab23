/* WaiterDashboard.modern.css - Modern RTL Responsive Coffee Waiter Dashboard Styles */
:root {
  --primary-color: #8B4513;
  --primary-dark: #654321;
  --primary-light: #D2B48C;
  --secondary-color: #F5F5DC;
  --accent-color: #FFD700;
  --bg-primary: #FAFAFA;
  --bg-secondary: #FFFFFF;
  --bg-dark: #2C2C2C;
  --bg-gray: #F8F9FA;
  --text-primary: #2C2C2C;
  --text-secondary: #666666;
  --text-light: wheat;
  --text-white: #FFFFFF;
  --border-color: #E0E0E0;
  --border-light: #F0F0F0;
  --border-dark: #CCCCCC;
  --shadow-light: 0 2px 4px rgba(0,0,0,0.05);
  --shadow-medium: 0 4px 8px rgba(0,0,0,0.1);
  --shadow-heavy: 0 8px 16px rgba(0,0,0,0.15);
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  --mobile-header-height: 60px;
  --mobile-nav-height: 70px;
  --mobile-content-padding: 1rem;
  --sidebar-width: 270px;
  --sidebar-bg: linear-gradient(135deg, #8B4513 0%, #654321 100%);
  --sidebar-radius: 0 16px 16px 0;
  --sidebar-shadow: 0 4px 24px rgba(0,0,0,0.08);
  --transition: all 0.3s cubic-bezier(.4,0,.2,1);
  --border-radius: 12px;
  --error: #e53935;
  --error-dark: #b71c1c;
}

body, html {
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  direction: rtl;
}

.waiter-dashboard {
  display: flex;
  min-height: 100vh;
  background-color: var(--bg-primary);
  position: relative;
  overflow-x: hidden;
}

.dashboard-sidebar {
  width: var(--sidebar-width);
  background: var(--sidebar-bg);
  color: var(--text-white);
  display: flex;
  flex-direction: column;
  border-radius: var(--sidebar-radius);
  box-shadow: var(--sidebar-shadow);
  z-index: 100;
  min-height: 100vh;
  transition: var(--transition);
  position: relative;
}
.dashboard-sidebar.hidden { display: none !important; }
.dashboard-sidebar.visible { display: flex; }

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 1.5rem 1rem 1.5rem;
  border-bottom: 1px solid rgba(255,255,255,0.08);
}
.sidebar-logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.3rem;
  font-weight: bold;
  color: var(--accent-color);
}
.sidebar-close-btn {
  background: rgba(255,255,255,0.1);
  border: none;
  color: white;
  padding: 0.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  transition: var(--transition);
}
/* Cart Item Notes Styles */
.cart-item-notes {
  margin-top: 0.75rem;
  width: 100%;
}

.notes-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.notes-input {
  width: 100%;
  min-height: 50px;
  padding: 0.75rem;
  border: 2px solid var(--border-light);
  border-radius: 8px;
  font-size: 0.85rem;
  font-family: inherit;
  resize: vertical;
  transition: var(--transition);
  background: var(--bg-secondary);
  direction: rtl;
}

.notes-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(139, 69, 19, 0.1);
}

.notes-input::placeholder {
  color: var(--text-secondary);
  opacity: 0.7;
}

/* Order Card Simplified Styles */
.order-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-light);
  margin-bottom: 1rem;
}

.order-status-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.order-total-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: var(--bg-gray);
  border-radius: var(--border-radius);
  font-weight: 600;
  font-size: 1.1rem;
}

.order-total-summary .total-amount {
  color: var(--primary-color);
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-content {
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-heavy);
  max-height: 90vh;
  overflow-y: auto;
  width: 100%;
  max-width: 800px;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-light);
  background: var(--bg-gray);
  border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.modal-header h2 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--primary-color);
  font-size: 1.25rem;
}

.close-modal-btn {
  background: var(--error);
  color: white;
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition);
}

.close-modal-btn:hover {
  background: var(--error-dark);
  transform: scale(1.1);
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  padding: 1.5rem;
  border-top: 1px solid var(--border-light);
  background: var(--bg-gray);
  border-radius: 0 0 var(--border-radius) var(--border-radius);
}

/* Order Details Modal */
.order-details-modal {
  max-width: 900px;
}

.order-details-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: var(--bg-gray);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-light);
}

.order-details-section h3 {
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--primary-color);
  font-size: 1.1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--border-light);
}

.order-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: var(--bg-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-light);
}

.info-label {
  font-weight: 600;
  color: var(--text-secondary);
}

.info-value {
  color: var(--text-primary);
  font-weight: 500;
}

.detailed-items-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.detailed-order-item {
  background: var(--bg-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-light);
  overflow: hidden;
}

.item-main-info {
  padding: 1rem;
}

.item-name-section h4 {
  margin: 0 0 0.5rem 0;
  color: var(--primary-color);
  font-size: 1.1rem;
}

.item-quantity-price {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.item-notes-section {
  background: var(--accent-color);
  background: rgba(255, 215, 0, 0.1);
  padding: 1rem;
  border-top: 1px solid var(--border-light);
}

.notes-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.notes-content {
  background: var(--bg-secondary);
  padding: 0.75rem;
  border-radius: 6px;
  border: 1px solid var(--border-light);
  font-style: italic;
  color: var(--text-primary);
}

/* Discount Section */
.discount-status {
  padding: 1rem;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
}

.discount-status.pending {
  background: rgba(255, 193, 7, 0.1);
  color: #856404;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.discount-status.approved {
  background: rgba(40, 167, 69, 0.1);
  color: #155724;
  border: 1px solid rgba(40, 167, 69, 0.3);
}

.discount-status.rejected {
  background: rgba(220, 53, 69, 0.1);
  color: #721c24;
  border: 1px solid rgba(220, 53, 69, 0.3);
}

.request-discount-btn {
  background: var(--accent-color);
  color: var(--text-primary);
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-weight: 500;
  transition: var(--transition);
}

.request-discount-btn:hover:not(:disabled) {
  background: #ffed4e;
  transform: translateY(-1px);
}

.request-discount-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Order Total Section */
.order-total-section {
  background: var(--primary-light);
  background: rgba(210, 180, 140, 0.2);
}

.total-breakdown {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.total-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
}

.total-row.discount-row .total-value {
  color: var(--error);
}

.total-row.final-total {
  border-top: 2px solid var(--primary-color);
  padding-top: 1rem;
  font-weight: 600;
  font-size: 1.2rem;
}

.total-row.final-total .total-value {
  color: var(--primary-color);
}

/* Discount Modal */
.discount-modal {
  max-width: 600px;
}

.discount-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

.form-input, .form-textarea {
  padding: 0.75rem;
  border: 2px solid var(--border-light);
  border-radius: 8px;
  font-size: 1rem;
  font-family: inherit;
  transition: var(--transition);
  direction: rtl;
}

.form-input:focus, .form-textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(139, 69, 19, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.discount-summary {
  background: var(--bg-gray);
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid var(--border-light);
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
}

.summary-row.discount span:last-child {
  color: var(--error);
  font-weight: 600;
}

.summary-row.total {
  border-top: 1px solid var(--border-light);
  font-weight: 600;
  font-size: 1.1rem;
  color: var(--primary-color);
}

.submit-discount-btn {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-weight: 500;
  transition: var(--transition);
}

.submit-discount-btn:hover:not(:disabled) {
  background: var(--primary-dark);
  transform: translateY(-1px);
}

.submit-discount-btn:disabled {
  background: var(--text-secondary);
  cursor: not-allowed;
  transform: none;
}

.cancel-btn {
  background: var(--text-secondary);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: var(--transition);
}

.cancel-btn:hover {
  background: #5a5a5a;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .modal-content {
    margin: 0.5rem;
    max-height: 95vh;
  }
  
  .modal-header, .modal-body, .modal-footer {
    padding: 1rem;
  }
  
  .order-info-grid {
    grid-template-columns: 1fr;
  }
  
  .item-quantity-price {
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .modal-footer {
    flex-direction: column;
  }
  
  .modal-footer button {
    width: 100%;
  }
}

.sidebar-nav {
  flex: 1;
  padding: 1.5rem 0.5rem 0 0.5rem;
}
.nav-menu {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}
.nav-item { width: 100%; }
.nav-link {
  width: 100%;
  background: none;
  border: none;
  color: var(--text-white);
  text-align: right;
  font-size: 1.1rem;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 0.75rem;
}
.nav-link.active, .nav-link:hover {
  background: rgba(255,255,255,0.08);
  color: var(--accent-color);
}

.sidebar-footer {
  padding: 1.5rem;
  border-top: 1px solid rgba(255,255,255,0.08);
  margin-top: auto;
}
.logout-btn {
  width: 100%;
  background: var(--error);
  color: #fff;
  border: none;
  border-radius: var(--border-radius);
  padding: 0.75rem 1rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: var(--transition);
}
.logout-btn:hover {
  background: var(--error-dark);
}

.dashboard-main {
  flex: 1;
  padding: 2rem 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  min-width: 0;
}
.content-card {
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  padding: 2rem 1.5rem;
  margin-bottom: 2rem;
}

.screen-header {
  margin-bottom: 1.5rem;
}
.screen-title {
  font-size: 2rem;
  font-weight: bold;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}
.screen-subtitle {
  color: var(--text-secondary);
  font-size: 1.1rem;
  margin-top: 0.5rem;
}

.menu-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}
.menu-item-card {
  background: var(--bg-gray);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  padding: 1.25rem 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  align-items: flex-start;
  border: 1px solid var(--border-color);
}
.menu-item-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}
.menu-item-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--primary-color);
}
.menu-item-price {
  font-size: 1rem;
  color: blue;
  font-weight: bold;
}
.menu-item-description {
  color: var(--text-secondary);
  font-size: 0.95rem;
}
.menu-item-categories {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}
.category-tag {
  background: var(--primary-light);
  color: var(--primary-color);
  border-radius: 8px;
  padding: 0.2rem 0.7rem;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.3rem;
}
.category-tag.small { font-size: 0.8rem; padding: 0.1rem 0.5rem; }
.add-to-cart-btn {
  background: var(--primary-color);
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 0.5rem 1.2rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}
.add-to-cart-btn:hover {
  background: var(--accent-color);
  color: var(--primary-dark);
}

/* Cart Section */
.cart-screen-content, .cart-section {
  margin-top: 1.5rem;
}
.cart-item-card, .cart-item {
  background: var(--bg-gray);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  padding: 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
  border: 1px solid var(--border-color);
}
.cart-item-info {
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
}
.cart-item-name {
  font-size: 1rem;
  font-weight: 600;
  color: var(--primary-color);
}
.cart-item-price {
  color: var(--accent-color);
  font-weight: bold;
}
.cart-item-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}
.quantity-btn {
  background: var(--primary-light);
  color: var(--primary-color);
  border: none;
  border-radius: 6px;
  padding: 0.3rem 0.7rem;
  font-size: 1rem;
  cursor: pointer;
  transition: var(--transition);
}
.quantity-btn:disabled { opacity: 0.5; cursor: not-allowed; }
.quantity-btn.plus { background: var(--accent-color); color: var(--primary-dark); }
.quantity-btn.minus { background: var(--primary-light); color: var(--primary-color); }
.quantity-display, .quantity {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0.5rem;
}
.remove-item-btn {
  background: var(--error);
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 0.3rem 0.7rem;
  font-size: 1rem;
  cursor: pointer;
  transition: var(--transition);
}
.remove-item-btn:hover { background: var(--error-dark); }

.cart-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
}
.clear-cart-btn, .submit-order-btn {
  background: var(--primary-color);
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 0.6rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: var(--transition);
}
.clear-cart-btn:hover { background: var(--error); }
.submit-order-btn:disabled { opacity: 0.5; cursor: not-allowed; }
.submit-order-btn:hover:not(:disabled) { background: var(--accent-color); color: var(--primary-dark); }

/* Order Summary */
.order-summary-section {
  background: var(--bg-gray);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  padding: 1.2rem 1rem;
  margin-top: 1.5rem;
}
.summary-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
.summary-items {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}
.summary-item {
  display: flex;
  justify-content: space-between;
  font-size: 1rem;
}
.summary-divider {
  border-bottom: 1px solid var(--border-color);
  margin: 0.5rem 0;
}
.summary-total {
  display: flex;
  justify-content: space-between;
  font-size: 1.1rem;
  font-weight: bold;
  color: var(--primary-color);
}

/* Forms */
.form-group {
  margin-bottom: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
}
.form-input {
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 0.5rem 1rem;
  font-size: 1rem;
  background: #fff;
  color: var(--text-primary);
  transition: var(--transition);
}
.form-input:focus {
  border-color: var(--primary-color);
  outline: none;
}

/* Empty States */
.empty-state, .empty-cart-state {
  text-align: center;
  color: var(--text-secondary);
  margin: 2rem 0;
}
.empty-cart-icon {
  font-size: 2.5rem;
  color: var(--primary-light);
  margin-bottom: 1rem;
}
.browse-menu-btn {
  background: var(--primary-color);
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 0.5rem 1.2rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  margin-top: 1rem;
  transition: var(--transition);
}
.browse-menu-btn:hover { background: var(--accent-color); color: var(--primary-dark); }

/* Sidebar Stats */
.sidebar-stats {
  margin: 2rem 0 1rem 0;
  background: rgba(255,255,255,0.07);
  border-radius: 10px;
  padding: 1rem 1.2rem;
  display: flex;
  flex-direction: column;
  gap: 0.7rem;
}
.stats-title {
  font-size: 1.1rem;
  color: var(--accent-color);
  font-weight: 600;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}
.stat-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: rgba(255,255,255,0.1);
  border-radius: 8px;
  padding: 0.5rem 1rem;
}
.stat-icon {
  font-size: 1.3rem;
  color: var(--accent-color);
}
.stat-details {
  display: flex;
  flex-direction: column;
  gap: 0.1rem;
}
.stat-value {
  font-size: 1.1rem;
  font-weight: 700;
}
.stat-label {
  font-size: 0.9rem;
  color: var(--text-light);
}

/* Orders Screen Styles */
.orders-stats, .tables-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: var(--bg-gray);
  border-radius: var(--border-radius);
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  border: 1px solid var(--border-color);
  transition: var(--transition);
}

.stat-card:hover {
  box-shadow: var(--shadow-medium);
  transform: translateY(-2px);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.stat-icon.pending { background: #ffc107; }
.stat-icon.preparing { background: #17a2b8; }
.stat-icon.ready { background: #28a745; }
.stat-icon.delivered { background: #6c757d; }
.stat-icon.active { background: var(--primary-color); }
.stat-icon.total { background: var(--accent-color); color: var(--primary-dark); }
.stat-icon.orders { background: var(--primary-light); color: var(--primary-color); }

.stat-info {
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
}

.stat-number {
  font-size: 1.8rem;
  font-weight: bold;
  color: var(--primary-color);
}

.stat-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
  font-weight: 500;
}

/* Orders List */
.orders-list, .tables-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.order-card, .table-card {
  background: var(--bg-gray);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
  overflow: hidden;
  transition: var(--transition);
}

.order-card:hover, .table-card:hover {
  box-shadow: var(--shadow-medium);
  transform: translateY(-2px);
}

.order-header, .table-header {
  padding: 1.5rem;
  background: linear-gradient(135deg, var(--primary-light) 0%, var(--secondary-color) 100%);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  border-bottom: 1px solid var(--border-color);
}

.order-info, .table-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.order-number, .table-number {
  font-size: 1.3rem;
  font-weight: bold;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
}

.order-meta, .table-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.order-meta span, .table-meta span {
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.order-status, .table-status {
  display: flex;
  align-items: center;
}

.status-badge {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  color: white;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;
}

.status-badge.active {
  background: var(--primary-color);
}

.status-badge.ready-to-close {
  background: var(--accent-color);
  color: var(--primary-dark);
}

/* Order Items */
.order-items, .table-orders {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-light);
}

.order-items h4, .table-orders h4 {
  margin: 0 0 1rem 0;
  color: var(--primary-color);
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.items-list, .table-orders-list {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.order-item, .table-order-item {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid var(--border-light);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.item-info {
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
}

.item-name {
  font-weight: 600;
  color: var(--primary-color);
}

.item-notes {
  font-size: 0.85rem;
  color: var(--text-secondary);
  font-style: italic;
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.item-details {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 0.9rem;
}

.item-quantity {
  background: var(--primary-light);
  color: var(--primary-color);
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-weight: 600;
}

.item-price {
  color: var(--text-secondary);
}

.item-total {
  font-weight: bold;
  color: var(--primary-color);
}

/* Order Summary in Table Cards */
.order-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  font-size: 0.9rem;
}

.order-summary .order-number {
  font-size: 1rem;
  margin: 0;
}

.order-items-count {
  color: var(--text-secondary);
}

.order-status {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  font-weight: 500;
}

.no-orders {
  text-align: center;
  color: var(--text-secondary);
  padding: 1rem;
  font-style: italic;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

/* Order Footer */
.order-footer, .table-footer {
  padding: 1.5rem;
  background: var(--bg-secondary);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.order-total, .table-total {
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
}

.total-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.total-amount {
  font-size: 1.2rem;
  font-weight: bold;
  color: var(--primary-color);
}

.order-actions, .table-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.deliver-btn, .details-btn, .cancel-btn, .add-order-btn, .view-details-btn, .close-account-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;
}

.deliver-btn {
  background: var(--accent-color);
  color: var(--primary-dark);
}

.deliver-btn:hover {
  background: #e6c200;
}

.details-btn, .view-details-btn {
  background: var(--primary-light);
  color: var(--primary-color);
}

.details-btn:hover, .view-details-btn:hover {
  background: #c9a876;
}

.cancel-btn, .close-account-btn {
  background: var(--error);
  color: white;
}

.cancel-btn:hover, .close-account-btn:hover {
  background: var(--error-dark);
}

.add-order-btn {
  background: var(--primary-color);
  color: white;
}

.add-order-btn:hover {
  background: var(--primary-dark);
}

/* Search Section Styles */
.search-section {
  background: var(--bg-gray);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid var(--border-color);
}

.search-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  color: var(--primary-color);
}

.search-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
}

.search-input-group {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  right: 1rem;
  color: var(--text-secondary);
  z-index: 2;
}

.search-input {
  width: 100%;
  padding: 0.75rem 3rem 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 1rem;
  background: white;
  transition: var(--transition);
}

.search-input:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 3px rgba(139, 69, 19, 0.1);
}

.clear-search {
  position: absolute;
  left: 1rem;
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: var(--transition);
}

.clear-search:hover {
  background: var(--bg-gray);
  color: var(--error);
}

.search-results {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-light);
}

.results-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.search-term {
  color: var(--primary-color);
}

.no-results {
  text-align: center;
  color: var(--text-secondary);
  padding: 1rem;
  background: var(--bg-secondary);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

/* Category Filter */
.category-filter {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.category-btn {
  padding: 0.5rem 1rem;
  border: 2px solid var(--border-color);
  background: white;
  color: var(--text-primary);
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;
}

.category-btn:hover {
  border-color: var(--primary-color);
  background: var(--primary-light);
  color: var(--primary-color);
}

.category-btn.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* Mobile Header Toggle */
.mobile-menu-toggle {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 1000;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.75rem;
  font-size: 1.2rem;
  cursor: pointer;
  box-shadow: var(--shadow-medium);
  transition: var(--transition);
}

.mobile-menu-toggle:hover {
  background: var(--primary-dark);
  transform: scale(1.1);
}

/* Content Container */
.content-container {
  max-width: 100%;
  margin: 0 auto;
  padding: 0;
}

/* Responsive Design for Orders and Tables */
@media (max-width: 768px) {
  .orders-stats, .tables-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.8rem;
  }
  
  .stat-card {
    padding: 0.8rem;
    flex-direction: column;
    text-align: center;
  }
  
  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }
  
  .order-header, .table-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .order-meta, .table-meta {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .order-footer, .table-footer {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .order-actions, .table-actions {
    justify-content: center;
  }
  
  .order-item, .table-order-item {
    flex-direction: column;
    gap: 0.8rem;
    align-items: stretch;
  }
  
  .item-details {
    justify-content: space-between;
  }
  
  .order-summary {
    flex-direction: column;
    gap: 0.5rem;
    align-items: stretch;
  }
  
  .category-filter {
    justify-content: center;
  }
  
  .search-input {
    padding: 1rem 3.5rem 1rem 1rem;
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .orders-stats, .tables-stats {
    grid-template-columns: 1fr;
  }
  
  .order-actions, .table-actions {
    flex-direction: column;
  }
  
  .deliver-btn, .details-btn, .cancel-btn, .add-order-btn, .view-details-btn, .close-account-btn {
    justify-content: center;
    width: 100%;
  }
}
