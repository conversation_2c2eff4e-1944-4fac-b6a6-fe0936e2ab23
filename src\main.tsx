import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';

import App from './App';
import './theme.css'; // Import our theme first
import './styles/global-enhancements.css'; // Global enhancements
import './index.css';
import './App.css';

// إخفاء تحذيرات DOM Mutation Events المهجورة والتحذيرات الأخرى
const originalConsoleWarn = console.warn;
const originalConsoleError = console.error;

console.warn = (...args: any[]) => {
  const message = args.join(' ');
  if (
    message.includes('DOMNodeInserted') ||
    message.includes('DOMNodeRemoved') ||
    message.includes('DOMCharacterDataModified') ||
    message.includes('DOMSubtreeModified') ||
    message.includes('mutation event') ||
    message.includes('Deprecation') ||
    message.includes('Support for this event type has been removed')
  ) {
    return; // إخفاء هذه التحذيرات
  }
  originalConsoleWarn.apply(console, args);
};

console.error = (...args: any[]) => {
  const message = args.join(' ');
  if (
    message.includes('DOMNodeInserted') ||
    message.includes('DOMNodeRemoved') ||
    message.includes('DOMCharacterDataModified') ||
    message.includes('DOMSubtreeModified') ||
    message.includes('mutation event') ||
    message.includes('Deprecation')
  ) {
    return; // إخفاء هذه الأخطاء
  }
  originalConsoleError.apply(console, args);
};

// إلغاء تسجيل جميع Service Workers لحل مشكلة التداخل
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    // إلغاء تسجيل جميع Service Workers الموجودة
    navigator.serviceWorker.getRegistrations().then(function(registrations) {
      for(let registration of registrations) {
        registration.unregister().then(() => {
          console.log('Service Worker unregistered successfully');
        });
      }
    });

    // مسح جميع الكاش
    if ('caches' in window) {
      caches.keys().then(function(names) {
        for (let name of names) {
          caches.delete(name);
        }
      });
    }
  });
}

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <App />
  </StrictMode>
);
