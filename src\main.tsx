import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';

import App from './App';
import './theme.css'; // Import our theme first
import './index.css';
import './App.css';

// إلغاء تسجيل جميع Service Workers لحل مشكلة التداخل
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    // إلغاء تسجيل جميع Service Workers الموجودة
    navigator.serviceWorker.getRegistrations().then(function(registrations) {
      for(let registration of registrations) {
        registration.unregister().then(() => {
          console.log('Service Worker unregistered successfully');
        });
      }
    });

    // مسح جميع الكاش
    if ('caches' in window) {
      caches.keys().then(function(names) {
        for (let name of names) {
          caches.delete(name);
        }
      });
    }
  });
}

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <App />
  </StrictMode>
);
