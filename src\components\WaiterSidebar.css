/* ========================================= */
/* Enhanced Waiter Sidebar - Based on waiter-sidebar-test.html */
/* ========================================= */

/* CSS Variables for consistent theming */
:root {
  --primary-color: #6d4c41;
  --primary-light: #8d6e63;
  --primary-dark: #5d4037;
  --accent-color: #ffab40;
  --accent-dark: #ff9800;
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --danger-color: #f44336;
  --info-color: #2196f3;
  --light-bg: #f5f7fa;
  --white: #ffffff;
  --border-radius: 12px;
  --border-radius-lg: 20px;
  --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  --box-shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.15);
  --transition: all 0.3s ease;
  --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Enhanced Animations */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* شريط العنوان البني */
.dashboard-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: white;
  padding: 1.5rem 2rem;
  box-shadow: var(--box-shadow);
  position: relative;
  z-index: 100;
  position: sticky;
  top: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.header-info h1 {
  font-size: 1.8rem;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.header-info p {
  opacity: 0.9;
  font-size: 1.1rem;
}

.header-stats {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* زر Toggle في شريط العنوان */
.sidebar-toggle-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-dark) 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius);
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: var(--transition);
  box-shadow: var(--box-shadow);
  white-space: nowrap;
  order: -1;
}

.sidebar-toggle-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--box-shadow-lg);
  background: linear-gradient(135deg, var(--accent-dark) 0%, #f57c00 100%);
}

.sidebar-toggle-btn i {
  font-size: 1.1rem;
}

/* القائمة الجانبية العمودية */
.vertical-sidebar {
  position: fixed;
  top: 0;
  right: 0;
  width: 300px;
  height: 100vh;
  background: linear-gradient(180deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: white;
  transform: translateX(100%);
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 10000;
  box-shadow: -5px 0 20px rgba(0, 0, 0, 0.3);
  overflow-y: auto;
  direction: rtl;
}

.vertical-sidebar.visible {
  transform: translateX(0);
}

.sidebar-content {
  padding: 1.5rem;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* رأس القائمة الجانبية */
.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 1rem;
  border-bottom: 2px solid rgba(255, 255, 255, 0.2);
}

.sidebar-logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.3rem;
  font-weight: 700;
  color: var(--accent-color);
}

.sidebar-logo i {
  font-size: 1.5rem;
  background: linear-gradient(135deg, var(--accent-color), #ffcc02);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.sidebar-close-btn {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  padding: 0.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: var(--transition);
  font-size: 1rem;
}

.sidebar-close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

/* قائمة التنقل */
.sidebar-nav {
  flex: 1;
}

.nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  width: 100%;
  padding: 1rem;
  background: transparent;
  border: none;
  color: white;
  text-align: right;
  cursor: pointer;
  border-radius: var(--border-radius);
  transition: var(--transition);
  font-size: 1rem;
  font-weight: 500;
  position: relative;
  overflow: hidden;
}

.nav-item::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 4px;
  height: 100%;
  background: var(--accent-color);
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.nav-item::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 1rem;
  width: 6px;
  height: 6px;
  background: var(--accent-color);
  border-radius: 50%;
  transform: translateY(-50%) scale(0);
  transition: transform 0.3s ease;
}

.nav-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(-5px);
}

.nav-item.active {
  background: linear-gradient(135deg, rgba(255, 171, 64, 0.2) 0%, rgba(255, 152, 0, 0.2) 100%);
  color: var(--accent-color);
  font-weight: 600;
}

.nav-item.active::before {
  transform: scaleY(1);
}

.nav-item.active::after {
  transform: translateY(-50%) scale(1);
}

.nav-icon {
  font-size: 1.2rem;
  width: 24px;
  text-align: center;
  flex-shrink: 0;
}

.nav-text {
  flex: 1;
}

/* إحصائيات سريعة */
.sidebar-stats {
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius);
  padding: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.stats-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--accent-color);
  font-size: 0.9rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  cursor: pointer;
  transition: var(--transition);
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-item:hover .stat-icon {
  transform: scale(1.1);
  background: rgba(255, 171, 64, 0.3);
}

.stat-item:hover .stat-value {
  color: var(--accent-color);
}

.stat-icon {
  background: rgba(255, 171, 64, 0.2);
  color: var(--accent-color);
  padding: 0.5rem;
  border-radius: 8px;
  font-size: 0.9rem;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  transition: var(--transition);
}

.stat-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  flex: 1;
}

.stat-value {
  font-weight: 700;
  font-size: 1.1rem;
  color: white;
  transition: var(--transition);
}

.stat-label {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
}

/* معلومات المستخدم */
.sidebar-user {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.user-avatar {
  background: linear-gradient(135deg, var(--accent-color), #ffcc02);
  color: var(--primary-dark);
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.3rem;
  font-weight: 600;
  flex-shrink: 0;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  flex: 1;
}

.user-name {
  font-weight: 600;
  font-size: 1rem;
  color: white;
}

.user-role {
  font-size: 0.85rem;
  color: var(--accent-color);
  font-weight: 500;
}

/* زر تسجيل الخروج */
.sidebar-footer {
  margin-top: auto;
}

.sidebar-footer .logout-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 1rem;
  background: linear-gradient(135deg, var(--danger-color) 0%, #d32f2f 100%);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: var(--transition);
  box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
}

.sidebar-footer .logout-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(244, 67, 54, 0.4);
  background: linear-gradient(135deg, #d32f2f 0%, #b71c1c 100%);
}

/* خلفية القائمة الجانبية للموبايل */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  backdrop-filter: blur(4px);
  animation: fadeIn 0.3s ease;
}

/* تحسين انميشن فتح القائمة */
.vertical-sidebar.visible .nav-item {
  animation: slideInRight 0.3s ease forwards;
  animation-delay: calc(var(--index, 0) * 0.1s);
}

/* تطبيق تأخير الانميشن على عناصر القائمة */
.nav-list .nav-item:nth-child(1) { --index: 0; }
.nav-list .nav-item:nth-child(2) { --index: 1; }
.nav-list .nav-item:nth-child(3) { --index: 2; }

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
  .vertical-sidebar {
    width: 280px;
  }
  
  .sidebar-content {
    padding: 1rem;
  }
  
  .sidebar-toggle-btn span {
    display: none;
  }
  
  .sidebar-toggle-btn {
    padding: 0.75rem;
    min-width: auto;
  }

  .header-content {
    padding: 0 1rem;
  }

  .dashboard-header {
    padding: 1rem 1.5rem;
  }
}

@media (max-width: 480px) {
  .vertical-sidebar {
    width: 90vw;
    max-width: 320px;
  }
}

/* تحسينات للتمرير السلس */
.vertical-sidebar {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 171, 64, 0.5) transparent;
}

.vertical-sidebar::-webkit-scrollbar {
  width: 6px;
}

.vertical-sidebar::-webkit-scrollbar-track {
  background: transparent;
}

.vertical-sidebar::-webkit-scrollbar-thumb {
  background: rgba(255, 171, 64, 0.5);
  border-radius: 3px;
}

.vertical-sidebar::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 171, 64, 0.7);
}

/* تحسين accessibility */
.nav-item:focus-visible {
  outline: 2px solid var(--accent-color);
  outline-offset: 2px;
}

.sidebar-toggle-btn:focus-visible {
  outline: 2px solid white;
  outline-offset: 2px;
}

/* إخفاء sidebar القديم عند استخدام الجديد */
.sidebar.old-sidebar {
  display: none !important;
}

/* التأكد من أن المحتوى الرئيسي لا يختفي خلف الـ sidebar */
.main-content {
  transition: var(--transition);
}

/* Header stats cards */
.header-stat-card {
  background: rgba(255, 255, 255, 0.1);
  padding: 1rem;
  border-radius: var(--border-radius);
  text-align: center;
  min-width: 80px;
}

.header-stat-card i {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: var(--accent-color);
}

.header-stat-card .stat-number {
  font-size: 1.5rem;
  font-weight: 700;
}

.header-stat-card .stat-label {
  font-size: 0.8rem;
  opacity: 0.8;
}

/* تحسينات للشاشات الصغيرة جداً */
@media (max-width: 360px) {
  .vertical-sidebar {
    width: 95vw;
  }
  
  .sidebar-content {
    padding: 0.75rem;
    gap: 1rem;
  }
  
  .nav-item {
    padding: 0.75rem;
    font-size: 0.9rem;
  }
  
  .sidebar-logo {
    font-size: 1.1rem;
  }
}
