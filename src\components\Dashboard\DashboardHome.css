/* Dashboard Home Styles */
.dashboard-home {
  padding: 1.5rem;
  max-width: 1400px;
  margin: 0 auto;
  background: var(--light-bg, #f8f9fa);
  min-height: 100vh;
}

/* Loading State */
.dashboard-home.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  color: var(--primary-color, #2c3e50);
}

.loading-spinner i {
  font-size: 2rem;
  color: var(--primary-color, #2c3e50);
}

/* Header */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: var(--white, #ffffff);
  border-radius: 1rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.welcome-section h1 {
  margin: 0;
  color: var(--primary-color, #2c3e50);
  font-size: 1.8rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.welcome-section h1 i {
  color: var(--secondary-color, #f39c12);
}

.role-badge {
  background: var(--primary-color, #2c3e50);
  color: var(--white, #ffffff);
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  margin-top: 0.5rem;
  display: inline-block;
}

.connection-indicator .status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
  font-size: 0.875rem;
}

.connection-indicator .status.connected {
  background: rgba(39, 174, 96, 0.1);
  color: var(--success-color, #27ae60);
}

.connection-indicator .status.disconnected {
  background: rgba(231, 76, 60, 0.1);
  color: var(--error-color, #e74c3c);
}

/* Current Shift */
.current-shift {
  background: var(--white, #ffffff);
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.shift-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.shift-header h2 {
  margin: 0;
  color: var(--primary-color, #2c3e50);
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.shift-status {
  padding: 0.375rem 0.75rem;
  border-radius: 0.5rem;
  font-weight: 500;
  font-size: 0.875rem;
}

.shift-status.active {
  background: rgba(39, 174, 96, 0.1);
  color: var(--success-color, #27ae60);
}

.shift-status.inactive {
  background: rgba(149, 165, 166, 0.1);
  color: var(--text-muted, #95a5a6);
}

.shift-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.shift-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: var(--light-bg, #f8f9fa);
  border-radius: 0.5rem;
}

.shift-item i {
  color: var(--secondary-color, #f39c12);
  width: 1.25rem;
}

/* Quick Stats */
.quick-stats {
  margin-bottom: 2rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  background: var(--white, #ffffff);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: var(--white, #ffffff);
}

.stat-card.orders .stat-icon {
  background: var(--primary-color, #2c3e50);
}

.stat-card.pending .stat-icon {
  background: var(--warning-color, #f39c12);
}

.stat-card.preparing .stat-icon {
  background: var(--info-color, #3498db);
}

.stat-card.ready .stat-icon {
  background: var(--success-color, #27ae60);
}

.stat-card.sales .stat-icon {
  background: var(--secondary-color, #f39c12);
}

.stat-card.tables .stat-icon {
  background: var(--purple, #9b59b6);
}

.stat-content h3 {
  margin: 0 0 0.25rem 0;
  font-size: 1.75rem;
  font-weight: bold;
  color: var(--primary-color, #2c3e50);
}

.stat-content p {
  margin: 0;
  color: var(--text-muted, #95a5a6);
  font-size: 0.875rem;
}

/* Quick Actions */
.quick-actions h2 {
  margin: 0 0 1.5rem 0;
  color: var(--primary-color, #2c3e50);
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.action-btn {
  background: var(--white, #ffffff);
  border: 2px solid var(--border-color, #ecf0f1);
  border-radius: 0.75rem;
  padding: 1.25rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  text-decoration: none;
  color: var(--text-color, #2c3e50);
}

.action-btn:hover {
  border-color: var(--primary-color, #2c3e50);
  background: var(--primary-color, #2c3e50);
  color: var(--white, #ffffff);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(44, 62, 80, 0.2);
}

.action-btn i {
  font-size: 1.5rem;
  transition: transform 0.2s ease;
}

.action-btn:hover i {
  transform: scale(1.1);
}

.action-btn span {
  font-weight: 500;
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-home {
    padding: 1rem;
  }

  .dashboard-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .welcome-section h1 {
    font-size: 1.5rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .stat-card {
    padding: 1rem;
  }

  .stat-icon {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }

  .stat-content h3 {
    font-size: 1.5rem;
  }

  .shift-details {
    grid-template-columns: 1fr;
  }

  .actions-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .action-btn {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .dashboard-home {
    padding: 0.75rem;
  }

  .dashboard-header,
  .current-shift,
  .stat-card {
    padding: 1rem;
  }

  .welcome-section h1 {
    font-size: 1.25rem;
  }

  .stat-content h3 {
    font-size: 1.25rem;
  }

  .actions-grid {
    grid-template-columns: 1fr;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .dashboard-home {
    background: #1a1a1a;
  }

  .dashboard-header,
  .current-shift,
  .stat-card,
  .action-btn {
    background: #2d2d2d;
    color: #ffffff;
  }

  .shift-item {
    background: #3d3d3d;
  }

  .stat-content h3 {
    color: #ffffff;
  }

  .action-btn {
    border-color: #444444;
  }

  .action-btn:hover {
    background: #3498db;
    border-color: #3498db;
  }
}
