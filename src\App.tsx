import React, { Suspense } from 'react';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import ErrorBoundary from './components/ErrorBoundary';
import Loading from './components/Loading';
import './App.css';
import ConnectionStatus from './components/ConnectionStatus';

// Lazy loading للصفحات
const LoginPage = React.lazy(() => import('./LoginPage'));
const ProtectedRoute = React.lazy(() => import('./ProtectedRoute'));
const WaiterDashboard = React.lazy(() => import('./WaiterDashboard'));
const ChefDashboard = React.lazy(() => import('./ChefDashboard'));
const ManagerDashboard = React.lazy(() => import('./ManagerDashboard'));
import './LoginPage.css';

function App() {
  return (
    <ErrorBoundary>
      <BrowserRouter>
        <Suspense fallback={<Loading overlay text="جاري تحميل الصفحة..." />}>
          <ConnectionStatus />
          <Routes>
            {/* صفحة الدخول */}
            <Route path="/" element={<LoginPage />} />
            <Route path="/login" element={<LoginPage />} />

            {/* لوحة تحكم النادل */}
            <Route
              path="/waiter"
              element={
                <ProtectedRoute allowedRoles={["waiter"]}>
                  <WaiterDashboard />
                </ProtectedRoute>
              }
            />

            {/* لوحة تحكم الطباخ */}
            <Route
              path="/chef"
              element={
                <ProtectedRoute allowedRoles={["chef"]}>
                  <ChefDashboard />
                </ProtectedRoute>
              }
            />

            {/* لوحة تحكم المدير */}
            <Route
              path="/manager"
              element={
                <ProtectedRoute allowedRoles={["manager"]}>
                  <ManagerDashboard />
                </ProtectedRoute>
              }
            />

            {/* إعادة توجيه أي مسار غير معروف إلى الصفحة الرئيسية */}
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </Suspense>
      </BrowserRouter>
    </ErrorBoundary>
  );
}

export default App;
