import React, { useState, useEffect, useCallback } from 'react';
import { authenticatedGet, authenticatedPut } from './utils/apiHelpers';
import { useToast } from './hooks/useToast';
import { notificationSound } from './utils/notificationSound';
import socket from './socket';
import './ChefDashboard.css';

interface ChefDashboardProps {
  user?: any;
  onLogout?: () => void;
}

interface OrderItem {
  _id: string;
  name: string;
  price: number;
  quantity: number;
  notes?: string;
  category?: string;
}

interface Order {
  _id: string;
  orderNumber: string;
  items: OrderItem[];
  totalAmount: number;
  status: 'pending' | 'preparing' | 'ready' | 'completed' | 'delivered';
  tableNumber?: number;
  customerName?: string;
  waiterName?: string;
  chefName?: string;
  createdAt: string;
  updatedAt: string;
  preparationTime?: number;
  notes?: string;
}

type FilterType = 'pending' | 'preparing' | 'ready' | 'all';

export default function ChefDashboard({ user: propUser, onLogout }: ChefDashboardProps) {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(false);
  const [currentFilter, setCurrentFilter] = useState<FilterType>('pending');
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [showOrderModal, setShowOrderModal] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const { showSuccess, showError } = useToast();

  // الحصول على بيانات المستخدم
  const user = propUser || JSON.parse(localStorage.getItem('user') || 'null');
  const chefName = user?.username || user?.name || localStorage.getItem('username') || 'الطباخ';
  const chefId = user?._id || user?.id || 'chef-user';

  // جلب الطلبات
  const fetchOrders = useCallback(async () => {
    try {
      setLoading(true);
      const response = await authenticatedGet('/api/orders');

      console.log('📊 استجابة API للطلبات:', response);

      // تحقق من أن الاستجابة مصفوفة مباشرة أو تحتوي على data
      if (Array.isArray(response)) {
        setOrders(response);
        console.log('✅ تم جلب الطلبات بنجاح:', response.length, 'طلب');
      } else if (response.success && Array.isArray(response.data)) {
        setOrders(response.data);
        console.log('✅ تم جلب الطلبات بنجاح:', response.data.length, 'طلب');
      } else if (response.data && Array.isArray(response.data)) {
        setOrders(response.data);
        console.log('✅ تم جلب الطلبات بنجاح:', response.data.length, 'طلب');
      } else {
        console.error('❌ خطأ في تنسيق البيانات:', response);
        setOrders([]);
      }
    } catch (error) {
      console.error('❌ خطأ في جلب الطلبات:', error);
      showError('فشل في جلب الطلبات');
      setOrders([]);
    } finally {
      setLoading(false);
    }
  }, [showError]);

  // تصفية الطلبات حسب النوع والطباخ
  const getFilteredOrders = useCallback(() => {
    let filtered = orders;

    // تصفية حسب النوع
    if (currentFilter === 'pending') {
      // الطلبات المعلقة - يمكن لأي طباخ قبولها
      filtered = orders.filter(order => order.status === 'pending');
    } else if (currentFilter === 'preparing') {
      // الطلبات قيد التحضير - عرض جميع الطلبات قيد التحضير
      filtered = orders.filter(order => order.status === 'preparing');
    } else if (currentFilter === 'ready') {
      // الطلبات الجاهزة - عرض جميع الطلبات الجاهزة
      filtered = orders.filter(order =>
        order.status === 'ready' || order.status === 'completed'
      );
    } else if (currentFilter === 'all') {
      // جميع الطلبات - عرض كل الطلبات مع التمييز
      filtered = orders.filter(order => {
        // عرض الطلبات المعلقة (يمكن لأي طباخ قبولها)
        if (order.status === 'pending') return true;

        // عرض طلبات هذا الطباخ فقط للحالات الأخرى
        return (order.chefName === chefName || order.chefName === user?.username);
      });
    }

    return filtered.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
  }, [orders, currentFilter, chefName, user?.username]);  // قبول طلب (تغيير الحالة إلى preparing)
  const acceptOrder = async (orderId: string) => {
    try {
      console.log('🔄 محاولة قبول الطلب:', { orderId, chefName, chefId });
      
      // التحقق من وجود token
      const token = localStorage.getItem('token') || localStorage.getItem('authToken');
      if (!token) {
        showError('جلسة المصادقة منتهية الصلاحية. يرجى تسجيل الدخول مرة أخرى.');
        if (onLogout) onLogout();
        return;
      }
      
      const response = await authenticatedPut(`/api/orders/${orderId}`, {
        status: 'preparing',
        chefName: chefName,
        chefId: chefId
      });

      console.log('📊 استجابة قبول الطلب:', response);

      if (response && (response.success || response.status === 'success')) {
        showSuccess('تم قبول الطلب بنجاح');
        fetchOrders();

        // إرسال إشعار عبر Socket.IO
        socket.emit('order-status-update', {
          orderId,
          status: 'preparing',
          chefName,
          chefId,
          message: `تم قبول الطلب من قبل ${chefName}`
        });
      } else {
        console.error('❌ فشل في قبول الطلب:', response);
        showError(response?.message || 'فشل في قبول الطلب');
      }
    } catch (error: any) {
      console.error('❌ خطأ في قبول الطلب:', error);
      
      if (error.message?.includes('Authentication required')) {
        showError('انتهت جلسة المصادقة. يرجى تسجيل الدخول مرة أخرى.');
        if (onLogout) onLogout();
      } else {
        showError('فشل في قبول الطلب');
      }
    }
  };  // إنهاء طلب (تغيير الحالة إلى ready)
  const completeOrder = async (orderId: string) => {
    try {
      console.log('🔄 محاولة إنهاء الطلب:', { orderId, chefName });
      
      // التحقق من وجود token
      const token = localStorage.getItem('token') || localStorage.getItem('authToken');
      if (!token) {
        showError('جلسة المصادقة منتهية الصلاحية. يرجى تسجيل الدخول مرة أخرى.');
        if (onLogout) onLogout();
        return;
      }
      
      const response = await authenticatedPut(`/api/orders/${orderId}`, {
        status: 'ready',
        chefName: chefName
      });

      console.log('📊 استجابة إنهاء الطلب:', response);

      if (response && (response.success || response.status === 'success')) {
        showSuccess('تم إنهاء الطلب بنجاح');
        fetchOrders();

        // إرسال إشعار عبر Socket.IO
        socket.emit('order-status-update', {
          orderId,
          status: 'ready',
          chefName,
          chefId,
          message: `تم إنهاء تحضير الطلب من قبل ${chefName}`
        });
      } else {
        console.error('❌ فشل في إنهاء الطلب:', response);
        showError(response?.message || 'فشل في إنهاء الطلب');
      }
    } catch (error: any) {
      console.error('❌ خطأ في إنهاء الطلب:', error);
      
      if (error.message?.includes('Authentication required')) {
        showError('انتهت جلسة المصادقة. يرجى تسجيل الدخول مرة أخرى.');
        if (onLogout) onLogout();
      } else {
        showError('فشل في إنهاء الطلب');
      }
    }
  };

  // تسجيل الخروج
  const handleLogout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('username');
    localStorage.removeItem('userId');
    if (onLogout) {
      onLogout();
    } else {
      window.location.href = '/';
    }
  };

  // عرض تفاصيل الطلب
  const showOrderDetails = (order: Order) => {
    setSelectedOrder(order);
    setShowOrderModal(true);
  };

  // إعداد Socket.IO
  useEffect(() => {
    fetchOrders();

    // تسجيل الطباخ في Socket.IO
    socket.emit('register-user', {
      userId: chefId,
      role: 'chef',
      name: chefName
    });

    socket.on('registration-confirmed', (data) => {
      console.log('✅ تم تسجيل الطباخ في Socket.IO:', data);
    });    // استقبال الطلبات الجديدة
    socket.on('new-order-notification', (data) => {
      console.log('🔔 طلب جديد وصل للطباخ:', data);
      
      // تشغيل صوت الإشعار
      notificationSound.playNotification();
      
      showSuccess(`🔔 طلب جديد رقم ${data.orderNumber || 'غير محدد'} من الطاولة ${data.tableNumber || 'غير محدد'}`);
      fetchOrders();
    });

    // استقبال تحديثات حالة الطلبات
    socket.on('order-status-update', (data) => {
      console.log('🔄 تحديث حالة طلب:', data);
      fetchOrders();
    });

    socket.on('connect', () => {
      console.log('🔌 الطباخ متصل بـ Socket.IO');
      socket.emit('register-user', {
        userId: chefId,
        role: 'chef',
        name: chefName
      });
    });

    return () => {
      socket.off('registration-confirmed');
      socket.off('new-order-notification');
      socket.off('order-status-update');
      socket.off('connect');
    };
  }, [fetchOrders, chefId, chefName, showSuccess]);

  const filteredOrders = getFilteredOrders();

  // حساب الإحصائيات - عرض جميع الطلبات
  const stats = {
    pending: orders.filter(o => o.status === 'pending').length,
    preparing: orders.filter(o => o.status === 'preparing').length,
    ready: orders.filter(o => o.status === 'ready' || o.status === 'completed').length,
    all: orders.length
  };

  return (
    <div className="chef-dashboard">
      {/* Header */}
      <header className="chef-header">
        <div className="header-content">
          <div className="header-left">
            <button
              className="sidebar-toggle"
              onClick={() => setSidebarOpen(!sidebarOpen)}
            >
              ☰
            </button>
            <h1>لوحة الطباخ</h1>
          </div>
          <div className="header-right">
            <span className="chef-name">مرحباً، {chefName}</span>
            <button className="logout-btn" onClick={handleLogout}>
              تسجيل الخروج
            </button>
          </div>
        </div>
      </header>

      <div className="dashboard-content">
        {/* Sidebar */}
        <aside className={`chef-sidebar ${sidebarOpen ? 'open' : ''}`}>
          <div className="sidebar-content">
            <div className="chef-profile">
              <div className="chef-avatar">👨‍🍳</div>
              <h3>{chefName}</h3>
              <p>طباخ</p>
            </div>

            <nav className="filter-nav">
              <button
                className={`filter-btn ${currentFilter === 'pending' ? 'active' : ''}`}
                onClick={() => setCurrentFilter('pending')}
              >
                <span className="filter-icon">⏳</span>
                <span className="filter-text">قيد الانتظار</span>
                <span className="filter-count">{stats.pending}</span>
              </button>

              <button
                className={`filter-btn ${currentFilter === 'preparing' ? 'active' : ''}`}
                onClick={() => setCurrentFilter('preparing')}
              >
                <span className="filter-icon">👨‍🍳</span>
                <span className="filter-text">قيد التحضير</span>
                <span className="filter-count">{stats.preparing}</span>
              </button>

              <button
                className={`filter-btn ${currentFilter === 'ready' ? 'active' : ''}`}
                onClick={() => setCurrentFilter('ready')}
              >
                <span className="filter-icon">✅</span>
                <span className="filter-text">جاهزة</span>
                <span className="filter-count">{stats.ready}</span>
              </button>

              <button
                className={`filter-btn ${currentFilter === 'all' ? 'active' : ''}`}
                onClick={() => setCurrentFilter('all')}
              >
                <span className="filter-icon">📋</span>
                <span className="filter-text">الكل</span>
                <span className="filter-count">{stats.all}</span>
              </button>
            </nav>
          </div>
        </aside>

        {/* Main Content */}
        <main className="chef-main">
          <div className="orders-section">
            <div className="section-header">
              <h2>
                {currentFilter === 'pending' && 'الطلبات قيد الانتظار'}
                {currentFilter === 'preparing' && 'الطلبات قيد التحضير'}
                {currentFilter === 'ready' && 'الطلبات الجاهزة'}
                {currentFilter === 'all' && 'جميع الطلبات'}
              </h2>
              <span className="orders-count">{filteredOrders.length} طلب</span>
            </div>

            {loading ? (
              <div className="loading">جاري التحميل...</div>
            ) : filteredOrders.length === 0 ? (
              <div className="no-orders">
                <div className="no-orders-icon">📋</div>
                <p>لا توجد طلبات في هذا القسم</p>
              </div>
            ) : (
              <div className="orders-grid">
                {filteredOrders.map(order => (
                  <div key={order._id} className={`order-card ${order.status}`}>
                    <div className="order-header">
                      <div className="order-number">#{order.orderNumber}</div>
                      <div className={`order-status ${order.status}`}>
                        {order.status === 'pending' && 'قيد الانتظار'}
                        {order.status === 'preparing' && 'قيد التحضير'}
                        {order.status === 'ready' && 'جاهز'}
                        {order.status === 'completed' && 'مكتمل'}
                      </div>
                    </div>

                    <div className="order-info">
                      {order.tableNumber && (
                        <div className="order-table">
                          <span className="icon">🏓</span>
                          طاولة {order.tableNumber}
                        </div>
                      )}
                      {order.customerName && (
                        <div className="order-customer">
                          <span className="icon">👤</span>
                          {order.customerName}
                        </div>
                      )}
                      {order.waiterName && (
                        <div className="order-waiter">
                          <span className="icon">🍽️</span>
                          {order.waiterName}
                        </div>
                      )}
                    </div>

                    <div className="order-items">
                      <div className="items-count">
                        {order.items.length} صنف - {order.totalAmount} ر.س
                      </div>
                      <div className="items-preview">
                        {order.items.slice(0, 2).map((item, index) => (
                          <span key={index} className="item-name">
                            {item.name} ({item.quantity})
                          </span>
                        ))}
                        {order.items.length > 2 && (
                          <span className="more-items">
                            +{order.items.length - 2} المزيد
                          </span>
                        )}
                      </div>
                    </div>

                    <div className="order-time">
                      <span className="icon">⏰</span>
                      {new Date(order.createdAt).toLocaleTimeString('ar-SA', {
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </div>

                    <div className="order-actions">                      <button
                        className="details-btn"
                        onClick={() => showOrderDetails(order)}
                      >
                        <i className="fas fa-eye"></i>
                        التفاصيل
                      </button>{order.status === 'pending' && (
                        <button
                          className="accept-btn"
                          onClick={() => {
                            console.log('🔘 تم الضغط على زر قبول الطلب:', order._id);
                            acceptOrder(order._id);
                          }}
                        >
                          <i className="fas fa-check"></i>
                          قبول الطلب
                        </button>
                      )}                      {order.status === 'preparing' && (
                        <button
                          className="complete-btn"
                          onClick={() => {
                            console.log('🔘 تم الضغط على زر إنهاء التحضير:', order._id);
                            completeOrder(order._id);
                          }}
                        >
                          <i className="fas fa-check-circle"></i>
                          إنهاء التحضير
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </main>
      </div>

      {/* Order Details Modal */}
      {showOrderModal && selectedOrder && (
        <div className="modal-overlay" onClick={() => setShowOrderModal(false)}>
          <div className="modal-content" onClick={e => e.stopPropagation()}>
            <div className="modal-header">
              <h3>تفاصيل الطلب #{selectedOrder.orderNumber}</h3>
              <button
                className="close-btn"
                onClick={() => setShowOrderModal(false)}
              >
                ×
              </button>
            </div>

            <div className="modal-body">
              <div className="order-details">
                <div className="detail-row">
                  <span className="label">الحالة:</span>
                  <span className={`value status ${selectedOrder.status}`}>
                    {selectedOrder.status === 'pending' && 'قيد الانتظار'}
                    {selectedOrder.status === 'preparing' && 'قيد التحضير'}
                    {selectedOrder.status === 'ready' && 'جاهز'}
                    {selectedOrder.status === 'completed' && 'مكتمل'}
                  </span>
                </div>

                {selectedOrder.tableNumber && (
                  <div className="detail-row">
                    <span className="label">الطاولة:</span>
                    <span className="value">{selectedOrder.tableNumber}</span>
                  </div>
                )}

                {selectedOrder.customerName && (
                  <div className="detail-row">
                    <span className="label">العميل:</span>
                    <span className="value">{selectedOrder.customerName}</span>
                  </div>
                )}

                {selectedOrder.waiterName && (
                  <div className="detail-row">
                    <span className="label">النادل:</span>
                    <span className="value">{selectedOrder.waiterName}</span>
                  </div>
                )}

                {selectedOrder.chefName && (
                  <div className="detail-row">
                    <span className="label">الطباخ:</span>
                    <span className="value">{selectedOrder.chefName}</span>
                  </div>
                )}

                <div className="detail-row">
                  <span className="label">الوقت:</span>
                  <span className="value">
                    {new Date(selectedOrder.createdAt).toLocaleString('ar-SA')}
                  </span>
                </div>

                <div className="detail-row">
                  <span className="label">المجموع:</span>
                  <span className="value total">{selectedOrder.totalAmount} ر.س</span>
                </div>
              </div>

              <div className="order-items-details">
                <h4>الأصناف:</h4>
                <div className="items-list">
                  {selectedOrder.items.map((item, index) => (
                    <div key={index} className="item-detail">
                      <div className="item-info">
                        <span className="item-name">{item.name}</span>
                        <span className="item-quantity">× {item.quantity}</span>
                      </div>
                      <div className="item-price">{item.price * item.quantity} ر.س</div>
                      {item.notes && (
                        <div className="item-notes">ملاحظة: {item.notes}</div>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {selectedOrder.notes && (
                <div className="order-notes">
                  <h4>ملاحظات الطلب:</h4>
                  <p>{selectedOrder.notes}</p>
                </div>
              )}
            </div>

            <div className="modal-actions">              {selectedOrder.status === 'pending' && (
                <button
                  className="accept-btn"
                  onClick={() => {
                    console.log('🔘 تم الضغط على زر قبول الطلب من النافذة المنبثقة:', selectedOrder._id);
                    acceptOrder(selectedOrder._id);
                    setShowOrderModal(false);
                  }}
                >
                  <i className="fas fa-check"></i>
                  قبول الطلب
                </button>
              )}              {selectedOrder.status === 'preparing' && (
                <button
                  className="complete-btn"
                  onClick={() => {
                    console.log('🔘 تم الضغط على زر إنهاء التحضير من النافذة المنبثقة:', selectedOrder._id);
                    completeOrder(selectedOrder._id);
                    setShowOrderModal(false);
                  }}
                >
                  <i className="fas fa-check-circle"></i>
                  إنهاء التحضير
                </button>
              )}              <button
                className="close-modal-btn"
                onClick={() => setShowOrderModal(false)}
              >
                <i className="fas fa-times"></i>
                إغلاق
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}